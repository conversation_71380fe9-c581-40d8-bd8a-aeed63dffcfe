from app.lib import types as core_types


class RestrictedPayloadKeyError(Exception):
    """
    Raised when restricted key found during payload validation
    """


def validate_payload_keys(payload: core_types.DataDict) -> None:
    """
    Validate payload doesn't have keys which are restricted to send to FCM
    """
    restricted_keys = ('from', 'message_type')
    if any(key.startswith(('google', 'gcm')) or key in restricted_keys for key in payload):
        raise RestrictedPayloadKeyError
