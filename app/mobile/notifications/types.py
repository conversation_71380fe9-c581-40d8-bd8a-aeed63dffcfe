from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime
from enum import auto

from app.lib import types as core_types
from app.lib.database import DBRow
from app.lib.enums import NamedEnum


class MobileNotificationStatus(NamedEnum):
    new = auto()
    seen = auto()


@dataclass
class MobileNotification:
    id: str
    role_id: str
    title: str
    description: str
    status: MobileNotificationStatus
    date_created: datetime

    payload: core_types.DataDict | None = None

    @staticmethod
    def from_db(row: DBRow) -> MobileNotification:
        return MobileNotification(
            id=row.id,
            role_id=row.role_id,
            title=row.title,
            status=row.status,
            description=row.description,
            payload=row.payload,
            date_created=row.date_created,
        )

    def to_response(self) -> core_types.DataDict:
        return {
            'id': self.id,
            'role_id': self.role_id,
            'title': self.title,
            'description': self.description,
            'status': self.status,
            'payload': self.payload,
            'timestamp': int(self.date_created.timestamp()),
            'date_created': self.date_created.isoformat(),
        }
