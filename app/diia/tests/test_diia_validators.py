import base64

import pytest
from aiohttp.test_utils import make_mocked_request

from app import diia
from app.diia import utils as diia_utils
from app.diia import validators as diia_validators
from app.diia.schemas import (
    AddSignatureDiiaAction,
    AddSignatureSharedDiiaAction,
    DiiaRequestBody,
)
from app.diia.types import AddSignatureDiiaCtx, AddSignatureSharedDiiaCtx
from app.lib import eusign_utils
from app.lib.enums import SignatureFormat
from app.services import services
from app.sign_sessions import utils as sign_sessions
from app.signatures.enums import SignatureAlgo
from app.tests.common import prepare_client, prepare_document_data


@pytest.mark.parametrize(
    'sign_algo, signature',
    [
        (SignatureAlgo.DSTU, 'fake_signature'),
        (SignatureAlgo.ECDSA, 'ZmFrZV9zaWduYXR1cmU='),  # base64 encoded "fake_signature"
    ],
)
@pytest.mark.parametrize(
    'action',
    [
        diia.DiiaAction.add_signature,
        diia.DiiaAction.add_signature_shared,
    ],
)
async def test_validate_diia_action(aiohttp_client, action, sign_algo, signature, monkeypatch):
    """
    Given a request from diia
    When validating this request with validator
    Expected to return proper context
    """

    app, client, user = await prepare_client(aiohttp_client)

    async def mock_request(*args, **kwargs):
        return 'https://ukrainer.net'

    async def mock_read_request_data_b64(request):
        return DiiaRequestBody.model_validate(
            {
                'signedItems': [
                    {
                        'signature': signature,
                    },
                ],
            }
        )

    monkeypatch.setattr(diia_utils, '_request_token', mock_request)
    monkeypatch.setattr(diia_utils, '_request_deeplink', mock_request)
    monkeypatch.setattr(diia_validators, 'read_request_body', mock_read_request_data_b64)
    document = await prepare_document_data(
        app=app,
        owner=user,
        title='doc',
        extension='.docx',
    )

    async with services.db.acquire() as conn:
        sign_session = await sign_sessions.create_sign_session_for_inbox_email(
            conn=conn,
            document_id=document.id,
            email=user.email,
            edrpou=user.company_edrpou,
            role_id=None,
            created_by=user.role_id,
            expire_days=30,
        )

    file_content = b'test_mocked_content'
    hash_base64 = await eusign_utils.generate_hash_base64(file_content)

    if action == diia.DiiaAction.add_signature:
        context = AddSignatureDiiaAction(
            document_id=document.id,
            email=user.email,
            user_id=user.id,
            role_id=user.role_id,
            should_register_signer=False,
            sign_algo=sign_algo,
            source=diia.DiiaActionSource.web,
        )
    elif action == diia.DiiaAction.add_signature_shared:
        context = AddSignatureSharedDiiaAction(
            document_id=document.id,
            email=sign_session.email,
            sign_session_id=sign_session.id,
            sign_algo=sign_algo,
            source=diia.DiiaActionSource.web,
        )
    else:
        raise ValueError('Invalid action')

    ctx = await diia.request_action(
        action=context,
        files=[
            diia.DiiaDeeplinkFile(
                title=f'Підпис документу: {document.title}',
                hash=hash_base64,
            ),
        ],
    )
    request_id = ctx.request_id

    headers = {
        'X-Diia-Id-Action': 'hashedFilesSigning',
        'X-Document-Request-Trace-Id': request_id,
        'Content-Type': 'multipart/form-data; boundary=BOUNDARY',
    }
    request = make_mocked_request(
        method='POST',
        path='/api/v2/diia',
        headers=headers,
        payload=None,
    )

    ctx = await diia_validators.validate_diia_action(request=request)
    assert ctx is not None

    if action == diia.DiiaAction.add_signature:
        assert isinstance(ctx, AddSignatureDiiaCtx)
        assert ctx.signer is not None
        assert ctx.document_id is not None
        assert ctx.data['document_id'] == document.id
        ctx_data = ctx.data

    elif action == diia.DiiaAction.add_signature_shared:
        assert isinstance(ctx, AddSignatureSharedDiiaCtx)
        assert ctx.email is not None
        assert ctx.sign_session is not None
        assert ctx.document is not None
        assert ctx.signature_data['document_id'] == document.id
        ctx_data = ctx.signature_data

    else:
        raise AssertionError('Invalid action')

    # Check sign algo specific fields
    match sign_algo:
        case SignatureAlgo.ECDSA:
            assert ctx_data.get('format') == SignatureFormat.internal_asic
            assert ctx_data.get('p7s') == base64.b64decode(signature.encode())

        case SignatureAlgo.DSTU:
            assert ctx_data.get('format') == SignatureFormat.external_separated
            assert ctx_data.get('key') == signature
