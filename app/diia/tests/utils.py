import json
from http import HTTPStatus

from aiohttp import ClientResponse, FormData
from aiohttp.test_utils import TestClient

from app.lib.database import DBRow
from app.lib.helpers import to_base64str
from app.tests.common import prepare_auth_headers


async def request_create_diia_action(
    client: TestClient,
    document_id: str,
    sign_algo: str = 'DSTU',
    expected_status: int = HTTPStatus.OK,
    user: DBRow | None = None,
    headers: dict[str, str] | None = None,
) -> ClientResponse:
    response = await client.post(
        path=f'/internal-api/documents/{document_id}/diia',
        json={'sign_algo': sign_algo},
        headers=headers or prepare_auth_headers(user),
    )
    assert response.status == expected_status
    return response


async def request_handle_diia_action(
    client: TestClient,
    request_id: str,
    signature: bytes = b'signature',
    signature_2: bytes | None = None,
    expected_status: int = HTTPStatus.OK,
) -> ClientResponse:
    action_data = {
        'signedItems': [
            {
                'signature': to_base64str(signature),
            }
        ],
    }
    if signature_2:
        action_data['signedItems'].append(
            {
                'signature': to_base64str(signature_2),
            }
        )

    data = FormData()
    data.add_field(
        name='encodeData',
        value=to_base64str(json.dumps(action_data)),
        filename='any-filename',
    )

    response = await client.post(
        path='/api/v2/diia',
        data=data,
        headers={
            'X-Diia-Id-Action': 'hashedFilesSigning',
            'X-Document-Request-Trace-Id': request_id,
        },
    )
    assert response.status == expected_status
    return response
