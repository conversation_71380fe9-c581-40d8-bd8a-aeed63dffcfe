import json
import logging
from uuid import uuid4

from aiohttp import web
from aiohttp.client_exceptions import ClientError
from yarl import URL

from api.errors import (
    Code,
    DiiaRequestException,
    InvalidRequest,
    ServerError,
    Timeout,
)
from app.config.helpers import get_level
from app.config.schemas import DiiaConfig
from app.diia.enums import DiiaActionSource, DiiaActionStatus
from app.diia.schemas import DiiaActionUnion
from app.diia.types import DiiaDeeplinkFile, DiiaRequestActionCtx
from app.i18n import _
from app.lib.enums import AppLevel
from app.lib.helpers import to_json
from app.lib.qr import build_qr_png
from app.lib.types import DataDict
from app.services import services
from app.signatures.enums import SignatureAlgo

SESSION_TOKEN_TTL = 90 * 60  # seconds
REQUEST_TOKEN_TTL = 20 * 60  # seconds

logger = logging.getLogger(__name__)


def get_diia_action_source(request: web.Request) -> DiiaActionSource:
    """
    Parse the request path and determine the source of the request

    Similar to "get_signature_source_from_request" in "app.signatures.utils"
    """
    if request.path.startswith('/mobile-api'):
        return DiiaActionSource.mobile
    return DiiaActionSource.web


def get_diia_config() -> DiiaConfig:
    config = services.config.diia
    if not config:
        raise ServerError(
            code=Code.unhandled_error,
            reason=_('Конфігурація Дії не встановлена'),
        )
    return config


def request_action_data_key(request_id: str) -> str:
    return f'diia:r::{request_id}'


async def save_request_action_data(
    *,
    request_id: str,
    action: DiiaActionUnion,
) -> None:
    """
    Save request action data to redis because we can't pass more context in request to diia
    """
    redis_key = request_action_data_key(request_id)

    value = action.to_dict()

    await services.redis.setex(redis_key, value=to_json(value), time=REQUEST_TOKEN_TTL)


async def update_request_action_data(
    *,
    request_id: str,
    status: DiiaActionStatus,
    extra_data: DataDict | None = None,
) -> None:
    try:
        data = await get_request_action_data(request_id)
    except InvalidRequest:
        return

    data['action_status'] = status
    data['extra_data'] = {**data.get('extra_data', {}), **(extra_data or {})}

    redis_key = request_action_data_key(request_id)

    await services.redis.set(redis_key, value=to_json(data), xx=True, keepttl=True)


async def get_request_action_data(request_id: str) -> DataDict:
    redis_key = request_action_data_key(request_id)
    value = await services.redis.get(redis_key)

    if not value:
        logger.warning(
            'Diia request_id not found',
            extra={
                'request_id': request_id,
            },
        )
        raise InvalidRequest()

    return json.loads(value)


async def _request_token(url: URL) -> str:
    """
    Requests for dev and prod diia environmnent are different:
        - For dev environment we are required to provide `Authorization`
            header with auth_acquirer_token which is unique for dev environment
        - For prod environment we are required to pass auth_token in url
            No auth_acquirer_token required
    """

    headers = {}
    if get_level() in (AppLevel.dev, AppLevel.local):
        config = get_diia_config()
        headers['Authorization'] = f'Basic {config.auth_acquirer_token}'

    async with services.http_client.get(url=url, headers=headers) as response:
        resp = await response.json()
        return resp['token']


async def _request(url: URL, headers: DataDict, data: DataDict) -> DataDict:
    response = None
    try:
        async with services.http_client.post(
            url=url,
            headers=headers,
            json=data,
        ) as response:
            resp = await response.json()
            return resp

    except TimeoutError:
        logger.error(
            'Timeout during send request to Diia',
        )
        raise Timeout()
    except ClientError:
        logger.exception(
            msg='Exception during requesting diia.',
            extra={
                'status_code': response.status if response else None,
                'body': await response.text() if response else None,
            },
        )
        raise DiiaRequestException(
            code=Code.external_resource_error,
            reason=_('Сталася помилка під час запиту до сервісу Дія'),
        )


async def _request_deeplink(url: URL, headers: DataDict, data: DataDict) -> str:
    resp = await _request(url=url, headers=headers, data=data)
    try:
        return resp['deeplink']
    except KeyError:
        raise ServerError(
            code=Code.unhandled_error,
            reason=_('У відповіді від сервісу Дія відсутній deeplink'),
        )


async def get_session_token() -> str:
    redis_key = 'diia::session_token'

    session_token = await services.redis.get(redis_key)
    if session_token:
        return session_token

    config = get_diia_config()

    url = URL(config.host).with_path(f'/api/v1/auth/acquirer/{config.token}')
    session_token = await _request_token(url)

    await services.redis.setex(redis_key, value=session_token, time=SESSION_TOKEN_TTL)
    return session_token


async def request_deeplink(
    *,
    request_id: str,
    files: list[DiiaDeeplinkFile],
    sign_algo: SignatureAlgo | None,
    return_link: str | None = None,
) -> str:
    config = get_diia_config()

    session_token = await get_session_token()

    url = URL(config.host).with_path(
        f'/api/v2/acquirers/branch/{config.branch_id}/offer-request/dynamic'
    )

    headers = {'Authorization': f'Bearer {session_token}'}

    data = {
        'offerId': config.offer_id,
        'requestId': request_id,
        'data': {
            'hashedFilesSigning': {
                'hashedFiles': [
                    # You can pass several files to sign by one request. On success sign Diia
                    # sends back signed files in response in the same order as they were sent here.
                    {
                        'fileName': file.title,
                        'fileHash': file.hash,
                    }
                    for file in files
                ]
            }
        },
    }

    if sign_algo:
        data['signAlgo'] = sign_algo.value

    if return_link:
        data['returnLink'] = return_link

    return await _request_deeplink(url, headers, data)


async def request_action(
    *,
    action: DiiaActionUnion,
    files: list[DiiaDeeplinkFile],
    sign_algo: SignatureAlgo | None = None,
) -> DiiaRequestActionCtx:
    """
    Save context to redis and create deeplink for Diia to sign files
    """
    request_id = str(uuid4())
    context_dict = action.to_dict()
    document_id = context_dict.get('document_id')
    user_email = context_dict.get('email')
    user_id = context_dict.get('user_id')

    logger.info(
        msg='Diia request_id created',
        extra={
            'request_id': request_id,
            'document_id': document_id,
            'user_email': user_email,
            'user_id': user_id,
        },
    )

    await save_request_action_data(
        action=action,
        request_id=request_id,
    )

    return_link = None

    # If diia request was initiated from the mobile app, a return link is responsible for
    # redirecting customer back to the mobile app
    if action.source == DiiaActionSource.mobile:
        return_link = f'{services.config.app.domain}/app/documents'

    deeplink = await request_deeplink(
        request_id=request_id,
        files=files,
        sign_algo=sign_algo,
        return_link=return_link,
    )

    logger.info(
        'Diia generated deeplink',
        extra={
            'request_id': request_id,
            'files': files,
            'document_id': document_id,
            'user_email': user_email,
            'user_id': user_id,
            'sign_algo': sign_algo,
        },
    )

    qr_png = build_qr_png(deeplink)

    return DiiaRequestActionCtx(deeplink=deeplink, qr=qr_png, request_id=request_id)
