from collections import defaultdict
from collections.abc import Iterable

import sqlalchemy as sa
from psycopg2.errors import UniqueViolation

from api.errors import AlreadyExists, Object
from app.auth.enums import RoleStatus
from app.auth.tables import role_table
from app.auth.types import User
from app.groups import db
from app.groups.db import select_group_members
from app.groups.types import Group, GroupMember, GroupMembersMapping
from app.lib.database import DBConnection
from app.models import select_all
from app.services import services
from worker import topics


async def add_group(
    conn: DBConnection, name: str, user: User, group_id: str | None = None
) -> Group:
    try:
        optional_params = {}
        if group_id:
            optional_params['id'] = group_id
        return await db.insert_group(
            conn=conn,
            name=name,
            created_by=user.role_id,
            company_id=user.company_id,
            **optional_params,
        )
    except UniqueViolation:
        raise AlreadyExists(obj=Object.group, name=name)


async def change_group_name(
    conn: DBConnection,
    id: str,
    name: str,
) -> Group | None:
    try:
        return await db.update_group(
            conn=conn,
            id=id,
            name=name,
        )
    except UniqueViolation:
        raise AlreadyExists(obj=Object.group, name=name)


async def remove_group(
    conn: DBConnection,
    *,
    id: str,
    deleted_by: str,
) -> Group | None:
    """
    Soft delete group.
    """

    group = await db.update_group(
        conn=conn,
        id=id,
        deleted_by=deleted_by,
    )
    if group:
        await services.kafka.send_record(
            topics.GROUPS_CHANGED,
            value={
                'group_id': group.id,
                'role_id': deleted_by,
                'change_at': group.date_updated,
            },
        )

    return group


async def add_group_member(
    conn: DBConnection,
    group_id: str,
    role_id: str,
    initiator: User,
) -> GroupMember:
    try:
        member = await db.insert_group_member(
            conn=conn,
            group_id=group_id,
            role_id=role_id,
            created_by=initiator.role_id,
        )
    except UniqueViolation:
        raise AlreadyExists(obj=Object.group_member, id=role_id)

    await services.kafka.send_record(
        topics.GROUPS_CHANGED,
        value={
            'group_id': group_id,
            'role_id': role_id,
            'change_at': member.date_updated,
        },
    )

    return member


async def remove_group_members(
    conn: DBConnection,
    *,
    ids: list[str],
    deleted_by: str,
) -> list[GroupMember]:
    """
    Soft delete group member
    """

    members = await db.update_group_members(
        conn=conn,
        ids=ids,
        deleted_by=deleted_by,
    )

    for group_id in {member.group_id for member in members}:
        await services.kafka.send_record(
            topics.GROUPS_CHANGED,
            value={
                'group_id': group_id,
                'role_id': deleted_by,
                'change_at': members[0].date_updated,
            },
        )

    return members


def map_roles_to_groups(
    group_members: list[GroupMember], role_ids: set[str]
) -> defaultdict[str, list[str]]:
    group_role_map = defaultdict(list)
    for member in group_members:
        if member.role_id in role_ids:
            group_role_map[member.group_id].append(member.role_id)
    return group_role_map


async def get_group_members_by_group_ids(
    conn: DBConnection,
    *,
    group_ids: Iterable[str],
    company_id: str | None = None,
) -> GroupMembersMapping:
    """
    Get group members for each group id.
    Returns only active roles for given company.

    res = get_group_members_by_group_ids(...)
    res.get('group_id') -> [role_id, ...]
    """
    if not group_ids:
        return defaultdict(list)

    group_members = await select_group_members(
        conn=conn,
        group_ids=list(group_ids),
        is_deleted=False,
        company_id=company_id,
    )

    query = sa.select([role_table.c.id]).where(
        sa.and_(
            role_table.c.id.in_([member.role_id for member in group_members]),
            role_table.c.status == RoleStatus.active,
        )
    )
    roles = await select_all(conn, query)
    role_ids = {role.id for role in roles}

    group_role_map = map_roles_to_groups(group_members, role_ids)

    return group_role_map
