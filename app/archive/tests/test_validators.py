import pytest

from api.errors import <PERSON><PERSON><PERSON>ed, AlreadyExi<PERSON>, Code, DoesNotExist, Error, InvalidRequest
from app.archive.tests.utils import archive_documents_request
from app.archive.validators import validate_archive_documents, validate_unarchive_documents
from app.auth.db import update_company_config
from app.auth.types import User
from app.lib.enums import DocumentStatus
from app.services import services
from app.tests.common import (
    prepare_client,
    prepare_document_data,
    set_billing_company_config,
    with_elastic,
)


async def test_validate_archive_documents_with_role_permission(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_archive_documents=True)

    document = await prepare_document_data(app, user, status_id=DocumentStatus.finished.value)

    async with services.db.acquire() as conn:
        await validate_archive_documents(
            conn=conn,
            user=User.from_row(user),
            data={'document_ids': [document.id]},
        )


async def test_validate_archive_documents_not_in_allowed_status(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_archive_documents=True)

    document = await prepare_document_data(app, user, status_id=DocumentStatus.finished.value)

    async with services.db.acquire() as conn:
        # Add archive settings
        await update_company_config(
            conn=conn,
            company_id=user.company_id,
            config={'archive_settings': {'allow_fully_signed_documents': False}},
        )

        with pytest.raises(Error) as e:
            await validate_archive_documents(
                conn=conn,
                user=User.from_row(user),
                data={'document_ids': [document.id]},
            )
            assert e.value.details['document_ids'] == [document.id]


async def test_validate_archive_documents_no_documents(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    async with services.db.acquire():
        with pytest.raises(InvalidRequest):
            await validate_archive_documents(
                conn=services.db,
                user=User.from_row(user),
                data={'document_ids': []},
            )


async def test_validate_archive_documents_no_role_permission(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_archive_documents=False)

    document = await prepare_document_data(app, user, status_id=DocumentStatus.finished.value)

    async with services.db.acquire() as conn:
        with pytest.raises(AccessDenied):
            await validate_archive_documents(
                conn=conn,
                user=User.from_row(user),
                data={'document_ids': [document.id]},
            )


async def test_validate_archive_documents_some_doc_is_already_archived(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    document = await prepare_document_data(app, user, status_id=DocumentStatus.finished.value)
    document_archived = await prepare_document_data(
        app, user, status_id=DocumentStatus.finished.value
    )
    await archive_documents_request(
        client=client,
        user=user,
        documents=[document_archived],
    )

    async with services.db.acquire() as conn:
        with pytest.raises(AlreadyExists):
            await validate_archive_documents(
                conn=conn,
                user=User.from_row(user),
                data={'document_ids': [document.id, document_archived.id]},
            )


async def test_validate_unarchive_documents_with_role_permission(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_archive_documents=True)

    document_archived = await prepare_document_data(
        app, user, status_id=DocumentStatus.finished.value
    )
    await archive_documents_request(
        client=client,
        user=user,
        documents=[document_archived],
    )

    async with services.db.acquire() as conn:
        await validate_unarchive_documents(
            conn=conn,
            user=User.from_row(user),
            data={'document_ids': [document_archived.id]},
        )


async def test_validate_unarchive_documents_no_documents(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    async with services.db.acquire():
        with pytest.raises(InvalidRequest):
            await validate_unarchive_documents(
                conn=services.db,
                user=User.from_row(user),
                data={'document_ids': []},
            )


async def test_validate_unarchive_documents_no_role_permission(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_archive_documents=False)

    document_archived = await prepare_document_data(
        app, user, status_id=DocumentStatus.finished.value
    )
    await archive_documents_request(
        client=client,
        user=user,
        documents=[document_archived],
    )

    async with services.db.acquire() as conn:
        with pytest.raises(AccessDenied):
            await validate_unarchive_documents(
                conn=conn,
                user=User.from_row(user),
                data={'document_ids': [document_archived.id]},
            )


async def test_validate_unarchive_documents_some_doc_is_not_archived(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    document = await prepare_document_data(app, user, status_id=DocumentStatus.finished.value)
    document_archived = await prepare_document_data(
        app, user, status_id=DocumentStatus.finished.value
    )
    await archive_documents_request(
        client=client,
        user=user,
        documents=[document_archived],
    )

    async with services.db.acquire() as conn:
        with pytest.raises(DoesNotExist):
            await validate_unarchive_documents(
                conn=conn,
                user=User.from_row(user),
                data={'document_ids': [document.id, document_archived.id]},
            )


async def test_validate_archive_documents_company_without_archive_functional(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client, can_archive_documents=True, enable_pro_functionality=False
    )

    document = await prepare_document_data(app, user, status_id=DocumentStatus.finished.value)

    async with services.db.acquire() as conn:
        with pytest.raises(AccessDenied):
            await validate_archive_documents(
                conn=conn,
                user=User.from_row(user),
                data={'document_ids': [document.id]},
            )


@pytest.mark.parametrize(
    ('archive_limit', 'error_text'),
    [
        (1, 'Перевищено ліміт, зазначений тарифом'),
        (2, 'Перевищено ліміт, зазначений тарифом. Ви можете додати до архіву ще 1 документ'),
        (3, 'Перевищено ліміт, зазначений тарифом. Ви можете додати до архіву ще 2 документи'),
        (10, 'Перевищено ліміт, зазначений тарифом. Ви можете додати до архіву ще 9 документів'),
        (101, 'Перевищено ліміт, зазначений тарифом. Ви можете додати до архіву ще 100 документів'),
    ],
)
async def test_validate_archive_documents_limit_reached(aiohttp_client, archive_limit, error_text):
    app, client, user = await prepare_client(aiohttp_client, can_archive_documents=True)
    await set_billing_company_config(
        company_id=user.company_id, max_archive_documents_count=archive_limit
    )

    documents_to_archive = []
    for _ in range(archive_limit):
        document_to_archived = await prepare_document_data(
            app, user, status_id=DocumentStatus.finished.value
        )
        documents_to_archive.append(document_to_archived)

    archived_document = await prepare_document_data(
        app, user, status_id=DocumentStatus.finished.value
    )
    await archive_documents_request(client=client, user=user, documents=[archived_document])

    async with services.db.acquire() as conn:
        async with with_elastic(app, [archived_document.id, *[i.id for i in documents_to_archive]]):
            with pytest.raises(Error) as e:
                await validate_archive_documents(
                    conn=conn,
                    user=User.from_row(user),
                    data={'document_ids': [doc.id for doc in documents_to_archive]},
                )
        assert e.value.code == Code.overdraft
        assert e.value.reason == error_text
