{% import "email/macros.jinja2" as macros %}


{% macro header(
    text,
    image_link,
    image_style,
    button_link,
    button_text
) -%}
    <table role="presentation"
           style="width: 100%; background: #ECF4FF; padding: 30px 40px; margin-top: 0; box-sizing: border-box; table-layout:fixed;">
        <tr>
            <td style="max-width: 50%">
                <h1 style="font-size: 24px; line-height: 30px;">
                    {{ text }}
                </h1>
                {% if button_link  %}
                    {{ macros.button_link_v2(button_link, title=button_text) }}
                {% endif %}

            </td>
            <td style="max-width: 50%; text-align: center;">
                <img
                    style="{{ image_style }}"
                    src="{{ static(image_link) }}"
                    alt=""
                />
            </td>
        </tr>
    </table>
{%- endmacro %}


{# Header where image is on the left side and text with button is on the right side #}
{% macro header_right(
    text,
    image_link,
    image_style,
    button_link = None,
    button_text = None,
    image_width = "30%",
    subheader = None
) -%}
    <table role="presentation"
           style="width: 100%; background: #ECF4FF; padding: 30px 40px; margin-top: 0; box-sizing: border-box; table-layout:fixed;">
        <tr>
            <td style="width: {{ image_width }}; text-align: left;">
                <img
                    style="{{ image_style }}"
                    src="{{ static(image_link) }}"
                    alt=""
                />
            </td>
            <td style="width: auto; text-align: left;">
                <h1 style="font-size: 24px; line-height: 30px;">
                    {{ text }}
                </h1>
                {% if subheader %}
                    <p style="line-height: 24px; margin-top: 10px; margin-bottom: 15px;">
                        {{ subheader }}
                    </p>
                {% endif %}
                {% if button_link  %}
                    {{ macros.button_link_v2(button_link, title=button_text) }}
                {% endif %}

            </td>

        </tr>
    </table>
{%- endmacro %}
