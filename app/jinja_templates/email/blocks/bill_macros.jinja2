{% import "email/blocks/table_blocks.jinja2" as t %}

{% macro generate_bill_table(bill_number, date_created, rate, count_documents, total, requisites, description, first_name, edrpou, email) %}
    {# Generate two-column table #}
    <tr><th width="160"></th><th></th></tr>
    {{ t.generate_bold_normal_cell("Номер рахунку", bill_number, ":") }}
    {{ t.generate_bold_normal_cell("Дата формування", date_created, ":") }}

    {% if rate and count_documents %}
        {# Combined rate: web + integration #}
        {{ t.generate_bold_normal_cell("Тариф", rate, ":") }}
        {{ t.generate_bold_normal_cell("Кількість документів", count_documents, ":") }}
    {% elif count_documents %}
        {# Top-up balance #}
        {{ t.generate_bold_normal_cell("Кількість документів", count_documents, ":") }}
    {% else %}
        {# Buy rate, extension #}
        {{ t.generate_bold_normal_cell("Тариф", rate, ":") }}
    {% endif %}

    {{ t.generate_bold_normal_cell("Всього з ПДВ", total, ":") }}
    {{ t.generate_bold_normal_cell("Рахунок отримувача", requisites, ':')}}
    {{ t.generate_bold_normal_cell("Призначення платежу", description, ":") }}
    {{ t.generate_bold_normal_cell("Покупець", first_name, ":") }}
    {{ t.generate_bold_normal_cell("ЄДРПОУ", edrpou, ":") }}
    {{ t.generate_bold_normal_cell("Сформував рахунок", email, ":") }}
{% endmacro %}
