import pytest

from api.errors import Error
from app.auth.types import User
from app.directories.tests.utils import prepare_directory
from app.directories.validators import (
    _validate_directories_parent_change,
    validate_create_directory,
    validate_delete_directories,
    validate_update_directory,
)
from app.services import services
from app.tests.common import prepare_client, prepare_document_data


async def test_validate_directory_tree_depth(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    monkeypatch.setattr('app.directories.validators.MAX_DIRECTORIES_DEPTH', 1)

    dir1 = await prepare_directory(name='root1', company_id=user.company_id)
    dir2 = await prepare_directory(
        name='root1_child', company_id=user.company_id, parent_id=dir1.id
    )
    dir3 = await prepare_directory(name='root2', company_id=user.company_id)

    # 1) OK, creation should pass
    data = {
        'name': 'child',
        'parent_id': dir1.id,
    }
    async with services.db.acquire() as conn:
        await validate_create_directory(conn, user=User.from_row(user), raw_data=data)

    # 2) ERROR, creation fails due to max_depth reached
    data = {
        'name': 'child',
        'parent_id': dir2.id,
    }
    async with services.db.acquire() as conn:
        with pytest.raises(Error) as e:
            await validate_create_directory(conn, user=User.from_row(user), raw_data=data)

    assert 'Максимальна вкладеність папок не має перевищувати' in e.value.reason

    # 3) OK, update without reaching max_depth
    data = {
        'id': dir2.id,
        'name': 'child',
    }
    async with services.db.acquire() as conn:
        await validate_update_directory(conn, user=User.from_row(user), raw_data=data)

    # 4) ERROR, update with reaching max_depth
    async with services.db.acquire() as conn:
        with pytest.raises(Error) as e:
            await _validate_directories_parent_change(
                conn=conn, new_parent=dir2, directories=[dir3]
            )

    assert 'Максимальна вкладеність папок не має перевищувати' in e.value.reason


async def test_validate_delete_directory(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    dir1 = await prepare_directory(name='root1', company_id=user.company_id)
    dir2 = await prepare_directory(
        name='root1_child', company_id=user.company_id, parent_id=dir1.id
    )

    await prepare_document_data(app, user, directory_id=dir2.id)

    # Act
    async with services.db.acquire() as conn:
        with pytest.raises(Error) as e:
            await validate_delete_directories(
                conn, user=User.from_row(user), raw_data={'directory_ids': [dir1.id]}
            )

    assert 'Неможливо видалити папки, оскільки вони містять документи' in e.value.reason
    assert e.value.details == {'failed_directories': ['root1/root1_child']}


async def test_validate_parent_change(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    dir0 = await prepare_directory(name='root0', company_id=user.company_id)
    dir1 = await prepare_directory(name='root1', company_id=user.company_id)
    dir2 = await prepare_directory(
        name='root1_child1', company_id=user.company_id, parent_id=dir1.id
    )
    dir3 = await prepare_directory(
        name='root1_child2', company_id=user.company_id, parent_id=dir1.id
    )
    dir4 = await prepare_directory(
        name='root1_child2_child1', company_id=user.company_id, parent_id=dir3.id
    )

    # OK, check should pass with new_parent = None
    async with services.db.acquire() as conn:
        await _validate_directories_parent_change(
            conn=conn, new_parent=None, directories=[dir2, dir3]
        )

    # OK, check should pass
    async with services.db.acquire() as conn:
        await _validate_directories_parent_change(
            conn=conn, new_parent=dir0, directories=[dir2, dir3]
        )

    # FAIL, cand update parent for list of directories with different parents
    async with services.db.acquire() as conn:
        with pytest.raises(Error) as e:
            await _validate_directories_parent_change(
                conn=conn, new_parent=dir2, directories=[dir1, dir3]
            )
    assert 'Усі переміщувані папки мають знаходитись в спільній папці' in e.value.reason

    # FAIL, cant update parent_id to itself.id
    async with services.db.acquire() as conn:
        with pytest.raises(Error) as e:
            await _validate_directories_parent_change(
                conn=conn, new_parent=dir2, directories=[dir2]
            )
    assert 'Не можливо перемістити папку саму в себе' in e.value.reason

    # FAIL, cant move directory under its child
    async with services.db.acquire() as conn:
        with pytest.raises(Error) as e:
            await _validate_directories_parent_change(
                conn=conn, new_parent=dir4, directories=[dir1]
            )
    assert 'в одну із свої дочірніх папок' in e.value.reason
