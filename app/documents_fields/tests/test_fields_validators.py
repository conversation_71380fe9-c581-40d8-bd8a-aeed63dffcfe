from typing import Any
from unittest.mock import Mock

import pytest

from api.errors import InvalidRequest
from app.documents_fields.enums import DocumentFieldType
from app.documents_fields.validators import (
    validate_document_parameter,
)


# Test successful validation cases
@pytest.mark.parametrize(
    'field_type, value, expected_result',
    [
        pytest.param(DocumentFieldType.text, None, None, id='none_value'),
        pytest.param(DocumentFieldType.text, 'Hello World', 'Hello World', id='text_valid'),
        pytest.param(DocumentFieldType.text, '  Trimmed  ', 'Trimmed', id='text_trimmed'),
        pytest.param(DocumentFieldType.text, '', '', id='text_empty'),
        pytest.param(DocumentFieldType.number, '123', '123', id='number_integer'),
        pytest.param(DocumentFieldType.number, '123.45', '123.45', id='number_float'),
        pytest.param(DocumentFieldType.number, '123,45', '123.45', id='number_comma_separator'),
        pytest.param(DocumentFieldType.number, '123.00', '123', id='number_trailing_zeros'),
        pytest.param(DocumentFieldType.number, '-123', '-123', id='number_negative'),
        pytest.param(
            DocumentFieldType.date,
            '2023-10-01',
            '2023-09-30T21:00:00+00:00',
            id='date_only',
        ),
        pytest.param(
            DocumentFieldType.date,
            '2023-10-01 12:34:56',
            '2023-10-01T09:34:56+00:00',
            id='date_with_time',
        ),
        pytest.param(
            DocumentFieldType.date,
            '2023-10-01 12:34:56+03:00',
            '2023-10-01T09:34:56+00:00',
            id='date_with_timezone',
        ),
        pytest.param(
            DocumentFieldType.text,
            '  Leading and trailing spaces  ',
            'Leading and trailing spaces',
            id='text_with_spaces',
        ),
        pytest.param(DocumentFieldType.number, '0', '0', id='number_zero'),
        pytest.param(
            DocumentFieldType.number,
            '123.4567',
            '123.4567',
            id='number_invalid_decimal_places',
        ),
    ],
)
def test_validate_document_parameter_success(
    field_type: DocumentFieldType, value: str, expected_result
):
    field = Mock()
    field.type_ = field_type
    result = validate_document_parameter(value, field)
    assert result == expected_result


@pytest.mark.parametrize(
    'value, enum_options, expected_result',
    [
        pytest.param('option1', ['option1', 'option2'], 'option1', id='enum_valid_first_option'),
        pytest.param('option2', ['option1', 'option2'], 'option2', id='enum_valid_second_option'),
        pytest.param('', ['', 'option1'], '', id='enum_empty_option'),
    ],
)
def test_validate_document_parameter_enum_success(
    value: Any,
    enum_options: list[str],
    expected_result: Any,
):
    field = Mock()
    field.type_ = DocumentFieldType.enum
    field.enum_options = enum_options
    result = validate_document_parameter(value, field)
    assert result == expected_result


@pytest.mark.parametrize(
    'field_type, value, error_message',
    [
        pytest.param(
            DocumentFieldType.text,
            123,
            'value is not a string',
            id='text_non_string',
        ),
        pytest.param(
            DocumentFieldType.text,
            'a' * (1024 + 2),
            'Значення параметра перевищує максимальну довжину',
            id='text_too_long',
        ),
        pytest.param(
            DocumentFieldType.number,
            'not a number',
            'Значення параметра повинно бути числом',
            id='number_invalid',
        ),
        pytest.param(
            DocumentFieldType.number,
            123,
            'value is not a string',
            id='number_non_string',
        ),
        pytest.param(
            DocumentFieldType.date,
            123,
            'value is not a string',
            id='date_non_string',
        ),
        pytest.param(
            DocumentFieldType.date,
            'invalid date',
            "Invalid character while parsing year ('i', Index: 0)",
            id='date_invalid_format',
        ),
        pytest.param(
            DocumentFieldType.date,
            '2021-13-01',
            'month must be in 1..12',
            id='date_invalid_month',
        ),
        pytest.param(
            DocumentFieldType.date,
            '2022-02-29',
            'day is out of range for month',
            id='date_invalid_leap_year',
        ),
    ],
)
def test_validate_document_parameter_failures(
    field_type: DocumentFieldType, value: Any, error_message: str
):
    field = Mock()
    field.type_ = field_type

    with pytest.raises(InvalidRequest) as exc:
        validate_document_parameter(value, field)

    assert exc.value.to_dict()['details']['__root__'] == error_message


@pytest.mark.parametrize(
    'value, enum_options, error_message',
    [
        pytest.param(
            'option3',
            ['option1', 'option2'],
            'Значення параметра не входить в перелік можливих значень',
            id='enum_invalid_option',
        ),
        pytest.param(
            123,
            ['option1', 'option2'],
            'value is not a string',
            id='enum_non_string',
        ),
    ],
)
def test_validate_document_parameter_enum_failures(
    value: Any, enum_options: list[str], error_message: str
):
    field = Mock()
    field.type_ = DocumentFieldType.enum
    field.enum_options = enum_options

    with pytest.raises(InvalidRequest) as exc:
        validate_document_parameter(value, field)

    assert exc.value.to_dict()['details']['__root__'] == error_message
