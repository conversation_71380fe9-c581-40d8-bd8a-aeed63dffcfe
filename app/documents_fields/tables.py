import sqlalchemy as sa

from app.documents_fields.enums import DocumentFieldType
from app.models import columns, metadata

# A table for storing the parameters that can be filled in document
documents_fields_table = sa.Table(
    'documents_fields',
    metadata,
    columns.UUID(),
    columns.ForeignKey('company_id', 'companies.id', nullable=False),
    columns.Text('name'),
    columns.SoftEnum('type', DocumentFieldType),
    columns.JSONB('enum_options', nullable=True),
    columns.Boolean('is_required'),
    columns.ForeignKey('created_by', 'roles.id', nullable=False, ondelete='NO ACTION'),
    columns.DateCreated(),
    columns.DateUpdated(),
    columns.DateDeleted(),
    sa.Column('order', sa.Integer(), default=None, nullable=True),  # starts from 1
)


documents_fields_access_table = sa.Table(
    'documents_fields_accesses',
    metadata,
    columns.UUID(),
    columns.ForeignKey('field_id', 'documents_fields.id', nullable=False),
    columns.ForeignKey('role_id', 'roles.id', nullable=False, index=True),
    columns.ForeignKey('updated_by', 'roles.id', nullable=False),
    columns.DateUpdated(),
    columns.DateDeleted(),
    sa.Index(
        'uix_documents_fields_accesses_field_id_role_id',
        'field_id',
        'role_id',
        unique=True,
    ),
)


document_parameters_table = sa.Table(
    'document_parameters',
    metadata,
    columns.UUID(),
    columns.ForeignKey(
        name='document_id',
        mixed='documents.id',
        nullable=False,
        ondelete='NO ACTION',
        index=True,
    ),
    columns.ForeignKey('company_id', 'companies.id', nullable=False, index=True),
    columns.ForeignKey(
        name='field_id',
        mixed='documents_fields.id',
        nullable=False,
        ondelete='NO ACTION',
        index=True,
    ),
    columns.Boolean('is_required'),
    # Check README.md for details about how we store date in this field
    columns.Text('value', nullable=True),
    columns.ForeignKey('updated_by', 'roles.id', nullable=False, ondelete='NO ACTION'),
    columns.DateUpdated(),
    columns.DateCreated(),
)


sa.Index(
    'uix_document_parameters_company_document_field',
    document_parameters_table.c.document_id,
    document_parameters_table.c.company_id,
    document_parameters_table.c.field_id,
    unique=True,
)
