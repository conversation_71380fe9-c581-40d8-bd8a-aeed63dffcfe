from __future__ import annotations

import logging
from dataclasses import asdict
from datetime import datetime

from app.auth.types import User
from app.auth.utils import update_company_document_fields_count
from app.documents_fields.db import (
    count_document_fields,
    delete_document_parameters,
    delete_documents_field,
    insert_documents_field,
    select_accessible_documents_fields_ids_by_document,
    select_max_document_fields_order,
    update_documents_field,
    upsert_document_parameters,
    upsert_documents_fields_accesses,
)
from app.documents_fields.types import (
    CreateDocumentsFieldCtx,
    DeleteDocumentMetaCtx,
    DocumentParameterAdd,
    DocumentsField,
    DocumentsFieldsAccessCtx,
    ParameterSettingSchema,
    SwapDocumentsFieldCtx,
    UpdateDocumentMetaCtx,
    UpdateDocumentParametersCtx,
)
from app.lib.database import DBConnection
from app.lib.datetime_utils import parse_local_datetime, to_utc_datetime, utc_now

logger = logging.getLogger(__name__)


async def _get_max_order(conn: DBConnection, company_id: str) -> int:
    order = await select_max_document_fields_order(conn, company_id)
    if order is None:
        order = await count_document_fields(conn, company_id)

    return order + 1


async def create(conn: DBConnection, ctx: CreateDocumentsFieldCtx) -> DocumentsField:
    order = await _get_max_order(conn, ctx.company_id)
    data = {**ctx.to_db(), 'order': order}
    field = await insert_documents_field(conn, data)
    await update_company_document_fields_count(ctx.company_id)

    return field


async def delete(conn: DBConnection, ctx: DeleteDocumentMetaCtx) -> None:
    await delete_documents_field(conn, field_id=ctx.field.id_)
    await update_company_document_fields_count(ctx.field.company_id)


async def swap(conn: DBConnection, ctx: SwapDocumentsFieldCtx) -> None:
    async with conn.begin():
        await update_documents_field(
            conn=conn,
            field_id=ctx.first_field.id_,
            data={'order': ctx.second_field.order},
        )
        await update_documents_field(
            conn=conn,
            field_id=ctx.second_field.id_,
            data={'order': ctx.first_field.order},
        )


async def update(conn: DBConnection, ctx: UpdateDocumentMetaCtx) -> None:
    await update_documents_field(
        conn=conn,
        field_id=ctx.field_id,
        data={'name': ctx.name, 'enum_options': ctx.enum_options},
    )


async def add_access(conn: DBConnection, ctx: DocumentsFieldsAccessCtx, user: User) -> None:
    logger.info('Open fields access', extra={'ctx': asdict(ctx)})

    data = [
        {
            'field_id': field_id,
            'role_id': role_id,
            'updated_by': user.role_id,
            'date_updated': utc_now(),
            'date_deleted': None,
        }
        for field_id in ctx.fields_ids
        for role_id in ctx.roles_ids
    ]
    await upsert_documents_fields_accesses(conn, data)


async def delete_access(conn: DBConnection, ctx: DocumentsFieldsAccessCtx, user: User) -> None:
    logger.info('Open fields access', extra={'ctx': asdict(ctx)})

    data = [
        {
            'field_id': field_id,
            'role_id': role_id,
            'updated_by': user.role_id,
            'date_updated': utc_now(),
            'date_deleted': utc_now(),
        }
        for field_id in ctx.fields_ids
        for role_id in ctx.roles_ids
    ]
    await upsert_documents_fields_accesses(conn, data)


def _prepare_update_document_parameters(
    *, document_id: str, company_id: str, role_id: str, parameters: list[ParameterSettingSchema]
) -> list[DocumentParameterAdd]:
    return [
        {
            **parameter.to_dict(),
            'company_id': company_id,
            'document_id': document_id,
            'updated_by': role_id,
        }
        for parameter in parameters
    ]


async def update_document_parameters(
    conn: DBConnection,
    ctx: UpdateDocumentParametersCtx,
    *,
    user: User,
    document_id: str,
) -> None:
    """Create, update or delete document parameters"""

    async with conn.begin():
        if ctx.add:
            # fill company, role and document for parameters
            parameters = _prepare_update_document_parameters(
                document_id=document_id,
                company_id=user.company_id,
                role_id=user.role_id,
                parameters=ctx.add,
            )
            # insert or update provided parameters
            await upsert_document_parameters(conn, parameters)

        if ctx.delete:
            fields_ids = [f.id_ for f in ctx.delete]
            await delete_document_parameters(
                conn=conn,
                document_id=document_id,
                company_id=user.company_id,
                fields_ids=fields_ids,
            )

        # remove all parameters for fields and insert new
        if ctx.replace is not None:
            # Fill company, role and document for parameters.
            # Contains only parameters accessible by the current user.
            parameters = _prepare_update_document_parameters(
                document_id=document_id,
                company_id=user.company_id,
                role_id=user.role_id,
                parameters=ctx.replace,
            )

            # We can't remove fields to which the current user doesn't have access
            accessible_fields_ids = await select_accessible_documents_fields_ids_by_document(
                conn=conn, document_id=document_id, user=user
            )
            if accessible_fields_ids:
                await delete_document_parameters(
                    conn=conn,
                    document_id=document_id,
                    company_id=user.company_id,
                    fields_ids=accessible_fields_ids,
                )
            await upsert_document_parameters(conn, parameters)


async def create_document_parameters_on_upload(
    conn: DBConnection,
    *,
    documents_ids: list[str],
    company_id: str,
    role_id: str,
    parameters: list[ParameterSettingSchema],
) -> None:
    """
    Create document parameters for documents on upload
    """
    new_parameters = []
    for document_id in documents_ids:
        document_parameters = _prepare_update_document_parameters(
            document_id=document_id,
            company_id=company_id,
            role_id=role_id,
            parameters=parameters,
        )
        new_parameters.extend(document_parameters)

    # insert or update provided parameters
    await upsert_document_parameters(conn, new_parameters)


def parse_db_document_parameter_date(value: str | None) -> datetime | None:
    """
    Parse of a string coming from the "document_parameters.value" column in the DB
    and convert it to UTC timezone to be used in the application.

    Check app/documents_fields/README.md for more details about the format of the value

    """
    if not value:
        return None

    return to_utc_datetime(parse_local_datetime(value))


def parse_input_document_parameter_date(value: str) -> datetime:
    """
    Parse a date string coming from user input (filters, create/update parameters, etc.)
    and convert it to UTC timezone for storage in the database.
    """
    return to_utc_datetime(parse_local_datetime(value))


def format_document_parameter_date(dt: datetime) -> str:
    """
    Historically, we stored dates in different formats in the database, but the most used
    format is ISO 8601 in UTC timezone. This function is an effort to unify the format and
    use it everywhere where we insert or update date document parameter values.
    """

    assert dt.tzinfo is not None, 'Date should be timezone-aware'
    return to_utc_datetime(dt).isoformat()
