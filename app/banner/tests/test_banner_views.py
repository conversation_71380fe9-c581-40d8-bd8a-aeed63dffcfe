from http import HTTPStatus

from app.auth.enums import EmployeePositions
from app.banner.enums import (
    BannerActivityPeriod,
    BannerAudienceType,
    BannerColor,
    BannerIncomingDocumentsSignCount,
    BannerOutgoingDocumentsCount,
    BannerStatus,
    EmployeesCount,
)
from app.banner.tests.utils import (
    get_banner_contents,
    get_banners,
    prepare_banner,
    prepare_banner_content,
)
from app.lib.enums import Language
from app.tests.common import datetime_test, prepare_auth_headers, prepare_client, set_company_config

BANNER_ID_1 = '00000000-0000-0000-0000-000000000001'
BANNER_ID_2 = '00000000-0000-0000-0000-000000000002'


async def test_create_banner(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou='********',
        enable_pro_functionality=False,
        super_admin_permissions={'can_edit_special_features': True},
    )
    await set_company_config(
        app=app,
        company_id=super_admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    response = await client.post(
        path='/internal-api/banner',
        json={
            'color': 'green',
            'status': 'active',
            'positions': ['accountant'],
            'start_date': '2022-01-01T00:00:00+00:00',
            'end_date': '2022-01-01T00:00:00+00:00',
            'analytics_category': 'test',
            'audience_type': 'TOV',
            'days_before_signature_expires': 10,
            'content': [
                {
                    'language': 'uk',
                    'text': 'test',
                    'link_text': 'test',
                    'link_url': 'https://test.com',
                }
            ],
            'outgoing_documents_count': ['1_10', '100_plus'],
            'incoming_documents_sign_count': ['11_24', 'has_unsigned'],
            'activity_period': '1month',
            'employees_count': ['unknown', '1-10'],
        },
        headers=prepare_auth_headers(super_admin),
    )
    assert response.status == HTTPStatus.CREATED, await response.json()

    banners = await get_banners()
    assert len(banners) == 1
    assert banners[0].color == BannerColor.green
    assert banners[0].status == BannerStatus.new
    assert banners[0].positions == [EmployeePositions.accountant]
    assert banners[0].start_date == datetime_test('2022-01-01T00:00:00+00:00')
    assert banners[0].end_date == datetime_test('2022-01-01T00:00:00+00:00')
    assert banners[0].analytics_category == 'test'
    assert banners[0].audience_type == BannerAudienceType.TOV
    assert banners[0].outgoing_documents_count == [
        BannerOutgoingDocumentsCount.range_1_10,
        BannerOutgoingDocumentsCount.range_100_plus,
    ]
    assert banners[0].incoming_documents_sign_count == [
        BannerIncomingDocumentsSignCount.range_11_24,
        BannerIncomingDocumentsSignCount.has_unsigned,
    ]
    assert banners[0].activity_period == BannerActivityPeriod.last_1_month
    assert banners[0].employees_count == [EmployeesCount.unknown, EmployeesCount.range_1_10]

    content = await get_banner_contents(banner_id=banners[0].id_)
    assert len(content) == 1
    assert content[0].language == Language.uk
    assert content[0].text == 'test'
    assert content[0].link_text == 'test'
    assert content[0].link_url == 'https://test.com/'


async def test_update_banner(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou='********',
        enable_pro_functionality=False,
        super_admin_permissions={'can_edit_special_features': True},
    )
    await set_company_config(
        app=app,
        company_id=super_admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    await prepare_banner(
        banner_id=BANNER_ID_1,
        color=BannerColor.green,
        start_date='2022-01-01T00:00:00+00:00',
        end_date='2022-01-01T00:00:00+00:00',
        analytics_category='test',
        positions=[EmployeePositions.accountant],
    )
    await prepare_banner_content(
        banner_id=BANNER_ID_1,
        language=Language.uk,
        text='тест',
        link_text='тест',
        link_url='https://test1.com',
    )
    await prepare_banner(
        banner_id=BANNER_ID_2,
        color=BannerColor.blue,
        start_date='2023-01-01T00:00:00+00:00',
        end_date='2023-01-01T00:00:00+00:00',
        analytics_category='some-category',
        positions=[EmployeePositions.director],
    )
    await prepare_banner_content(
        banner_id=BANNER_ID_2,
        language=Language.en,
        text='test',
        link_text='test',
        link_url='https://test2.com',
    )

    response = await client.put(
        path=f'/internal-api/banner/{BANNER_ID_1}',
        json={
            'color': BannerColor.yellow.value,
            'status': BannerStatus.active.value,
            'positions': [EmployeePositions.director.value],
            'outgoing_documents_count': ['1_10', '100_plus'],
            'incoming_documents_sign_count': ['11_24', 'has_unsigned'],
            'employees_count': ['unknown', '1-10'],
            'start_date': '2022-01-02T00:00:00+00:00',
            'end_date': '2022-01-02T00:00:00+00:00',
            'analytics_category': 'test',
            'audience_type': 'FOP',
            'days_before_signature_expires': 15,
            'content': [
                {
                    'language': Language.uk.value,
                    'text': 'тест2',
                    'link_text': 'тест2',
                    'link_url': 'https://test3.com',
                }
            ],
        },
        headers=prepare_auth_headers(super_admin),
    )
    assert response.status == HTTPStatus.OK, await response.json()
    banners = await get_banners()
    assert len(banners) == 2
    banner1 = next(b for b in banners if b.id_ == BANNER_ID_1)
    assert banner1.color == BannerColor.yellow
    assert banner1.status == BannerStatus.active
    assert banner1.positions == [EmployeePositions.director]
    assert banner1.outgoing_documents_count == [
        BannerOutgoingDocumentsCount.range_1_10,
        BannerOutgoingDocumentsCount.range_100_plus,
    ]
    assert banner1.incoming_documents_sign_count == [
        BannerIncomingDocumentsSignCount.range_11_24,
        BannerIncomingDocumentsSignCount.has_unsigned,
    ]
    assert banner1.employees_count == [EmployeesCount.unknown, EmployeesCount.range_1_10]
    assert banner1.start_date == datetime_test('2022-01-02T00:00:00+00:00')
    assert banner1.end_date == datetime_test('2022-01-02T00:00:00+00:00')
    assert banner1.analytics_category == 'test'
    assert banner1.audience_type == BannerAudienceType.FOP
    assert banner1.days_before_signature_expires == 15
    contents1 = await get_banner_contents(banner_id=banner1.id_)
    assert len(contents1) == 1
    assert contents1[0].language == Language.uk
    assert contents1[0].text == 'тест2'
    assert contents1[0].link_text == 'тест2'
    assert contents1[0].link_url == 'https://test3.com/'

    banner2 = next(b for b in banners if b.id_ == BANNER_ID_2)
    assert banner2.color == BannerColor.blue
    assert banner2.status == BannerStatus.new
    assert banner2.positions == [EmployeePositions.director]
    assert banner2.positions == [EmployeePositions.director]
    assert banner2.start_date == datetime_test('2023-01-01T00:00:00+00:00')
    assert banner2.end_date == datetime_test('2023-01-01T00:00:00+00:00')
    assert banner2.analytics_category == 'some-category'
    assert banner2.audience_type is None
    assert banner2.days_before_signature_expires is None
    contents2 = await get_banner_contents(banner_id=banner2.id_)
    assert len(contents2) == 1
    assert contents2[0].language == Language.en
    assert contents2[0].text == 'test'
    assert contents2[0].link_text == 'test'
    assert contents2[0].link_url == 'https://test2.com'
