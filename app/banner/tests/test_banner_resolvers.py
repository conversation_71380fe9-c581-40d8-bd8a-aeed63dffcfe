from datetime import UTC, datetime

import pytest
from dateutil.relativedelta import relativedelta

from app.auth.enums import EmployeePositions
from app.banner.enums import (
    BannerActivityPeriod,
    BannerAudienceType,
    BannerColor,
    BannerIncomingDocumentsSignCount,
    BannerOutgoingDocumentsCount,
    BannerPositionFilter,
    BannerRate,
    BannerStatus,
    EmployeesCount,
)
from app.banner.tests.utils import prepare_banner, prepare_banner_content
from app.billing.api import add_company_rate
from app.billing.enums import AccountRate, CompanyRateStatus
from app.billing.types import CompanyRate
from app.es.utils import (
    count_incoming_company_documents,
    count_sent_outgoing_documents_elastic,
)
from app.lib.datetime_utils import midnight, utc_now
from app.lib.enums import DocumentStatus, Language
from app.services import services
from app.signatures.db import insert_certificate_info
from app.signatures.enums import SignatureType
from app.tests.common import (
    FOP_EDRPOU,
    SUPER_ADMIN_EDRPOU,
    TOV_EDRPOU,
    fetch_graphql,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_flow_item,
    prepare_signature_data,
    prepare_user_data,
    set_company_config,
    with_elastic,
)

BANNER_ID_1 = '********-0000-0000-0000-********0001'
BANNER_ID_2 = '********-0000-0000-0000-********0002'
BANNER_ID_3 = '********-0000-0000-0000-********0003'
BANNER_ID_4 = '********-0000-0000-0000-********0004'
BANNER_ID_5 = '********-0000-0000-0000-********0005'


COMPANY_RATE_1 = '10000000-0000-0000-0000-********0001'
COMPANY_RATE_2 = '10000000-0000-0000-0000-********0002'
COMPANY_RATE_3 = '10000000-0000-0000-0000-********0003'
COMPANY_RATE_4 = '10000000-0000-0000-0000-********0004'

OLD_DATE_1 = datetime(year=2014, day=1, month=1, tzinfo=UTC)
OLD_DATE_2 = datetime(year=2015, day=1, month=1, tzinfo=UTC)
FUTURE_DATE_1 = datetime(year=3015, day=1, month=1, tzinfo=UTC)
FUTURE_DATE_2 = datetime(year=3016, day=1, month=1, tzinfo=UTC)


GQL_SA_BANNERS = """
    query SA_Banners {
        saBanner {
            id
            dateFrom
            dateTo
            color
            status
            positions
            analyticsCategory
            audienceType
            daysBeforeSignatureExpires
            outgoingDocumentsCount
            incomingDocumentsSignCount
            activityPeriod
            employeesCount

            text
            linkText
            link

            textEn
            linkTextEn
            linkEn
        }
    }
"""

GQL_ACTIVE_BANNER = """
    query ActiveBanner {
        activeBanner {
            id
        }
    }
"""


@pytest.mark.parametrize(
    'is_admin, sa_perms, has_access',
    [
        (True, {}, False),
        (False, {}, False),
        (False, {'can_view_client_data': True}, False),
        (False, {'can_edit_client_data': True}, False),
        (False, {'can_edit_special_features': True}, True),
    ],
)
async def test_resolve_banner_access(aiohttp_client, is_admin, sa_perms, has_access):
    """
    Given a superadmin which has / has not been granted certain permissions
    When calling saBanners graphql node
    Expected to return valid list of banners
    """
    # Arrange superadmin user and config
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        super_admin_permissions=sa_perms,
    )
    await set_company_config(app, company_id=user.company_id, master=True, admin_is_superadmin=True)

    # Arrange banner
    banner = await prepare_banner()
    await prepare_banner_content(banner_id=banner.id_)

    # Act
    data = await fetch_graphql(
        client=client,
        query=GQL_SA_BANNERS,
        headers=prepare_auth_headers(user),
    )

    # Assert
    expected_banners_ids = [banner.id_] if has_access else []
    actual_banners_ids = [item['id'] for item in data['saBanner']]
    assert actual_banners_ids == expected_banners_ids


async def test_resolve_banner_fields(aiohttp_client):
    """
    Given a superadmin which has permissions to query banners
    When calling saBanner graphql node
    Expected to return valid list of banners with valid fields
    """
    # Arrange superadmin
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin=False,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        super_admin_permissions={'can_edit_special_features': True},
    )
    await set_company_config(app, company_id=user.company_id, master=True, admin_is_superadmin=True)

    # Arrange banners
    banner1 = await prepare_banner(
        banner_id=BANNER_ID_1,
        color=BannerColor.green,
        start_date='2022-01-01T00:00:00+00:00',
        end_date='2022-01-01T00:00:00+00:00',
        analytics_category='test',
        audience_type=BannerAudienceType.FOP,
        days_before_signature_expires=15,
        positions=[EmployeePositions.accountant, EmployeePositions.director],
        outgoing_documents_count=[
            BannerOutgoingDocumentsCount.range_1_10,
            BannerOutgoingDocumentsCount.range_100_plus,
        ],
        incoming_documents_sign_count=[
            BannerIncomingDocumentsSignCount.range_1_10,
            BannerIncomingDocumentsSignCount.range_51_100,
        ],
        activity_period=BannerActivityPeriod.last_1_month,
        employees_count=[EmployeesCount.unknown, EmployeesCount.range_1_10],
        date_created='2022-01-01T00:00:00+00:00',
    )
    await prepare_banner_content(
        banner_id=banner1.id_,
        text='some text',
        link_text='some link',
        link_url='https://testtext.com/',
    )

    banner2 = await prepare_banner(
        banner_id=BANNER_ID_2,
        color=BannerColor.blue,
        start_date='2023-01-01T00:00:00+00:00',
        end_date='2023-01-01T00:00:00+00:00',
        analytics_category='testOther',
        audience_type=BannerAudienceType.TOV,
        positions=None,
        date_created='2023-01-02T00:00:00+00:00',
    )
    await prepare_banner_content(
        banner_id=banner2.id_,
        language=Language.en,
        text='test',
        link_text='test',
        link_url='https://test.com/',
    )
    await prepare_banner_content(
        banner_id=banner2.id_,
        language=Language.uk,
        text='тест',
        link_text='тест',
        link_url='https://test1.com/',
    )

    # Act
    data = await fetch_graphql(
        client=client,
        query=GQL_SA_BANNERS,
        headers=prepare_auth_headers(user),
    )

    # Assert
    assert data['saBanner'] == [
        {
            'id': BANNER_ID_2,
            'dateFrom': '2023-01-01T00:00:00+00:00',
            'dateTo': '2023-01-01T00:00:00+00:00',
            'color': 'blue',
            'status': 'new',
            'positions': None,
            'outgoingDocumentsCount': None,
            'incomingDocumentsSignCount': None,
            'activityPeriod': None,
            'employeesCount': None,
            'analyticsCategory': 'testOther',
            'audienceType': BannerAudienceType.TOV,
            'daysBeforeSignatureExpires': None,
            'text': 'тест',
            'linkText': 'тест',
            'link': 'https://test1.com/',
            'textEn': 'test',
            'linkTextEn': 'test',
            'linkEn': 'https://test.com/',
        },
        {
            'id': BANNER_ID_1,
            'dateFrom': '2022-01-01T00:00:00+00:00',
            'dateTo': '2022-01-01T00:00:00+00:00',
            'color': 'green',
            'status': 'new',
            'positions': ['accountant', 'director'],
            'outgoingDocumentsCount': ['1_10', '100_plus'],
            'incomingDocumentsSignCount': ['1_10', '51_100'],
            'activityPeriod': '1month',
            'employeesCount': ['unknown', '1-10'],
            'analyticsCategory': 'test',
            'audienceType': BannerAudienceType.FOP,
            'daysBeforeSignatureExpires': 15,
            'text': 'some text',
            'linkText': 'some link',
            'link': 'https://testtext.com/',
            'textEn': None,
            'linkTextEn': None,
            'linkEn': None,
        },
    ]


@pytest.mark.parametrize(
    'user_data, rates, banners, expected_response',
    [
        pytest.param(
            {},
            [],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.hidden,
                }
            ],
            None,
            id='no_hidden_banners',
        ),
        pytest.param(
            {'role_position': EmployeePositions.accountant.name_uk},
            [],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'positions': [EmployeePositions.accountant, EmployeePositions.director],
                }
            ],
            {'id': BANNER_ID_1},
            id='match_multiple_positions_banner',
        ),
        pytest.param(
            {'role_position': EmployeePositions.director.name_uk},
            [],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'positions': [EmployeePositions.accountant, EmployeePositions.director],
                }
            ],
            {'id': BANNER_ID_1},
            id='match_multiple_positions_banner_2',
        ),
        pytest.param(
            {'role_position': EmployeePositions.it_director.name_uk},
            [],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'positions': [EmployeePositions.accountant, EmployeePositions.director],
                }
            ],
            None,
            id='not_any_position_banner',
        ),
        pytest.param(
            {'role_position': None},
            [],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'positions': [EmployeePositions.accountant, EmployeePositions.director],
                }
            ],
            None,
            id='user_without_position',
        ),
        pytest.param(
            {'role_position': None},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.pro,
                    'status': CompanyRateStatus.active,
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.pro, BannerRate.trial],
                }
            ],
            {'id': BANNER_ID_1},
            id='user_with_pro_rate',
        ),
        pytest.param(
            {'role_position': None},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.start,
                    'status': CompanyRateStatus.active,
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.start],
                }
            ],
            {'id': BANNER_ID_1},
            id='user_with_start_rate',
        ),
        pytest.param(
            {'role_position': None},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.pro_plus_trial,
                    'status': CompanyRateStatus.active,
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.pro, BannerRate.trial],
                }
            ],
            {'id': BANNER_ID_1},
            id='user_with_trial_rate',
        ),
        pytest.param(
            {'role_position': None},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'date_created': '2022-01-01T00:00:00+00:00',
                },
                {
                    'id_': COMPANY_RATE_2,
                    'rate': AccountRate.pro,
                    'status': CompanyRateStatus.active,
                    'date_created': '2022-01-02T00:00:00+00:00',
                },
                {
                    'id_': COMPANY_RATE_3,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'date_created': '2022-01-03T00:00:00+00:00',
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.pro],
                }
            ],
            {'id': BANNER_ID_1},  # only web rates matter
            id='user_with_web_and_integration_rates',
        ),
        pytest.param(
            {'role_position': None},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'date_created': '2022-01-01T00:00:00+00:00',
                },
                {
                    'id_': COMPANY_RATE_2,
                    'rate': AccountRate.ultimate_2022_12,
                    'status': CompanyRateStatus.active,
                    'date_created': '2022-01-02T00:00:00+00:00',
                },
                {
                    'id_': COMPANY_RATE_3,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'date_created': '2022-01-03T00:00:00+00:00',
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.ultimate],
                }
            ],
            {'id': BANNER_ID_1},  # only web rates matter
            id='user_with_web_and_integration_rates_ultimate',
        ),
        pytest.param(
            {'role_position': EmployeePositions.it_director.name_uk},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.pro_plus_trial,
                    'status': CompanyRateStatus.active,
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.start],
                },
                {
                    'banner_id': BANNER_ID_2,
                    'status': BannerStatus.active,
                },
            ],
            {'id': BANNER_ID_2},
            id='banner_fallback_banner_without_rates_conditions',
        ),
        pytest.param(
            {'role_position': EmployeePositions.it_director.name_uk},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.pro_plus_trial,
                    'status': CompanyRateStatus.active,
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.start],
                },
                {
                    'banner_id': BANNER_ID_2,
                    'status': BannerStatus.active,
                    'positions': [EmployeePositions.accountant],
                },
                {
                    'banner_id': BANNER_ID_3,
                    'status': BannerStatus.active,
                },
            ],
            {'id': BANNER_ID_3},
            id='banner_fallback_banner_without_rates_and_positions_conditions',
        ),
        pytest.param(
            {'role_position': EmployeePositions.accountant.name_uk},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.start,
                    'status': CompanyRateStatus.active,
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.start],
                    'positions': [EmployeePositions.accountant],
                },
                {
                    'banner_id': BANNER_ID_2,
                    'status': BannerStatus.active,
                },
            ],
            {'id': BANNER_ID_1},
            id='banner_match_by_positions_and_rates',
        ),
        pytest.param(
            {'role_position': EmployeePositions.accountant.name_uk},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.start,
                    'status': CompanyRateStatus.active,
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.start],
                    'positions': [EmployeePositions.accountant],
                    'date_created': '2022-01-01T00:00:00+00:00',
                },
                {
                    'banner_id': BANNER_ID_2,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.start],
                    'positions': [EmployeePositions.accountant],
                    'date_created': '2022-01-02T00:00:00+00:00',
                },
                {
                    'banner_id': BANNER_ID_3,
                    'status': BannerStatus.active,
                },
            ],
            {'id': BANNER_ID_2},  # latest banner by date_created
            id='latest_banner_match_by_positions_and_rates',
        ),
        pytest.param(
            {'role_position': EmployeePositions.accountant.name_uk},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.start,
                    'status': CompanyRateStatus.active,
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.start],
                    'positions': [EmployeePositions.accountant],
                    'date_created': '2022-01-01T00:00:00+00:00',
                },
                {
                    'banner_id': BANNER_ID_2,
                    'status': BannerStatus.active,
                    'positions': [EmployeePositions.accountant],
                    'date_created': '2022-01-02T00:00:00+00:00',
                },
                {
                    'banner_id': BANNER_ID_3,
                    'status': BannerStatus.active,
                },
            ],
            {'id': BANNER_ID_1},  # score 2
            id='highest_score_banner_match_by_positions_and_rates',
        ),
        pytest.param(
            {'company_edrpou': TOV_EDRPOU},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.start,
                    'status': CompanyRateStatus.active,
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'audience_type': BannerAudienceType.FOP,
                    'date_created': '2022-01-01T00:00:00+00:00',
                },
                {
                    'banner_id': BANNER_ID_2,
                    'status': BannerStatus.active,
                    'audience_type': BannerAudienceType.TOV,
                    'date_created': '2022-01-02T00:00:00+00:00',
                },
                {
                    'banner_id': BANNER_ID_3,
                    'status': BannerStatus.active,
                },
            ],
            {'id': BANNER_ID_2},  # score 1
            id='match_by_banner_audience_type_TOV',
        ),
        pytest.param(
            {'company_edrpou': FOP_EDRPOU},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.start,
                    'status': CompanyRateStatus.active,
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'audience_type': BannerAudienceType.FOP,
                    'date_created': '2022-01-01T00:00:00+00:00',
                },
                {
                    'banner_id': BANNER_ID_2,
                    'status': BannerStatus.active,
                    'audience_type': BannerAudienceType.TOV,
                    'date_created': '2022-01-02T00:00:00+00:00',
                },
                {
                    'banner_id': BANNER_ID_3,
                    'status': BannerStatus.active,
                },
            ],
            {'id': BANNER_ID_1},  # score 1
            id='match_by_banner_audience_type_FOP',
        ),
        pytest.param(
            {},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.archive_small,
                    'status': CompanyRateStatus.active,
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.archive_small],
                    'date_created': '2022-01-01T00:00:00+00:00',
                },
                {
                    'banner_id': BANNER_ID_2,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.archive_big],
                    'date_created': '2022-01-02T00:00:00+00:00',
                },
                {
                    'banner_id': BANNER_ID_3,
                    'status': BannerStatus.active,
                },
            ],
            {'id': BANNER_ID_1},  # score 1
            id='match_by_archive_small_rate',
        ),
        pytest.param(
            {},
            [
                {
                    'id_': COMPANY_RATE_1,
                    'rate': AccountRate.archive_big,
                    'status': CompanyRateStatus.active,
                },
            ],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.archive_small],
                    'date_created': '2022-01-01T00:00:00+00:00',
                },
                {
                    'banner_id': BANNER_ID_2,
                    'status': BannerStatus.active,
                    'rates': [BannerRate.archive_big],
                    'date_created': '2022-01-02T00:00:00+00:00',
                },
                {
                    'banner_id': BANNER_ID_3,
                    'status': BannerStatus.active,
                },
            ],
            {'id': BANNER_ID_2},  # score 1
            id='match_by_archive_big_rate',
        ),
        pytest.param(
            {'role_position': EmployeePositions.accountant.name_uk},
            [],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'positions': [BannerPositionFilter.other],
                }
            ],
            None,
            id='mismatch_other_position',
        ),
        pytest.param(
            {'role_position': EmployeePositions.accountant.name_uk},
            [],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'positions': [EmployeePositions.accountant, BannerPositionFilter.other],
                }
            ],
            {'id': BANNER_ID_1},
            id='match_regular_position_with_other',
        ),
        pytest.param(
            {'role_position': 'random_other_position'},
            [],
            [
                {
                    'banner_id': BANNER_ID_1,
                    'status': BannerStatus.active,
                    'positions': [BannerPositionFilter.other],
                }
            ],
            {'id': BANNER_ID_1},
            id='match_other_position',
        ),
    ],
)
async def test_resolve_active_banner(
    aiohttp_client,
    user_data: dict,
    rates: list[dict],
    banners: list[dict],
    expected_response: dict,
):
    """
    Given a user with certain properties set
    Given a banner with certain filters
    When querying graphql active banner for user
    Expected valid banner to show up
    """
    # Arrange user
    app, client, user = await prepare_client(
        aiohttp_client,
        **user_data,
        enable_pro_functionality=False,
    )

    # Arrange rates if any
    async with services.db.acquire() as conn:
        for raw_rate in rates:
            raw_rate = {
                **raw_rate,
                'amount': 0,
                'company_id': user.company_id,
                'company_edrpou': user.company_edrpou,
                'status': CompanyRateStatus.active,
                'start_date': OLD_DATE_1,
                'end_date': FUTURE_DATE_2,
            }
            rate = CompanyRate(**raw_rate)
            await add_company_rate(conn, rate=rate, user=user)

    # Arrange banners with filters
    for banner_raw in banners:
        await prepare_banner(**banner_raw)

    # Act
    data = await fetch_graphql(
        client=client,
        query=GQL_ACTIVE_BANNER,
        headers=prepare_auth_headers(user),
    )

    # Assert
    assert data == {'activeBanner': expected_response}


@pytest.mark.parametrize(
    'certificate_acsk, expected_banners_count',
    [('АЦСК Україна', 1), ('ТОВ Вчасно Сервіс', 0)],
)
async def test_resolve_banner_for_expiring_signature(
    aiohttp_client, certificate_acsk, expected_banners_count
):
    """
    Given a user with expiring signature certificate
    When resolving active banners for user
    Expected valid banner to resolve
    """
    # Arrange user
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)

    # Arrange banner
    await prepare_banner(
        banner_id=BANNER_ID_1, status=BannerStatus.active, days_before_signature_expires=15
    )

    # Arrange expiring certificate
    async with services.db.acquire() as conn:
        await insert_certificate_info(
            conn=conn,
            data={
                'role_id': user.role_id,
                'is_stamp': True,
                'serial_number': 'test',
                'acsk': certificate_acsk,
                'type': SignatureType.file_key,
                'state': None,
                'locality': None,
                'date_end': (midnight() + relativedelta(days=10)).date(),
            },
        )

    # Act
    data = await fetch_graphql(
        client=client,
        query=GQL_ACTIVE_BANNER,
        headers=prepare_auth_headers(user),
    )

    # Assert
    if expected_banners_count:
        assert len(data['activeBanner']) == expected_banners_count
    else:
        assert data['activeBanner'] is None


async def test_resolve_banner_for_expiring_signature_when_new_available(aiohttp_client):
    """
    Given a user with expiring signature certificate and with new signature certificate
    When resolving active banners for user
    Expected valid banner NOT to resolve
    """
    # Arrange user
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)

    # Arrange banner
    await prepare_banner(
        banner_id=BANNER_ID_1, status=BannerStatus.active, days_before_signature_expires=15
    )

    # Arrange expiring certificate
    async with services.db.acquire() as conn:
        # Insert expiring certificate
        await insert_certificate_info(
            conn=conn,
            data={
                'role_id': user.role_id,
                'is_stamp': True,
                'serial_number': 'test',
                'acsk': 'АЦСК Україна',
                'type': SignatureType.file_key,
                'state': None,
                'locality': None,
                'date_end': (midnight() + relativedelta(days=10)).date(),
            },
        )
        # Insert fresh certificate
        await insert_certificate_info(
            conn=conn,
            data={
                'role_id': user.role_id,
                'is_stamp': True,
                'serial_number': 'test',
                'acsk': 'АЦСК Україна',
                'type': SignatureType.file_key,
                'state': None,
                'locality': None,
                'date_end': (midnight() + relativedelta(days=365)).date(),
            },
        )

    # Act
    data = await fetch_graphql(
        client=client,
        query=GQL_ACTIVE_BANNER,
        headers=prepare_auth_headers(user),
    )

    # Assert
    assert data['activeBanner'] is None


async def test_count_banner_documents(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)
    activity_period = BannerActivityPeriod.last_1_month
    recipient = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou='55555555',
    )
    recipient2 = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou='66666666',
    )

    # Prepare documents
    # incoming +1
    doc1 = await prepare_document_data(
        app,
        owner=recipient,
        document_recipients=[{'edrpou': user.company_edrpou, 'emails': [user.email]}],
        status_id=DocumentStatus.signed_and_sent.value,
    )
    await prepare_signature_data(
        app=app,
        owner=user,
        document=doc1,
        update_access=False,
    )
    await prepare_signature_data(
        app=app,
        owner=user,
        document=doc1,
        update_access=False,
    )
    # outgoing +1
    doc2 = await prepare_document_data(
        app, owner=user, status_id=DocumentStatus.signed_and_sent.value
    )
    await prepare_signature_data(
        app=app,
        owner=user,
        document=doc2,
        update_access=False,
    )
    # (outgoing not sent)
    doc3 = await prepare_document_data(
        app,
        owner=user,
        document_recipients=[{'edrpou': recipient.company_edrpou, 'emails': [recipient.email]}],
        status_id=DocumentStatus.uploaded.value,
    )

    # ( incoming, 1 sign from another company)
    doc4 = await prepare_document_data(
        app,
        owner=recipient,
        document_recipients=[{'edrpou': user.company_edrpou, 'emails': [user.email]}],
        status_id=DocumentStatus.signed_and_sent.value,
    )
    await prepare_signature_data(
        app=app,
        owner=recipient,
        document=doc4,
        update_access=False,
    )

    # ( incoming, 1 sign, but too old)
    doc5 = await prepare_document_data(app, owner=recipient)
    await prepare_signature_data(
        app=app,
        owner=user,
        document=doc4,
        update_access=False,
        date_created=utc_now() - relativedelta(months=1, days=1),
    )

    # ( outgoing, but too old)
    doc6 = await prepare_document_data(
        app,
        owner=user,
        status_id=DocumentStatus.signed_and_sent.value,
        date_created=utc_now() - relativedelta(months=1, days=1),
    )

    # +1 incoming unsigned multilateral document
    doc7 = await prepare_document_data(
        app,
        owner=recipient2,
        is_multilateral=True,
        status_id=DocumentStatus.flow.value,
    )
    await prepare_flow_item(
        app,
        document_id=doc7.id,
        receivers=[user.email],
        edrpou=user.company_edrpou,
        signatures_count=1,
        pending_signatures_count=1,
        order=None,
    )
    await prepare_flow_item(
        app,
        document_id=doc7.id,
        receivers=[recipient.email],
        edrpou=recipient.company_edrpou,
        signatures_count=1,
        pending_signatures_count=1,
        order=None,
    )
    await prepare_flow_item(
        app,
        document_id=doc7.id,
        receivers=[recipient2.email],
        edrpou=recipient2.company_edrpou,
        signatures_count=1,
        pending_signatures_count=1,
        order=None,
    )

    async with with_elastic(app, [doc1.id, doc2.id, doc3.id, doc4.id, doc5.id, doc6.id, doc7.id]):
        outgoing_docs = await count_sent_outgoing_documents_elastic(
            user.company_edrpou, activity_period
        )
        incoming_signed_docs = await count_incoming_company_documents(
            is_signed=True,
            company_edrpou=user.company_edrpou,
            activity_period=activity_period,
        )
        incoming_unsigned_docs = await count_incoming_company_documents(
            is_signed=False,
            company_edrpou=user.company_edrpou,
            activity_period=activity_period,
        )
    assert incoming_signed_docs == 1  # doc1
    assert outgoing_docs == 1  # doc2
    assert incoming_unsigned_docs == 1  # doc7
