import csv
import tempfile
from http import HTTPStatus

import aiohttp.web
import pytest

from app.lib.datetime_utils import ONE_DAY_DELTA
from app.lib.helpers import reset_rate_limit, validate_rate_limit
from app.tests.common import prepare_app_client, prepare_user_data

INTERNAL_CHECK_COMPANY_URL = '/api/private/integrations/landing/check/company'
INTERNAL_CHECK_COMPANY_UPLOAD_URL = '/api/private/integrations/landing/check/company/upload'

HEADER_KEY = 'X-Vchasno-Private-Token'
HEADERS = {HEADER_KEY: '9d1fe28f-f6a1-4dd2-b957-0c8be810b849'}


def _create_form_data(filename: str, ip: str | None = None) -> aiohttp.FormData:
    form = aiohttp.FormData()
    form.add_field('file', open(filename).read(), filename='test.csv')
    if ip:
        form.add_field('ip', ip)
    return form


def _create_tmp_csv_file(
    data: tuple[list, list] = (['ЄДРПОУ/ІПН *'], ['55555555']),
) -> tempfile.NamedTemporaryFile:
    with tempfile.NamedTemporaryFile(suffix='.csv', mode='w', delete=False) as csvfile:
        filewriter = csv.writer(csvfile)
        filewriter.writerows(data)
        return csvfile


async def test_company_landing_exist(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)
    exist_company = '55555555'
    missing_company = '22222222'

    await prepare_user_data(app, company_edrpou=exist_company, company_name='Jedi entertainment')
    response = await client.post(
        INTERNAL_CHECK_COMPANY_URL,
        json={'edrpou': exist_company, 'ip': '0.0.0.0'},
        headers=HEADERS,
    )

    assert response.status == HTTPStatus.OK
    data = await response.json()
    assert data['edrpou'] == exist_company
    assert data['name']
    assert data['is_registered']

    response = await client.post(
        INTERNAL_CHECK_COMPANY_URL,
        json={'edrpou': missing_company, 'ip': '0.0.0.0'},
        headers=HEADERS,
    )
    assert response.status == HTTPStatus.OK
    data = await response.json()
    assert data['edrpou'] == missing_company
    assert not data['name']
    assert data['is_registered'] is False


async def test_company_landing_ip(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)
    response = await client.post(
        INTERNAL_CHECK_COMPANY_URL,
        json={'edrpou': '55555555', 'ip': '0.0.0.0'},
        headers=HEADERS,
    )
    assert response.status == HTTPStatus.OK


async def test_company_landing_ip_throttling(aiohttp_client):
    json = {'edrpou': '55555555', 'ip': '0.0.0.0'}
    app, client = await prepare_app_client(aiohttp_client)

    await reset_rate_limit('landing:check_company:0.0.0.0')

    for _ in range(29):
        await validate_rate_limit(
            key='landing:check_company:0.0.0.0',
            limit=30,
            delta=ONE_DAY_DELTA,
        )

    # 30 -> OK
    response = await client.post(
        INTERNAL_CHECK_COMPANY_URL,
        json=json,
        headers=HEADERS,
    )
    assert response.status == HTTPStatus.OK

    # 31 -> TOO_MANY_REQUESTS
    response = await client.post(
        INTERNAL_CHECK_COMPANY_URL,
        json=json,
        headers=HEADERS,
    )
    assert response.status == HTTPStatus.TOO_MANY_REQUESTS


@pytest.mark.parametrize('json', [{'ip': '256.0.0.0'}, {'ip': '*'}, {'ip': None}, {}])
async def test_company_landing_ip_negative(aiohttp_client, json):
    app, client = await prepare_app_client(aiohttp_client)
    json.update({'edrpou': '55555555'})
    response = await client.post(
        INTERNAL_CHECK_COMPANY_URL,
        json=json,
        headers=HEADERS,
    )
    assert response.status == HTTPStatus.BAD_REQUEST


async def test_company_upload_landing_ip(aiohttp_client):
    csvfile = _create_tmp_csv_file()

    app, client = await prepare_app_client(aiohttp_client)

    form = _create_form_data(csvfile.name, '0.0.0.0')

    response = await client.post(
        INTERNAL_CHECK_COMPANY_UPLOAD_URL,
        data=form,
        headers=HEADERS,
    )
    assert response.status == HTTPStatus.OK


async def test_company_upload_landing_ip_throttling(aiohttp_client):
    csvfile = _create_tmp_csv_file()

    app, client = await prepare_app_client(aiohttp_client)

    await reset_rate_limit('landing:check_company_upload:0.0.0.0')

    for _ in range(2):
        await validate_rate_limit(
            key='landing:check_company_upload:0.0.0.0',
            limit=3,
            delta=ONE_DAY_DELTA,
        )

    # 3 -> OK
    form = _create_form_data(csvfile.name, '0.0.0.0')
    response = await client.post(
        INTERNAL_CHECK_COMPANY_UPLOAD_URL,
        data=form,
        headers=HEADERS,
    )
    assert response.status == HTTPStatus.OK

    # 4 -> TOO_MANY_REQUESTS
    form = _create_form_data(csvfile.name, '0.0.0.0')
    response = await client.post(
        INTERNAL_CHECK_COMPANY_UPLOAD_URL,
        data=form,
        headers=HEADERS,
    )
    assert response.status == HTTPStatus.TOO_MANY_REQUESTS


@pytest.mark.parametrize('json', [{'ip': '256.0.0.0'}, {'ip': '*'}, {'ip': None}, {}])
async def test_company_landing_upload_ip_negative(aiohttp_client, json):
    app, client = await prepare_app_client(aiohttp_client)
    json.update({'file': 'file'})
    response = await client.post(
        INTERNAL_CHECK_COMPANY_UPLOAD_URL,
        json=json,
        headers=HEADERS,
    )
    assert response.status == HTTPStatus.BAD_REQUEST


@pytest.mark.parametrize(
    'headers',
    [None, {}, {HEADER_KEY: ''}, {HEADER_KEY: {}}, {HEADER_KEY: 'Invalid_token'}, {}],
)
async def test_company_landing_access_negative(aiohttp_client, headers):
    app, client = await prepare_app_client(aiohttp_client)
    response = await client.post(INTERNAL_CHECK_COMPANY_UPLOAD_URL, json={})
    assert response.status == HTTPStatus.UNAUTHORIZED
