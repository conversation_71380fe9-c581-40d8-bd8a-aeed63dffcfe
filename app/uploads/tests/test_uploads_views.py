import io
import tempfile
from http import HTTPStatus
from pathlib import Path
from typing import Any
from unittest.mock import AsyncMock

import pytest
import sqlalchemy as sa
from aiohttp import FormData

from api.public.tests.common import (
    TEST_RECIPIENT_EDRPOU,
    TEST_RECIPIENT_EDRPOU_2,
    TEST_RECIPIENT_EDRPOU_3,
    TEST_RECIPIENT_EMAIL,
    TEST_RECIPIENT_EMAIL_2,
    TEST_RECIPIENT_EMAIL_3,
)
from app.auth.db import select_company_by_edrpou
from app.billing.db import update_billing_company_config
from app.comments.db import select_comments
from app.document_automation.tests.common import (
    CREATE_TEMPLATE_DATA,
    get_document_automation_template,
    prepare_template_coworkers,
    request_create_template,
)
from app.document_categories.types import PublicDocumentCategory
from app.document_versions.enums import DocumentVersionType
from app.document_versions.tables import document_version_table
from app.documents.db import (
    select_bilateral_document_recipient,
    select_document_access_level,
    select_document_by_id,
    select_documents_by_owner_edrpou,
    select_listings,
)
from app.documents.enums import (
    AccessSource,
    DocumentAccessLevel,
    FirstSignBy,
)
from app.documents.tables import document_table
from app.documents.tests.utils import get_document_recipients, prepare_private_document
from app.documents.types import DocumentRecipient, RecipientAggregatedData
from app.documents.utils import save_hidden_emails
from app.documents_fields.db import select_document_parameters
from app.documents_fields.enums import DocumentFieldType
from app.documents_fields.tests.common import create_documents_field
from app.documents_required_fields.enums import DocumentCategoryFields
from app.events import document_actions
from app.events.document_actions.db import select_document_actions_for
from app.flags import FeatureFlags
from app.flow.db import select_flows_by
from app.flow.tables import doc_flow_table
from app.flow.tests.test_flow_view import ADD_FLOW_URL
from app.flow.tests.utils import get_flows
from app.flow.types import FlowItem
from app.groups.db import insert_group_member
from app.groups.utils import add_group
from app.lib.enums import DocumentStatus, SignatureFormat, UserRole
from app.lib.helpers import ensure_mimetype, to_json
from app.lib.types import DataDict
from app.models import select_all, select_one
from app.reviews.db import (
    select_review_requests,
    select_review_setting,
)
from app.reviews.tables import review_request_table
from app.services import services
from app.signatures.db import select_signatures, select_signatures_by_documents_for_api
from app.signatures.tables import document_signer_table
from app.signatures.tests.utils import get_document_signers
from app.signatures.types import SignersInfoCtx
from app.signatures.utils import unpack_wrapped_signature_container
from app.tags.db import select_tags_by_documents
from app.tags.tables import tag_table
from app.tests.common import (
    API_V2_DOCUMENTS_URL,
    GROUP,
    ROLE,
    TEST_COMPANY_EDRPOU,
    TEST_COMPANY_EDRPOU_2,
    UPLOAD_DOCUMENT_URL,
    cleanup_on_teardown,
    get_document,
    prepare_auth_headers,
    prepare_automation,
    prepare_automation_template,
    prepare_client,
    prepare_document_data,
    prepare_document_recipients,
    prepare_document_required_fields,
    prepare_document_upload,
    prepare_form_data,
    prepare_group,
    prepare_public_document_categories,
    prepare_user_data,
    request_create_tags_for_roles,
    request_document_update,
    set_billing_company_config,
    sign_and_send_document,
)

DATA_PATH = Path(__file__).parent / 'data'
DOC_PATH = DATA_PATH / 'doc.txt'
ARCH_PATH = DATA_PATH / 'docs_with_signatures.zip'
ARCH_PATH_WITHOUT_SIGNATURES = DATA_PATH / 'docs_without_signatures.zip'

TAG_NAME_1 = 'TAG1'
TAG_NAME_2 = 'TAG2'
TAG_NAME_3 = 'TAG3'
TAG_NAME_4 = 'TAG4'
TEST_UUID = 'abb541ee-128d-42d9-b9b1-73c0bae7d72f'

ROLE_ID_1 = '7c42f17e-a041-4bfb-b8af-e91a217035aa'
ROLE_ID_2 = '0c5fc1f8-9125-4001-be11-86a8aba2016b'
ROLE_ID_3 = 'aca94f05-1571-48c3-bfb8-ce72ce1cd6a0'
ROLE_ID_4 = '2fb8a564-0c8e-48a8-954a-d207e7bf750b'

USER_EMAIL_1 = '<EMAIL>'
USER_EMAIL_2 = '<EMAIL>'
USER_EMAIL_3 = '<EMAIL>'
USER_EMAIL_4 = '<EMAIL>'


async def fake_verify(*args, **kwargs):
    return {
        'issuer': 'O=Державне підприємство «Українські спеціальні системи»;OU=Центр '
        'сертифікації ключів;CN=АЦСК ДП '
        '«УСС»;Serial=UA-*************;C=UA;L=Київ',
        'issuer_cn': 'АЦСК ДП «УСС»',
        'serial': '0FAF1DAC271389DB040000000E000000E5F70900',
        'subject': 'O=Тестова організація - юридична особа;OU=Тестовий підрозділ '
        'юридичної особи;Title=Тестова посада тестового підрозділу '
        'юридичної особи;CN=Тестова Юридична '
        'Особа;SN=Тестова;GivenName=Юридична Особа;Serial=14;C=UA;L=Київ',
        'subject_address': '',
        'subject_cn': 'Тестова Юридична Особа',
        'subject_dns': '',
        'subject_drfo_code': '0000000000',
        'subject_edrpou_code': '********',
        'subject_email': '<EMAIL>',
        'subject_fullname': 'Тестова Юридична Особа',
        'subject_locality': 'Київ',
        'subject_org': 'Тестова організація - юридична особа',
        'subject_org_unit': 'Тестовий підрозділ юридичної особи',
        'subject_phone': '+38 (0 55) 555-55-55',
        'subject_state': '',
        'subject_title': 'Тестова посада тестового підрозділу юридичної особи',
        'time': {
            'day': 4,
            'day_of_week': 2,
            'hour': 0,
            'milliseconds': 0,
            'minute': 2,
            'month': 7,
            'second': 54,
            'year': 2018,
        },
        'time_avail': True,
        'timestamp': True,
    }


async def test_upload(aiohttp_client, monkeypatch, s3_emulation):
    """Basic test for document upload."""
    monkeypatch.setattr('app.lib.eusign_utils.verify', fake_verify)

    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou='********',
        create_api_account=True,
        feature_flags={'pass_sign_info_to_backend': False},
    )

    response = await client.post(
        '/internal-api/documents',
        data=prepare_form_data(file=DOC_PATH),
        headers=prepare_auth_headers(user),
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.CREATED, response_json

    resp = await client.post(
        '/internal-api/indexation-status',
        headers=prepare_auth_headers(user),
        json={'document_ids': [response_json['documents'][0]['id']]},
    )
    assert resp.status == HTTPStatus.OK

    async with app['db'].acquire() as conn:
        docs = await select_documents_by_owner_edrpou(conn, edrpou='********')
        assert len(docs) == 1

    assert response_json == {
        'documents': [
            {'id': docs[0].id},
        ],
    }


@pytest.mark.slow
async def test_archive_with_signatures(aiohttp_client, monkeypatch, s3_emulation):
    monkeypatch.setattr('app.lib.eusign_utils.verify', fake_verify)

    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou='********',
        create_api_account=True,
        feature_flags={'pass_sign_info_to_backend': False},
    )

    try:
        resp = await client.post(
            '/internal-api/documents',
            data=prepare_form_data(file=ARCH_PATH),
            headers=prepare_auth_headers(user),
        )
        assert resp.status == 201

        async with app['db'].acquire() as conn:
            docs = await select_documents_by_owner_edrpou(conn, edrpou='********')
            doc_ids = [d.id for d in docs]
            signatures = await select_signatures_by_documents_for_api(conn, doc_ids)

        assert len(docs) == 2
        assert len(signatures) == 2

        for doc in docs:
            assert doc.edrpou_owner == '********'
            assert doc.edrpou_recipient == '********'
            assert doc.email_recipient == '<EMAIL>'

        for s in signatures:
            assert s.document_id in doc_ids
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'enable_pro, edrpou_recipient, email_recipient',
    [(True, '********', '<EMAIL>'), (False, None, None)],
)
async def test_upload_from_web_without_integration_empty_metadata(
    aiohttp_client, monkeypatch, enable_pro, edrpou_recipient, email_recipient
):
    monkeypatch.setattr('app.lib.eusign_utils.verify', fake_verify)

    company_edrpou = '********'
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=company_edrpou,
        enable_pro_functionality=enable_pro,
    )

    resp = await client.post(
        '/internal-api/documents',
        data=prepare_form_data(file=ARCH_PATH_WITHOUT_SIGNATURES),
        headers=prepare_auth_headers(user),
    )
    assert resp.status == 201

    async with app['db'].acquire() as conn:
        docs = await select_documents_by_owner_edrpou(conn, edrpou=company_edrpou)

    assert len(docs) == 2

    for doc in docs:
        assert doc.edrpou_owner == '********'
        assert doc.edrpou_recipient == edrpou_recipient
        assert doc.email_recipient == email_recipient
        assert doc.vendor == '1C'

    # post document with recipient_edrpou for always parse metadata
    recipient_edrpou_meta = '33333333'
    monkeypatch.setattr(
        'app.uploads.utils.PARSE_META_BY_RECIPIENT_EDRPOU',
        {
            recipient_edrpou_meta,
        },
    )
    filename = f'{company_edrpou}_{recipient_edrpou_meta}_20200623_doc_1488.pdf'
    with tempfile.TemporaryDirectory() as tmpdir:
        filepath = Path(tmpdir) / filename
        filepath.write_text('茶' * 1024)
        resp = await client.post(
            path='/internal-api/documents',
            data=prepare_form_data(file=filepath),
            headers=prepare_auth_headers(user),
        )
        assert resp.status == 201

    async with app['db'].acquire() as conn:
        docs = await select_documents_by_owner_edrpou(conn, edrpou=company_edrpou)
        docs = [doc for doc in docs if doc.edrpou_recipient == recipient_edrpou_meta]

    assert len(docs) == 1
    doc = docs[0]
    assert doc.edrpou_owner == company_edrpou
    assert doc.edrpou_recipient == recipient_edrpou_meta


async def test_tags_on_document_uploaded(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    admin = await prepare_user_data(app, email='b@c', user_role=UserRole.admin.value)

    tags = await request_create_tags_for_roles(
        client=client,
        user=admin,
        tags_names=[TAG_NAME_1],
        roles_ids=[user.role_id],
    )
    assert len(tags) == 1
    tag_id = tags[0]['id']

    async with app['db'].acquire() as conn:

        async def upload_test_document(
            user,
            filename,
            expected_status,
            tags_ids=None,
            tags_names=None,
        ):
            response = await prepare_document_upload(
                app,
                client,
                user=user,
                filename=filename,
                tags=tags_ids,
                new_tags=tags_names,
                raw_response=True,
            )
            assert response.status == expected_status, await response.json() or response.status

        async def check_tags_by_document_title(title, tags_names):
            document = await select_one(
                conn, sa.select([document_table]).where(document_table.c.title == title)
            )
            tags = await select_tags_by_documents(
                conn=conn,
                documents_ids=[document.id],
            )

            names = {tag.name for tag in tags}
            assert len(tags) == len(tags_names), names
            assert set(tags_names) == set(names)

        # Upload without tags
        await upload_test_document(
            user,
            filename='1.txt',
            expected_status=HTTPStatus.CREATED,
        )
        await check_tags_by_document_title('1', [])

        # Upload with not existed new tags
        await upload_test_document(
            user,
            filename='2.txt',
            expected_status=HTTPStatus.CREATED,
            tags_names=[TAG_NAME_2],
        )
        await check_tags_by_document_title('2', [TAG_NAME_2])

        # Upload with existed tag and new tag
        await upload_test_document(
            user,
            filename='3.txt',
            tags_ids=[tag_id],
            tags_names=[TAG_NAME_3],
            expected_status=HTTPStatus.CREATED,
        )
        await check_tags_by_document_title('3', [TAG_NAME_3, TAG_NAME_1])

        # Upload with new bad tag
        await upload_test_document(
            user,
            filename='4.txt',
            tags_names=['1' * 1000],
            expected_status=HTTPStatus.BAD_REQUEST,
        )

        # Upload with bad tag_id
        await upload_test_document(
            user,
            filename='5.txt',
            tags_ids=[TEST_UUID],
            expected_status=HTTPStatus.FORBIDDEN,
        )

        # Upload with duplicated tag
        await upload_test_document(
            user,
            filename='6.txt',
            tags_ids=[TAG_NAME_2],
            expected_status=HTTPStatus.BAD_REQUEST,
        )

        # Upload with new not existed tags that are duplicated in request
        await upload_test_document(
            user,
            filename='7.txt',
            tags_names=[TAG_NAME_4, TAG_NAME_4],
            expected_status=HTTPStatus.CREATED,
        )
        await check_tags_by_document_title('7', [TAG_NAME_4])


async def test_upload_share_to(aiohttp_client, mailbox):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    coworker1 = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_upload(
        app,
        client,
        user=owner,
        share_to=[owner.role_id, coworker1.role_id],
    )

    assert len(mailbox) == 1
    assert mailbox[0]['To'] == '<EMAIL>'
    async with app['db'].acquire() as conn:
        listings = await select_listings(conn, [document.id])

    assert len(listings) == 2
    listing_map = {listing.role_id: listing for listing in listings}

    assert owner.role_id in listing_map
    assert listing_map[owner.role_id].sources == AccessSource.default

    assert coworker1.role_id in listing_map
    assert listing_map[coworker1.role_id].sources == AccessSource.viewer


@pytest.mark.parametrize(
    'signature_format, expected',
    [
        (SignatureFormat.internal_appended.value, SignatureFormat.internal_appended),
        (SignatureFormat.external_wrapped.value, SignatureFormat.external_wrapped),
        (SignatureFormat.external_separated.value, SignatureFormat.external_separated),
        (None, SignatureFormat.external_separated),
    ],
)
async def test_upload_with_signature_format(aiohttp_client, signature_format, expected):
    app, client, user = await prepare_client(aiohttp_client)
    await prepare_document_upload(
        app,
        client,
        user=user,
        signature_format=signature_format,
    )
    async with app['db'].acquire() as conn:
        document = await select_one(conn, document_table.select())

    assert document is not None
    assert document.signature_format == expected


async def test_upload_with_ordered_reviews(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    another_user = await prepare_user_data(app, email='<EMAIL>')
    yet_another_user = await prepare_user_data(app, email='<EMAIL>')
    group = await prepare_group(app, 'test', user, [user, another_user])

    reviewers = [
        {'id': user.role_id, 'type': ROLE},
        {'id': another_user.role_id, 'type': ROLE},
        {'id': yet_another_user.role_id, 'type': ROLE},
        {'id': group.id, 'type': GROUP},
    ]

    await set_billing_company_config(
        company_id=user.company_id,
        allow_ordered_reviews=False,
    )

    async def upload_document_with_ordered_review(reviewers: list):
        try:
            return await prepare_document_upload(
                app,
                client,
                user=user,
                reviewers=reviewers,
                parallel_review=False,
            )
        except AssertionError:
            return None

    document = await upload_document_with_ordered_review(reviewers)
    assert document is not None

    async with app['db'].acquire() as conn:
        await update_billing_company_config(
            conn=conn,
            company_id=user.company_id,
            config={'allow_ordered_reviews': True},
        )

        document = await upload_document_with_ordered_review(reviewers)
        assert document is not None

        requests = await select_review_requests(
            conn, user.company_id, [document.id], sort_by_order=True
        )

        assert len(requests) == 4
        assert requests[0].order == 1 and requests[0].to_role_id == user.role_id
        assert requests[1].order == 2 and requests[1].to_role_id == another_user.role_id
        assert requests[2].order == 3 and requests[2].to_role_id == yet_another_user.role_id
        assert (
            requests[3].order == 4
            and requests[3].to_group_id == group.id
            and not requests[3].to_role_id
        )

        review_setting = await select_review_setting(
            conn=conn,
            document_id=document.id,
            company_id=user.company_id,
        )
        assert review_setting.is_parallel is False


async def test_upload_wrapped_signature_container(
    aiohttp_client,
    s3_emulation,
):
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou='********',
        create_api_account=True,
        feature_flags={'pass_sign_info_to_backend': False},
    )

    # prepare form data
    path_ = DATA_PATH / 'wrapped_container.p7s'
    form_data = FormData()
    with open(path_, 'rb') as handler:
        content = handler.read()

    container = await unpack_wrapped_signature_container(content)

    filename = '<EMAIL>.p7s'
    form_data.add_field(
        name='file',
        value=io.BytesIO(content),
        filename=filename,
        content_type=ensure_mimetype(path_.name),
    )

    response = await client.post(
        '/internal-api/documents',
        data=form_data,
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.CREATED, await response.text()

    async with app['db'].acquire() as conn:
        docs = await select_documents_by_owner_edrpou(conn, edrpou='********')
        doc_ids = {d.id for d in docs}
        assert len(doc_ids) == 1
        document_id = doc_ids.pop()
        document = await select_document_by_id(conn, document_id)
        signatures = await select_signatures(conn, [document_id])

    assert len(signatures) == 1
    signature = signatures[0]
    assert signature.is_internal is True
    assert signature.format == SignatureFormat.internal_wrapped
    assert signature.key_exists == bool(signature.key_serial_number)
    assert signature.stamp_exists == bool(signature.stamp_serial_number)
    assert signature.key_serial_number is not None
    assert signature.stamp_serial_number is not None

    assert document.status_id == DocumentStatus.signed.value
    assert document.extension == '.xml'

    assert len(s3_emulation) == 2

    original = s3_emulation.get_document_original(document_id=document_id)
    p7s = s3_emulation.get_internal_signature(
        document_id=document_id,
        signature_id=signature.id,
    )
    assert p7s.body == content
    assert original.body == container.root


async def test_upload_chargeless(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
    )

    async with app['db'].acquire() as conn:
        company = await select_company_by_edrpou(conn, user.company_edrpou)
        units = company.upload_documents_left

        await prepare_document_upload(app=app, client=client, user=user)
        company = await select_company_by_edrpou(conn, user.company_edrpou)
        assert units - company.upload_documents_left == 1

        await prepare_document_upload(
            app=app,
            client=client,
            user=user,
            is_chargeless=True,
        )
        company = await select_company_by_edrpou(conn, user.company_edrpou)
        # still charged only 1 unit after 2 uploads
        assert units - company.upload_documents_left == 1


async def test_upload_document_with_template_id(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    await prepare_template_coworkers(app, CREATE_TEMPLATE_DATA)
    await set_billing_company_config(company_id=user.company_id, document_templates_enabled=True)

    response = await request_create_template(client, user, data=CREATE_TEMPLATE_DATA)
    assert response.status == 201

    template = await get_document_automation_template(app)
    assert template
    document = await prepare_document_upload(
        app,
        client,
        user=user,
        template_id=template.id,
    )
    assert document
    actions = await select_document_actions_for(role_id=user.role_id)
    assert len(actions) == 2
    assert {a.action for a in actions} == {
        document_actions.Action.document_upload,
        document_actions.Action.template_apply_manually,
    }
    for a in actions:
        if a.action == document_actions.Action.template_apply_manually:
            assert a.extra == {'template_name': template.name}


async def test_upload_with_category(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    await prepare_public_document_categories(amount=3)

    async def check_upload(category, expected):
        document = await prepare_document_upload(app, client, user=user, category=category)
        assert document.category == expected

    await check_upload(category=PublicDocumentCategory.other.value, expected=None)

    await check_upload(
        category=PublicDocumentCategory.act.value, expected=PublicDocumentCategory.act.value
    )

    await check_upload(category=None, expected=None)


async def test_upload_with_not_exist_parent(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    response = await prepare_document_upload(
        app, client, user=user, parent_id=TEST_UUID, raw_response=True
    )
    assert response.status == 404

    result = await response.json()
    assert result['code'] == 'object_does_not_exist'
    assert result['details'] == {
        'id': TEST_UUID,
        'type': 'document',
        'type_label': 'Документ',
    }


async def test_upload_with_no_access_to_parent(aiohttp_client):
    """Test uploading a document with parent_id that the user doesn't have access to"""
    app, client, owner_user = await prepare_client(aiohttp_client)

    parent_document = await prepare_document_data(app=app, owner=owner_user)

    second_user = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou='98765432',
    )

    second_client = await aiohttp_client(app)

    response = await prepare_document_upload(
        app,
        second_client,
        user=second_user,
        parent_id=parent_document.id,
        raw_response=True,
    )

    assert response.status == 403

    result = await response.json()
    assert result['code'] == 'access_denied'
    assert result['reason'] == 'Доступ до документу заборонено'


async def test_upload_with_private_parent(aiohttp_client):
    """Test uploading a document with parent_id that is marked as private"""
    app, client, user = await prepare_client(aiohttp_client)
    parent_document = await prepare_document_data(app=app, owner=user)
    await prepare_private_document(
        document_id=parent_document.id,
        company_edrpou=user.company_edrpou,
    )
    response = await prepare_document_upload(
        app=app,
        client=client,
        user=user,
        parent_id=parent_document.id,
        raw_response=True,
    )

    assert response.status == 400

    result = await response.json()
    assert result['code'] == 'invalid_document_link'
    assert (
        result['reason'] == 'Наразі не підтримується звʼязування для приватних документів. '
        'Змініть налаштування приватності документів та спробуйте ще раз'
    )


async def test_upload_private_document_with_non_private_parent(aiohttp_client):
    """Test that we can't upload a private document with a extended parent document"""
    app, client, user = await prepare_client(aiohttp_client)

    parent_document = await prepare_document_data(app=app, owner=user)

    params = {
        'parent_id': parent_document.id,
        'access_settings': {'level': DocumentAccessLevel.private},
    }
    data = FormData()
    data.add_field('file', b'content', filename='private_doc.txt')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        path=UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )

    assert response.status == 400

    result = await response.json()
    assert result['code'] == 'invalid_document_link'
    # Check the error message mentions private documents
    assert 'Наразі не підтримується звʼязування для приватних документів' in result['reason']


@pytest.mark.parametrize('param_type', ['query', 'form'])
async def test_upload_private_document(aiohttp_client, param_type: str):
    """Test that we can upload a private document"""
    app, client, user = await prepare_client(aiohttp_client)

    data = FormData()
    data.add_field('file', b'content', filename='private_doc.txt')

    query: Any = None
    if param_type == 'query':
        # For API clients
        query = {'access_settings_level': DocumentAccessLevel.private.value}
    elif param_type == 'form':
        # For Internal API
        form_params = {'access_settings': {'level': DocumentAccessLevel.private}}
        data.add_field('params', value=to_json(form_params), content_type='application/json')
    else:
        raise ValueError(f'Unknown param_type: {param_type}')

    response = await client.post(
        path=UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
        params=query,
    )

    assert response.status == HTTPStatus.CREATED

    result = await response.json()
    document_id = result['documents'][0]['id']
    async with services.db.acquire() as conn:
        level = await select_document_access_level(
            conn=conn,
            document_id=document_id,
            edrpou=user.company_edrpou,
        )
    assert level == DocumentAccessLevel.private.value


async def test_upload_hidden_email_not_found(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    response = await prepare_document_upload(
        app,
        client,
        user=user,
        recipient_edrpou='99999999',
        recipient_emails=None,
        hide_recipient_emails=True,
        raw_response=True,
    )
    assert response.status == HTTPStatus.BAD_REQUEST
    data = await response.json()
    assert 'Не можемо знайти підібраний email.' in data['reason']


async def test_upload_with_email_and_hidden_email(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    response = await prepare_document_upload(
        app,
        client,
        user=user,
        recipient_edrpou='99999999',
        recipient_emails=['<EMAIL>'],
        hide_recipient_emails=True,
        raw_response=True,
    )
    assert response.status == HTTPStatus.CREATED


async def test_upload__select_last_recipient_email(aiohttp_client, test_flags):
    test_flags[FeatureFlags.USE_LATEST_RECIPIENTS_TABLE.value] = True
    app, client, user = await prepare_client(aiohttp_client)

    document1 = await prepare_document_data(app, user)
    document2 = await prepare_document_data(app, user)

    await prepare_document_recipients(
        data=[
            {'edrpou': '99999999', 'emails': ['<EMAIL>'], 'document_id': document1.id},
            {'edrpou': '88888888', 'emails': ['t@com'], 'document_id': document1.id},
        ]
    )
    await prepare_document_recipients(
        data=[
            {'edrpou': '99999999', 'emails': ['<EMAIL>', '<EMAIL>'], 'document_id': document2.id},
            {'edrpou': '********', 'emails': ['c@com'], 'document_id': document2.id},
        ]
    )

    response = await prepare_document_upload(
        app,
        client,
        user=user,
        recipient_edrpou='99999999',
        recipient_emails=None,
        hide_recipient_emails=False,
        raw_response=True,
    )
    assert response.status == HTTPStatus.CREATED
    response_json = await response.json()
    document_id = response_json['documents'][0]['id']

    document = await get_document(document_id=document_id)
    assert document.edrpou_recipient == '99999999'
    assert document.email_recipient == '<EMAIL>, <EMAIL>'

    recipients = await get_document_recipients(document_id=document_id)

    assert len(recipients) == 1
    assert recipients[0].emails == ['<EMAIL>', '<EMAIL>']
    assert recipients[0].edrpou == '99999999'


@pytest.mark.parametrize('hide_recipient_emails', [True, False])
async def test_upload_without_edrpou(aiohttp_client, hide_recipient_emails):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_upload(
        app,
        client,
        user=user,
        recipient_edrpou=None,
        recipient_emails=['<EMAIL>'],
        hide_recipient_emails=hide_recipient_emails,
    )
    async with app['db'].acquire() as conn:
        recipient = await select_bilateral_document_recipient(
            conn=conn,
            document_id=document.id,
            document_owner_edrpou=document.edrpou_owner,
        )
    assert recipient is None
    assert document.edrpou_recipient is None
    assert document.email_recipient is None


async def test_upload_with_hidden_email(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    await save_hidden_emails(
        redis=app['redis'],
        user=user,
        recipients=[
            RecipientAggregatedData(edrpou='99999999', emails=['<EMAIL>'], is_hidden=True)
        ],
    )

    document = await prepare_document_upload(
        app,
        client,
        user=user,
        recipient_edrpou='99999999',
        recipient_emails=None,
        hide_recipient_emails=True,
    )
    async with app['db'].acquire() as conn:
        recipient = await select_bilateral_document_recipient(
            conn=conn,
            document_id=document.id,
            document_owner_edrpou=document.edrpou_owner,
        )
    assert recipient is not None
    assert recipient.emails == ['<EMAIL>']
    assert recipient.edrpou == '99999999'
    assert recipient.is_emails_hidden is True

    assert document.edrpou_recipient == '99999999'
    assert document.email_recipient == '<EMAIL>'


async def test_upload_empty(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    data = FormData()
    data.add_field('file', b'', filename='hello.txt')
    response = await client.post(
        UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )
    assert response.status == HTTPStatus.BAD_REQUEST, await response.json()
    assert (await response.json())['code'] == 'empty_upload_file'


async def test_upload_with_template_with_signers(aiohttp_client, monkeypatch):
    """
    Given:
        - user company has two or more suitable templates
    When:
        - a user uploads a document with selected template
    Then:
        - only selected template is applied
        - any other automations are ignored
    """
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email='<EMAIL>')
    async with services.db.acquire() as conn:
        # prepare suitable but not applied template
        suiting_signers = {
            'is_ordered': False,
            'signer_entities': [{'id': user.role_id, 'type': ROLE}],
        }
        suiting_template = await prepare_automation_template(
            conn, user, signers_settings=suiting_signers
        )
        suiting_conditions = {'and': [{'or': [{'eq': ['#document_side', 'internal']}]}]}
        await prepare_automation(
            conn,
            suiting_conditions,
            template_id=suiting_template.id,
            company_id=user.company_id,
        )

        # prepare non-suitable template that will be selected on upload
        # & therefore applied to the document
        applied_signers = {
            'is_ordered': False,
            'signer_entities': [{'id': coworker.role_id, 'type': ROLE}],
        }
        applied_template = await prepare_automation_template(
            conn, user, signers_settings=applied_signers
        )
        applied_conditions = {'and': [{'or': [{'eq': ['#document_side', 'inbox']}]}]}
        await prepare_automation(
            conn,
            applied_conditions,
            template_id=applied_template.id,
            company_id=user.company_id,
        )

    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    params = {
        'template_id': applied_template.id,
        'is_internal': '1',
        # also pass signer data in url to simulate front-end logic
        # but it will be orverriden by template's signer settings
        'signer_roles': [user.role_id],
    }

    # Act
    response = await client.post(
        API_V2_DOCUMENTS_URL,
        headers=prepare_auth_headers(user),
        params=params,
        data=data,
    )

    # Assert
    assert response.status == HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        signers = await select_all(conn, document_signer_table.select())

    assert len(signers) == 1
    assert signers[0].assigner == user.role_id
    assert signers[0].role_id == coworker.role_id


async def test_upload_with_template_with_signers_old_template_format(aiohttp_client, monkeypatch):
    """
    Given:
        - user company has two or more suitable templates
    When:
        - a user uploads a document with selected template
    Then:
        - only selected template is applied
        - any other automations are ignored
    """
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email='<EMAIL>')
    async with services.db.acquire() as conn:
        # prepare suitable but not applied template
        suiting_signers = {
            'is_ordered': False,
            'signer_entities': [{'id': user.role_id, 'type': ROLE}],
        }
        suiting_template = await prepare_automation_template(
            conn, user, signers_settings=suiting_signers
        )
        suiting_conditions = {'and': [{'or': [{'eq': ['#document_side', 'internal']}]}]}
        await prepare_automation(
            conn,
            suiting_conditions,
            template_id=suiting_template.id,
            company_id=user.company_id,
        )

        # prepare non-suitable template that will be selected on upload
        # & therefore applied to the document
        applied_signers = {
            'is_ordered': False,
            'signers_ids': [coworker.role_id],
        }
        applied_template = await prepare_automation_template(
            conn, user, signers_settings=applied_signers
        )
        applied_conditions = {'and': [{'or': [{'eq': ['#document_side', 'inbox']}]}]}
        await prepare_automation(
            conn,
            applied_conditions,
            template_id=applied_template.id,
            company_id=user.company_id,
        )

    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    params = {
        'template_id': applied_template.id,
        'is_internal': '1',
        # also pass signer data in url to simulate front-end logic
        # but it will be orverriden by template's signer settings
        'signer_roles': [user.role_id],
    }

    # Act
    response = await client.post(
        API_V2_DOCUMENTS_URL,
        headers=prepare_auth_headers(user),
        params=params,
        data=data,
    )

    # Assert
    assert response.status == HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        signers = await select_all(conn, document_signer_table.select())

    assert len(signers) == 1
    assert signers[0].assigner == user.role_id
    assert signers[0].role_id == coworker.role_id


async def test_upload_with_template_with_reviewers(aiohttp_client, monkeypatch):
    """
    Given:
        - user company has two or more suitable templates
    When:
        - a user uploads a document with selected template
    Then:
        - only selected template is applied
        - any other automations are ignored
    """
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email='<EMAIL>')
    async with services.db.acquire() as conn:
        # prepare suitable but not applied template
        suiting_review = {'is_required': False, 'reviewers_ids': [user.role_id]}
        suiting_template = await prepare_automation_template(
            conn, user, review_settings=suiting_review
        )
        suiting_conditions = {'and': [{'or': [{'eq': ['#document_side', 'internal']}]}]}
        await prepare_automation(
            conn,
            suiting_conditions,
            template_id=suiting_template.id,
            company_id=user.company_id,
        )

        # prepare non-suitable template that will be selected on upload
        # & therefore applied to the document
        applied_review = {'is_required': True, 'reviewers_ids': [coworker.role_id]}
        applied_template = await prepare_automation_template(
            conn, user, review_settings=applied_review
        )
        applied_conditions = {'and': [{'or': [{'eq': ['#document_side', 'inbox']}]}]}
        await prepare_automation(
            conn,
            applied_conditions,
            template_id=applied_template.id,
            company_id=user.company_id,
        )

    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    params = {
        'template_id': applied_template.id,
        'is_internal': '1',
        # also pass review data in url to simulate front-end logic
        # but it will be overridden by template's review settings
        'reviewers_ids': [user.role_id],
    }

    # Act
    response = await client.post(
        API_V2_DOCUMENTS_URL,
        headers=prepare_auth_headers(user),
        params=params,
        data=data,
    )

    # Assert
    assert response.status == HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        review_requests = await select_all(conn, review_request_table.select())

    assert len(review_requests) == 1
    assert review_requests[0].from_role_id == user.role_id
    assert review_requests[0].to_role_id == coworker.role_id


async def test_upload_with_invalid_template(aiohttp_client):
    """
    Given:
        - partner's company has one or more templates
    When:
        - user uploads a document with template from partner's company
    Then:
        - upload is unsuccessful, user receives 404 error code
    """
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)
    partner = await prepare_user_data(
        app, company_edrpou=TEST_RECIPIENT_EDRPOU, email='<EMAIL>'
    )
    async with services.db.acquire() as conn:
        template = await prepare_automation_template(conn, partner)

    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    params = {'template_id': template.id}

    # Act
    response = await client.post(
        API_V2_DOCUMENTS_URL,
        headers=prepare_auth_headers(user),
        params=params,
        data=data,
    )

    # Assert
    assert response.status == HTTPStatus.NOT_FOUND


@pytest.mark.parametrize(
    'amount, expected',
    [
        # amount is nullable field
        (None, None),
        # zero value is allowed
        ('0', 0),
        # whole value converts to integer
        ('1234362', 1_234_362),
        ('-1234362', -1_234_362),
        # value range is -100B <= x <= 100B
        ('10000000000000', 100_000_000_000_00),
        ('-10000000000000', -100_000_000_000_00),
    ],
)
async def test_upload_with_amount(aiohttp_client, monkeypatch, amount, expected):
    """
    Given:
        - `amount` parameter is present in request payload
    When:
        - user specifies amount before uploading the document
    Then:
        - `amount` column is filled in `documents` table
    """
    # Arrange
    _, client, user = await prepare_client(aiohttp_client)
    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    params = {}
    if amount is not None:
        params['amount'] = amount

    # Act
    resp = await client.post(
        UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        params=params,
        data=data,
    )

    # Assert
    assert resp.status == HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        document = await select_one(conn, document_table.select())

    assert document is not None
    assert document.amount == expected


@pytest.mark.parametrize(
    'amount',
    [
        # amount is out of range (-100B <= x <= 100B)
        '10000000000001',
        '-10000000000001',
    ],
)
async def test_upload_with_invalid_amount(aiohttp_client, amount):
    """
    Given:
        - invalid `amount` parameter is present in request payload
    When:
        - user specifies invalid amount before uploading the document
    Then:
        - upload is failed, user has 400 Bad Request error code
    """
    # Arrange
    _, client, user = await prepare_client(aiohttp_client)
    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    params = {'amount': amount}

    # Act
    resp = await client.post(
        UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        params=params,
        data=data,
    )

    # Assert
    assert resp.status == HTTPStatus.BAD_REQUEST


async def test_upload_multipart_form_parameters(aiohttp_client):
    """Test that you can send uploading parameters as part of multipart-form/data"""
    app, client, user = await prepare_client(aiohttp_client)

    params = {
        'doc_number': 'FormTest1',
        'title': 'TestTitle',
        'date_document': '2022-01-01',
        'flows': [
            {
                'emails': ['<EMAIL>', '<EMAIL>'],
                'edrpou': '44444444',
                'sign_num': 2,
            },
            {
                'emails': ['<EMAIL>'],
                'edrpou': '********',
                'sign_num': 1,
            },
            {
                'emails': ['<EMAIL>'],
                'edrpou': '66666666',
                'sign_num': 1,
            },
        ],
        'new_tags': ['TestTag1'],
        'is_multilateral': True,
    }

    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )
    assert response.status == HTTPStatus.CREATED, await response.json()

    async with services.db.acquire() as conn:
        flows = await select_all(conn, doc_flow_table.select())
        assert len(flows) == 3
        assert {f['edrpou'] for f in flows} == {'44444444', '********', '66666666'}
        tags = await select_all(conn, tag_table.select())
        assert len(tags) == 1
        assert tags[0]['name'] == 'TestTag1'

        documents = await select_all(conn, document_table.select())
        assert len(documents) == 1
        document = documents[0]
        assert document.title == 'TestTitle'
        assert document.number == 'FormTest1'
        assert document.date_document.isoformat() == '2022-01-01T00:00:00+00:00'


@pytest.mark.parametrize('param_type', ['form', 'query'])
async def test_upload_multilateral_with_flows(aiohttp_client, param_type):
    """Test that you can upload multilateral document with flows"""
    app, client, user = await prepare_client(aiohttp_client)

    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')

    flows_data = [
        {'order': 0, 'sign_num': 2, 'edrpou': '44444444', 'emails': ['<EMAIL>', '<EMAIL>']},
        {'order': 1, 'sign_num': 1, 'edrpou': '********', 'emails': ['<EMAIL>']},
        {'order': 2, 'sign_num': 1, 'edrpou': '66666666', 'emails': ['<EMAIL>']},
    ]

    query = None
    if param_type == 'form':
        # For Internal API
        form_params = {'is_multilateral': True, 'flows': flows_data}
        data.add_field('params', value=to_json(form_params), content_type='application/json')
    elif param_type == 'query':
        # For API clients
        query = {'is_multilateral': '1', 'flows': to_json(flows_data)}
    else:
        raise ValueError(f'Unknown param_type: {param_type}')

    response = await client.post(
        UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
        params=query,
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.CREATED, response_json

    document_id = response_json['documents'][0]['id']

    document = await get_document(document_id=document_id)
    assert document.is_multilateral is True

    recipients = await get_document_recipients(document_id=document_id)
    assert len(recipients) == 3

    recipients_map: dict[str, DocumentRecipient]
    recipients_map = {recipient.edrpou: recipient for recipient in recipients}

    recipient_1 = recipients_map['44444444']
    assert recipient_1.emails == ['<EMAIL>', '<EMAIL>']
    assert recipient_1.from_flow is True

    recipient_2 = recipients_map['********']
    assert recipient_2.emails == ['<EMAIL>']
    assert recipient_2.from_flow is True

    recipient_3 = recipients_map['66666666']
    assert recipient_3.emails == ['<EMAIL>']
    assert recipient_3.from_flow is True

    flows = await get_flows(document_id=document_id)
    assert len(flows) == 3

    flows_map: dict[str, FlowItem]
    flows_map = {flow.edrpou: flow for flow in flows}

    flow_1 = flows_map['44444444']
    assert flow_1.signatures_count == 2
    assert flow_1.pending_signatures_count == 2
    assert flow_1.order == 0
    assert flow_1.receivers_id == recipient_1.id

    flow_2 = flows_map['********']
    assert flow_2.signatures_count == 1
    assert flow_2.pending_signatures_count == 1
    assert flow_2.order == 1
    assert flow_2.receivers_id == recipient_2.id

    flow_3 = flows_map['66666666']
    assert flow_3.signatures_count == 1
    assert flow_3.pending_signatures_count == 1
    assert flow_3.order == 2
    assert flow_3.receivers_id == recipient_3.id


@pytest.mark.parametrize('param_type', ['form', 'query'])
async def test_upload_multilateral_with_document_parameters(aiohttp_client, param_type):
    """Test that you can upload a document with document parameters"""
    app, client, user = await prepare_client(aiohttp_client)

    field1 = await create_documents_field(
        app=app,
        user=user,
        name='user_field1',
        type=DocumentFieldType.text,
    )
    field2 = await create_documents_field(
        app=app,
        user=user,
        name='user_field2',
        type=DocumentFieldType.date,
    )
    await create_documents_field(
        app=app,
        user=user,
        name='user_field3',
        type=DocumentFieldType.number,
    )

    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')

    parameters_data = {
        'parameters': [
            {'field_id': field1.id, 'value': 'test_value1'},
            {'field_id': field2.id, 'value': '2022-01-01'},
        ]
    }

    query = None
    if param_type == 'form':
        # For Internal API
        form_params = {'parameters': parameters_data}
        data.add_field('params', value=to_json(form_params), content_type='application/json')
    elif param_type == 'query':
        # For API clients
        query = {'parameters': to_json(parameters_data)}
    else:
        raise ValueError(f'Unknown param_type: {param_type}')

    response = await client.post(
        UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
        params=query,
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.CREATED, response_json

    document_id = response_json['documents'][0]['id']

    async with services.db.acquire() as conn:
        parameters = await select_document_parameters(
            conn=conn,
            company_id=user.company_id,
            document_id=document_id,
        )
        assert len(parameters) == 2
        assert {(p.field_id, p.value) for p in parameters} == {
            (field1.id, 'test_value1'),
            (
                field2.id,
                # dates we store in UTC, in Kyiv timezone it will be 2022-01-01
                '2021-12-31T22:00:00+00:00',
            ),
        }


@pytest.mark.parametrize(
    'enable_pro_rate',
    [
        True,
        # TODO[version]: for now all companies can upload versioned documents
        #  after finding out how this feature will be used by companies.
        #  Use this ref (version_rate_limit) to find all places that should be uncommented.
        # False,
    ],
)
@pytest.mark.parametrize(
    'is_versioned',
    [
        True,
        False,
        None,
    ],
)
async def test_upload_versioned_rate(aiohttp_client, monkeypatch, is_versioned, enable_pro_rate):
    # Arrange
    _, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=enable_pro_rate)
    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    params = {}

    if is_versioned is not None:
        params['is_versioned'] = str(is_versioned)

    # Act
    resp = await client.post(
        UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        params=params,
        data=data,
    )

    # Assert

    # user is not allowed to use functional without proper rate
    if not enable_pro_rate and is_versioned:
        assert resp.status == HTTPStatus.FORBIDDEN

    else:
        assert resp.status == HTTPStatus.CREATED

        async with services.db.acquire() as conn:
            document = await select_one(conn, document_table.select())
            versions = await select_all(conn, document_version_table.select())

        assert document is not None

        if is_versioned:
            assert len(versions) == 1
            assert versions[0].document_id == document.id
            assert versions[0].type == DocumentVersionType.new_upload
        else:
            assert len(versions) == 0


@pytest.mark.parametrize(
    ('extension', 'status_code'),
    [
        ('xml', HTTPStatus.BAD_REQUEST),
        ('doc', HTTPStatus.CREATED),
    ],
)
async def test_upload_versioned_extension(aiohttp_client, monkeypatch, extension, status_code):
    # Arrange
    _, client, user = await prepare_client(aiohttp_client)
    data = FormData()
    data.add_field('file', b'content', filename=f'hello.{extension}')
    params = {'is_versioned': str(True)}

    # Act
    resp = await client.post(
        UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        params=params,
        data=data,
    )

    # Assert
    async with services.db.acquire() as conn:
        document = await select_one(conn, document_table.select())
        versions = await select_all(conn, document_version_table.select())

    assert resp.status == status_code
    if status_code == HTTPStatus.BAD_REQUEST:
        assert document is None
        assert len(versions) == 0
    elif status_code == HTTPStatus.CREATED:
        assert document is not None
        assert len(versions) == 1
        assert versions[0].document_id == document.id
        assert versions[0].type == DocumentVersionType.new_upload


@pytest.mark.parametrize(
    ('first_sign_by', 'status_code'),
    [
        (FirstSignBy.recipient.value, HTTPStatus.CREATED),
        (FirstSignBy.owner.value, HTTPStatus.BAD_REQUEST),
    ],
)
async def test_upload_versioned_first_sign_by(
    aiohttp_client, monkeypatch, first_sign_by, status_code
):
    # Arrange
    _, client, user = await prepare_client(aiohttp_client)
    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    params = {'is_versioned': str(True), 'first_sign_by': first_sign_by}

    # Act
    resp = await client.post(
        UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        params=params,
        data=data,
    )

    # Assert
    async with services.db.acquire() as conn:
        document = await select_one(conn, document_table.select())
        versions = await select_all(conn, document_version_table.select())

    assert resp.status == status_code

    if status_code == HTTPStatus.BAD_REQUEST:
        assert document is None
        assert len(versions) == 0
    elif status_code == HTTPStatus.CREATED:
        assert document is not None
        assert len(versions) == 1
        assert versions[0].document_id == document.id
        assert versions[0].type == DocumentVersionType.new_upload


@pytest.mark.parametrize(
    'flows_param, expected_emails',
    [
        pytest.param(
            [
                {
                    'emails': [],
                    'edrpou': TEST_COMPANY_EDRPOU,
                    'sign_num': 1,  # will be overwritten by len(signers_roles)
                },
                {
                    'emails': ['<EMAIL>'],
                    'edrpou': '********',
                    'sign_num': 1,
                },
            ],
            # both coworkers should receive to sign the document
            {
                '<EMAIL>': ['invite_document'],
                '<EMAIL>': ['documents_for_signer'],
                '<EMAIL>': ['documents_for_signer'],
            },
            id='parallel_flows',
        ),
        pytest.param(
            [
                {
                    'emails': [],
                    'edrpou': TEST_COMPANY_EDRPOU,
                    'sign_num': 1,  # will be overwritten by len(signers_roles),
                    'order': 0,
                },
                {
                    'emails': ['<EMAIL>'],
                    'edrpou': '********',
                    'sign_num': 1,
                    'order': 1,
                },
            ],
            # both coworkers should receive to sign the document
            {
                '<EMAIL>': ['documents_for_signer'],
                '<EMAIL>': ['documents_for_signer'],
            },
            id='ordered_first_owner',
        ),
        pytest.param(
            [
                {
                    'emails': ['<EMAIL>'],
                    'edrpou': '********',
                    'sign_num': 1,
                    'order': 0,
                },
                {
                    'emails': [],
                    'edrpou': TEST_COMPANY_EDRPOU,
                    'sign_num': 1,  # will be overwritten by len(signers_roles),
                    'order': 1,
                },
            ],
            # only recipient should receive notification about the document
            {
                '<EMAIL>': ['invite_document'],
            },
            id='ordered_first_recipient',
        ),
    ],
)
async def test_upload_multilateral_with_document_signers(
    aiohttp_client,
    mailbox,
    flows_param,
    expected_emails: dict[str, list[str]],
):
    """Test upload document with document signers"""
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )

    # Prepare two coworkers
    coworker1 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    coworker2 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=TEST_COMPANY_EDRPOU,
    )

    params = {
        'is_multilateral': True,
        'signer_roles': [coworker1.role_id, coworker2.role_id],
        'flows': flows_param,
    }

    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        path=UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.CREATED, response_json

    document_id = response_json['documents'][0]['id']

    # Two signers should be created
    signers = await get_document_signers(document_id=document_id)
    assert len(signers) == 2
    assert {s.role_id for s in signers} == {coworker1.role_id, coworker2.role_id}

    # Two flows should be created
    flows = await get_flows(document_id=document_id)
    assert len(flows) == 2
    flows_map = {flow.edrpou: flow for flow in flows}
    # owner flow should be equal to len(signers_roles)
    assert flows_map[TEST_COMPANY_EDRPOU].signatures_count == 2
    assert flows_map['********'].signatures_count == 1

    # Check document parameters
    document = await get_document(document_id=document_id)
    assert document.expected_owner_signatures == 1
    assert document.expected_recipient_signatures == 1
    assert document.is_multilateral is True

    assert len(mailbox) == len(expected_emails)
    assert mailbox.emails_set == set(expected_emails)
    for email, expected_templates in expected_emails.items():
        messages = mailbox.by_email(email)
        messages_templates = {msg['X-Template'] for msg in messages}
        assert len(messages_templates) == len(expected_templates), email
        assert set(messages_templates) == set(expected_templates), email


async def test_edit_signers_after_upload_multilateral_with_signers(aiohttp_client):
    """Fix DOC-6004 coverage"""
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )

    # Prepare two coworkers
    coworker1 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    coworker2 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=TEST_COMPANY_EDRPOU,
    )

    params = {
        'is_multilateral': True,
        'signer_roles': [coworker1.role_id, coworker2.role_id],
    }

    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        path=UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )
    assert response.status == HTTPStatus.CREATED
    document_id = (await response.json())['documents'][0]['id']

    # Two signers should be created
    assert len(await get_document_signers(document_id=document_id)) == 2

    assert len(await get_flows(document_id=document_id)) == 0

    document = await get_document(document_id=document_id)
    assert document.is_multilateral is True

    response = await request_document_update(
        client,
        user=user,
        document=document,
        signers=[coworker1],
    )
    assert response.status == HTTPStatus.OK


@pytest.mark.parametrize('add_signer_roles_on_upload', [True, False])
async def test_signatures_count_on_multilateral_with_signers(
    aiohttp_client, add_signer_roles_on_upload
):
    """Test fix bug on upload multilateral document with 2 document signers (DOC-5783)"""
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client, company_edrpou=TEST_COMPANY_EDRPOU
    )

    # Prepare two coworkers, two recipients
    coworker1 = await prepare_user_data(
        app=app, email='<EMAIL>', company_edrpou=TEST_COMPANY_EDRPOU
    )
    coworker2 = await prepare_user_data(
        app=app, email='<EMAIL>', company_edrpou=TEST_COMPANY_EDRPOU
    )
    rec1 = await prepare_user_data(
        app=app, email='<EMAIL>', company_edrpou=TEST_COMPANY_EDRPOU_2
    )
    rec2 = await prepare_user_data(app=app, email='<EMAIL>', company_edrpou='********')

    params = {'is_multilateral': True}
    if add_signer_roles_on_upload:
        params['signer_roles'] = [coworker1.role_id, coworker2.role_id]
    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        path=UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )
    document_id = (await response.json())['documents'][0]['id']
    assert response.status == HTTPStatus.CREATED

    receivers = [
        {
            'edrpou': user.company_edrpou,
            'emails': [user.email],
            'sign_num': 1,  # Ignore if signers already added
        },
        {
            'edrpou': rec1.company_edrpou,
            'emails': [rec1.email],
            'sign_num': 1,
        },
        {
            'edrpou': rec2.company_edrpou,
            'emails': [rec2.email],
            'sign_num': 1,
        },
    ]
    data = {'docs': [document_id], 'receivers': receivers}
    response = await client.post(ADD_FLOW_URL, json=data, headers=prepare_auth_headers(user))
    assert response.status == HTTPStatus.CREATED

    async with app['db'].acquire() as conn:
        doc_flows = await select_flows_by(conn, document_id=document_id)
        changed_doc = await select_document_by_id(conn, document_id)
        # Two signers should be created
        signers = await get_document_signers(document_id=document_id)
    assert changed_doc.status_id == DocumentStatus.flow.value
    assert len(doc_flows) == 3
    assert len(signers) == (2 if add_signer_roles_on_upload else 0)

    async with app['db'].acquire() as conn:
        doc_flows = await select_flows_by(conn, document_id=document_id, edrpou=user.company_edrpou)
        assert doc_flows[0].signatures_count == (2 if add_signer_roles_on_upload else 1)
        assert doc_flows[0].pending_signatures_count == (2 if add_signer_roles_on_upload else 1)


async def test_count_of_signatures_by_public_api(aiohttp_client):
    """
    Test case for https://tabula-rasa.atlassian.net/browse/DOC-5773
    For backward compatibility, added logic for cases when user set `expected_owner_signatures`,
     without set signers (`signer_roles` param) using  public API
    """
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client, company_edrpou=TEST_COMPANY_EDRPOU, create_api_account=True
    )
    rec1 = await prepare_user_data(
        app=app, email='<EMAIL>', company_edrpou=TEST_COMPANY_EDRPOU_2
    )

    query_params = {
        'expected_owner_signatures': 2,
        'recipient_edrpou': TEST_COMPANY_EDRPOU_2,
        'recipient_emails': rec1.email,
    }

    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')

    response = await client.post(
        API_V2_DOCUMENTS_URL,
        headers=prepare_auth_headers(user),
        data=data,
        params=query_params,
    )
    assert response.status == HTTPStatus.CREATED, await response.json()
    document_id = (await response.json())['documents'][0]['id']

    # First sign from owner, status 7003, don't send to recipient, wait second sign from owner
    await sign_and_send_document(client, document_id, user)

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn=conn, document_id=document_id)
        assert document.status_id == DocumentStatus.signed.value
        assert document.expected_owner_signatures == 2

    # Second sign from owner, status 7004, sended to recipient
    await sign_and_send_document(client, document_id, user)

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn=conn, document_id=document_id)
        assert document.status_id == DocumentStatus.signed_and_sent.value


async def test_upload_multilateral_with_document_signers_parameters(
    aiohttp_client,
    monkeypatch,
    mailbox,
):
    """Test upload document with document signers"""
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )

    # Prepare two coworkers
    coworker1 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    coworker2 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=TEST_COMPANY_EDRPOU,
    )

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )
        await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=user.role_id,
        )

    params = {
        'is_multilateral': True,
        'parallel_signing': False,
        'signer_parameters': {
            'signers': [
                {
                    'value': coworker2.email,
                    'signer_type': SignersInfoCtx.EntityType.email,
                },
                {
                    'value': group.id,
                    'signer_type': SignersInfoCtx.EntityType.group,
                },
                {
                    'value': coworker1.role_id,
                    'signer_type': SignersInfoCtx.EntityType.role,
                },
            ]
        },
        'flows': [
            {
                'emails': [],
                'edrpou': TEST_COMPANY_EDRPOU,
                'sign_num': 1,  # will be overwritten by len(signers_roles)
            },
            {
                'emails': ['<EMAIL>'],
                'edrpou': '********',
                'sign_num': 1,
            },
        ],
    }

    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        path=UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.CREATED, response_json

    document_id = response_json['documents'][0]['id']

    # Three signers should be created
    signers = await get_document_signers(document_id=document_id)
    assert len(signers) == 3
    assert all(s.order is not None for s in signers)
    signers = sorted(signers, key=lambda s: s.order)
    signer1, signer2, signer3 = signers

    assert signer1.company_id == user.company_id
    assert signer1.role_id == coworker2.role_id
    assert signer1.group_id is None
    assert signer1.order == 1

    assert signer1.company_id == user.company_id
    assert signer2.role_id is None
    assert signer2.group_id == group.id
    assert signer2.order == 2

    assert signer1.company_id == user.company_id
    assert signer3.role_id == coworker1.role_id
    assert signer3.group_id is None
    assert signer3.order == 3

    # Three flows should be created
    flows = await get_flows(document_id=document_id)
    assert len(flows) == 2
    flows_map = {flow.edrpou: flow for flow in flows}
    # owner flow should be equal to len(signers_roles)
    assert flows_map[TEST_COMPANY_EDRPOU].signatures_count == 3
    assert flows_map['********'].signatures_count == 1

    # Check document parameters
    document = await get_document(document_id=document_id)
    assert document.expected_owner_signatures == 1
    assert document.expected_recipient_signatures == 1
    assert document.is_multilateral is True


@pytest.mark.parametrize('is_versioned', [True, False])
async def test_upload_with_comment(aiohttp_client, is_versioned):
    """Test upload document with comment"""
    app, client, user = await prepare_client(aiohttp_client=aiohttp_client)

    params = {
        'comment': {
            'text': 'test comment',
        },
        'is_versioned': is_versioned,
    }

    data = FormData()
    # multiple files
    data.add_field('file', b'content', filename='hello1.txt')
    data.add_field('file', b'content', filename='hello2.txt')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        path=UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.CREATED, response_json

    documents_ids = [doc['id'] for doc in response_json['documents']]
    async with services.db.acquire() as conn:
        comments = await select_comments(conn, documents_ids=documents_ids)

    assert len(comments) == 2
    assert {comment.document_id for comment in comments} == set(documents_ids)

    for comment in comments:
        assert comment.text == 'test comment'
        assert comment.access_company_id is None
        assert comment.role_id == user.role_id
        if is_versioned:
            assert comment.document_version_id is not None
        else:
            assert comment.document_version_id is None

    actions = await select_document_actions_for(role_id=user.role_id)
    assert len(actions) == 4
    assert len([_ for _ in actions if _.action == document_actions.Action.comment_create]) == 2
    assert len([_ for _ in actions if _.action == document_actions.Action.document_upload]) == 2


async def test_upload_with_recipients_2way_document_fist_sign_owner_is_emails_hidden(
    aiohttp_client,
    monkeypatch,
):
    app, client, user = await prepare_client(aiohttp_client=aiohttp_client)
    recipient = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    validate_hidden_emails_mock = AsyncMock(
        return_value={
            recipient.company_edrpou: [recipient.email],
        }
    )
    monkeypatch.setattr(
        'app.uploads.validators.validate_hidden_emails_in_redis',
        validate_hidden_emails_mock,
    )

    params = {
        'recipients': {
            'is_ordered': False,
            'recipients': [
                {
                    'edrpou': user.company_edrpou,
                    'emails': None,
                    'is_email_hidden': False,
                    'role': 'signer',
                },
                {
                    'edrpou': recipient.company_edrpou,
                    'emails': None,
                    'is_email_hidden': True,
                    'role': 'signer',
                },
            ],
        },
    }

    data = FormData()
    data.add_field('file', b'content', filename='hello1.txt')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        path=UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.CREATED, response_json

    async with services.db.acquire() as conn:
        document = await select_one(conn, document_table.select())
        recipients = await get_document_recipients(document_id=document.id)

    assert not document.is_internal
    assert not document.is_multilateral
    assert document.expected_recipient_signatures == 1
    assert document.expected_owner_signatures == 1
    assert document.first_sign_by == FirstSignBy.owner

    # For bilateral document, we should have 1 record in recipient table (for recipient)
    assert len(recipients) == 1
    recipients_result = [
        {
            'edrpou': r.edrpou,
            'emails': r.emails,
            'is_email_hidden': r.is_emails_hidden,
            'document_id': r.document_id,
            'from_flow': r.from_flow,
            'external_email': r.external_meta,
        }
        for r in recipients
    ]
    assert recipients_result == [
        {
            'edrpou': recipient.company_edrpou,
            'emails': [recipient.email],
            'is_email_hidden': True,
            'document_id': document.id,
            'from_flow': False,
            'external_email': None,
        },
    ]

    # Mock should be call with request to find hidden email
    validate_hidden_emails_mock.assert_called_once_with(
        role_id=user.role_id,
        recipient_edrpous=[recipient.company_edrpou],
    )


@pytest.mark.parametrize(
    'params, expected',
    [
        # Empty recipients -> bilateral document
        pytest.param(
            {
                'recipients': {'is_ordered': False, 'recipients': []},
            },
            {
                'is_internal': False,
                'is_multilateral': False,
                'expected_recipient_signatures': 1,
                'expected_owner_signatures': 1,
                'first_sign_by': FirstSignBy.owner,
                'signers': [],
                'flows': [],
                # For bilateral document, we should have 1 access record for an owner; access for
                # recipient will be opened when an owner sends a document (it should be sent
                # manually)
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'recipients': [],
            },
            id='without_recipients',
        ),
        # One recipient and it's owner company -> internal document
        pytest.param(
            {
                'recipients': {
                    'is_ordered': False,
                    'recipients': [
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
            },
            {
                'is_internal': True,
                'is_multilateral': False,
                'expected_recipient_signatures': 0,
                'expected_owner_signatures': 1,
                'first_sign_by': FirstSignBy.owner,
                'signers': [],
                'flows': [],
                # For internal document, we should have 1 access record for an owner
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'recipients': [],
            },
            id='internal_document',
        ),
        # Case with for an internal document with document signers
        pytest.param(
            {
                'recipients': {
                    'is_ordered': False,
                    'recipients': [
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
                'signer_parameters': {
                    'signers': [
                        {
                            'value': ROLE_ID_3,
                            'signer_type': SignersInfoCtx.EntityType.role,
                        },
                        {
                            'value': USER_EMAIL_4,
                            'signer_type': SignersInfoCtx.EntityType.email,
                        },
                    ]
                },
                'parallel_signing': False,
            },
            {
                'is_internal': True,
                'is_multilateral': False,
                'expected_recipient_signatures': 0,
                'expected_owner_signatures': 2,  # len(signers)
                'first_sign_by': FirstSignBy.owner,
                'signers': [
                    {
                        'role_id': ROLE_ID_3,
                        'order': 1,
                    },
                    {
                        'role_id': ROLE_ID_4,
                        'order': 2,
                    },
                ],
                'flows': [],
                # Owner + 2 signers
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_3,
                        'sources': AccessSource.default,
                    },
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_4,
                        'sources': AccessSource.default,
                    },
                ],
                'recipients': [],
            },
            id='internal_document_with_signers',
        ),
        # Case of a bilateral document, where first sign by owner
        pytest.param(
            {
                'recipients': {
                    'is_ordered': False,
                    'recipients': [
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU,
                            'emails': [TEST_RECIPIENT_EMAIL],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
            },
            {
                'is_internal': False,
                'is_multilateral': False,
                'expected_recipient_signatures': 1,
                'expected_owner_signatures': 1,
                'first_sign_by': FirstSignBy.owner,
                'signers': [],
                'flows': [],
                # For bilateral document, we should have 1 access record for an owner; access for
                # recipient will be opened when an owner sends a document
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                # 1 record for recipient
                'recipients': [
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'emails': [TEST_RECIPIENT_EMAIL],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
            },
            id='first_sign_by_owner',
        ),
        # Case of a bilateral document, where first sign by recipient
        pytest.param(
            {
                'recipients': {
                    'is_ordered': False,
                    'recipients': [
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU,
                            'emails': [TEST_RECIPIENT_EMAIL],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
            },
            {
                'is_internal': False,
                'is_multilateral': False,
                'expected_recipient_signatures': 1,
                'expected_owner_signatures': 1,
                'first_sign_by': FirstSignBy.recipient,
                'signers': [],
                'flows': [],
                # For bilateral document, we should have 1 access record for an owner; access for
                # recipient will be opened when an owner sends a document (it should be sent
                # manually)
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                # One record in recipient table (for recipient)
                'recipients': [
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'emails': [TEST_RECIPIENT_EMAIL],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
            },
            id='first_sign_by_recipient',
        ),
        # Case when an owner is viewer, not signer
        pytest.param(
            {
                'recipients': {
                    'is_ordered': False,
                    'recipients': [
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'viewer',
                        },
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU,
                            'emails': [TEST_RECIPIENT_EMAIL],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
            },
            {
                'is_internal': False,
                'is_multilateral': False,
                'expected_recipient_signatures': 1,
                'expected_owner_signatures': 0,
                'first_sign_by': FirstSignBy.recipient,
                'signers': [],
                'flows': [],
                # For bilateral document, we should have 1 access record for an owner; access for
                # recipient will be opened when an owner sends a document (it should be sent
                # manually)
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                # One record in recipient table (for recipient)
                'recipients': [
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'emails': [TEST_RECIPIENT_EMAIL],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
            },
            id='owner_is_viewer',
        ),
        # Case when a recipient is viewer, not signer
        pytest.param(
            {
                'recipients': {
                    'is_ordered': False,
                    'recipients': [
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU,
                            'emails': [TEST_RECIPIENT_EMAIL],
                            'is_email_hidden': False,
                            'role': 'viewer',
                        },
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
            },
            {
                'is_internal': False,
                'is_multilateral': False,
                'expected_recipient_signatures': 0,
                'expected_owner_signatures': 1,
                'first_sign_by': FirstSignBy.owner,
                'signers': [],
                'flows': [],
                # For bilateral document, we should have 1 access record for an owner; access for
                # recipient will be opened when an owner sends a document (it should be sent
                # manually)
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                # One record in recipient table (for recipient)
                'recipients': [
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'emails': [TEST_RECIPIENT_EMAIL],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
            },
            id='recipient_is_viewer',
        ),
        # Case for a bilateral document with 2 signer parameters (signing by coworkers)
        pytest.param(
            {
                'recipients': {
                    'is_ordered': False,
                    'recipients': [
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU,
                            'emails': [TEST_RECIPIENT_EMAIL],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
                'signer_parameters': {
                    'signers': [
                        {
                            'value': ROLE_ID_3,
                            'signer_type': SignersInfoCtx.EntityType.role,
                        },
                        {
                            'value': USER_EMAIL_4,
                            'signer_type': SignersInfoCtx.EntityType.email,
                        },
                    ]
                },
                'parallel_signing': False,
            },
            {
                'is_internal': False,
                'is_multilateral': False,
                'expected_recipient_signatures': 1,
                'expected_owner_signatures': 2,  # len(signers)
                'first_sign_by': FirstSignBy.owner,
                'signers': [
                    {
                        'role_id': ROLE_ID_3,
                        'order': 1,
                    },
                    {
                        'role_id': ROLE_ID_4,
                        'order': 2,
                    },
                ],
                'flows': [],
                # Owner + 2 signers
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_3,
                        'sources': AccessSource.default,
                    },
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_4,
                        'sources': AccessSource.default,
                    },
                ],
                'recipients': [
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'emails': [TEST_RECIPIENT_EMAIL],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
            },
            id='bilateral_document_with_signers',
        ),
        # Case for a multilateral document with 2 companies
        pytest.param(
            {
                'recipients': {
                    'is_ordered': True,
                    'recipients': [
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU,
                            'emails': [TEST_RECIPIENT_EMAIL],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
            },
            {
                'is_internal': False,
                'is_multilateral': True,
                'expected_recipient_signatures': 1,
                'expected_owner_signatures': 1,
                'first_sign_by': FirstSignBy.owner,
                'signers': [],
                'flows': [
                    {
                        'order': 0,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'meta': {},
                    },
                    {
                        'order': 1,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'meta': {},
                    },
                    {
                        'order': 2,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'meta': {},
                    },
                ],
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                # Each recipient should have a record in the recipient table
                'recipients': [
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'emails': [TEST_RECIPIENT_EMAIL],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                ],
            },
            id='multilateral_document',
        ),
        # Multilateral document with 3 companies
        pytest.param(
            {
                'recipients': {
                    'is_ordered': True,
                    'recipients': [
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU,
                            'emails': [TEST_RECIPIENT_EMAIL],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU_2,
                            'emails': [TEST_RECIPIENT_EMAIL_2],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                }
            },
            {
                'is_internal': False,
                'is_multilateral': True,
                'expected_recipient_signatures': 1,
                'expected_owner_signatures': 1,
                'first_sign_by': FirstSignBy.owner,
                'signers': [],
                'flows': [
                    {
                        'order': 0,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'meta': {},
                    },
                    {
                        'order': 1,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'meta': {},
                    },
                    {
                        'order': 2,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': TEST_RECIPIENT_EDRPOU_2,
                        'meta': {},
                    },
                ],
                # For multilateral document, we should have 1 access records for an owner
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                # Each recipient should have a record in the recipient table
                'recipients': [
                    {
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'emails': [TEST_RECIPIENT_EMAIL],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU_2,
                        'emails': [TEST_RECIPIENT_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                ],
            },
            id='multilateral_document_3_companies',
        ),
        # Parallel multilateral document with 3 companies
        pytest.param(
            {
                'recipients': {
                    'is_ordered': False,
                    'recipients': [
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU,
                            'emails': [TEST_RECIPIENT_EMAIL],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU_2,
                            'emails': [TEST_RECIPIENT_EMAIL_2],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
            },
            {
                'is_internal': False,
                'is_multilateral': True,
                'expected_recipient_signatures': 1,
                'expected_owner_signatures': 1,
                'first_sign_by': FirstSignBy.owner,
                'signers': [],
                'flows': [
                    {
                        'order': None,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'meta': {},
                    },
                    {
                        'order': None,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'meta': {},
                    },
                    {
                        'order': None,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': TEST_RECIPIENT_EDRPOU_2,
                        'meta': {},
                    },
                ],
                # Such as we don't send a document to recipients automatically, we should have
                # access only for an owner
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                # Each recipient should have a record in the recipient table
                'recipients': [
                    {
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'emails': [TEST_RECIPIENT_EMAIL],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU_2,
                        'emails': [TEST_RECIPIENT_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                ],
            },
            id='parallel_multilateral_document',
        ),
        # Multilateral document (2 companies) with document signers (signing by coworkers)
        pytest.param(
            {
                'recipients': {
                    'is_ordered': True,
                    'recipients': [
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU,
                            'emails': [TEST_RECIPIENT_EMAIL],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
                'signer_parameters': {
                    'signers': [
                        {
                            'value': ROLE_ID_3,
                            'signer_type': SignersInfoCtx.EntityType.role,
                        },
                        {
                            'value': USER_EMAIL_4,
                            'signer_type': SignersInfoCtx.EntityType.email,
                        },
                    ]
                },
                'parallel_signing': False,
            },
            {
                'is_internal': False,
                'is_multilateral': True,
                'expected_recipient_signatures': 1,
                'expected_owner_signatures': 1,
                'first_sign_by': FirstSignBy.owner,
                'signers': [
                    {
                        'role_id': ROLE_ID_3,
                        'order': 1,
                    },
                    {
                        'role_id': ROLE_ID_4,
                        'order': 2,
                    },
                ],
                'flows': [
                    {
                        'order': 0,
                        'signatures_count': 2,
                        'pending_signatures_count': 2,
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'meta': {},
                    },
                    {
                        'order': 1,
                        # TODO: investigate why each flow has 2 signatures in the case of a
                        #  multilateral document with document signers. Seems like current
                        #  behavior is incorrect and only first flow for company should have
                        #  signatures_count equal to document signers count.
                        #  See: app.uploads.utils._prepare_file_flows
                        'signatures_count': 2,
                        'pending_signatures_count': 2,
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'meta': {},
                    },
                    {
                        'order': 2,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'meta': {},
                    },
                ],
                # 1 access records for an owner + 2 for signers
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_3,
                        'sources': AccessSource.default,
                    },
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_4,
                        'sources': AccessSource.default,
                    },
                ],
                # Each recipient should have a record in the recipient table
                'recipients': [
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'emails': [TEST_RECIPIENT_EMAIL],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                ],
            },
            id='multilateral_document_with_signers',
        ),
        pytest.param(
            {
                'recipients': {
                    'is_ordered': True,
                    'recipients': [
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'viewer',
                        },
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU,
                            'emails': [TEST_RECIPIENT_EMAIL],
                            'is_email_hidden': False,
                            'role': 'viewer',
                        },
                        # First signer in the list. All recipients above should get access to the
                        # document
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU_2,
                            'emails': [TEST_RECIPIENT_EMAIL_2],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': TEST_RECIPIENT_EDRPOU_3,
                            'emails': [TEST_RECIPIENT_EMAIL_3],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
            },
            {
                'is_internal': False,
                'is_multilateral': True,
                'expected_recipient_signatures': 1,
                'expected_owner_signatures': 1,
                'first_sign_by': FirstSignBy.owner,
                'signers': [],
                'flows': [
                    {
                        'order': 0,
                        'signatures_count': 0,
                        'pending_signatures_count': 0,
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'meta': {},
                    },
                    {
                        'order': 1,
                        'signatures_count': 0,
                        'pending_signatures_count': 0,
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'meta': {},
                    },
                    {
                        'order': 2,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': TEST_RECIPIENT_EDRPOU_2,
                        'meta': {},
                    },
                    {
                        'order': 3,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': TEST_RECIPIENT_EDRPOU_3,
                        'meta': {},
                    },
                ],
                'listing': [
                    {
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                # Each recipient should have a record in the recipient table
                'recipients': [
                    {
                        'edrpou': TEST_COMPANY_EDRPOU,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU,
                        'emails': [TEST_RECIPIENT_EMAIL],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU_2,
                        'emails': [TEST_RECIPIENT_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': TEST_RECIPIENT_EDRPOU_3,
                        'emails': [TEST_RECIPIENT_EMAIL_3],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                ],
            },
            id='multilateral_document_owner_is_viewer_and_first',
        ),
    ],
)
async def test_upload_with_recipients_parameters(
    aiohttp_client,
    params: dict[str, Any],
    expected: dict[str, Any],
):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=USER_EMAIL_1,
        role_id=ROLE_ID_1,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    # recipient
    await prepare_user_data(
        app=app,
        email=USER_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    # coworkers
    await prepare_user_data(
        app=app,
        email=USER_EMAIL_3,
        role_id=ROLE_ID_3,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    await prepare_user_data(
        app=app,
        email=USER_EMAIL_4,
        role_id=ROLE_ID_4,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )

    data = FormData()
    data.add_field('file', b'content', filename='hello1.txt')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        path=UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.CREATED, response_json

    async with services.db.acquire() as conn:
        document = await select_one(conn, document_table.select())

        signers = await get_document_signers(document_id=document.id)
        recipients = await get_document_recipients(document_id=document.id)
        flows = await get_flows(document_id=document.id)
        listing = await select_listings(conn, document_ids=[document.id])

    assert document.is_internal == expected['is_internal']
    assert document.is_multilateral == expected['is_multilateral']
    assert document.expected_recipient_signatures == expected['expected_recipient_signatures']
    assert document.expected_owner_signatures == expected['expected_owner_signatures']
    assert document.first_sign_by == expected['first_sign_by']

    assert len(signers) == len(expected['signers'])
    signers_result = [
        {
            'role_id': s.role_id,
            'order': s.order,
        }
        for s in signers
    ]
    signers_result = sorted(signers_result, key=lambda x: (x['order'], x['role_id']))
    signers_expected = sorted(expected['signers'], key=lambda x: (x['order'], x['role_id']))
    assert signers_result == signers_expected

    assert len(flows) == len(expected['flows'])
    flows_result = [
        {
            'order': f.order,
            'signatures_count': f.signatures_count,
            'pending_signatures_count': f.pending_signatures_count,
            'edrpou': f.edrpou,
            'meta': f.meta.to_db(),
        }
        for f in flows
    ]
    flows_result = sorted(
        flows_result,
        key=lambda x: (
            x['order'],
            x['edrpou'],
            x['meta'],
            x['signatures_count'],
            x['pending_signatures_count'],
        ),
    )
    flows_expected = sorted(
        expected['flows'],
        key=lambda x: (
            x['order'],
            x['edrpou'],
            x['meta'],
            x['signatures_count'],
            x['pending_signatures_count'],
        ),
    )
    assert flows_result == flows_expected

    assert len(listing) == len(expected['listing'])
    listing_result = [
        {
            'access_edrpou': item.access_edrpou,
            'role_id': item.role_id,
            'sources': AccessSource.default,
        }
        for item in listing
    ]
    listing_result = sorted(listing_result, key=lambda x: (x['access_edrpou'], x['role_id'] or ''))
    listing_expected = sorted(
        expected['listing'],
        key=lambda x: (x['access_edrpou'], x['role_id'] or ''),
    )
    assert listing_result == listing_expected

    # One record in recipient table (for recipient)
    assert len(recipients) == len(expected['recipients'])
    recipients_result = [
        {
            'edrpou': r.edrpou,
            'emails': r.emails,
            'is_email_hidden': r.is_emails_hidden,
            'from_flow': r.from_flow,
            'external_email': r.external_meta,
        }
        for r in recipients
    ]
    recipients_result = sorted(recipients_result, key=lambda x: (x['edrpou'], x['emails']))
    recipients_expected = sorted(expected['recipients'], key=lambda x: (x['edrpou'], x['emails']))
    assert recipients_result == recipients_expected


async def test_upload_multilateral_with_required_fields(aiohttp_client):
    """
    Check that it's not possible to upload a multilateral document when required fields are missing.
    """
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=USER_EMAIL_1,
        role_id=ROLE_ID_1,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    recipient1 = await prepare_user_data(
        app=app,
        email=TEST_RECIPIENT_EMAIL,
        role_id=ROLE_ID_2,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )
    recipient2 = await prepare_user_data(
        app=app,
        email=TEST_RECIPIENT_EMAIL_2,
        role_id=ROLE_ID_3,
        company_edrpou=TEST_RECIPIENT_EDRPOU_2,
    )

    async with services.db.acquire() as conn:
        await prepare_document_required_fields(
            conn=conn,
            company_id=user.company_id,
            document_category=DocumentCategoryFields.other,
            is_name_required=False,
            is_type_required=False,
            is_number_required=False,
            is_date_required=False,
            is_amount_required=True,
        )
        await prepare_document_required_fields(
            conn=conn,
            company_id=recipient1.company_id,
            document_category=DocumentCategoryFields.other,
            is_name_required=False,
            is_type_required=False,
            is_number_required=False,
            is_date_required=True,
            is_amount_required=False,
        )
        await prepare_document_required_fields(
            conn=conn,
            company_id=recipient2.company_id,
            document_category=DocumentCategoryFields.other,
            is_name_required=False,
            is_type_required=False,
            is_number_required=True,
            is_date_required=False,
            is_amount_required=False,
        )

    data = FormData()
    data.add_field('file', b'content', filename='hello1.txt')
    data.add_field(
        'params',
        value=to_json(
            {
                'recipients': {
                    'is_ordered': True,
                    'recipients': [
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': recipient1.company_edrpou,
                            'emails': [recipient1.email],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': recipient2.company_edrpou,
                            'emails': [recipient2.email],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                }
            }
        ),
        content_type='application/json',
    )

    response = await client.post(
        path=UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )
    response_json = await response.json()
    # We aren't sending a document to recipients automatically, so no errors should be raised
    assert response.status == HTTPStatus.CREATED, response_json

    documents_ids = [doc['id'] for doc in response_json['documents']]
    assert len(documents_ids) == 1
    flows = await get_flows(document_id=documents_ids[0])
    assert len(flows) == 3
    for flow in flows:
        assert flow.pending_signatures_count == 1
        assert flow.signatures_count == 1
        assert flow.is_sent is False
        assert flow.meta.send_jobs_executed is False


@pytest.mark.parametrize(
    'params, expected_emails',
    [
        pytest.param(
            {
                'roles': [ROLE_ID_1, ROLE_ID_2, ROLE_ID_3],
                'is_ordered': True,
            },
            {},
            id='ordered_first_uploader',
        ),
        pytest.param(
            {
                'roles': [ROLE_ID_3, ROLE_ID_2, ROLE_ID_1],
                'is_ordered': True,
            },
            {
                USER_EMAIL_3: ['documents_for_signer'],
            },
            id='ordered_second_uploader',
        ),
        pytest.param(
            {
                'roles': [ROLE_ID_1, ROLE_ID_2, ROLE_ID_3],
                'is_ordered': False,
            },
            {
                # The owner shouldn't receive any notifications, because he is doing this action
                USER_EMAIL_2: ['documents_for_signer'],
                USER_EMAIL_3: {'documents_for_signer'},
            },
            id='parallel_first_uploader',
        ),
    ],
)
async def test_upload_internal_versioned_with_signers_notifications(
    aiohttp_client,
    mailbox,
    params: DataDict,
    expected_emails: DataDict,
):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=USER_EMAIL_1,
        role_id=ROLE_ID_1,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )

    await prepare_user_data(
        app=app,
        email=USER_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    await prepare_user_data(
        app=app,
        email=USER_EMAIL_3,
        role_id=ROLE_ID_3,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )

    data = FormData()
    data.add_field('file', b'content', filename='hello1.txt')
    data.add_field(
        'params',
        value=to_json(
            {
                # Internal document
                'recipients': {
                    'is_ordered': False,
                    'recipients': [
                        {
                            'edrpou': TEST_COMPANY_EDRPOU,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
                # Signers
                'signer_parameters': {
                    'signers': [
                        {'value': role_id, 'signer_type': SignersInfoCtx.EntityType.role}
                        for role_id in params['roles']
                    ]
                },
                'parallel_signing': params['is_ordered'] is False,
            }
        ),
        content_type='application/json',
    )

    response = await client.post(
        path=UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.CREATED, response_json

    assert len(mailbox.emails_set) == len(expected_emails)
    assert mailbox.emails_set == set(expected_emails)
    for email, expected_templates in expected_emails.items():
        messages = mailbox.by_email(email)
        messages_templates = {msg['X-Template'] for msg in messages}
        assert len(messages_templates) == len(expected_templates), email
        assert set(messages_templates) == set(expected_templates), email
