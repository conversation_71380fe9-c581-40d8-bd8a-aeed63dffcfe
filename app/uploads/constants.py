#: Bytes in megabyte
MB = 1_000_000

#: Number of files to uploaded to S3 in one chunk
S3_CHUNK_SIZE = 25


SIGNATURE_FILE_EXTENSION = ('.p7s',)

# always parse MetaData for documents with this recipient_edrpous
PARSE_META_BY_RECIPIENT_EDRPOU = {
    '34185974',  # Electrolux
    '37183000',  # Electrolux
    '31316718',  # NP
}

# Key for redis queue for monitoring an uploading process of documents. This key contains
# ID of documents that are not indexed in Elasticsearch yet. We don't use it as a indexation
# queue, just for monitoring purposes.
WEB_UPLOADING_KEY = 'web_uploading'
