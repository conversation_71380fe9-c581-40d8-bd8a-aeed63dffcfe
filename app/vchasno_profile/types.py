from dataclasses import dataclass
from datetime import datetime
from typing import Self, TypedDict

import pydantic

from app.auth.types import BaseUser
from app.lib.database import DBRow
from app.lib.types import DataDict
from app.vchasno_profile.enums import VchasnoProfileSyncEntityType


class VchasnoUser(pydantic.BaseModel):
    """
    Represents a model of a user in the Vchasno ecosystem in form how other services see it.

    This model have some overlapping fields with `ConciergeProfile` model, so check it before
    making changes here
    """

    id: str
    email: str | None

    # The old "phone" field has been replaced by "contact_phone", and "auth_phone" has been added
    # for authentication purposes. In the EDO database, we still use the "phone" field, but in
    # communication with other services, "auth_phone" and "contact_phone" should be used.
    phone: str | None  # deprecated
    auth_phone: str | None
    contact_phone: str | None

    first_name: str | None
    second_name: str | None
    last_name: str | None
    date_created: datetime
    date_updated: datetime
    date_deleted: datetime | None

    @classmethod
    def from_user(cls, user: BaseUser) -> Self:
        """
        Build a VchasnoUser from BaseUser model.
        """
        return cls(
            id=user.id,
            email=user.email,
            first_name=user.first_name,
            second_name=user.second_name,
            last_name=user.last_name,
            # Auth phone in users table are created specifically for authentication
            auth_phone=user.auth_phone,
            # Phones in EDO users are primarily used for contacting them by support and sales
            # teams. They also are used for 2FA authentication, but we need to move this logic
            # to "auth_phone" field in the future.
            phone=user.phone,  # deprecated
            contact_phone=user.phone,
            date_created=user.date_created,
            date_updated=user.date_updated,
            date_deleted=user.date_deleted,
        )


class CreateVchasnoUserResponse(pydantic.BaseModel):
    is_created: bool
    user: VchasnoUser

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json')

    @classmethod
    def build(cls, *, is_created: bool, user: BaseUser) -> Self:
        return cls(
            is_created=is_created,
            user=VchasnoUser.from_user(user),
        )


@dataclass
class VchasnoProfileSync:
    id: int
    entity_type: VchasnoProfileSyncEntityType
    entity_id: str
    date_created: datetime

    @property
    def is_user_entity(self) -> bool:
        return self.entity_type == VchasnoProfileSyncEntityType.user

    @classmethod
    def from_row(cls, row: DBRow) -> Self:
        return cls(
            id=row.id,
            entity_type=row.entity_type,
            entity_id=row.entity_id,
            date_created=row.date_created,
        )


class VchasnoProfileSyncEntity(TypedDict):
    id: str
    type: VchasnoProfileSyncEntityType
