from __future__ import annotations

from contextvars import ContextVar
from typing import TYPE_CHECKING

from aiohttp import ClientSession, web
from featureflags_client.http.client import FeatureFlagsClient
from featureflags_client.http.managers.base import BaseManager
from hiku.engine import Engine as HikuEngine
from vchasno_crm import VchasnoCR<PERSON>

from app.es.types import ES
from app.lib.hrs.client import HRSClient

# # To avoid circular dependencies, try not to add import from the "app" module
# here. If you need some class for type hints, just import it under the flag
# TYPE_CHECKING, like those lines below.
if TYPE_CHECKING:
    from conciergelib.aiohttp.client import ConciergeBackendClient
    from types_aiobotocore_bedrock_runtime.client import BedrockRuntimeClient
    from types_aiobotocore_s3.client import S3Client
    from types_aiobotocore_sesv2.client import SESV2Client
    from types_aiobotocore_textract.client import TextractClient

    from app.analytics.google.client import GoogleAnalyticsClient
    from app.config.schemas import Config
    from app.lib.brokers import <PERSON>f<PERSON><PERSON><PERSON>
    from app.lib.database import AsyncDBEngine
    from app.lib.sender.client import <PERSON>vo<PERSON>ender
    from app.lib.sign_context import SignContext
    from app.lib.types import Redis
    from app.mobile.fcm.client import FCMClient


class Services:
    def __init__(self) -> None:
        self.ctx_app: ContextVar[web.Application] = ContextVar('app')
        self.ctx_config: ContextVar[Config] = ContextVar('config')
        self.ctx_db: ContextVar[AsyncDBEngine] = ContextVar('db')
        self.ctx_events_db: ContextVar[AsyncDBEngine] = ContextVar('events_db')
        self.ctx_es: ContextVar[ES] = ContextVar('es')
        self.ctx_db_readonly: ContextVar[AsyncDBEngine] = ContextVar('db_readonly')
        self.ctx_fcm_client: ContextVar[FCMClient] = ContextVar('fcm_client')
        self.ctx_ff_client: ContextVar[FeatureFlagsClient] = ContextVar('ff_client')
        self.ctx_ff_manager: ContextVar[BaseManager] = ContextVar('ff_manager')
        self.ctx_hiku_engine: ContextVar[HikuEngine] = ContextVar('hiku_engine')
        self.ctx_redis: ContextVar[Redis] = ContextVar('redis')
        self.ctx_http_client: ContextVar[ClientSession] = ContextVar('http_client')
        self.ctx_kafka: ContextVar[KafkaClient] = ContextVar('kafka')
        self.ctx_sign_context: ContextVar[SignContext] = ContextVar('sign_context')
        self.ctx_crm_client: ContextVar[VchasnoCRM | None] = ContextVar('crm_client')
        self.ctx_evo_sender: ContextVar[EvoSender] = ContextVar('evo_sender')
        self.ctx_s3_client: ContextVar[S3Client] = ContextVar('s3')
        self.ctx_bedrock_client: ContextVar[BedrockRuntimeClient | None] = ContextVar(
            'bedrock_client'
        )
        self.ctx_textract_client: ContextVar[TextractClient | None] = ContextVar('textract_client')
        self.ctx_google_analytics_client: ContextVar[GoogleAnalyticsClient | None] = ContextVar(
            'google_analytics_client'
        )
        self.ctx_concierge_client: ContextVar[ConciergeBackendClient] = ContextVar(
            'concierge_client'
        )
        self.ctx_aws_ses_client: ContextVar[SESV2Client | None] = ContextVar('aws_ses_client')
        self.ctx_hrs_client: ContextVar[HRSClient] = ContextVar('hrs_client')

    @property
    def app(self) -> web.Application:
        return self.ctx_app.get()

    @property
    def config(self) -> Config:
        return self.ctx_config.get()

    @property
    def db(self) -> AsyncDBEngine:
        return self.ctx_db.get()

    @property
    def db_readonly(self) -> AsyncDBEngine:
        return self.ctx_db_readonly.get()

    @property
    def events_db(self) -> AsyncDBEngine:
        return self.ctx_events_db.get()

    @property
    def es(self) -> ES:
        return self.ctx_es.get()

    @property
    def fcm_client(self) -> FCMClient:
        return self.ctx_fcm_client.get()

    @property
    def ff_client(self) -> FeatureFlagsClient:
        return self.ctx_ff_client.get()

    @property
    def ff_manager(self) -> BaseManager:
        return self.ctx_ff_manager.get()

    @property
    def hiku_engine(self) -> HikuEngine:
        return self.ctx_hiku_engine.get()

    @property
    def redis(self) -> Redis:
        return self.ctx_redis.get()

    @property
    def http_client(self) -> ClientSession:
        return self.ctx_http_client.get()

    @property
    def crm_client(self) -> VchasnoCRM | None:
        return self.ctx_crm_client.get()

    @property
    def kafka(self) -> KafkaClient:
        return self.ctx_kafka.get()

    @property
    def sign_context(self) -> SignContext:
        return self.ctx_sign_context.get()

    @property
    def evo_sender(self) -> EvoSender:
        return self.ctx_evo_sender.get()

    @property
    def s3_client(self) -> S3Client:
        return self.ctx_s3_client.get()

    @property
    def bedrock_client(self) -> BedrockRuntimeClient | None:
        return self.ctx_bedrock_client.get()

    @property
    def textract_client(self) -> TextractClient | None:
        return self.ctx_textract_client.get()

    @property
    def google_analytics_client(self) -> GoogleAnalyticsClient | None:
        return self.ctx_google_analytics_client.get()

    @property
    def concierge_client(self) -> ConciergeBackendClient:
        return self.ctx_concierge_client.get()

    @property
    def aws_ses_client(self) -> SESV2Client | None:
        return self.ctx_aws_ses_client.get()

    @property
    def hrs_client(self) -> HRSClient:
        return self.ctx_hrs_client.get()


services = Services()


def setup_services(app: web.Application) -> None:
    """
    Setup services for cron & worker

    TODO: find a way to avoid settings services in the app object
    """
    from app.config.utils import get_config

    services.ctx_config.set(get_config(app))
    services.ctx_app.set(app)
    services.ctx_db.set(app['db'])
    services.ctx_db_readonly.set(app['db_readonly'])
    services.ctx_events_db.set(app['events_db'])
    services.ctx_es.set(app['es'])
    services.ctx_fcm_client.set(app['fcm_client'])
    services.ctx_ff_client.set(app['ff_client'])
    services.ctx_ff_manager.set(app['ff_manager'])
    services.ctx_hiku_engine.set(app['hiku_engine'])
    services.ctx_redis.set(app['redis'])
    services.ctx_http_client.set(app['client_session'])
    services.ctx_kafka.set(app['kafka'])
    services.ctx_sign_context.set(app['sign_context'])
    services.ctx_crm_client.set(app['crm_client'])
    services.ctx_evo_sender.set(app['evo_sender'])
    services.ctx_s3_client.set(app['s3_client'])
    services.ctx_bedrock_client.set(app['bedrock_client'])
    services.ctx_textract_client.set(app['textract_client'])
    services.ctx_concierge_client.set(app['concierge_client'])
    services.ctx_aws_ses_client.set(app['aws_ses_client'])
    services.ctx_hrs_client.set(app['hrs_client'])
