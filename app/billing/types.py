from __future__ import annotations

import datetime
import json
from abc import ABC, abstractmethod
from collections.abc import Sequence
from dataclasses import dataclass
from decimal import Decimal
from typing import (
    Annotated,
    Literal,
    NamedTuple,
    NotRequired,
    Protocol,
    Self,
    TypedDict,
    assert_never,
)

import pydantic

from app.analytics.types import GoogleAnalyticsMeta
from app.billing.constants import INTEGRATION_PRICE_PER_DOCUMENT
from app.billing.enums import (
    AccountRate,
    AccountType,
    BankEnum,
    BillingAccountSource,
    BillPaymentSource,
    BillPaymentStatus,
    BillPaymentType,
    BillServicesType,
    BillServiceType,
    BillSource,
    BillStatus,
    BonusType,
    CompanyLimit,
    CompanyPermission,
    CompanyRateStatus,
    FakeBillRate,
    RateExtensionStatus,
    RateExtensionType,
    TransactionType,
)
from app.lib import money
from app.lib.database import DBRow
from app.lib.datetime_utils import to_local_datetime
from app.lib.helpers import not_none, to_json
from app.lib.types import DataDict

DATE_WHEN_START_PRO_DOCUMENTS_LIMITS_ENABLED = to_local_datetime(datetime.datetime(2024, 9, 4))


def _validate_unit_price(price: Decimal) -> Decimal:
    """
    Check that unit price doesn't have a fractional part
    """

    if price % 1:
        raise ValueError(
            f'Unit price should be integer as Decimal. Got: {price}. You probably forgot '
            'to convert units to subunits or round subunits after division. Use '
            'module app.lib.money to perform proper conversions.'
        )

    return price.to_integral()


# ціна в копійках
BillPrice = Annotated[Decimal, pydantic.AfterValidator(_validate_unit_price)]


class BankClient(Protocol):
    async def fetch_transactions(
        self,
        start_date: datetime.datetime | None = None,
        end_date: datetime.datetime | None = None,
    ) -> Sequence[BankTransaction]: ...


class Account(NamedTuple):
    id: str

    company_id: str
    rate: AccountRate
    type: AccountType

    amount: int
    amount_left: int
    units: int
    units_left: int

    date_created: datetime.datetime

    employee_amount: int | None = None
    initiator_id: str | None = None

    source: BillingAccountSource | None = None
    date_expired: datetime.datetime | None = None
    date_deleted: datetime.datetime | None = None

    @property
    def documents_left_percentage(self) -> float | None:
        left_sum = self.units_left + self.amount_left
        all_sum = self.units + self.amount

        if not all_sum:
            return None

        return left_sum * 100 / all_sum


@dataclass(kw_only=True)
class CompanyRate:
    id_: str
    company_id: str
    company_edrpou: str

    rate: AccountRate
    status: CompanyRateStatus

    start_date: datetime.datetime
    end_date: datetime.datetime | None

    amount: int

    bill_id: str | None = None
    units: int | None = None
    employee_amount: int | None = None
    initiator_id: str | None = None
    price_per_user: int | None = None  # в копійках

    source: BillingAccountSource | None = None
    date_created: datetime.datetime | None = None
    date_expired: datetime.datetime | None = None

    @property
    def is_active(self) -> bool:
        return self.status == CompanyRateStatus.active

    @staticmethod
    def from_db(row: DBRow) -> CompanyRate:
        """
        CompanyRate data is stored in two tables: "billing_accounts" and "companies"

        See "COMPANY_RATE_COLUMNS" and "company_rate_join" for some details
        """
        return CompanyRate(
            id_=row['id'],
            company_id=row['company_id'],
            company_edrpou=row['company_edrpou'],
            initiator_id=row['initiator_id'],
            rate=row['rate'],
            status=row['status'],
            bill_id=row['bill_id'],
            units=row['units'],
            source=row['source'],
            start_date=row['activation_date'],
            end_date=row['date_expired'],
            amount=row['amount'],
            employee_amount=row['employee_amount'],
            price_per_user=row['price_per_user'],
            date_created=row['date_created'],
            date_expired=row['date_expired'],
        )

    def to_db(self) -> DataDict:
        data = {
            'id': self.id_,
            'company_id': self.company_id,
            'initiator_id': self.initiator_id,
            'rate': self.rate,
            'status': self.status,
            'bill_id': self.bill_id,
            'source': self.source,
            'activation_date': self.start_date,
            'date_expired': self.end_date,
            'amount': self.amount,
            'type': AccountType.client_rate,
            'employee_amount': self.employee_amount,
            'price_per_user': self.price_per_user,
        }
        if self.date_created is not None:
            data['date_created'] = self.date_created
        return data

    def to_serializable(self) -> DataDict:
        return json.loads(to_json(self.to_db()))

    @property
    def has_unlimited_documents(self) -> bool:
        if self.rate.is_free:
            return False

        # In the past, only free plans had a limited document count, but PRO and START plans
        # created after 4 September 2024 should also have a limited document count. To implement
        # that, we are using hardcoded date to check if the plan was created before or after that
        # date. This is not the cleanest solution, but it was the fastest way to implement this
        # feature, and there is not much value in refactoring it now.
        #
        # If you are reading this comment after September 4, 2025, the last PRO and START plans
        # with unlimited documents have probably expired, and this condition could be removed.
        #
        # Fore more details: https://tabula-rasa.atlassian.net/browse/DOC-6529
        rate_date = self.date_created or self.start_date
        if self.rate.is_start:
            return rate_date < DATE_WHEN_START_PRO_DOCUMENTS_LIMITS_ENABLED

        if self.rate.is_pro:
            return rate_date < DATE_WHEN_START_PRO_DOCUMENTS_LIMITS_ENABLED

        # Other rates, like ultimate has unlimited documents count
        return True


@dataclass
class UserAddBillOptions:
    email: str
    edrpou: str
    name: str

    role_id: str | None = None
    user_id: str | None = None
    company_id: str | None = None


@dataclass(kw_only=True)
class AddBillServiceRateOptions:
    type: Literal[BillServiceType.rate] = BillServiceType.rate
    units: int
    unit_price: BillPrice  # в копійках

    rate: AccountRate
    date_from: datetime.datetime | None

    # How many employees included in the rate?
    # Used in ultimate rate, because there is no standard number of employees for this rate.
    limits_employees_count: int | None
    price_per_user: Decimal | None  # в копійках

    def to_db(self) -> DataDict:
        return BillServiceRate(
            type=self.type,
            units=self.units,
            unit_price=self.unit_price,
            rate=self.rate,
            date_from=self.date_from,
            limits_employees_count=self.limits_employees_count,
            price_per_user=self.price_per_user,
        ).to_db()


@dataclass(kw_only=True)
class AddBillServiceExtensionOptions:
    type: Literal[BillServiceType.extension] = BillServiceType.extension
    units: int
    unit_price: BillPrice  # в копійках, ex "price_per_user"

    extension: RateExtensionType
    date_from: datetime.date | None

    # don't need to store it in DB because we have a connection between bill and extension
    company_rate: CompanyRate

    def to_db(self) -> DataDict:
        return BillServiceExtension(
            type=self.type,
            units=self.units,
            unit_price=self.unit_price,
            extension=self.extension,
            date_from=self.date_from,
        ).to_db()


@dataclass(kw_only=True)
class AddBillServiceUnitsOptions:
    type: Literal[BillServiceType.units] = BillServiceType.units
    units: int
    unit_price: BillPrice  # в копійках

    def to_db(self) -> DataDict:
        return BillServiceUnits(
            type=self.type,
            units=self.units,
            unit_price=self.unit_price,
        ).to_db()


# You can use `type` field as a discriminator for tagged union
type AddBillServiceOptions = (
    AddBillServiceRateOptions | AddBillServiceExtensionOptions | AddBillServiceUnitsOptions
)


@dataclass(kw_only=True)
class AddBillOptions:
    """
    Options to create a new bill.

    NOTE: do not add default values to fields to catch early mistakes with mypy.
    """

    name: str
    edrpou: str

    email: str
    company_id: str | None
    role_id: str | None
    user_id: str | None

    agreement: str | None
    contract_basis: str | None
    payment_purpose: str | None

    services_type: BillServicesType
    services: list[AddBillServiceOptions]

    # Custom total price for the whole bill.
    # It may be different from the sum of all services prices
    custom_price: Decimal | None  # в гривнях

    status: BillStatus | None

    source: BillSource | None
    is_card_payment: bool

    def to_db(self) -> DataDict:
        legacy_columns = self._services_to_legacy_columns()
        return {
            'name': self.name,
            'edrpou': self.edrpou,
            'is_legal': True,
            'email': self.email,
            'role_id': self.role_id,
            'company_id': self.company_id,
            'user_id': self.user_id,
            'agreement': self.agreement,
            'payment_purpose': self.payment_purpose,
            'services_type': self.services_type,
            'custom_price': self.custom_price,
            'status_id': s.value if (s := self.status) else None,
            'source': self.source,
            'contract_basis': self.contract_basis,
            'services': [service.to_db() for service in self.services],
            **legacy_columns,
        }

    def _services_to_legacy_columns(self) -> DataDict:
        default_values: DataDict = {
            'count_documents': None,
            'max_employees_count': None,
        }
        data: DataDict
        if self.services_type == BillServicesType.rate:
            service = self.services[0]
            assert isinstance(service, AddBillServiceRateOptions), 'Invalid service type'

            data = {
                **default_values,
                'rate': service.rate.value,
                'date_from': service.date_from,
            }
            if service.limits_employees_count:
                data['max_employees_count'] = service.limits_employees_count
            return data

        if self.services_type == BillServicesType.documents:
            service = self.services[0]
            assert isinstance(service, AddBillServiceUnitsOptions), 'Invalid service type'
            data = {
                **default_values,
                'count_documents': service.units,
                'price_per_document': money.to_units_string_from_subunits(service.unit_price),
            }
            return data

        if self.services_type == BillServicesType.add_employee:
            service = self.services[0]
            assert isinstance(service, AddBillServiceExtensionOptions), 'Invalid service type'
            return {
                **default_values,
                'rate': FakeBillRate.add_employee.value,
                'max_employees_count': service.units,
                'date_from': service.date_from,
            }
        if self.services_type == BillServicesType.integration_and_documents:
            rate_service = next(s for s in self.services if s.type == BillServiceType.rate)
            units_service = next(s for s in self.services if s.type == BillServiceType.units)
            assert isinstance(rate_service, AddBillServiceRateOptions), 'Invalid service type'
            assert isinstance(units_service, AddBillServiceUnitsOptions), 'Invalid service type'
            return {
                **default_values,
                'rate': FakeBillRate.integration_with_documents.value,
                'date_from': rate_service.date_from,
                'count_documents': units_service.units,
            }
        if self.services_type == BillServicesType.web_and_archive:
            # This is a new type of bill that doesn't use legacy columns
            return {}

        assert_never(self.services_type)


class BillServiceRate(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(frozen=True)

    type: Literal[BillServiceType.rate] = BillServiceType.rate
    units: int
    unit_price: BillPrice  # в копійках

    rate: AccountRate
    date_from: datetime.datetime | None

    # How many employees included in the rate?
    # Used in ultimate rate, because there is no standard number of employees for this rate.
    # TODO: consider to save all permissions and limits that should be activated with this rate
    #  to have a self-contained object with all data needed to activate the rate.
    limits_employees_count: int | None
    price_per_user: Decimal | None = None

    def to_db(self) -> DataDict:
        return self.model_dump(mode='json')

    @pydantic.field_validator('rate', mode='after')
    @classmethod
    def _validate_not_fake_rate(cls, value: AccountRate) -> AccountRate:
        if value in (
            FakeBillRate.integration_with_documents,
            FakeBillRate.add_employee,
        ):
            raise ValueError(f'Fake rate is not allowed here: {value}')
        return value

    @property
    def total_price(self) -> Decimal:
        return self.unit_price * self.units


class BillServiceExtension(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(frozen=True)

    type: Literal[BillServiceType.extension] = BillServiceType.extension
    units: int
    unit_price: BillPrice  # в копійках, ex "price_per_user"

    extension: RateExtensionType
    date_from: datetime.date | None

    def to_db(self) -> DataDict:
        return self.model_dump(mode='json')


class BillServiceUnits(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(frozen=True)

    type: Literal[BillServiceType.units] = BillServiceType.units
    units: int
    unit_price: BillPrice  # в копійках

    def to_db(self) -> DataDict:
        return self.model_dump(mode='json')


type BillService = BillServiceRate | BillServiceExtension | BillServiceUnits


class Bill(pydantic.BaseModel):
    """
    This model is represented bill object in database.

    WARN: Don't add other fields that are not stored in `bills` table.
    """

    # This model is frozen because it is used in a graph that requires hashable objects.
    model_config = pydantic.ConfigDict(frozen=True)

    id: str

    # autoincrement number, used in the bill number
    seqnum: int

    # Company and user who created the bill
    role_id: str | None
    user_id: str | None
    company_id: str | None

    # Basic form fields for all types of bills
    email: str
    edrpou: str
    name: str
    is_legal: bool
    date_from: datetime.datetime | None
    agreement: str | None
    contract_basis: str | None
    payment_purpose: str | None

    # when this field is None, it means that it's not migrated to services yet
    services_type: BillServicesType
    services: tuple[BillService, ...]

    # Custom total price for the whole bill set by the manager from CRM
    custom_price: Decimal | None  # в гривнях

    document_id: str | None
    status_id: int | None
    payment_status: BillPaymentStatus | None
    source: BillSource | None
    date_created: datetime.datetime

    @property
    def id_(self) -> str:
        """
        This method is used to be compatible with the old code.
        """
        return self.id

    @property
    def date_iso(self) -> str:
        return self.date_created.isoformat()

    @property
    def date_str_uk(self) -> str:
        """
        Example: 24.10.2024
        """
        return self.date_created.strftime('%d.%m.%Y')

    @property
    def status(self) -> BillStatus | None:
        if status_id := self.status_id:
            return BillStatus(status_id)
        return None

    @property
    def _deprecated_rate(self) -> str | None:
        """
        Get the rate as in the old columns.
        """
        if self.services_type == BillServicesType.rate:
            return self.single_rate_service.rate.value
        if self.services_type == BillServicesType.add_employee:
            return FakeBillRate.add_employee.value
        if self.services_type == BillServicesType.integration_and_documents:
            return FakeBillRate.integration_with_documents.value
        return None

    @property
    def total_sum(self) -> Decimal:
        """
        Total sum of the bill in subunits
        """
        if self.custom_price is not None:
            return money.to_subunits_decimal_from_units(self.custom_price)

        return self.services_total

    @property
    def _deprecated_count_documents(self) -> int | None:
        """
        Get the count of documents as in the old columns
        """
        if self.services_type in (
            BillServicesType.integration_and_documents,
            BillServicesType.documents,
        ):
            return self.single_units_service.units
        return None

    @property
    def _deprecated_max_employees_count(self) -> int | None:
        """
        Get the max employees to count as in the old columns
        """
        if self.services_type == BillServicesType.rate:
            rate = self.single_rate_service.rate
            if rate.is_ultimate:
                return self.single_rate_service.limits_employees_count
        elif self.services_type == BillServicesType.add_employee:
            return self.single_extension_service.units
        return None

    @property
    def number(self) -> str:
        """
        Example: ВЧ-00000000001
        """
        return f'ВЧ-{str(self.seqnum).zfill(11)}'

    @property
    def payment_type(self) -> BillPaymentType | None:
        if self.services_type in (
            BillServicesType.rate,
            BillServicesType.add_employee,
            BillServicesType.integration_and_documents,
            BillServicesType.web_and_archive,
        ):
            return BillPaymentType.rate_payment
        if self.services_type == BillServicesType.documents:
            return BillPaymentType.document_payment
        return None

    @classmethod
    def _prepare_services_from_db(cls, row: DBRow) -> tuple[BillServicesType, list[BillService]]:
        """
        Convert old bills columns to new services columns.
        """
        from app.billing.utils import get_rate_price

        services_type: BillServicesType | None = row.services_type
        services: list[BillService] | None = row.services

        # Since September 2024, we've introduced "services" and "services_type" columns and all
        # new bills should have it. Such as those new are more precise and flexible, we just use
        # them here, instead of reconstructing them from the old columns: "rate", "count_documents",
        # "max_employees_count", "price_per_document", "custom_price". For old bills, we take the
        # old columns and convert them to the new format to use the same logic for all bills.
        if services_type is not None:
            assert services is not None, 'services should be not None if services_type is not None'
            return services_type, services

        date_from: datetime.datetime | None = row.date_from
        edrpou: str = row.edrpou
        custom_price: Decimal | None = None  # в копійках
        if (p := row.custom_price) is not None:
            custom_price = money.to_subunits_decimal_from_units(p)

        # Old columns
        count_documents: int | None = row.count_documents
        max_employees_count: int | None = row.max_employees_count
        price_per_document: Decimal | None = None  # в копійках
        if (p := row.price_per_document) is not None:
            price_per_document = money.to_subunits_decimal_from_units(p)
        rate: AccountRate | FakeBillRate | None = None
        if raw_rate := row.rate:
            rate = (
                AccountRate(raw_rate)
                if raw_rate in AccountRate.__members__
                else FakeBillRate(raw_rate)
            )

        if rate == FakeBillRate.add_employee:
            services_type = BillServicesType.add_employee
            total_price = not_none(custom_price)
            units = not_none(max_employees_count)
            unit_price = money.to_subunits_decimal(total_price / units)
            services = [
                BillServiceExtension(
                    units=units,
                    unit_price=unit_price,
                    extension=RateExtensionType.employees,
                    date_from=date_from,
                )
            ]
            return services_type, services

        if rate == FakeBillRate.integration_with_documents:
            services_type = BillServicesType.integration_and_documents
            rate = AccountRate.integration

            rate_price = get_rate_price(rate=rate, edrpou=edrpou)  # в гривнях
            unit_rate = money.to_subunits_decimal_from_units(rate_price)  # в копійках
            services = [
                BillServiceRate(
                    units=1,
                    unit_price=unit_rate,
                    rate=rate,
                    date_from=None,
                    limits_employees_count=None,
                    price_per_user=None,
                ),
                BillServiceUnits(
                    units=not_none(count_documents),
                    unit_price=not_none(price_per_document),
                ),
            ]
            return services_type, services

        if rate:
            assert isinstance(rate, AccountRate), 'Expected AccountRate type'
            services_type = BillServicesType.rate

            limits_employees_count: int | None = None
            if rate.is_ultimate:
                limits_employees_count = max_employees_count
                unit_price = not_none(custom_price)  # в копійках
            else:
                rate_price = get_rate_price(rate=rate, edrpou=edrpou)  # в гривнях
                unit_price = money.to_subunits_decimal_from_units(rate_price)  # в копійках

            services = [
                BillServiceRate(
                    units=1,
                    unit_price=unit_price,
                    rate=rate,
                    date_from=None,
                    limits_employees_count=limits_employees_count,
                    price_per_user=None,
                )
            ]
            return services_type, services

        if count_documents:
            services_type = BillServicesType.documents
            if price_per_document is not None:
                unit_price = price_per_document
            else:
                # Some bills from CRM don't have a price per document,
                # so we need to calculate it manually using the custom price.
                if custom_price is not None:
                    unit_price = money.to_subunits_decimal(custom_price / count_documents)
                else:
                    unit_price = money.to_subunits_decimal_from_units(
                        INTEGRATION_PRICE_PER_DOCUMENT
                    )

            services = [
                BillServiceUnits(
                    units=count_documents,
                    unit_price=unit_price,
                )
            ]
            return services_type, services

        raise ValueError(f'Unknown bill type:{row}')

    @classmethod
    def from_row(cls, row: DBRow) -> Self:
        services_type, services = cls._prepare_services_from_db(row)

        return cls(
            id=row['id'],
            seqnum=row['seqnum'],
            role_id=row['role_id'],
            user_id=row['user_id'],
            company_id=row['company_id'],
            is_legal=row['is_legal'],
            email=row['email'],
            edrpou=row['edrpou'],
            name=row['name'],
            date_from=row['date_from'],
            agreement=row['agreement'],
            contract_basis=row['contract_basis'],
            payment_purpose=row['payment_purpose'],
            services_type=services_type,
            services=tuple(services),
            custom_price=row['custom_price'],
            document_id=row['document_id'],
            status_id=row['status_id'],
            payment_status=row['payment_status'],
            source=row['source'],
            date_created=row['date_created'],
        )

    @property
    def services_total(self) -> Decimal:
        """
        Calculate the total price for all services in the bill.

        Returns: sum в копійках
        """
        _sum = Decimal(0)
        for service in self.services or []:
            _sum += service.unit_price * service.units
        return _sum

    @property
    def rate_services(self) -> list[BillServiceRate]:
        return [s for s in self.services if s.type == BillServiceType.rate]

    @property
    def rates(self) -> list[AccountRate]:
        return [s.rate for s in self.services if s.type == BillServiceType.rate]

    @property
    def extension_services(self) -> list[BillServiceExtension]:
        return [s for s in self.services if s.type == BillServiceType.extension]

    @property
    def units_services(self) -> list[BillServiceUnits]:
        return [s for s in self.services if s.type == BillServiceType.units]

    @property
    def single_rate_service(self) -> BillServiceRate:
        """
        Type safe method to get the service from the bill that has only one "rate" service.
        """
        services = self.rate_services
        assert len(services) == 1, 'There should be only one rate service'
        return services[0]

    @property
    def single_units_service(self) -> BillServiceUnits:
        """
        Type safe method to get the service from the bill that has only one "units" service.
        """
        services = self.units_services
        assert len(services) == 1, 'There should be only one units service'
        return services[0]

    @property
    def single_extension_service(self) -> BillServiceExtension:
        """
        Type safe method to get the service from the bill that has only one "extension" service.
        """
        services = self.extension_services
        assert len(services) == 1, 'There should be only one extension service'
        return services[0]


class Bonus(NamedTuple):
    id: str

    key: str
    title: str
    type: BonusType

    units: int
    period: int

    date_created: datetime.datetime

    created_by: str | None = None
    description: str | None = None

    date_expired: datetime.datetime | None = None
    date_deleted: datetime.datetime | None = None


class CancelResourcesContext(NamedTuple):
    role_id: str
    company_id: str
    units: int
    comment: str
    accounts: list[Account]
    service_account_id: str
    transaction_type: TransactionType


class ChargeBonusContext(NamedTuple):
    document_id: str
    role_id: str
    account_id: str


class ChargeCreditContext(NamedTuple):
    document_id: str
    role_id: str
    account_id: str


class ChargeDebitContext(NamedTuple):
    document_id: str
    role_id: str
    account: Account


class DownloadBillsOptions(NamedTuple):
    date_from: str | None = None
    date_to: str | None = None


class UpdateCompanyRateCtx(NamedTuple):
    rate: CompanyRate
    new_status: CompanyRateStatus

    start_date: datetime.datetime | None
    end_date: datetime.datetime | None

    def to_db(self) -> DataDict:
        data: DataDict = {}
        if self.start_date is not None:
            data['activation_date'] = self.start_date
        if self.end_date is not None:
            data['date_expired'] = self.end_date
        return data

    @property
    def is_status_changed(self) -> bool:
        return self.rate.status != self.new_status

    @property
    def should_activate(self) -> bool:
        return self.is_status_changed and self.new_status == CompanyRateStatus.active

    @property
    def should_deactivate(self) -> bool:
        return self.is_status_changed and self.new_status == CompanyRateStatus.new


class Transaction(NamedTuple):
    id: str

    from_: str
    to_: str

    type: TransactionType
    amount: int
    units: int
    comment: str

    date_created: datetime.datetime

    operator_id: str | None = None
    initiator_id: str | None = None


@dataclass
class PaymentTransaction:
    id_: str
    bill_id: str
    role_id: str
    company_id: str
    payment_status: BillPaymentStatus
    date_created: datetime.datetime

    status_code: int | None = None
    source: BillPaymentSource | None = None

    @staticmethod
    def from_db(row: DBRow) -> PaymentTransaction:
        return PaymentTransaction(
            id_=row['id'],
            bill_id=row['bill_id'],
            role_id=row['role_id'],
            company_id=row['company_id'],
            payment_status=row['payment_status'],
            status_code=row['status_code'],
            source=row['source'],
            date_created=row['date_created'],
        )


@dataclass(frozen=True)
class BankTransaction(ABC):
    """
    Abstract class for bank transaction
    transaction_body - contains specific data according to concrete bank
    """

    transaction_id: str
    amount: Decimal  # в копійках
    bank: BankEnum

    id_: str | None = None
    is_processed: bool | None = False
    transaction_description: str | None = None
    transaction_date: datetime.datetime | None = None
    transaction_body: DataDict | None = None
    details: str | None = None
    bill_id: str | None = None

    @classmethod
    @abstractmethod
    def from_response(cls, data: DataDict) -> Self:
        """Builds BankTransaction object from raw transaction data of concrete bank"""

    @property
    @abstractmethod
    def sender_edrpou(self) -> str | None:
        """Property to get transaction sender edrpou"""

    @classmethod
    def from_db(cls, row: DBRow) -> Self:
        return cls(
            id_=row['id'],
            transaction_id=row['transaction_id'],
            transaction_description=row['transaction_description'],
            transaction_date=row['transaction_date'],
            transaction_body=row['transaction_body'],
            bill_id=row['bill_id'],
            is_processed=row['is_processed'],
            amount=money.to_subunits_decimal(row['amount']),
            details=row['details'],
            bank=row['bank'],
        )

    def to_db(self) -> DataDict:
        """
        Convert to dict for saving to db

        NOTE: don't use that data for logging, use "to_log_extra" instead
        """
        return {
            'transaction_id': self.transaction_id,
            'transaction_description': self.transaction_description,
            'transaction_body': self.transaction_body,
            'transaction_date': self.transaction_date,
            'amount': self.amount,
            'bank': self.bank,
        }

    def to_log_extra(self) -> DataDict:
        return {
            'transaction_id': self.transaction_id,
            'transaction_description': self.transaction_description,
            # don't spread body dict into multiple fields
            'transaction_body': str(self.transaction_body),
            'transaction_date': self.transaction_date,
            'amount': self.amount,
            'bank': self.bank,
        }


def to_account(data: DBRow) -> Account:
    return Account(
        id=data.id,
        company_id=data.company_id,
        initiator_id=data.initiator_id,
        employee_amount=data.employee_amount,
        type=data.type,
        rate=data.rate,
        source=data.source,
        amount=data.amount,
        amount_left=data.amount_left,
        units=data.units,
        units_left=data.units_left,
        date_created=data.date_created,
        date_expired=data.date_expired,
        date_deleted=data.date_deleted,
    )


def to_bonus(data: DBRow) -> Bonus:
    return Bonus(
        id=data.id,
        created_by=data.created_by,
        key=data.key,
        title=data.title,
        description=data.description,
        type=data.type,
        units=data.units,
        period=data.period,
        date_created=data.date_created,
        date_expired=data.date_expired,
        date_deleted=data.date_deleted,
    )


def to_transaction(data: DBRow) -> Transaction:
    return Transaction(
        id=data.id,
        from_=data.from_,
        to_=data.to_,
        operator_id=data.operator_id,
        initiator_id=data.initiator_id,
        type=data.type,
        amount=data.amount,
        units=data.units,
        comment=data.comment,
        date_created=data.date_created,
    )


Permissions = dict[CompanyPermission, bool]
Limits = dict[CompanyLimit, int | None]


@dataclass(frozen=True)
class MarketingCampaignTarget:
    email: str
    edrpou: str

    is_used: bool = False

    @staticmethod
    def from_dict(data: DataDict) -> MarketingCampaignTarget:
        return MarketingCampaignTarget(
            email=data['email'],
            edrpou=data['edrpou'],
            is_used=data.get('is_used', False),
        )

    def to_dict(self) -> DataDict:
        return {
            'email': self.email,
            'edrpou': self.edrpou,
            'is_used': self.is_used,
        }


class BillingCompanyConfig(pydantic.BaseModel):
    """
    Current limits & features for company.

    NOTE: keep in sync with CompanyLimit, CompanyPermission and UpdateBillingCompanyConfigDict
    """

    # See CompanyLimit for all possible keys

    # None means that company has unlimited access to this feature
    # Historically, we develop features first, and only later, after some time,
    # introduce billing limits for them separately. In such cases, we don't want
    # to disrupt the work of existing customers who are on previous rates.
    # That's why we use "None" (unlimited) as the default for most of the limits.
    # New customers, when activating or deactivating a rate, will receive some
    # limit for these features.
    # On the other hand, some features, like archives, were developed from the
    # ground up with billing limits in mind. Therefore, no one was using that
    # feature under previous rates. That's why we can use 0 as the default value
    # for that feature.
    max_archive_documents_count: int | None = 0
    max_additional_fields_count: int | None = None
    max_employees_count: int | None = None
    max_documents_count: int | None = None
    max_tags_count: int | None = None
    max_automation_count: int | None = None
    max_required_fields_count: int | None = None
    max_versions_count: int | None = None

    # See CompanyPermission for all possible keys
    api_enabled: bool = False
    can_enforce_2fa: bool = False
    external_comments_enabled: bool = False
    internal_comments_enabled: bool = False
    internal_document_enabled: bool = False
    can_manage_employee_access: bool = False
    reviews_enabled: bool = False

    # Obsolete CompanyPermission
    tags_enabled: bool = False
    allow_ordered_reviews: bool = False
    document_templates_enabled: bool = False
    custom_documents_fields_enabled: bool = False

    model_config = pydantic.ConfigDict(
        # Frozen is required here to use this class in hiku graphql engine, which
        # expects that returned objects are hashable.
        frozen=True,
    )

    @property
    def max_visible_documents_count(self) -> int | None:
        """
        Calculate the total number of documents that the company can see.
        """
        # Can view all documents
        if not self.max_documents_count:
            return None

        # Currently, archived documents only increase the visible document count.
        # Unlimited archived documents are not supported and should be counted as 0 additional
        # documents that user can view.
        return self.max_documents_count + (self.max_archive_documents_count or 0)

    def to_db(self) -> DataDict:
        return self.model_dump(mode='json')

    def to_update_dict(self) -> UpdateBillingCompanyConfigDict:
        """
        This method returns partial dict to update fields that was set explicitly.
        """
        return self.model_dump(exclude_unset=True, mode='json')  # type: ignore


class UpdateBillingCompanyConfigDict(TypedDict, total=False):
    """
    Dict for updating billing company config.

    NOTE: keep in sync with CompanyLimit, CompanyPermission and BillingCompanyConfig
    """

    max_archive_documents_count: int | None
    max_additional_fields_count: int | None
    max_employees_count: int | None
    max_documents_count: int | None
    max_tags_count: int | None
    max_automation_count: int | None
    max_required_fields_count: int | None
    max_versions_count: int | None

    api_enabled: bool
    can_enforce_2fa: bool
    external_comments_enabled: bool
    internal_comments_enabled: bool
    internal_document_enabled: bool
    can_manage_employee_access: bool
    reviews_enabled: bool

    tags_enabled: bool
    allow_ordered_reviews: bool
    document_templates_enabled: bool
    custom_documents_fields_enabled: bool


@dataclass(frozen=True)
class BillingCompany:
    company_id: str
    config: BillingCompanyConfig
    date_created: datetime.datetime
    date_updated: datetime.datetime

    @staticmethod
    def from_db(row: DBRow) -> BillingCompany:
        config = BillingCompanyConfig.model_validate(row.config)
        return BillingCompany(
            company_id=row.company_id,
            config=config,
            date_created=row.date_created,
            date_updated=row.date_updated,
        )


class RateExtensionConfig(pydantic.BaseModel):
    units: int

    def to_dict(self) -> DataDict:
        return self.model_dump(mode='json')


@dataclass(frozen=True)
class RateExtension:
    id: str
    account_id: str
    type: RateExtensionType
    # If you want to add different config, create a union type with different configs
    # RateExtensionConfig = RateExtensionConfig1 | RateExtensionConfig2
    config: RateExtensionConfig | None
    status: RateExtensionStatus
    bill_id: str
    date_created: datetime.datetime
    date_updated: datetime.datetime | None

    # Date when trial should be expired
    date_expiring: datetime.datetime | None

    planned_activation_date: datetime.date | None

    @staticmethod
    def from_db(row: DBRow) -> RateExtension:
        raw_config = row.config
        config: RateExtensionConfig | None = None
        if raw_config is not None:
            config = RateExtensionConfig.model_validate(raw_config)

        return RateExtension(
            id=row.id,
            account_id=row.account_id,
            type=row.type,
            config=config,
            status=row.status,
            bill_id=row.bill_id,
            date_created=row.date_created,
            date_updated=row.date_updated,
            date_expiring=row.date_expiring,
            planned_activation_date=row.planned_activation_date,
        )


@dataclass
class DeactivateRateConfigUpdated:
    company_config_update: UpdateBillingCompanyConfigDict
    company_config_remove: list[str]
    billing_config_update: UpdateBillingCompanyConfigDict


class CheckoutExtensionProperties(pydantic.BaseModel):
    type: RateExtensionType
    price_per_unit_base: float
    price_per_unit_total: float


class CheckoutWebRateProperties(pydantic.BaseModel):
    rate_set: Literal['web'] = 'web'
    rate: AccountRate

    can_buy: bool

    # By word "extend" we mean that the user can purchase the same rate for the next year.
    can_extend: bool

    price_per_year: float | None  # в копійках
    price_per_month: float | None  # в копійках

    documents_to_send: int | None

    comments: bool
    manage_employees_access: bool
    internal_documents: bool
    reviews: bool
    enforce_2fa: bool

    employees: int | None
    tags: int | None
    additional_fields: int | None
    templates: int | None
    required_fields: int | None

    antivirus: bool
    cloud_storage: bool

    kep_keys: int
    signing_incoming_documents: int | None
    documents_to_view: int | None

    extensions: list[CheckoutExtensionProperties] | None

    @classmethod
    def build(
        cls,
        rate: AccountRate,
        can_buy: bool,
        can_extend: bool,
        price_per_year: float | None,
        price_per_month: float | None,
        documents_to_send: int | None,
        kep_keys: int,
        antivirus: bool,
        cloud_storage: bool,
        documents_to_view: int | None,
        extensions: list[CheckoutExtensionProperties] | None,
        company_edrpou: str,
    ) -> Self:
        from app.billing import utils

        limits = utils.get_rate_config_limits(rate, edrpou=company_edrpou)
        permissions = utils.get_rate_config_permissions(rate, edrpou=company_edrpou)
        return cls(
            rate=rate,
            # actions
            can_buy=can_buy,
            can_extend=can_extend,
            # Price
            price_per_year=price_per_year,
            price_per_month=price_per_month,
            # Balance
            documents_to_send=documents_to_send,
            # From permissions config
            comments=(
                permissions.get(CompanyPermission.external_comments, False)
                and permissions.get(CompanyPermission.internal_comments, False)
            ),
            manage_employees_access=permissions.get(
                CompanyPermission.manage_employee_access, False
            ),
            internal_documents=permissions.get(CompanyPermission.internal_documents, False),
            reviews=permissions.get(CompanyPermission.reviews, False),
            enforce_2fa=permissions.get(CompanyPermission.enforce_2fa, False),
            # From limits config
            employees=limits.get(CompanyLimit.employees, 0),
            tags=limits.get(CompanyLimit.tags, 0),
            additional_fields=limits.get(CompanyLimit.additional_fields, 0),
            templates=limits.get(CompanyLimit.automation, 0),
            required_fields=limits.get(CompanyLimit.required_fields, 0),
            documents_to_view=documents_to_view,
            # Permissions and limits that are not rate config
            antivirus=antivirus,
            cloud_storage=cloud_storage,
            kep_keys=kep_keys,
            signing_incoming_documents=None,  # unlimited for all rates
            extensions=extensions,
        )


class CheckoutIntegrationRateProperties(pydantic.BaseModel):
    rate_set: Literal['integration'] = 'integration'
    rate: AccountRate
    can_buy: bool
    can_extend: bool
    can_buy_documents: bool
    price_per_year: float
    price_per_document: float
    signing_documents: bool
    comments: bool
    tarification: bool


class CheckoutArchiveRateProperties(pydantic.BaseModel):
    rate_set: Literal['archive'] = 'archive'
    rate: AccountRate
    can_buy: bool
    can_extend: bool
    price_per_year: float
    price_per_month: float
    archive_documents_to_view: int

    @classmethod
    def build(
        cls,
        rate: AccountRate,
        can_buy: bool,
        can_extend: bool,
        company_edrpou: str,
    ) -> Self:
        from app.billing.utils import get_rate_config_limits, get_rate_price

        limits = get_rate_config_limits(rate, edrpou=company_edrpou)
        return cls(
            rate=rate,
            can_buy=can_buy,
            can_extend=can_extend,
            price_per_year=get_rate_price(rate, edrpou=company_edrpou),
            price_per_month=get_rate_price(rate, edrpou=company_edrpou) // 12,
            archive_documents_to_view=limits.get(CompanyLimit.max_archive_documents_count) or 0,
        )


class CheckoutProperties(pydantic.BaseModel):
    rates_web: list[CheckoutWebRateProperties] = pydantic.Field(default_factory=list)
    rates_integration: list[CheckoutIntegrationRateProperties] = pydantic.Field(
        default_factory=list
    )
    rates_archive: list[CheckoutArchiveRateProperties] = pydantic.Field(default_factory=list)


@dataclass(frozen=True, kw_only=True)
class ChargeDocumentContext:
    """
    Object to pass context who charges the document
    """

    document_id: str

    # ID of a company who pays for the document
    payer_id: str

    # Current user role_id, can have different company ID than "company_id" field
    role_id: str

    # Is document from API?
    is_api: bool

    accounts: list[Account]


RateConfigPermissions = dict[CompanyPermission, bool]
RateConfigLimits = dict[CompanyLimit, int | None]


class RateConfig(TypedDict):
    permissions: RateConfigPermissions
    limits: RateConfigLimits

    # How much documents for sending will be added to the company's balance?
    # NOTE: in the current state, all web rates except "ultimate" have this field,
    # ultimate has unlimited documents.
    units: NotRequired[int]


class IncreaseEmployeesSchema(pydantic.BaseModel):
    employee_amount: int = pydantic.Field(ge=0)
    price_per_employee: int = pydantic.Field(ge=0)


class BillResources(NamedTuple):
    rates: list[CompanyRate]
    rate_extension: RateExtension | None
    document_units: int | None


@dataclass
class ActivateBonusCtx:
    bonus_key: str
    company_id: str
    bonus: Bonus
    is_duplicated: bool = False


@dataclass
class ActivateEmployeeExtensionCtx:
    company_rate: CompanyRate
    old_max_employees_count: int


@dataclass
class CancelResourcesCtx:
    role_id: str
    company_id: str
    units: int
    comment: str
    accounts: list[Account]


@dataclass
class AddBillFromWebCtx:
    options: AddBillOptions
    analytics_meta: GoogleAnalyticsMeta | None


@dataclass
class ActivateCustomBonusesFromFileCtx:
    bonuses: list[DataDict]
    counters: DataDict
