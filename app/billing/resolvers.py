import logging
from collections import defaultdict
from copy import deepcopy
from typing import (
    Any,
)

from hiku.engine import (
    Context,
    pass_context,
)
from hiku.graph import Nothing

from api.graph.constants import DB_ENGINE_KEY, DB_READONLY_KEY
from api.graph.types import FieldList
from api.graph.utils import get_base_graph_user
from app.auth.db import select_companies_by_ids
from app.billing import db
from app.billing.db import (
    select_bills_by_edrpous,
)
from app.billing.enums import CompanyLimit
from app.billing.types import Bill, BillingCompanyConfig
from app.billing.utils import (
    format_bill_number,
    get_bill_total,
    get_billing_companies_configs,
    get_rate_config,
)
from app.lib import money
from app.lib.database import DBRow
from app.lib.types import (
    StrList,
)

logger = logging.getLogger(__name__)


@pass_context
async def resolve_billing_company_config(
    ctx: Context,
    companies_ids: list[str],
) -> list[BillingCompanyConfig]:
    """
    Resolve billing company config by companies ids
    """

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        configs = await get_billing_companies_configs(conn, companies_ids=companies_ids)

    return [configs[company_id] for company_id in companies_ids]


@pass_context
async def resolve_companies_bills(ctx: Context, companies_ids: StrList) -> list[list[Bill]]:
    """Return bills for the company"""

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        companies = await select_companies_by_ids(conn, companies_ids=companies_ids)
        companies_map = {company.edrpou: company.id for company in companies}
        bills = await select_bills_by_edrpous(conn=conn, edrpous=list(companies_map.keys()))

    bills_map: defaultdict[str, list[Bill]] = defaultdict(list)
    for bill in bills:
        company_id = companies_map[bill.edrpou]
        bills_map[company_id].append(bill)

    return [bills_map.get(company_id, []) for company_id in companies_ids]


async def resolve_bill_services(bills: list[Bill]) -> Any:
    return [bill.services for bill in bills]


async def resolve_bill_number(_: FieldList, bills: list[Bill]) -> list[list[str]]:
    return [[bill.number] for bill in bills]


async def resolve_bill_amount(_: FieldList, bills: list[Bill]) -> list[list[int]]:
    result = []
    for bill in bills:
        total = await get_bill_total(bill)  # в гривнях
        total_subunits = money.to_subunits_decimal_from_units(total)
        result.append([int(total_subunits)])  # в копійках
    return result


async def get_bill_old_fields(fields: FieldList, bills: list[Bill]) -> list[list[Any]]:
    result: list[list[Any]] = []
    for bill in bills:
        resolved_fields: list[Any] = []
        for field in fields:
            if field.name == 'rate':
                resolved_fields.append(bill._deprecated_rate)
            elif field.name == 'count_documents':
                resolved_fields.append(bill._deprecated_count_documents)
            elif field.name == 'max_employees_count':
                resolved_fields.append(bill._deprecated_max_employees_count)
            else:
                resolved_fields.append(None)

        result.append(resolved_fields)

    return result


@pass_context
async def resolve_company_rate_dynamic_fields(
    ctx: Context,
    fields: FieldList,
    rates_ids: list[str],
) -> list[list[Any]]:
    """
    One resolve for several fields that should calculate dynamically, and can't be just mapped
    from a database. Fields covered:
     - config
     - billNumber/bill_number
     - units
     - unitsLeft/units_left

    Usually frontend selects all of these fields at once, so there is no need to add logic to
    resolve each field separately.
    """
    user = get_base_graph_user(ctx)
    if not user:
        return [[Nothing] * len(fields) for _ in rates_ids]

    deprecated_fields = [f.name for f in fields if f.name in ('bill_number', 'units_left')]
    if deprecated_fields:
        logger.warning(
            msg='Deprecated fields are used in the company rate dynamic fields resolver',
            extra={
                'deprecated_fields': deprecated_fields,
            },
        )

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        company_rates = await db.select_company_rate_fields_for_graph(
            conn=conn,
            rates_ids=rates_ids,
        )
        rates_mapping = {rate.id: rate for rate in company_rates}

        # Build a mapping for bill numbers: { bill_id: bill_seqnum }
        bill_seqnums_mapping = await db.select_bills_seqnums_for_graph(
            conn=conn,
            bills_ids=[r.bill_id for r in company_rates if r.bill_id],
        )

        units_balances = await db.select_company_rate_units_balance(
            conn=conn,
            rates_ids=rates_ids,
        )
        rate_balance_mapping = {balance.rate_id: balance for balance in units_balances}

        results_mapping: dict[str, dict[str, Any]] = {}
        for rate_id in rates_ids:
            # Default values for each field
            rate_fields_map: dict[str, Any] = {
                'config': None,
                'bill_number': None,
                'units': None,
                'units_left': None,
            }

            rate_row: DBRow | None = rates_mapping.get(rate_id)
            if rate_row is not None:
                rate_config = get_rate_config(rate_row.rate, edrpou=rate_row.edrpou)
                if rate_config and rate_row.rate.is_ultimate:
                    rate_config = deepcopy(rate_config)
                    rate_config['limits'][CompanyLimit.employees] = rate_row.employee_amount

                rate_fields_map['config'] = rate_config

                if rate_row.bill_id and (bill_seqnum := bill_seqnums_mapping.get(rate_row.bill_id)):
                    rate_fields_map['bill_number'] = format_bill_number(bill_seqnum)

                if balance := rate_balance_mapping.get(rate_id):
                    rate_fields_map['units'] = balance.units
                    rate_fields_map['units_left'] = balance.units_left

            results_mapping[rate_id] = rate_fields_map

        # Build a list of lists with resolved fields for each rate
        results: list[list[Any]] = []
        for rate_id in rates_ids:
            result_mapping = results_mapping[rate_id]
            rate_fields = []
            for field in fields:
                if field.name == 'config':
                    rate_fields.append(result_mapping['config'])
                elif field.name in ('billNumber', 'bill_number'):
                    rate_fields.append(result_mapping['bill_number'])
                elif field.name == 'units':
                    rate_fields.append(result_mapping['units'])
                elif field.name in ('units_left', 'unitsLeft'):
                    rate_fields.append(result_mapping['units_left'])
                else:
                    rate_fields.append(None)
            results.append(rate_fields)

        return results
