import sqlalchemy as sa

from app.models import columns, metadata

from .enums import (
    SignSessionDocumentStatus,
    SignSessionSource,
    SignSessionStatus,
    SignSessionType,
    SignSessionVendor,
)

sign_session_table = sa.Table(
    'sign_sessions',
    metadata,
    columns.UUID(),
    columns.ForeignKey('created_by', 'roles.id', nullable=False),
    columns.ForeignKey(
        'document_id',
        'documents.id',
        index=True,
        nullable=False,
        ondelete='NO ACTION',
    ),
    # Next tree columns "role_id", "email", "edrpou" represent a user for which we are
    # creating a sign session. See app/sign_session/README.md to better understand what
    # data we have in those columns
    columns.ForeignKey('role_id', 'roles.id', nullable=True),
    columns.EDRPOU(nullable=False),
    columns.Email(nullable=False),
    columns.Boolean('is_legal', nullable=True),
    columns.Enum('type', SignSessionType, SignSessionType.sign_session),
    # TODO: Fill that field for old sign sessions
    # Source of sign session (API, EDI, inbox email)
    columns.SoftEnum('source', SignSessionSource, default_value=None, nullable=True),
    columns.Enum('vendor', SignSessionVendor, nullable=True),
    columns.Enum('status', SignSessionStatus, SignSessionStatus.created),
    columns.Enum('document_status', SignSessionDocumentStatus, nullable=True),
    sa.Column('on_finish_url', sa.Text()),
    sa.Column('on_cancel_url', sa.Text()),
    sa.Column('on_document_view_hook', sa.Text()),
    sa.Column('on_document_comment_hook', sa.Text()),
    sa.Column('on_document_reject_hook', sa.Text()),
    sa.Column('on_document_sign_hook', sa.Text()),
    # Date when sign session must be expired
    columns.DateExpired(),
    # parameters that must be propagated to sign request
    columns.JSONB('sign_parameters', nullable=True),
    columns.DateCreated(),
    columns.DateUpdated(),
    columns.DateTime('date_started'),
    columns.DateTime('date_cancelled'),
    columns.DateTime('date_finished'),
)
