from __future__ import annotations

import datetime
from dataclasses import dataclass

from yarl import URL

from app.auth.types import Role
from app.documents.types import DocumentDetails
from app.lib.database import DBRow
from app.lib.datetime_utils import utc_now
from app.lib.helpers import safe_enum_value
from app.lib.types import DataDict
from app.sign_sessions import helpers
from app.sign_sessions.enums import (
    SignSessionDocumentStatus,
    SignSessionSource,
    SignSessionStatus,
    SignSessionType,
    SignSessionVendor,
)


@dataclass
class CreateSignSessionOptionsBase:
    edrpou: str
    email: str
    role: Role | None
    is_legal: bool
    session_type: SignSessionType
    sign_session: SignSessionExtended | None

    # indicates that sign session is internal and used for document viewing in web
    is_internal_view: bool = False

    @property
    def role_id(self) -> str | None:
        return role.id_ if (role := self.role) else None


@dataclass
class CreateSignSessionOptions:
    created_by: str
    document: DBRow

    edrpou: str
    email: str

    vendor: str | None
    on_cancel_url: str | None
    on_finish_url: str | None

    sign_parameters: DataDict | None

    version: str

    type: SignSessionType
    source: SignSessionSource

    is_legal: bool = True

    role_id: str | None = None
    role: DBRow | None = None

    existed_sign_session: SignSessionExtended | None = None
    on_document_view_hook: str | None = None
    on_document_comment_hook: str | None = None
    on_document_sign_hook: str | None = None
    on_document_reject_hook: str | None = None

    # indicates that sign session is internal and used for document viewing in web
    is_internal_view: bool = False

    @property
    def document_details(self) -> DocumentDetails:
        return DocumentDetails(
            id_=self.document.id,
            title=self.document.title,
        )


@dataclass
class SignSession:
    """Sign session row in database"""

    id: str
    created_by: str  # roles.id
    document_id: str
    role_id: str | None  # roles.id
    edrpou: str
    email: str
    is_legal: bool | None
    type: SignSessionType
    source: SignSessionSource | None
    vendor: SignSessionVendor | None
    status: SignSessionStatus
    document_status: SignSessionDocumentStatus | None
    on_finish_url: str | None
    on_cancel_url: str | None
    on_document_view_hook: str | None
    on_document_comment_hook: str | None
    on_document_reject_hook: str | None
    on_document_sign_hook: str | None
    sign_parameters: DataDict | None
    date_created: datetime.datetime
    date_updated: datetime.datetime
    date_expired: datetime.datetime | None
    date_started: datetime.datetime | None
    date_cancelled: datetime.datetime | None
    date_finished: datetime.datetime | None

    @staticmethod
    def from_row(row: DBRow) -> SignSession:
        return SignSession(
            id=row.id,
            created_by=row.created_by,
            document_id=row.document_id,
            role_id=row.role_id,
            edrpou=row.edrpou,
            email=row.email,
            is_legal=row.is_legal,
            type=row.type,
            vendor=row.vendor,
            status=row.status,
            source=row.source,
            document_status=row.document_status,
            on_finish_url=row.on_finish_url,
            on_cancel_url=row.on_cancel_url,
            on_document_view_hook=row.on_document_view_hook,
            on_document_comment_hook=row.on_document_comment_hook,
            on_document_reject_hook=row.on_document_reject_hook,
            on_document_sign_hook=row.on_document_sign_hook,
            sign_parameters=row.sign_parameters,
            date_expired=row.date_expired,
            date_created=row.date_created,
            date_updated=row.date_updated,
            date_started=row.date_started,
            date_cancelled=row.date_cancelled,
            date_finished=row.date_finished,
        )

    def build_start_url(
        self,
        *,
        absolute: bool = True,
        params: DataDict | None = None,
    ) -> str:
        """Build URL for starting sign session. Entrypoint of sign session"""
        return helpers.build_sign_session_url(
            sign_session_id=self.id,
            absolute=absolute,
            params=params,
        )

    @property
    def cancel_url(self) -> str:
        """
        Add sign session ID as query parameter to cancel URL:
        {on_cancel_url}?sid={sign_session_id}
        """
        url = URL(self.on_cancel_url or '').update_query(sid=self.id)
        return str(url)

    @property
    def finish_url(self) -> str:
        """
        Add sign session ID as query parameter to finish URL:
        {on_finish_url}?sid={sign_session_id}
        """
        url = URL(self.on_finish_url or '').update_query(sid=self.id)
        return str(url)


@dataclass
class SignSessionExtended(SignSession):
    # SignSession attributes
    ...

    # Document attributes
    document_title: str
    document_edrpou_owner: str

    # Role attributes
    user_role: int | None
    can_download_document: bool
    can_print_document: bool
    can_comment_document: bool
    can_sign_and_reject_document: bool
    can_sign_and_reject_document_external: bool
    can_sign_and_reject_document_internal: bool
    can_delete_document: bool
    company_id: str | None
    user_id: str | None

    @property
    def is_finished(self) -> bool:
        return self.status == SignSessionStatus.finished

    @property
    def is_expired(self) -> bool:
        """Check if sign session is expired"""
        if self.date_expired is None:
            return False
        return self.date_expired < utc_now()

    @property
    def document_details(self) -> DocumentDetails:
        return DocumentDetails(
            id_=self.document_id,
            title=self.document_id,
        )

    @staticmethod
    def from_row(row: DBRow) -> SignSessionExtended:
        return SignSessionExtended(
            id=row.id,
            created_by=row.created_by,
            document_id=row.document_id,
            role_id=row.role_id,
            edrpou=row.edrpou,
            email=row.email,
            is_legal=row.is_legal,
            type=row.type,
            source=row.source,
            vendor=row.vendor,
            status=row.status,
            document_status=row.document_status,
            on_finish_url=row.on_finish_url,
            on_cancel_url=row.on_cancel_url,
            on_document_view_hook=row.on_document_view_hook,
            on_document_comment_hook=row.on_document_comment_hook,
            on_document_reject_hook=row.on_document_reject_hook,
            on_document_sign_hook=row.on_document_sign_hook,
            sign_parameters=row.sign_parameters,
            date_expired=row.date_expired,
            date_created=row.date_created,
            date_updated=row.date_updated,
            date_started=row.date_started,
            date_cancelled=row.date_cancelled,
            date_finished=row.date_finished,
            document_title=row.document_title,
            document_edrpou_owner=row.document_edrpou_owner,
            user_role=row.user_role,
            can_download_document=bool(row.can_download_document),
            can_print_document=bool(row.can_print_document),
            can_comment_document=bool(row.can_comment_document),
            can_sign_and_reject_document=bool(row.can_sign_and_reject_document),
            can_sign_and_reject_document_external=bool(row.can_sign_and_reject_document_external),
            can_sign_and_reject_document_internal=bool(row.can_sign_and_reject_document_internal),
            can_delete_document=bool(row.can_delete_document),
            company_id=row.company_id,
            user_id=row.user_id,
        )

    def to_api(self) -> DataDict:
        """Prepare API response dict for sign session"""
        return {
            'id': self.id,
            'url': self.build_start_url(),
            'created_by': self.created_by,
            'document_id': self.document_id,
            'role_id': self.role_id,
            'edrpou': self.edrpou,
            'email': self.email,
            'is_legal': self.is_legal,
            'vendor': safe_enum_value(self.vendor),
            'type': safe_enum_value(self.type),
            'status': safe_enum_value(self.status),
            'document_status': safe_enum_value(self.document_status),
            'on_finish_url': self.on_finish_url,
            'on_cancel_url': self.on_cancel_url,
            'on_document_view_hook': self.on_document_view_hook,
            'on_document_comment_hook': self.on_document_comment_hook,
            'on_document_reject_hook': self.on_document_reject_hook,
            'on_document_sign_hook': self.on_document_sign_hook,
        }
