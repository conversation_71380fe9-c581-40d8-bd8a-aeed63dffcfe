import uuid

import pytest
from aiohttp import web
from aiohttp.test_utils import make_mocked_request

from app.app import create_app
from app.tests.common import (
    TEST_USER_PASSWORD,
    cleanup_on_teardown,
    insert_test_user,
    prepare_app_client,
    prepare_user_data,
)
from app.tokens.db import insert_short_registration_token
from app.tokens.validators import (
    validate_email,
    validate_payload,
    validate_short_invite_token,
    validate_token,
)

TEST_EDRPOU = '12345678'
TEST_EMAIL = '<EMAIL>'
TEST_TOKEN = 'test-token'


async def test_validate_email(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)
    request = make_mocked_request('GET', '/', app=app)

    try:
        async with app['db'].acquire() as conn:
            await validate_email(request, conn, TEST_EMAIL)
    finally:
        await cleanup_on_teardown(app)


async def test_validate_email_invalid(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)
    request = make_mocked_request('GET', '/', app=app)

    try:
        async with app['db'].acquire() as conn:
            await insert_test_user(
                conn,
                {
                    'email': TEST_EMAIL,
                    'password': TEST_USER_PASSWORD,
                },
            )

            with pytest.raises(web.HTTPFound) as err:
                await validate_email(request, conn, TEST_EMAIL)
            assert err.value.location == '/invalid-token?email=1'
    finally:
        await cleanup_on_teardown(app)


async def test_validate_payload():
    request = make_mocked_request('GET', '/', app=create_app())
    validate_payload(request, {'edrpou': TEST_EDRPOU, 'email': TEST_EMAIL})


@pytest.mark.parametrize(
    'invalid_payload',
    [
        {'edrpou': TEST_EDRPOU},
        {'email': TEST_EMAIL},
        {'email': TEST_EMAIL, 'e': TEST_EDRPOU},
    ],
)
async def test_validate_payload_invalid(invalid_payload):
    request = make_mocked_request('GET', '/', app=create_app())
    with pytest.raises(web.HTTPFound) as err:
        validate_payload(request, invalid_payload)
    assert err.value.location == '/invalid-token?payload=1'


async def test_validate_short_invite_token(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)

    request = make_mocked_request('GET', '/', app=app)
    data = {'edrpou': TEST_EDRPOU, 'email': TEST_EMAIL}

    try:
        async with app['db'].acquire() as conn:
            short_token = await insert_short_registration_token(conn, dict(data))
            result = await validate_short_invite_token(request, conn, short_token)
            assert result == dict(data, token=short_token)
    finally:
        await cleanup_on_teardown(app)


async def test_validate_short_invite_token_does_not_exist(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)

    request = make_mocked_request('GET', '/', app=app)

    try:
        async with app['db'].acquire() as conn:
            with pytest.raises(web.HTTPFound) as err:
                await validate_short_invite_token(request, conn, str(uuid.uuid4()))
            assert err.value.location == '/invalid-token?token=1'
    finally:
        await cleanup_on_teardown(app)


async def test_validate_short_invite_token_invalid(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)

    request = make_mocked_request('GET', '/', app=app)
    data = {'edrpou': TEST_EDRPOU, 'email': TEST_EMAIL}

    try:
        user = await prepare_user_data(app)
        async with app['db'].acquire() as conn:
            short_token = await insert_short_registration_token(
                conn, dict(data, registered_user_id=user.id)
            )
            with pytest.raises(web.HTTPFound) as err:
                await validate_short_invite_token(request, conn, short_token)
            assert err.value.location == '/invalid-token?user=1'
    finally:
        await cleanup_on_teardown(app)


async def test_validate_token():
    request = make_mocked_request('GET', '/', app=create_app())
    validate_token(request, TEST_TOKEN)


async def test_validate_token_empty():
    request = make_mocked_request('GET', '/', app=create_app())
    with pytest.raises(web.HTTPFound) as err:
        validate_token(request, '')
    assert err.value.location == '/invalid-token?empty=1'
