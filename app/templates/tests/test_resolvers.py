from app.auth.types import User
from app.document_categories.db import insert_document_category
from app.services import services
from app.templates.tests.utils import prepare_favorite, prepare_template
from app.tests.common import fetch_graphql, prepare_auth_headers, prepare_client, prepare_user_data


async def test_resolve_templates(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(app, email='<EMAIL>', company_edrpou='11223344')

    async with services.db.acquire() as conn:
        document_category = await insert_document_category(
            conn=conn,
            title='Title',
            company_id=user.company_id,
        )
    template = await prepare_template(
        user=User.from_row(user), category_id=document_category.id, title='Unique name'
    )
    await prepare_favorite(template, User.from_row(user))
    template2 = await prepare_template(user=User.from_row(user), title='Template2')
    template3_public = await prepare_template(
        user=User.from_row(recipient), title='Template3', is_private=False
    )
    # should not be returned as it is not in the same company
    await prepare_template(user=User.from_row(recipient), title='Template4')

    def build_query(filters: str) -> str:
        if filters:
            filters = f'({filters})'

        return f"""
        {{
            allTemplates{filters} {{
                count
                templates {{
                    id
                    title
                    isFavorite
                    creatorRoleId
                    previewImgUrl
                }}
            }}
        }}
        """

    # User has access to all template in his company and all public templates
    headers = prepare_auth_headers(user)
    response = await fetch_graphql(client, build_query(''), headers)
    assert response['allTemplates']['count'] == 3
    assert sorted(t['id'] for t in response['allTemplates']['templates']) == sorted(
        [template.id, template2.id, template3_public.id]
    )

    # Test filter by name
    response = await fetch_graphql(client, build_query('search: "Templ"'), headers)
    assert response['allTemplates']['count'] == 2
    assert sorted(t['id'] for t in response['allTemplates']['templates']) == sorted(
        [template3_public.id, template2.id]
    )

    # Test filter by category
    response = await fetch_graphql(
        client, build_query(f'categories: [{document_category.id}]'), headers
    )
    assert response['allTemplates']['count'] == 1
    assert response['allTemplates']['templates'][0]['id'] == template.id

    # Test only private
    response = await fetch_graphql(client, build_query('is_public: false'), headers)
    assert response['allTemplates']['count'] == 2
    assert sorted(t['id'] for t in response['allTemplates']['templates']) == sorted(
        [template.id, template2.id]
    )

    # Test only public
    response = await fetch_graphql(client, build_query('is_public: true'), headers)
    assert response['allTemplates']['count'] == 1
    assert response['allTemplates']['templates'][0]['id'] == template3_public.id

    # Test only favorite
    response = await fetch_graphql(client, build_query('is_favorite: true'), headers)
    assert response['allTemplates']['count'] == 1
    assert response['allTemplates']['templates'][0]['id'] == template.id

    # Test only unfavorite
    response = await fetch_graphql(client, build_query('is_favorite: false'), headers)
    assert response['allTemplates']['count'] == 2
    assert sorted(t['id'] for t in response['allTemplates']['templates']) == sorted(
        [template2.id, template3_public.id]
    )
