import datetime
from typing import Annotated

from pydantic import AfterValidator, BaseModel, Field, field_validator

from app.i18n import _
from app.lib.types import StrDict
from app.lib.validators_pydantic import UUID, IntID, Price
from app.templates.constants import (
    ALLOWED_TEMPLATE_EXTENSIONS,
    EMPTY_DOCX,
    EMPTY_XLSX,
    PREVIEW_MAX_SIZE,
)


def validate_template_extension(value: str) -> str:
    if not value.endswith(tuple(ALLOWED_TEMPLATE_EXTENSIONS)):
        raise ValueError(
            _('Підтримуються тільки формати {allowed_extensions}').bind(
                allowed_extensions=str(ALLOWED_TEMPLATE_EXTENSIONS)
            )
        )
    return value


TemplateExtension = Annotated[
    str, Field(..., min_length=2, max_length=64), AfterValidator(validate_template_extension)
]


class AddTemplateSchema(BaseModel):
    title: str = Field(..., min_length=1, max_length=100)
    category_id: IntID | None = None
    extension: TemplateExtension = '.docx'
    content: bytes | None = None

    @property
    def empty_file(self) -> bytes:
        if self.extension == '.docx':
            return EMPTY_DOCX
        if self.extension == '.xlsx':
            return EMPTY_XLSX

        raise NotImplementedError()


class UpdateTemplateSchema(BaseModel):
    title: str | None = Field(None, min_length=1, max_length=100)
    category_id: IntID | None = None
    extension: TemplateExtension | None = None
    # set document_template_id from validation
    template_id: UUID


class CopyDocumentAsTemplateSchema(BaseModel):
    document_id: UUID
    version_id: UUID | None = None


class DocumentFromTemplateSchema(BaseModel):
    template_id: UUID
    # will be used to set in new document instead
    title: str | None = Field(None, max_length=255)
    category_id: IntID | None = None
    date_document: datetime.date | None = None
    number: str | None = Field(None, max_length=512)
    amount: Price | None = None
    expected_owner_signatures: int = Field(1, ge=0, le=1_000)
    expected_recipient_signatures: int = Field(1, ge=0, le=1_000)
    # given texts will be replaced in template
    extra_fields: StrDict | None = None


class TemplatePreviewSchema(BaseModel):
    template_id: UUID
    size: int = Field(PREVIEW_MAX_SIZE, gt=0)

    @field_validator('size')
    @staticmethod
    def validate_size(value: int) -> int:
        allowed_sizes = (PREVIEW_MAX_SIZE, 1024)
        if value not in allowed_sizes:
            raise ValueError(f'Allowed sizes {allowed_sizes} you passed {value}')

        return value
