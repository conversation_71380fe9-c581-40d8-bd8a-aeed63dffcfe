from http import HTTPStatus

import pytest

from api.errors import Code, Error
from app.documents.tables import document_table
from app.documents.utils import send_document
from app.lib import eusign_utils
from app.lib.enums import DocumentStatus, SignatureFormat, SignatureType, Source
from app.lib.helpers import decode_base64_str
from app.models import select_all, select_one
from app.signatures.enums import SignatureSource
from app.signatures.tables import signature_table
from app.signatures.tests.test_signatures_views import fake_get_cert_info
from app.signatures.utils import add_signature, add_signature_schedule_async_jobs
from app.tests.common import (
    API_V2_ADD_FLOW_API_URL,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_sign_session_data,
    prepare_sign_session_headers,
    prepare_signature_form_data,
    prepare_signature_info,
    prepare_user_data,
)

TEST_EDRPOU_1 = '55555555'
TEST_EDRPOU_2 = '77777777'
TEST_EDRPOU_3 = '88888888'
TEST_EMAIL_1 = '<EMAIL>'
TEST_EMAIL_2 = '<EMAIL>'
TEST_EMAIL_3 = '<EMAIL>'
SIGNATURE_KEY_1 = 'c2lnbmF0dXJlMQ=='
SIGNATURE_KEY_2 = 'c2lnbmF0dXJlMg=='
SIGNATURE_KEY_3 = 'c2lnbmF0dXJlMw=='


async def _prepare_flows(client, user, document):
    response = await client.post(
        path=API_V2_ADD_FLOW_API_URL.format(document_id=document.id),
        json=[
            {'edrpou': TEST_EDRPOU_1, 'emails': [TEST_EMAIL_1], 'sign_num': 1},
            {'edrpou': TEST_EDRPOU_2, 'emails': [TEST_EMAIL_2], 'sign_num': 1},
            {'edrpou': TEST_EDRPOU_3, 'emails': [TEST_EMAIL_3], 'sign_num': 1},
        ],
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.CREATED


async def _sign(monkeypatch, conn, document, edrpou, key, user=None):
    data = {
        'key': key,
        'format': SignatureFormat.external_separated.value,
        'source': SignatureSource.api.value,
        'document_id': document.id,
    }
    sign = prepare_signature_info(SignatureType.signature, edrpou)
    monkeypatch.setattr(eusign_utils, 'verify_sync', lambda *_, **__: sign)
    ctx = await add_signature(conn=conn, user=user, data=data)
    await add_signature_schedule_async_jobs(
        conn=conn,
        ctx=ctx,
        request_source=Source.api_public,
    )


async def _send(conn, document, edrpou):
    await send_document(
        conn=conn,
        user=None,
        raw_data={'document_id': document.id},
        request_source=Source.api_public,
        company_edrpou=edrpou,
    )


async def test_add_anon_signature_multilateral(monkeypatch, aiohttp_client, s3_emulation):
    monkeypatch.setattr(eusign_utils, 'get_cert_info', fake_get_cert_info)
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=TEST_EDRPOU_1,
        create_billing_account=True,
        feature_flags={'pass_sign_info_to_backend': False},
    )
    document = await prepare_document_data(
        app,
        user,
        expected_owner_signatures=0,
        status_id=DocumentStatus.uploaded.value,
        is_multilateral=True,
        title='hello',
        extension='.xml',
    )

    await _prepare_flows(client, user, document)

    # Create 3 signature from different EDRPOUs without authors
    async with app['db'].acquire() as conn:
        await _sign(monkeypatch, conn, document, TEST_EDRPOU_1, SIGNATURE_KEY_1)
        await _sign(monkeypatch, conn, document, TEST_EDRPOU_2, SIGNATURE_KEY_2)
        await _sign(monkeypatch, conn, document, TEST_EDRPOU_3, SIGNATURE_KEY_3)

        document = await select_one(conn, document_table.select())
        assert document.status_id == DocumentStatus.finished.value
        assert document.date_finished is not None

        signatures = await select_all(conn, signature_table.select())
        assert len(signatures) == 3
        signatures_map = {s.key_owner_edrpou: s for s in signatures}

        signature_1 = signatures_map[TEST_EDRPOU_1]
        assert signature_1.role_id is None
        assert signature_1.user_id is None
        assert signature_1.key_owner_edrpou == TEST_EDRPOU_1
        assert signature_1.key_exists is True

        file_1 = s3_emulation.get_external_signature_key(
            document_id=document.id,
            signature_id=signature_1.id,
        )
        assert file_1.body == decode_base64_str(SIGNATURE_KEY_1)

        signature_2 = signatures_map[TEST_EDRPOU_2]
        assert signature_2.role_id is None
        assert signature_2.user_id is None
        assert signature_2.key_owner_edrpou == TEST_EDRPOU_2
        assert signature_2.key_exists is True

        file_2 = s3_emulation.get_external_signature_key(
            document_id=document.id,
            signature_id=signature_2.id,
        )
        assert file_2.body == decode_base64_str(SIGNATURE_KEY_2)

        signature_3 = signatures_map[TEST_EDRPOU_3]
        assert signature_3.role_id is None
        assert signature_3.user_id is None
        assert signature_3.key_owner_edrpou == TEST_EDRPOU_3
        assert signature_3.key_exists is True

        file_3 = s3_emulation.get_external_signature_key(
            document_id=document.id,
            signature_id=signature_3.id,
        )
        assert file_3.body == decode_base64_str(SIGNATURE_KEY_3)


async def test_add_anon_signature_bilateral(monkeypatch, aiohttp_client, s3_emulation):
    monkeypatch.setattr(eusign_utils, 'get_cert_info', fake_get_cert_info)
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=TEST_EDRPOU_1,
        create_billing_account=True,
        feature_flags={'pass_sign_info_to_backend': False},
    )
    document = await prepare_document_data(
        app,
        user,
        document_recipients=[{'edrpou': TEST_EDRPOU_2, 'emails': [TEST_EMAIL_2]}],
        status_id=DocumentStatus.ready_to_be_signed.value,
        title='hello',
        extension='.xml',
    )

    # Create 2 signature from different EDRPOUs without authors
    async with app['db'].acquire() as conn:
        await _sign(monkeypatch, conn, document, TEST_EDRPOU_1, SIGNATURE_KEY_1)
        await _send(conn, document, TEST_EDRPOU_1)

        document = await select_one(conn, document_table.select())
        assert document.status_id == DocumentStatus.signed_and_sent.value

        await _sign(monkeypatch, conn, document, TEST_EDRPOU_2, SIGNATURE_KEY_2)
        await _send(conn, document, TEST_EDRPOU_2)

        document = await select_one(conn, document_table.select())
        assert document.status_id == DocumentStatus.finished.value
        assert document.date_finished is not None

        signatures = await select_all(conn, signature_table.select())
        assert len(signatures) == 2
        signatures_map = {s.key_owner_edrpou: s for s in signatures}

        signature_1 = signatures_map[TEST_EDRPOU_1]
        assert signature_1.role_id is None
        assert signature_1.user_id is None
        assert signature_1.key_owner_edrpou == TEST_EDRPOU_1
        assert signature_1.key_exists is True

        file_1 = s3_emulation.get_external_signature_key(
            document_id=document.id,
            signature_id=signature_1.id,
        )
        assert file_1.body == decode_base64_str(SIGNATURE_KEY_1)

        signature_2 = signatures_map[TEST_EDRPOU_2]
        assert signature_2.role_id is None
        assert signature_2.user_id is None
        assert signature_2.key_owner_edrpou == TEST_EDRPOU_2
        assert signature_2.key_exists is True

        file_2 = s3_emulation.get_external_signature_key(
            document_id=document.id,
            signature_id=signature_2.id,
        )
        assert file_2.body == decode_base64_str(SIGNATURE_KEY_2)


async def test_add_anon_signature_internal(monkeypatch, aiohttp_client, s3_emulation):
    monkeypatch.setattr(eusign_utils, 'get_cert_info', fake_get_cert_info)
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=TEST_EDRPOU_1,
        create_billing_account=True,
        feature_flags={'pass_sign_info_to_backend': False},
    )
    document = await prepare_document_data(
        app,
        user,
        status_id=DocumentStatus.uploaded.value,
        is_internal=True,
        title='hello',
        extension='.xml',
    )

    async with app['db'].acquire() as conn:
        with pytest.raises(Error) as err:
            await _sign(monkeypatch, conn, document, TEST_EDRPOU_2, SIGNATURE_KEY_2)
        assert err.value.code == Code.access_denied

        await _sign(monkeypatch, conn, document, TEST_EDRPOU_1, SIGNATURE_KEY_1)

        document = await select_one(conn, document_table.select())
        assert document.status_id == DocumentStatus.finished.value
        assert document.date_finished is not None

        signatures = await select_all(conn, signature_table.select())
        assert len(signatures) == 1
        signature_1 = signatures[0]
        assert signature_1.role_id is None
        assert signature_1.user_id is None
        assert signature_1.key_owner_edrpou == TEST_EDRPOU_1
        assert signature_1.key_exists is True

        file_1 = s3_emulation.get_external_signature_key(
            document_id=document.id,
            signature_id=signature_1.id,
        )
        assert file_1.body == decode_base64_str(SIGNATURE_KEY_1)


async def test_add_signature_from_session(monkeypatch, aiohttp_client, eusign_mock):
    """Checks if we correctly add signature by AuthUser from session without role_id"""
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=TEST_EDRPOU_1,
        create_billing_account=True,
        feature_flags={'pass_sign_info_to_backend': False},
    )
    document = await prepare_document_data(
        app,
        user,
        document_recipients=[{'edrpou': TEST_EDRPOU_2, 'emails': [TEST_EMAIL_2]}],
        status_id=DocumentStatus.ready_to_be_signed.value,
        title='hello',
        extension='.xml',
    )

    async with app['db'].acquire() as conn:
        eusign_mock.edrpou = TEST_EDRPOU_1
        await _sign(monkeypatch, conn, document, TEST_EDRPOU_1, SIGNATURE_KEY_1, user=user)
        await _send(conn, document, TEST_EDRPOU_1)

        document = await select_one(conn, document_table.select())
        assert document.status_id == DocumentStatus.signed_and_sent.value

        eusign_mock.edrpou = TEST_EDRPOU_2

        # In web we do not allow to sign document without active role. To register role in
        # sign session we ask user to sign and send registration token separately.
        sign_session = await prepare_sign_session_data(
            app=app,
            user=user,
            document=document,
            edrpou=TEST_EDRPOU_2,
            email=TEST_EMAIL_2,
        )
        sign_data = prepare_signature_form_data(
            recipient_edrpou=TEST_EDRPOU_2,
            recipient_email=TEST_EMAIL_2,
            key='c2lnbmF0dXJlMg==',  # mocked
            append_key_data=False,
            append_stamp_data=False,
        )
        sign_headers = prepare_sign_session_headers(sign_session, client)
        response = await client.post(
            f'/internal-api/documents/{document.id}/signatures',
            data=sign_data,
            headers=sign_headers,
        )
        assert response.status == HTTPStatus.FORBIDDEN
        assert await response.json() == {
            'code': 'login_required',
            'details': {'case': 1},
            'reason': 'Будь ласка, авторизуйтесь для доступу до сторінки',
        }

        # Let's create role in sign session and try to sign again with the same session.
        # In real life this will be done by requesting company registration token, signing it
        # and sending it to the endpoint to create signer role.
        await prepare_user_data(app=app, email=TEST_EMAIL_2, company_edrpou=TEST_EDRPOU_2)

        # Now when we have user exists in sign session, we can sign document with previously
        # created session
        response = await client.post(
            f'/internal-api/documents/{document.id}/signatures',
            data=sign_data,
            headers=sign_headers,
        )
        assert response.status == HTTPStatus.CREATED

        document = await select_one(conn, document_table.select())
        assert document.status_id == DocumentStatus.finished.value
        assert document.date_finished is not None
