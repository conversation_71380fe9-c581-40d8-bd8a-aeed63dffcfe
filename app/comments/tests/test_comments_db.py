import ujson

from app.comments.db import select_document_inbox_email_comment, select_last_external_comment
from app.services import services
from app.tests.common import (
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_document_data_for_email,
    prepare_user_data,
    request_add_comment,
)

TEST_EMAIL_1 = '<EMAIL>'
TEST_EMAIL_2 = '<EMAIL>'

TEST_EDRPOU_1 = '12345678'
TEST_EDRPOU_2 = '87654321'


async def test_select_last_comment(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    data = await prepare_document_data_for_email(app, owner=user)

    headers = prepare_auth_headers(data.recipient)
    url = f'/internal-api/documents/{data.document.id}/comments'
    comment1 = 'comment1'
    comment2 = 'comment2'

    async with app['db'].acquire() as conn:
        comment_result = await select_last_external_comment(conn, data.document.id)
        assert comment_result is None

        data1 = ujson.dumps({'text': comment1})
        await client.post(url, data=data1, headers=headers)
        comment_result = await select_last_external_comment(conn, data.document.id)
        assert comment_result.text == comment1

        data2 = ujson.dumps({'text': comment2})
        await client.post(url, data=data2, headers=headers)
        comment_result = await select_last_external_comment(conn, data.document.id)
        assert comment_result.text == comment2


async def test_select_document_inbox_email_comment(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        company_edrpou=TEST_EDRPOU_1,
    )
    recipient = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_2,
        company_edrpou=TEST_EDRPOU_2,
    )
    document = await prepare_document_data(app, owner=user, another_recipients=[recipient])

    async with services.db.acquire() as conn:
        # Test with no comments
        comment = await select_document_inbox_email_comment(
            conn=conn,
            document_id=document.id,
            sender_edrpou=TEST_EDRPOU_1,
        )
        assert comment is None

        # Internal comment should be not selected
        await request_add_comment(
            client=client,
            user=user,
            document=document,
            comment='Internal owner comment',
            is_internal=True,
        )
        comment = await select_document_inbox_email_comment(
            conn=conn,
            document_id=document.id,
            sender_edrpou=TEST_EDRPOU_1,
        )
        assert comment is None

        # Recipient comment is also not selected
        await request_add_comment(
            client=client,
            user=recipient,
            document=document,
            comment='External recipient comment',
            is_internal=False,
        )
        comment = await select_document_inbox_email_comment(
            conn=conn,
            document_id=document.id,
            sender_edrpou=TEST_EDRPOU_1,
        )
        assert comment is None

        # Owner comment is selected
        await request_add_comment(
            client=client,
            user=user,
            document=document,
            comment='First external owner comment',
            is_internal=False,
        )
        comment = await select_document_inbox_email_comment(
            conn=conn,
            document_id=document.id,
            sender_edrpou=TEST_EDRPOU_1,
        )
        assert comment is not None
        assert comment.text == 'First external owner comment'

        # Second and further comments should not be selected
        await request_add_comment(
            client=client,
            user=user,
            document=document,
            comment='Second external owner comment',
            is_internal=False,
        )
        comment = await select_document_inbox_email_comment(
            conn=conn,
            document_id=document.id,
            sender_edrpou=TEST_EDRPOU_1,
        )
        assert comment is not None
        assert comment.text == 'First external owner comment'

        # Empty when sender is empty, nothing to select
        comment = await select_document_inbox_email_comment(
            conn=conn,
            document_id=document.id,
            sender_edrpou=None,
        )
        assert comment is None
