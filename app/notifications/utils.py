import logging

from app.auth.db import select_base_user
from app.lib.database import DBConnection
from app.lib.types import DataDict, StrDict
from app.lib.urls import build_url
from app.notifications import db
from app.notifications.constants import UNSUBSCRIPTION_KEY
from app.services import services
from app.tokens.utils import generate_jwt_token

logger = logging.getLogger(__name__)


async def prepare_unsubscribe_block(
    email: str | None = None,  # for unregistered subscriptions
    payload: StrDict | None = None,  # for registered subscriptions
) -> DataDict | None:
    """
    Prepare url for unsubscribe block
    - Pass only email if user is unregistered (doesn't have a company)
    - Pass payload dictionary with role_id if user is registered
    """
    if email is not None:
        return await prepare_unsubscribe_unregistered_block(email=email)
    if payload is not None:
        return prepare_unsubscribe_registered_block(payload=payload)

    logger.info('Not able to prepare unsubscribe block', extra={'email': email, 'payload': payload})
    return None


def prepare_unsubscribe_registered_block(payload: StrDict) -> StrDict:
    label = 'Відписатися від сповіщень'
    route_name = 'api.notifications.unsubscribe'

    token = generate_jwt_token(payload, services.config.tokens.private_key)
    logger.info(f'Generated unsubscribe JWT token {token} for {payload["role_id"]} role_id')
    return {
        'label': label,
        'url': build_url(route_name, jwt_token=token),
    }


async def prepare_unsubscribe_unregistered_block(email: str) -> DataDict | None:
    """Build url for unregistered users (exist in service and with any
    active role in profile).
    if user has completed registration just return None
    """
    async with services.db.acquire() as conn:
        user = await select_base_user(conn, email=email)

    if user and user.registration_completed:
        return None

    token = generate_jwt_token(
        payload={
            'email': email,
            'action': UNSUBSCRIPTION_KEY,
        },
        private_key_path=services.config.tokens.private_key,
    )
    return {
        'label': 'Відписатися від сповіщень',
        'url': build_url(
            route='api.notifications.unregistered.unsubscriptions',
            get={'token': token},
        ),
    }


async def is_unsubscribed(conn: DBConnection, email: str) -> bool:
    return await db.is_unsubscribed(conn=conn, email=email)
