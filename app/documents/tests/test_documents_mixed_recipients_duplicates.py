import pytest

from app.auth.enums import RoleStatus
from app.auth.types import User
from app.documents.utils import share_document
from app.lib.enums import UserRole
from app.services import services
from app.tests.common import (
    prepare_client,
    prepare_document_data,
    prepare_user_data,
)


async def _send_to_two_recipients_one_active_one_unreg(
    aiohttp_client,
    *,
    recipient_edrpou: str,
    active_email: str,
    unregistered_email: str,
):
    app, _client, owner_row = await prepare_client(aiohttp_client)

    # Create active admin user for recipient company (will be direct recipient)
    await prepare_user_data(
        app=app,
        email=active_email,
        company_edrpou=recipient_edrpou,
        user_role=UserRole.admin.value,
        role_status=RoleStatus.active,
    )

    # Prepare a document owned by current user
    document = await prepare_document_data(app=app, owner=owner_row)

    # Share document to unregistered email → triggers fallback to admins
    async with services.db.acquire() as conn:
        await share_document(
            conn=conn,
            recipient_edrpou=recipient_edrpou,
            recipient_email=unregistered_email,
            document_id=document.id,
            sender=User.from_row(owner_row),
        )

        # Share document to active user → direct inbox notification
        await share_document(
            conn=conn,
            recipient_edrpou=recipient_edrpou,
            recipient_email=active_email,
            document_id=document.id,
            sender=User.from_row(owner_row),
        )


async def _mailbox_count_for(mailbox, email: str) -> int:
    return len([m for m in mailbox if email in m['To']])


@pytest.mark.asyncio
@pytest.mark.xfail(reason='Historical behavior prior to fix (duplicate emails)')
async def test_mixed_recipient_active_and_unregistered_produces_duplicate_email(
    aiohttp_client, mailbox
):
    """Historical: duplicate emails before fix (kept as xfail for regression context)."""

    recipient_edrpou = '12345678'
    active_email = '<EMAIL>'
    unregistered_email = '<EMAIL>'

    await _send_to_two_recipients_one_active_one_unreg(
        aiohttp_client,
        recipient_edrpou=recipient_edrpou,
        active_email=active_email,
        unregistered_email=unregistered_email,
    )

    count = await _mailbox_count_for(mailbox, active_email)
    assert count >= 2


@pytest.mark.asyncio
async def test_mixed_recipient_active_and_unregistered_no_duplicates_after_fix(
    aiohttp_client, mailbox
):
    """Expected behavior after fix: only one email to the active recipient.

    ER: only one email is received by the active recipient (no fallback duplicate).
    """

    recipient_edrpou = '22334455'
    active_email = '<EMAIL>'
    unregistered_email = '<EMAIL>'

    await _send_to_two_recipients_one_active_one_unreg(
        aiohttp_client,
        recipient_edrpou=recipient_edrpou,
        active_email=active_email,
        unregistered_email=unregistered_email,
    )

    count = await _mailbox_count_for(mailbox, active_email)
    assert count == 1
