from typing import NotRequired, TypedDict

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import insert

from app.documents_ai.enums import (
    DocumentMetaSuggestionReview,
    DocumentMetaSuggestionStatus,
    StructuredDataErrorKey,
)
from app.documents_ai.enums import (
    StructuredDataExtractionStatus as SDAStatus,
)
from app.documents_ai.schemas import (
    DocumentMetaSuggestion,
    DocumentScheduledMetaSuggestion,
    MetaInfo,
)
from app.documents_ai.tables import (
    document_structured_data_table,
    documents_meta_suggestions_table,
    scheduled_document_meta_suggestions_table,
)
from app.documents_ai.types import DocumentDataExtraction
from app.lib.database import DBConnection
from app.lib.types import DataDict
from app.models import select_all, select_one


class InsertScheduledDocumentMetaSuggestionDict(TypedDict):
    document_id: str
    scheduled_by: str
    status: DocumentMetaSuggestionStatus
    suggestion_id: NotRequired[str | None]


class UpdateScheduledDocumentMetaSuggestionDict(TypedDict):
    status: NotRequired[DocumentMetaSuggestionStatus | None]
    suggestion_id: NotRequired[str | None]


class UpdateDocumentMetaSuggestionDict(TypedDict):
    review: NotRequired[DocumentMetaSuggestionReview | None]


async def insert_document_suggestion(
    conn: DBConnection,
    data: DocumentMetaSuggestion,
) -> DocumentMetaSuggestion:
    row = await select_one(
        conn,
        insert(documents_meta_suggestions_table)
        .values(data.to_db())
        .returning(documents_meta_suggestions_table),
    )
    return DocumentMetaSuggestion.from_db(row)


async def select_document_suggestions(
    conn: DBConnection,
    document_id: str,
) -> list[DocumentMetaSuggestion]:
    rows = await select_all(
        conn,
        query=documents_meta_suggestions_table.select().where(
            documents_meta_suggestions_table.c.document_id == document_id
        ),
    )
    return [DocumentMetaSuggestion.from_db(row) for row in rows]


async def insert_or_replace_extractions(
    conn: DBConnection,
    *,
    company_id: str,
    data: list[DocumentDataExtraction],
) -> set[str]:
    """
    If extraction is exists and its in ERROR status - allow to update it.
    """

    to_upsert = set()
    to_ignore = set()
    document_ids = {item.document_id for item in data}
    data_by_document_id = {item.document_id: item for item in data}

    existing_extractions = await select_extractions_for_documents(
        conn, document_ids=list(document_ids), company_id=company_id
    )
    for item in existing_extractions:
        if item.status == SDAStatus.ERROR:
            to_upsert.add(item.document_id)
        else:
            # just ignore documents which already have extractions without errors
            to_ignore.add(item.document_id)

    # process the rest of doc_ids without existing extractions
    for doc_id in document_ids:
        if doc_id not in to_ignore:
            to_upsert.add(doc_id)

    if to_upsert:
        insert_data = [data_by_document_id[doc_id].to_db() for doc_id in to_upsert]
        query = (
            insert(document_structured_data_table)
            .values(insert_data)
            .returning(document_structured_data_table.c.document_id)
        )

        query = query.on_conflict_do_update(
            index_elements=[
                document_structured_data_table.c.company_id,
                document_structured_data_table.c.document_id,
            ],
            set_={
                'date_updated': sa.text('now()'),
                'status': sa.text('EXCLUDED.status'),
                'error_message': sa.text('EXCLUDED.error_message'),
                'llm_name': None,
                'request_duration': None,
                'input_tokens': None,
                'output_tokens': None,
            },
        )
        res = await select_all(conn, query)
        return {row.document_id for row in res}

    return set()


async def update_extraction_status(
    conn: DBConnection,
    *,
    document_id: str,
    company_id: str,
    status: SDAStatus,
    error_message: StructuredDataErrorKey | None = None,
    meta_data: MetaInfo | None = None,
) -> None:
    values: DataDict = {
        'status': status,
        'error_message': error_message.value if error_message else None,
    }
    if meta_data:
        values.update(meta_data.to_db())
    await conn.execute(
        sa.update(document_structured_data_table)
        .where(
            sa.and_(
                document_structured_data_table.c.document_id == document_id,
                document_structured_data_table.c.company_id == company_id,
            )
        )
        .values(values)
    )


async def select_extractions_for_documents(
    conn: DBConnection, *, company_id: str, document_ids: list[str]
) -> list[DocumentDataExtraction]:
    rows = await select_all(
        conn,
        sa.select([document_structured_data_table]).where(
            sa.and_(
                document_structured_data_table.c.document_id.in_(document_ids),
                document_structured_data_table.c.company_id == company_id,
            )
        ),
    )
    return [DocumentDataExtraction.from_row(row) for row in rows]


async def select_document_suggestion_by_id(
    conn: DBConnection,
    suggestion_id: str,
) -> DocumentMetaSuggestion | None:
    row = await select_one(
        conn,
        query=documents_meta_suggestions_table.select().where(
            documents_meta_suggestions_table.c.id == suggestion_id
        ),
    )
    return DocumentMetaSuggestion.from_db(row) if row else None


async def update_document_meta_suggestion(
    conn: DBConnection,
    suggestion_id: str,
    data: UpdateDocumentMetaSuggestionDict,
) -> DocumentMetaSuggestion:
    row = await select_one(
        conn=conn,
        query=(
            documents_meta_suggestions_table.update()
            .values({**data})
            .where(documents_meta_suggestions_table.c.id == suggestion_id)
            .returning(documents_meta_suggestions_table)
        ),
    )

    return DocumentMetaSuggestion.from_db(row)


async def insert_scheduled_document_meta_suggestion(
    conn: DBConnection,
    data: InsertScheduledDocumentMetaSuggestionDict,
) -> DocumentScheduledMetaSuggestion:
    row = await select_one(
        conn,
        insert(scheduled_document_meta_suggestions_table)
        .values(data)
        .returning(scheduled_document_meta_suggestions_table),
    )
    return DocumentScheduledMetaSuggestion.from_db(row)


async def insert_scheduled_documents_meta_suggestions(
    conn: DBConnection,
    records: list[InsertScheduledDocumentMetaSuggestionDict],
) -> list[DocumentScheduledMetaSuggestion]:
    rows = await select_all(
        conn,
        insert(scheduled_document_meta_suggestions_table)
        .values(records)
        .returning(scheduled_document_meta_suggestions_table),
    )
    return [DocumentScheduledMetaSuggestion.from_db(row) for row in rows]


async def select_scheduled_documents_meta_suggestions(
    conn: DBConnection,
    document_ids: list[str] | None = None,
    status: DocumentMetaSuggestionStatus | None = None,
    limit: int | None = None,
) -> list[DocumentScheduledMetaSuggestion]:
    filters = []
    if document_ids:
        filters.append(scheduled_document_meta_suggestions_table.c.document_id.in_(document_ids))
    if status:
        filters.append(scheduled_document_meta_suggestions_table.c.status == status)

    query = (
        scheduled_document_meta_suggestions_table.select()
        .where(sa.and_(*filters))
        .order_by(scheduled_document_meta_suggestions_table.c.date_created.asc())
    )

    if limit is not None:
        query = query.limit(limit)

    rows = await select_all(conn, query=query)
    return [DocumentScheduledMetaSuggestion.from_db(row) for row in rows]


async def update_scheduled_document_meta_suggestions(
    conn: DBConnection,
    document_ids: list[str],
    data: UpdateScheduledDocumentMetaSuggestionDict,
) -> list[DocumentScheduledMetaSuggestion]:
    rows = await select_all(
        conn=conn,
        query=(
            scheduled_document_meta_suggestions_table.update()
            .values({**data, 'date_updated': sa.text('now()')})
            .where(scheduled_document_meta_suggestions_table.c.document_id.in_(document_ids))
            .returning(scheduled_document_meta_suggestions_table)
        ),
    )

    return [DocumentScheduledMetaSuggestion.from_db(row) for row in rows]


async def update_scheduled_document_meta_suggestion(
    conn: DBConnection,
    document_id: str,
    data: UpdateScheduledDocumentMetaSuggestionDict,
) -> DocumentScheduledMetaSuggestion:
    row = await select_one(
        conn=conn,
        query=(
            scheduled_document_meta_suggestions_table.update()
            .values({**data, 'date_updated': sa.text('now()')})
            .where(scheduled_document_meta_suggestions_table.c.document_id == document_id)
            .returning(scheduled_document_meta_suggestions_table)
        ),
    )

    return DocumentScheduledMetaSuggestion.from_db(row)
