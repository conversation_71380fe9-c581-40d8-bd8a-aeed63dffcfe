import logging

from aiohttp import web

from api.errors import InvalidRequest
from api.utils import api_response
from app.auth.db import select_company_by_edrpou
from app.auth.decorators import login_required
from app.auth.types import User
from app.auth.validators import validate_user_permission
from app.documents.db import select_document_by_id
from app.documents.validators import validate_document_access, validate_documents_access
from app.documents_ai import schemas
from app.documents_ai.db import (
    insert_or_replace_extractions,
    select_extractions_for_documents,
    update_document_meta_suggestion,
)
from app.documents_ai.enums import StructuredDataExtractionStatus as SDAStatus
from app.documents_ai.schemas import (
    DocumentMetaSuggestionResponse,
    DocumentSummaryResponse,
    GetStatusResponse,
    GetStatusResponseItem,
    ReviewScheduledDocumentMetaSuggestionSchema,
    StructuredDataModel,
)
from app.documents_ai.types import DocumentDataExtraction
from app.documents_ai.utils import (
    download_document_structured_data,
    get_document_summary,
    suggest_document_meta_by_content,
    suggest_document_meta_by_document_id,
)
from app.documents_ai.validators import (
    validate_document_meta_suggest_enabled,
    validate_review_document_meta_suggestion,
    validate_start_extraction_request,
    validate_upload_file,
)
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.types import DataDict
from app.lib.validators import validate_json_post_request
from app.openapi.decorators import openapi_docs
from app.openapi.types import OpenApiParam
from app.services import services
from worker import topics

logger = logging.getLogger(__name__)


OPENAPI_TAG_AI = 'DOCUMENTS_AI'


@openapi_docs(
    summary=_('Визначити реквізити документа по його вмісту'),
    tags=[OPENAPI_TAG_AI],
    response=DocumentMetaSuggestionResponse,
)
@login_required()
async def document_meta_suggest_by_content(request: web.Request, user: User) -> web.Response:
    await validate_document_meta_suggest_enabled(user.company_id)
    uploaded_file = await validate_upload_file(request)

    suggest_obj = await suggest_document_meta_by_content(
        content=uploaded_file.content,
        extension=uploaded_file.extension,
        owner_edrpou=user.company_edrpou,
    )

    if not suggest_obj:
        return web.json_response(DocumentMetaSuggestionResponse.empty().to_api())

    recipient_company = None
    if suggest_obj.edrpou_recipient is not None:
        async with services.db_readonly.acquire() as conn:
            recipient_company = await select_company_by_edrpou(conn, suggest_obj.edrpou_recipient)

    resp_obj = DocumentMetaSuggestionResponse(
        title=uploaded_file.title,
        category=suggest_obj.category,
        edrpou_recipient=suggest_obj.edrpou_recipient,
        date_document=suggest_obj.date_document,
        number=suggest_obj.number,
        amount=suggest_obj.amount,
        recipient_name=recipient_company.name if recipient_company else None,
        recipient_email=None,
    )
    return api_response(request, data=resp_obj.to_api())


@openapi_docs(
    summary=_('Визначити реквізити документа по document_id'),
    params_path={
        'document_id': OpenApiParam(required=True, schema=pv.UUID),
    },
    response=DocumentMetaSuggestionResponse,
    tags=[OPENAPI_TAG_AI],
)
@login_required()
async def document_meta_suggest_by_id(request: web.Request, user: User) -> web.Response:
    try:
        document_id = validators.validate_pydantic_adapter(
            pv.UUIDAdapter, value=request.match_info['document_id']
        )
    except KeyError:
        raise InvalidRequest()

    await validate_document_meta_suggest_enabled(user.company_id)
    suggest_obj = await suggest_document_meta_by_document_id(document_id, user=user)

    if not suggest_obj:
        return web.json_response(DocumentMetaSuggestionResponse.empty().to_api())

    async with services.db_readonly.acquire() as conn:
        doc_row = await select_document_by_id(conn, document_id=document_id)
        recipient_company = None
        if suggest_obj.edrpou_recipient is not None:
            recipient_company = await select_company_by_edrpou(conn, suggest_obj.edrpou_recipient)

    resp_obj = DocumentMetaSuggestionResponse(
        title=doc_row.title if doc_row else None,
        category=suggest_obj.category,
        edrpou_recipient=suggest_obj.edrpou_recipient,
        date_document=suggest_obj.date_document,
        number=suggest_obj.number,
        amount=suggest_obj.amount,
        recipient_name=recipient_company.name if recipient_company else None,
        recipient_email=None,
    )
    return api_response(request, data=resp_obj.to_api())


@openapi_docs(
    summary=_('Зробити коротке резюме документа по document_id'),
    params_path={
        'document_id': OpenApiParam(required=True, schema=pv.UUID),
    },
    response=DocumentSummaryResponse,
    tags=[OPENAPI_TAG_AI],
)
@login_required()
async def document_summary(request: web.Request, user: User) -> web.Response:
    if not get_flag(FeatureFlags.AI_DOCUMENT_SUMMARY_BLOCK):
        raise InvalidRequest(reason=_('Функціонал не доступний'))

    try:
        document_id = validators.validate_pydantic_adapter(
            pv.UUIDAdapter, value=request.match_info['document_id']
        )
    except KeyError:
        raise InvalidRequest()

    doc_summaries = await get_document_summary(document_id, user=user)
    return api_response(request, data={'data': [item.to_api() for item in doc_summaries]})


@openapi_docs(
    summary='Розпочати витягування структурованих даних з документів',
    request_json=schemas.ExtractionRequest,
    response=schemas.StartExtractionResponse,
    tags=[OPENAPI_TAG_AI],
)
@login_required()
async def start_structured_data_extraction(request: web.Request, user: User) -> web.Response:
    if not get_flag(FeatureFlags.ENABLE_AI_STRUCTURED_DATA_EXTRACTION):
        raise InvalidRequest(reason=_('Функціонал не доступний'))

    validate_user_permission(user, {'can_extract_document_structured_data'})

    body = await validate_json_post_request(request)
    req = validators.validate_pydantic(schemas.ExtractionRequest, body)

    document_ids = req.document_ids
    if not document_ids:
        return api_response(request, data={'data': []})

    job_data_values: list[DataDict] = []

    async with services.db.acquire() as conn:
        ctx = await validate_start_extraction_request(
            conn=conn, user=user, document_ids=document_ids
        )

        upsert_data = [
            DocumentDataExtraction(
                document_id=doc_item.doc_id,
                status=SDAStatus.PENDING,
                company_id=user.company_id,
            )
            for doc_item in ctx.documents
        ]

        document_ids_to_extract = await insert_or_replace_extractions(
            conn, data=upsert_data, company_id=user.company_id
        )

        # Start the extraction job only for documents that were inserted
        for doc_item in ctx.documents:
            if doc_item.doc_id in document_ids_to_extract:
                job_data_values.append(
                    {
                        'document_id': doc_item.doc_id,
                        'version_id': doc_item.version_id,
                        'company_id': user.company_id,
                    }
                )

    if job_data_values:
        await services.kafka.send_records(
            topic=topics.DOCUMENT_STRUCTURED_DATA_EXTRACTION, values=job_data_values
        )

    resp = schemas.StartExtractionResponse(
        data=[
            schemas.GetStatusResponseItem(document_id=doc_id, status=SDAStatus.PENDING)
            for doc_id in document_ids
        ]
    )
    return api_response(request, data=resp.model_dump(mode='json'))


@openapi_docs(
    summary='Отримати статус витягування структурованих даних з документів',
    request_json=schemas.ExtractionRequest,
    response=schemas.GetStatusResponse,
    tags=[OPENAPI_TAG_AI],
)
@login_required()
async def get_structured_data_extraction_status(request: web.Request, user: User) -> web.Response:
    if not get_flag(FeatureFlags.ENABLE_AI_STRUCTURED_DATA_EXTRACTION):
        raise InvalidRequest(reason=_('Функціонал не доступний'))

    body = await validate_json_post_request(request)
    req = validators.validate_pydantic(schemas.ExtractionRequest, body)

    document_ids = req.document_ids
    if not document_ids:
        return api_response(request, data={'data': []})

    async with services.db_readonly.acquire() as conn:
        await validate_documents_access(conn=conn, user=user, doc_ids=document_ids)
        extractions = await select_extractions_for_documents(
            conn, company_id=user.company_id, document_ids=document_ids
        )

    resp = GetStatusResponse(
        data=[
            GetStatusResponseItem(document_id=item.document_id, status=item.status)
            for item in extractions
        ]
    )
    return api_response(request, data=resp.model_dump(mode='json'))


@openapi_docs(
    summary='Отримати витягнуті структуровані дані документа',
    params_path={
        'document_id': OpenApiParam(required=True, schema=pv.UUID),
    },
    response=StructuredDataModel,
    tags=[OPENAPI_TAG_AI],
)
@login_required()
async def get_document_structured_data(request: web.Request, user: User) -> web.Response:
    """Get extracted structured data for a document by document_id"""
    if not get_flag(FeatureFlags.ENABLE_AI_STRUCTURED_DATA_EXTRACTION):
        raise InvalidRequest(reason=_('Функціонал не доступний'))

    try:
        document_id = validators.validate_pydantic_adapter(
            pv.UUIDAdapter, value=request.match_info['document_id']
        )
    except KeyError:
        raise InvalidRequest()

    async with services.db_readonly.acquire() as conn:
        await validate_document_access(conn=conn, user=user, document_id=document_id)

    structured_data = await download_document_structured_data(document_id=document_id, user=user)
    if not structured_data:
        raise InvalidRequest(reason=_('Структуровані дані для документа не знайдено'))

    return api_response(request, data=structured_data.to_api())


@openapi_docs(
    summary=_('Оцінити чи було вірно роспізнано реквізити документу'),
    params_path={
        'suggestion_id': OpenApiParam(required=True, schema=pv.UUID),
    },
)
@login_required()
async def review_document_meta_suggestion(request: web.Request, user: User) -> web.Response:
    data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(
        schema=ReviewScheduledDocumentMetaSuggestionSchema,
        data=data,
    )

    try:
        suggestion_id = validators.validate_pydantic_adapter(
            pv.UUIDAdapter, value=request.match_info['suggestion_id']
        )
    except KeyError:
        raise InvalidRequest()

    await validate_review_document_meta_suggestion(
        suggestion_id=suggestion_id,
        user=user,
    )

    async with services.db.acquire() as conn:
        await update_document_meta_suggestion(
            conn=conn,
            suggestion_id=suggestion_id,
            data={'review': valid_data.review},
        )

    return api_response(request, {}, status=200)
