from http import HTTPStatus
from unittest import mock

import pytest
import ujson
from aiohttp import FormData

from app.document_categories.db import insert_document_category
from app.document_categories.types import PublicDocumentCategory
from app.documents_ai import db, enums, schemas
from app.documents_ai.db import select_extractions_for_documents
from app.documents_ai.enums import StructuredDataExtractionStatus
from app.documents_ai.utils import get_document_structured_data_s3_key
from app.lib import s3_utils
from app.services import services
from app.tests.common import (
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    patch_bedrock_client,
    prepare_auth_headers,
    prepare_client,
    prepare_company_data,
    prepare_document_data,
    prepare_public_document_categories,
    set_company_config,
)


@pytest.mark.parametrize(
    'feature_enabled, expected_status',
    [
        (True, HTTPStatus.OK),
        (False, HTTPStatus.FORBIDDEN),
    ],
)
async def test_suggest_by_doc_content(
    aiohttp_client, monkeypatch, feature_enabled, expected_status
):
    """
    General test for happy path for document AI suggest
    Check internal API for frontend by document content
    """

    app, client, user = await prepare_client(aiohttp_client)
    await set_company_config(
        app,
        company_id=user.company_id,
        allow_suggesting_document_meta_with_ai=feature_enabled,
    )
    patch_bedrock_client(monkeypatch)
    await prepare_public_document_categories(amount=5)

    await prepare_company_data(
        app=app,
        edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        name='TEST_COMPANY_NAME',
    )

    data = FormData()
    data.add_field('file', b'content', filename='test.pdf')

    resp = await client.post(
        '/internal-api/documents/suggest',
        headers=prepare_auth_headers(user),
        data=data,
    )
    assert resp.status == expected_status
    resp_json = await resp.json()

    if feature_enabled:
        assert sorted(
            [
                'title',
                'category',
                'date',
                'number',
                'amount',
                'companyEdrpou',
                'companyName',
                'companyEmail',
            ]
        ) == sorted(resp_json.keys())
        assert resp_json['title'] == 'test'
        assert resp_json['number'] == 'ЕДО - 13722'
        assert resp_json['amount'] == '123.23'
        assert resp_json['companyEdrpou'] == TEST_DOCUMENT_EDRPOU_RECIPIENT
        assert resp_json['companyName'] == 'TEST_COMPANY_NAME'
        assert resp_json['category'] == PublicDocumentCategory.contract.value


async def test_suggest_by_doc_id(aiohttp_client, monkeypatch):
    """
    General test for happy path for document AI suggest
    Check internal API for frontend by document_id
    """

    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')
    patch_bedrock_client(monkeypatch)
    await prepare_public_document_categories(amount=5)

    await prepare_company_data(
        app=app,
        edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        name='TEST_COMPANY_NAME',
    )

    resp = await client.post(
        f'/internal-api/documents/{document.id}/suggest',
        headers=prepare_auth_headers(user),
    )
    assert resp.status == HTTPStatus.OK
    resp_json = await resp.json()

    assert sorted(
        [
            'title',
            'category',
            'date',
            'number',
            'amount',
            'companyEdrpou',
            'companyName',
            'companyEmail',
        ]
    ) == sorted(resp_json.keys())
    assert resp_json['title'] == 'test'
    assert resp_json['number'] == 'ЕДО - 13722'
    assert resp_json['amount'] == '123.23'
    assert resp_json['companyEdrpou'] == TEST_DOCUMENT_EDRPOU_RECIPIENT
    assert resp_json['companyName'] == 'TEST_COMPANY_NAME'
    assert resp_json['category'] == PublicDocumentCategory.contract.value


async def test_internal_document_category_was_suggested(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client)
    await set_company_config(
        app,
        company_id=user.company_id,
        allow_suggesting_document_meta_with_ai=True,
    )

    # Prepare document category
    async with services.db.acquire() as conn:
        document_category = await insert_document_category(
            conn=conn,
            title='INTERNAL DOCUMENT CATEGORY',
            company_id=user.company_id,
        )

    patch_bedrock_client(monkeypatch, document_category.id)

    data = FormData()
    data.add_field('file', b'content', filename='test.pdf')

    resp = await client.post(
        '/internal-api/documents/suggest',
        headers=prepare_auth_headers(user),
        data=data,
    )
    assert resp.status == HTTPStatus.OK
    resp_json = await resp.json()

    assert sorted(
        [
            'title',
            'category',
            'date',
            'number',
            'amount',
            'companyEdrpou',
            'companyName',
            'companyEmail',
        ]
    ) == sorted(resp_json.keys())
    assert resp_json['title'] == 'test'
    assert resp_json['number'] == 'ЕДО - 13722'
    assert resp_json['amount'] == '123.23'
    assert resp_json['companyEdrpou'] == TEST_DOCUMENT_EDRPOU_RECIPIENT
    assert resp_json['category'] == document_category.id


async def test_start_extraction_view(aiohttp_client, monkeypatch):
    # Arrange
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    await prepare_public_document_categories(amount=5)
    document = await prepare_document_data(
        app, owner=user, title='test', category=PublicDocumentCategory.act.value
    )

    # Capture published kafka records
    published = {}

    async def _fake_send_records(*, topic, values):
        published['topic'] = topic
        published['values'] = values

    monkeypatch.setattr(services.kafka, 'send_records', _fake_send_records)

    # Act
    resp = await client.post(
        '/internal-api/documents/ai/extract-data',
        json={'document_ids': [document.id]},
        headers=prepare_auth_headers(user),
    )
    assert resp.status == HTTPStatus.OK
    body = await resp.json()
    assert body['data'] and body['data'][0]['document_id'] == document.id

    # Assert
    # After request, DB should have a PENDING row and kafka published
    async with services.db.acquire() as conn:
        rows = await select_extractions_for_documents(
            conn, company_id=user.company_id, document_ids=[document.id]
        )
        assert len(rows) == 1
        assert rows[0].status == StructuredDataExtractionStatus.PENDING.value

    assert 'values' in published
    assert published['values'][0]['document_id'] == document.id


async def test_get_extractions_status(aiohttp_client, monkeypatch):
    # Arrange
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    await prepare_public_document_categories(amount=5)
    document = await prepare_document_data(
        app, owner=user, title='test', category=PublicDocumentCategory.act.value
    )

    # avoid extraction task calls
    monkeypatch.setattr(services.kafka, 'send_records', mock.AsyncMock())

    resp = await client.post(
        '/internal-api/documents/ai/extract-data',
        json={'document_ids': [document.id]},
        headers=prepare_auth_headers(user),
    )
    assert resp.status == HTTPStatus.OK
    body = await resp.json()
    assert body['data'] and body['data'][0]['document_id'] == document.id

    # Act
    resp = await client.post(
        '/internal-api/documents/ai/extract-data-status',
        json={'document_ids': [document.id]},
        headers=prepare_auth_headers(user),
    )
    assert resp.status == HTTPStatus.OK
    body = await resp.json()

    # Assert
    assert body['data'] and body['data'][0]['document_id'] == document.id
    assert body['data'][0]['status'] == StructuredDataExtractionStatus.PENDING.value


async def test_get_extracted_data_success(aiohttp_client, monkeypatch, s3_emulation):
    """Test successful retrieval of extracted structured data"""

    # Arrange
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')

    structured_data = {
        'extracted_data': {
            'details': {
                'number': {
                    'value': 'DOC-123',
                    'bboxes': [{'bbox': [10, 20, 30, 40], 'page': 1}],
                },
                'date_created': {
                    'value': '2024-01-15',
                    'bboxes': [{'bbox': [50, 60, 70, 80], 'page': 1}],
                },
            },
            'parties_information': {
                'customer': {
                    'company_name': {'value': 'Test Company', 'bboxes': []},
                    'edrpou': {'value': '12345678', 'bboxes': []},
                }
            },
            'items': [
                {
                    'name': {'value': 'Product 1', 'bboxes': []},
                    'price': {'value': 100.0, 'bboxes': []},
                }
            ],
            'total_price': {
                'total_price_with_vat': {'value': 120.0, 'bboxes': []},
            },
        }
    }

    await s3_emulation.upload(
        s3_utils.UploadFile(
            key=get_document_structured_data_s3_key(document.id),
            body=ujson.dumps(structured_data).encode(),
        )
    )

    # Act
    resp = await client.get(
        f'/internal-api/documents/{document.id}/ai/structured-data',
        headers=prepare_auth_headers(user),
    )

    assert resp.status == HTTPStatus.OK
    body = await resp.json()

    # Assert
    assert 'extracted_data' in body
    assert body['extracted_data']['details']['number']['value'] == 'DOC-123'
    assert (
        body['extracted_data']['parties_information']['customer']['company_name']['value']
        == 'Test Company'
    )
    assert len(body['extracted_data']['items']) == 1
    assert body['extracted_data']['items'][0]['name']['value'] == 'Product 1'


async def test_review_document_meta_suggestion(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app=app, owner=user)

    async with services.db.acquire() as conn:
        suggestion = await db.insert_document_suggestion(
            conn=conn,
            data=schemas.DocumentMetaSuggestion(
                document_id=document.id,
                category=None,
                edrpou=None,
                date=None,
                number=None,
                amount=None,
                request_duration=100,
                chunk_length=100,
                tokens_used=100,
                model=enums.ModelName.claude_haiku_3,
            ),
        )

    response = await client.post(
        f'/internal-api/documents/suggestions/{suggestion.id}/review',  # type: ignore
        json={'review': enums.DocumentMetaSuggestionReview.valid},
        headers=prepare_auth_headers(user),
    )
    assert response.status == 200

    async with services.db.acquire() as conn:
        suggestion = await db.select_document_suggestion_by_id(
            conn=conn,
            suggestion_id=suggestion.id,  # type: ignore
        )

    assert suggestion.review == enums.DocumentMetaSuggestionReview.valid

    response = await client.post(
        f'/internal-api/documents/suggestions/{suggestion.id}/review',  # type: ignore
        json={'review': enums.DocumentMetaSuggestionReview.invalid},
        headers=prepare_auth_headers(user),
    )
    assert response.status == 400

    data = await response.json()
    assert data == {
        'code': 'invalid_request',
        'details': {},
        'reason': 'Підбір реквізитів вже було оцінено',
    }
