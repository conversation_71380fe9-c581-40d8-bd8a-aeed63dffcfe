from aiohttp import web

from app.auth.types import User
from app.feedbacks import db, validators
from app.services import services


async def add_feedback(request: web.Request, user: User) -> web.Response:
    valid_data = await validators.validate_add_feedback(request)
    async with services.db.acquire() as conn:
        data = valid_data.model_dump(mode='json', exclude_unset=True)
        data['role_id'] = user.role_id
        await db.insert_feedback(conn=conn, data=data)
    return web.HTTPCreated()


async def get_ai_survey(_: web.Request, user: User) -> web.Response:
    async with services.db.acquire() as conn:
        survey = await db.select_ai_survey(conn=conn, user_id=user.id)
    return web.json_response(survey.model_dump(mode='json', by_alias=True) if survey else {})


async def add_ai_survey(request: web.Request, user: User) -> web.Response:
    valid_data = await validators.validate_upsert_ai_survey(request)
    async with services.db.acquire() as conn:
        data = valid_data.model_dump(mode='json')
        data['user_id'] = user.id
        data['role_id'] = user.role_id
        await db.upsert_ai_survey(conn=conn, data=data)
    return web.HTTPCreated()
