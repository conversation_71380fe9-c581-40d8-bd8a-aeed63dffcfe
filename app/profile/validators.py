from __future__ import annotations

import copy
import logging
from datetime import timed<PERSON><PERSON>
from typing import (
    Self,
    TypedDict,
)

import jwt
import pydantic
import trafaret as t
from aiohttp import web
from pydantic import BaseModel, Field, model_validator
from trafaret_validator import TrafaretValidator

from api.errors import (
    AccessDenied,
    AlreadyExists,
    Code,
    DoesNotExist,
    Error,
    InvalidRequest,
    Object,
)
from app.auth.constants import HRS_NOT_BILLABLE_PERMISSIONS, USER_PERMISSIONS
from app.auth.db import (
    exists_role_by_user_id,
    get_super_admin_permissions,
    is_user_email_exists,
    select_base_user,
    select_company_by_edrpou,
    select_company_by_id,
    select_latest_user_email_changes,
    select_role_by,
    select_role_by_id,
    select_user_companies,
    select_user_email_change,
)
from app.auth.enums import (
    InviteSource,
    RoleActivationSource,
    RoleStatus,
    UserEmailChangeStatus,
)
from app.auth.helpers import autogenerate_password
from app.auth.schemas import (
    AntivirusSettings,
    ArchiveSettings,
    DefaultRolePermissionsCoworker,
    DefaultRolePermissionsKey,
    UploadsConfig,
)
from app.auth.types import (
    AuthUser,
    BaseUser,
    Company,
    Role,
    UpdateRoleDict,
    User,
    UserEmailChange,
)
from app.auth.utils import (
    get_company_config,
    get_user,
    has_permission,
    has_super_admin_access,
    is_active_role,
    is_super_admin_company,
)
from app.auth.validators import (
    validate_add_employee,
    validate_is_user_current_password_valid,
    validate_user_permission,
)
from app.billing.api import activate_free_companies_free_rate
from app.billing.constants import LIMIT_NAMES
from app.billing.enums import BillPaymentStatus, CompanyLimit, CompanyPermission
from app.billing.types import BillingCompanyConfig
from app.billing.utils import get_billing_company_config, get_rate_limit, is_allowed_by_rate
from app.document_automation.db import count_document_automation_templates
from app.documents_fields.db import count_document_fields
from app.esputnik.enums import SubscriptionSource
from app.i18n import _
from app.i18n.types import LazyI18nString
from app.lib import validators, validators_pydantic
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import ONE_HOUR_DELTA, utc_now
from app.lib.enums import DocumentListSortDate, Language, SignatureFormat, UserRole
from app.lib.helpers import validate_rate_limit
from app.lib.types import DataDict, StrDict, StrList
from app.profile.emailing import (
    USER_EMAIL_CHANGE_CONFIRM_TOKEN_ACTION,
    USER_EMAIL_CHANGE_VERIFY_EMAIL_TOKEN_ACTION,
)
from app.profile.schemas import (
    ConfirmUserEmailChangeSchema,
    StartUserEmailChangeSchema,
    UpdateUserValidator,
    VerifyUserEmailChange2FASchema,
    VerifyUserEmailChangePasswordSchema,
    VerifyUserEmailChangeSchema,
)
from app.profile.types import (
    EsputnikSubscriptionCtx,
    PermissionEditRole,
    RecoverPasswordCtx,
    UpdatePasswordCtx,
    UpdatePhoneCtx,
    UpdateRoleCtx,
    UpdateUserDataCtx,
)
from app.profile.utils import get_phone_verification_otp, get_recover_password_jwt
from app.registration.types import AddActiveRoleCtx
from app.registration.utils import is_valid_email_domains
from app.registration.validators import validate_base_add_company
from app.services import services
from app.tokens.utils import verify_jwt_token

logger = logging.getLogger(__name__)
DEFAULT_PERMISSION_ERROR_MESSAGE = _('Функціонал не доступний для компанії')


DENY_ROLE_TRAFARET = t.Dict(
    {
        'role_id': validators.UUID(),
    }
)


class UpdateLanguageValidator(TrafaretValidator):
    language = validators.Language()


class UpdateMobileUsageValidator(TrafaretValidator):
    mobile_usage = t.Bool()


class UpdateTrialAutoEnabledValidator(BaseModel):
    trial_auto_enabled: bool


SUPER_ADMIN_PERMISSIONS_TRAFARET = t.Dict(
    {
        t.Key('can_view_client_data', optional=True): t.Bool(),
        t.Key('can_edit_client_data', optional=True): t.Bool(),
        t.Key('can_edit_special_features', optional=True): t.Bool(),
    }
)


class UpdateRoleSchema(BaseModel):
    """
    Pydantic schema for validating input upon role update
    """

    allowed_ips: list[validators_pydantic.IP] | None = None
    allowed_api_ips: list[validators_pydantic.IP] | None = Field(None, max_length=1000)
    user_role: UserRole | None = None

    # Permissions
    can_view_document: bool | None = None
    can_comment_document: bool | None = None
    can_upload_document: bool | None = None
    can_download_document: bool | None = None
    can_print_document: bool | None = None
    can_delete_document: bool | None = None
    can_sign_and_reject_document: bool | None = None
    can_sign_and_reject_document_external: bool | None = None
    can_sign_and_reject_document_internal: bool | None = None
    can_invite_coworkers: bool | None = None
    can_edit_company: bool | None = None
    can_edit_roles: bool | None = None
    can_create_tags: bool | None = None
    can_edit_document_automation: bool | None = None
    can_edit_document_fields: bool | None = None
    can_edit_document_category: bool | None = None
    can_extract_document_structured_data: bool | None = None
    can_edit_document_structured_data: bool | None = None
    can_archive_documents: bool | None = None
    can_delete_archived_documents: bool | None = None
    can_edit_templates: bool | None = None
    can_edit_directories: bool | None = None
    can_remove_itself_from_approval: bool | None = None
    can_edit_security: bool | None = None
    can_download_actions: bool | None = None
    can_change_document_signers_and_reviewers: bool | None = None
    can_delete_document_extended: bool | None = None
    can_edit_company_contact: bool | None = None
    can_edit_required_fields: bool | None = None
    can_view_private_document: bool | None = None
    can_view_coworkers: bool | None = None
    can_edit_version_settings: bool | None = None
    can_add_document_versions: bool | None = None
    can_delete_other_versions: bool | None = None

    # Notifications
    # NOTICE: do not forget to update `can_update_without_permission` property
    can_receive_inbox: bool | None = None
    can_receive_inbox_as_default: bool | None = None
    can_receive_comments: bool | None = None
    can_receive_rejects: bool | None = None
    can_receive_reminders: bool | None = None
    can_receive_reviews: bool | None = None
    can_receive_review_process_finished: bool | None = None
    can_receive_review_process_finished_assigner: bool | None = None
    can_receive_sign_process_finished: bool | None = None
    can_receive_sign_process_finished_assigner: bool | None = None
    can_receive_notifications: bool | None = None
    can_receive_access_to_doc: bool | None = None
    can_receive_delete_requests: bool | None = None
    can_receive_finished_docs: bool | None = None
    can_receive_new_roles: bool | None = None
    can_receive_token_expiration: bool | None = None
    can_receive_email_change: bool | None = None
    can_receive_admin_role_deletion: bool | None = None

    # Misc
    show_invite_tooltip: bool | None = None  # Regular users can disable showing invite tooltips
    show_child_documents: bool | None = None  # Change visibility of child documents in listings
    position: str | None = Field(max_length=2048, default=None)
    sort_documents: DocumentListSortDate | None = None
    status: RoleStatus | None = None
    super_admin_permissions: DataDict | None = None

    def to_update_dict(self) -> UpdateRoleDict:  # noqa: C901
        data: UpdateRoleDict = {}

        if self.allowed_ips is not None:
            data['allowed_ips'] = self.allowed_ips
        if self.allowed_api_ips is not None:
            data['allowed_api_ips'] = self.allowed_api_ips
        if self.user_role is not None:
            data['user_role'] = self.user_role

        # Permissions
        if self.can_view_document is not None:
            data['can_view_document'] = self.can_view_document
        if self.can_comment_document is not None:
            data['can_comment_document'] = self.can_comment_document
        if self.can_upload_document is not None:
            data['can_upload_document'] = self.can_upload_document
        if self.can_download_document is not None:
            data['can_download_document'] = self.can_download_document
        if self.can_print_document is not None:
            data['can_print_document'] = self.can_print_document
        if self.can_delete_document is not None:
            data['can_delete_document'] = self.can_delete_document
        if self.can_sign_and_reject_document is not None:
            data['can_sign_and_reject_document'] = self.can_sign_and_reject_document
            # Set both external and internal permissions for backward compatibility
            data['can_sign_and_reject_document_external'] = self.can_sign_and_reject_document
            data['can_sign_and_reject_document_internal'] = self.can_sign_and_reject_document
        if self.can_sign_and_reject_document_external is not None:
            data['can_sign_and_reject_document_external'] = (
                self.can_sign_and_reject_document_external
            )
        if self.can_sign_and_reject_document_internal is not None:
            data['can_sign_and_reject_document_internal'] = (
                self.can_sign_and_reject_document_internal
            )
        if self.can_invite_coworkers is not None:
            data['can_invite_coworkers'] = self.can_invite_coworkers
        if self.can_edit_company is not None:
            data['can_edit_company'] = self.can_edit_company
        if self.can_edit_roles is not None:
            data['can_edit_roles'] = self.can_edit_roles
        if self.can_create_tags is not None:
            data['can_create_tags'] = self.can_create_tags
        if self.can_edit_document_automation is not None:
            data['can_edit_document_automation'] = self.can_edit_document_automation
        if self.can_edit_document_fields is not None:
            data['can_edit_document_fields'] = self.can_edit_document_fields
        if self.can_edit_document_category is not None:
            data['can_edit_document_category'] = self.can_edit_document_category
        if self.can_extract_document_structured_data is not None:
            data['can_extract_document_structured_data'] = self.can_extract_document_structured_data
        if self.can_edit_document_structured_data is not None:
            data['can_edit_document_structured_data'] = self.can_edit_document_structured_data
        if self.can_archive_documents is not None:
            data['can_archive_documents'] = self.can_archive_documents
        if self.can_delete_archived_documents is not None:
            data['can_delete_archived_documents'] = self.can_delete_archived_documents
        if self.can_edit_templates is not None:
            data['can_edit_templates'] = self.can_edit_templates
        if self.can_edit_directories is not None:
            data['can_edit_directories'] = self.can_edit_directories
        if self.can_remove_itself_from_approval is not None:
            data['can_remove_itself_from_approval'] = self.can_remove_itself_from_approval
        if self.can_edit_security is not None:
            data['can_edit_security'] = self.can_edit_security
        if self.can_download_actions is not None:
            data['can_download_actions'] = self.can_download_actions
        if self.can_change_document_signers_and_reviewers is not None:
            data['can_change_document_signers_and_reviewers'] = (
                self.can_change_document_signers_and_reviewers
            )
        if self.can_delete_document_extended is not None:
            data['can_delete_document_extended'] = self.can_delete_document_extended
        if self.can_edit_company_contact is not None:
            data['can_edit_company_contact'] = self.can_edit_company_contact
        if self.can_edit_required_fields is not None:
            data['can_edit_required_fields'] = self.can_edit_required_fields
        if self.can_view_private_document is not None:
            data['can_view_private_document'] = self.can_view_private_document
        if self.can_view_coworkers is not None:
            data['can_view_coworkers'] = self.can_view_coworkers
        if self.can_edit_version_settings is not None:
            data['can_edit_version_settings'] = self.can_edit_version_settings
        if self.can_add_document_versions is not None:
            data['can_add_document_versions'] = self.can_add_document_versions
        if self.can_delete_other_versions is not None:
            data['can_delete_other_versions'] = self.can_delete_other_versions

        # Notifications
        if self.can_receive_inbox is not None:
            data['can_receive_inbox'] = self.can_receive_inbox
        if self.can_receive_inbox_as_default is not None:
            data['can_receive_inbox_as_default'] = self.can_receive_inbox_as_default
        if self.can_receive_comments is not None:
            data['can_receive_comments'] = self.can_receive_comments
        if self.can_receive_rejects is not None:
            data['can_receive_rejects'] = self.can_receive_rejects
        if self.can_receive_reminders is not None:
            data['can_receive_reminders'] = self.can_receive_reminders
        if self.can_receive_reviews is not None:
            data['can_receive_reviews'] = self.can_receive_reviews
        if self.can_receive_review_process_finished is not None:
            data['can_receive_review_process_finished'] = self.can_receive_review_process_finished
        if self.can_receive_review_process_finished_assigner is not None:
            data['can_receive_review_process_finished_assigner'] = (
                self.can_receive_review_process_finished_assigner
            )
        if self.can_receive_sign_process_finished is not None:
            data['can_receive_sign_process_finished'] = self.can_receive_sign_process_finished
        if self.can_receive_sign_process_finished_assigner is not None:
            data['can_receive_sign_process_finished_assigner'] = (
                self.can_receive_sign_process_finished_assigner
            )
        if self.can_receive_notifications is not None:
            data['can_receive_notifications'] = self.can_receive_notifications
        if self.can_receive_access_to_doc is not None:
            data['can_receive_access_to_doc'] = self.can_receive_access_to_doc
        if self.can_receive_delete_requests is not None:
            data['can_receive_delete_requests'] = self.can_receive_delete_requests
        if self.can_receive_finished_docs is not None:
            data['can_receive_finished_docs'] = self.can_receive_finished_docs
        if self.can_receive_new_roles is not None:
            data['can_receive_new_roles'] = self.can_receive_new_roles
        if self.can_receive_token_expiration is not None:
            data['can_receive_token_expiration'] = self.can_receive_token_expiration
        if self.can_receive_email_change is not None:
            data['can_receive_email_change'] = self.can_receive_email_change
        if self.can_receive_admin_role_deletion is not None:
            data['can_receive_admin_role_deletion'] = self.can_receive_admin_role_deletion

        # Misc
        if self.show_invite_tooltip is not None:
            data['show_invite_tooltip'] = self.show_invite_tooltip
        if self.show_child_documents is not None:
            data['show_child_documents'] = self.show_child_documents
        if self.position is not None:
            data['position'] = self.position
        if self.sort_documents is not None:
            data['sort_documents'] = self.sort_documents
        if self.status is not None:
            data['status'] = self.status

        return data

    @model_validator(mode='after')
    def validate_at_least_one_parameter(self) -> Self:
        if not self.model_dump(exclude_none=True):
            raise ValueError('Provide at least one parameter')
        return self

    def update_can_receive_notifications(self) -> None:
        if self.can_receive_notifications is not None:
            self.can_receive_inbox = self.can_receive_notifications
            self.can_receive_inbox_as_default = self.can_receive_notifications
            self.can_receive_comments = self.can_receive_notifications
            self.can_receive_rejects = self.can_receive_notifications
            self.can_receive_reminders = self.can_receive_notifications
            self.can_receive_reviews = self.can_receive_notifications
            self.can_receive_review_process_finished = self.can_receive_review_process_finished
            self.can_receive_review_process_finished_assigner = (
                self.can_receive_review_process_finished_assigner
            )
            self.can_receive_sign_process_finished = self.can_receive_sign_process_finished
            self.can_receive_sign_process_finished_assigner = (
                self.can_receive_sign_process_finished_assigner
            )
            self.can_receive_notifications = self.can_receive_notifications
            self.can_receive_access_to_doc = self.can_receive_notifications
            self.can_receive_delete_requests = self.can_receive_notifications
            self.can_receive_finished_docs = self.can_receive_notifications
            self.can_receive_new_roles = self.can_receive_notifications
            self.can_receive_token_expiration = self.can_receive_notifications
            self.can_receive_email_change = self.can_receive_notifications
            self.can_receive_admin_role_deletion = self.can_receive_notifications

    @property
    def can_update_without_permission(self) -> bool:
        """
        Validate whether update request could be processed
        without permission check (e.g. is_admin etc.)
        """

        # Warning: update this list carefully.
        # You could allow by mistake for all clients to update settings
        keys_to_update_without_permission = [
            'can_receive_inbox',
            'can_receive_inbox_as_default',
            'can_receive_comments',
            'can_receive_rejects',
            'can_receive_reminders',
            'can_receive_reviews',
            'can_receive_notifications',
            'can_receive_access_to_doc',
            'can_receive_delete_requests',
            'can_receive_finished_docs',
            'can_receive_review_process_finished',
            'can_receive_review_process_finished_assigner',
            'can_receive_sign_process_finished',
            'can_receive_sign_process_finished_assigner',
            'can_receive_new_roles',
            'can_receive_token_expiration',
            'can_receive_email_change',
            'can_receive_admin_role_deletion',
            'sort_documents',
            'show_invite_tooltip',
            'show_child_documents',
            'position',
        ]

        # Never delete this line
        assert 'super_admin_permissions' not in keys_to_update_without_permission

        for key in self.model_dump(exclude_none=True):
            if key not in keys_to_update_without_permission:
                return False
        return True


class UpdateAPIConfigSA(TypedDict):
    retry_delay_min: int
    send_document_status_url: str
    request_timeout: int
    retries: int


class UpdateBaseCompanyConfigSchemaSA(pydantic.BaseModel):
    """
    Selected fields from BaseCompanyConfig that allowed to be updated by super admin
    """

    model_config = pydantic.ConfigDict(extra='forbid', validate_assignment=True)

    allow_send_document_status: bool = False
    allow_send_document_finish_status: bool = False
    api: UpdateAPIConfigSA | None = None  # Value "None" is valid for this field
    allow_parent_company_pay_for_documents: bool = False
    paid_internal_documents: bool = False
    hide_sender_email_in_notifications: bool = False
    archive_settings: ArchiveSettings | None = None
    soft_access_to_tags: bool = False

    antivirus_settings: AntivirusSettings | None = None
    default_role_permissions_key: DefaultRolePermissionsKey | None = None
    default_role_permissions: DefaultRolePermissionsCoworker | None = None

    allowed_signature_types: list[SignatureFormat] | None = None

    @pydantic.model_validator(mode='after')
    def _not_allow_none_as_set_value(self) -> Self:
        """
        For some fields, we use "None" as "not set" value. Those fields are not allowed to pass
        in the request to be able to use the "exclude_unset" option during serialization.
        """
        pv.model_validate_no_explicit_nulls(
            model=self,
            include={
                'antivirus_settings',
                'default_role_permissions_key',
                'default_role_permissions',
            },
        )
        return self


class UpdateAdminCompanyConfigSchemaSA(pydantic.BaseModel):
    """
    Selected fields from AdminCompanyConfig that allowed to be updated by super admin
    """

    model_config = pydantic.ConfigDict(extra='forbid', validate_assignment=True)

    enable_2fa_for_internal_users: bool = False
    allow_pay_as_recipient: bool = False
    parent_company: str | None = None  # Value "None" is valid for this field
    allow_parent_company_pay_for_documents: bool = False

    uploads: UploadsConfig | None = None

    @pydantic.model_validator(mode='after')
    def _not_allow_none_as_set_value(self) -> Self:
        """
        For some fields, we use "None" as "not set" value. Those fields are not allowed to pass
        in the request to be able to use the "exclude_unset" option during serialization.
        """
        pv.model_validate_no_explicit_nulls(
            model=self,
            include={'uploads'},
        )
        return self


class UpdateCompanyConfigSchemaSA(pydantic.BaseModel):
    """
    Schema for updating company config by super admin.
    """

    model_config = pydantic.ConfigDict(extra='forbid', validate_assignment=True)

    company_id: pv.UUID
    config: UpdateBaseCompanyConfigSchemaSA | None = None
    admin_config: UpdateAdminCompanyConfigSchemaSA | None = None

    def to_config_data(self) -> DataDict:
        if not self.config:
            return {}
        return self.config.model_dump(exclude_unset=True, mode='json')

    def to_admin_config_data(self) -> DataDict:
        if not self.admin_config:
            return {}
        return self.admin_config.model_dump(exclude_unset=True, mode='json')


class AgreeOnRolesValidator(TrafaretValidator):
    role_ids = t.List(validators.UUID())


class DeleteRoleValidator(TrafaretValidator):
    role_id = validators.UUID()


class RecoverPasswordSchema(pydantic.BaseModel):
    token: str = pydantic.Field(min_length=1, max_length=1_000_000)  # ~ 1 MB
    password: pv.Password


class RemindPasswordValidator(TrafaretValidator):
    email = validators.email()


class UpdateBillingCompanyConfigSchema(pydantic.BaseModel):
    company_id: pv.UUID
    config: BillingCompanyConfig


class UpdateBillStatusSchema(pydantic.BaseModel):
    bill_id: str
    status: BillPaymentStatus

    @pydantic.model_validator(mode='after')
    def validate_filters(self) -> UpdateBillStatusSchema:
        if self.status not in (BillPaymentStatus.canceled, BillPaymentStatus.refund):
            raise InvalidRequest(reason=_('Invalid value. Only "canceled" / "refund" allowed'))
        return self


class UpdateDefaultRecipientValidator(TrafaretValidator):
    role_id = validators.UUID() | t.Null()


class EsputnikSubscriptionValidator(TrafaretValidator):
    name = t.String(max_length=64)
    email = validators.email()
    source = validators.Enum(SubscriptionSource)


class UpdateCompanySchema(pydantic.BaseModel):
    ipn: pv.IPN | None = None
    phone: pv.Phone | None = None
    render_signature_in_interface: bool | None = None
    render_signature_on_print_document: bool | None = None
    render_review_in_interface: bool | None = None
    render_review_on_print_document: bool | None = None

    def to_company_data(self) -> DataDict:
        """
        Convert schema to dict suitable for company update
        """
        return self.model_dump(exclude_unset=True)

    def to_event_data(self, company: Company) -> DataDict:
        """
        Convert schema to dict suitable for update company config
        """

        # Add to event only fields that were changed
        data = self.model_dump(exclude_unset=True)
        for key, value in copy.deepcopy(data).items():
            if company and getattr(company, key) == value:
                data.pop(key, None)

        return data


class UpdatePasswordValidator(TrafaretValidator):
    current_password = validators.InsecurePassword() | t.Null()
    new_password = validators.password()


class UpdateUserPhoneSchema(pydantic.BaseModel):
    phone: pv.Phone

    # next fields required when user try to update verified phone number
    password: str | None = pydantic.Field(None, min_length=7, max_length=255)
    code: str | None = pydantic.Field(None, min_length=6, max_length=6)


class VerifyPhoneSchema(pydantic.BaseModel):
    code: str


class SendVerifyPhoneScheme(pydantic.BaseModel):
    phone: pv.Phone

    # required when user try to update verified phone number
    password: str | None = pydantic.Field(None, min_length=7, max_length=255)


class UpdateAuthPhoneSchema(pydantic.BaseModel):
    enable: bool


async def validate_role_exists(conn: DBConnection, role_id: str) -> Role:
    """Check role exists"""
    role = await select_role_by_id(conn, role_id=role_id)
    if role is None:
        raise DoesNotExist(Object.role, id=role_id)

    return role


async def validate_add_company(
    conn: DBConnection,
    user: BaseUser,
    data: DataDict,
    raise_already_exists: bool = True,
) -> AddActiveRoleCtx:
    """Trivial validator of adding new company to user.

    As of now any user can be added to any company we don't do any extra checks
    here. Going forward we'll need to verify that user has EDRPOU on backend
    and do not allow to fish data. Same to registration complete action.
    """

    valid_data = await validate_base_add_company(conn, user, data)
    user_id = user.id

    already_exists = False
    role = await select_role_by(conn, valid_data.company_edrpou, user_id, valid_data.is_legal)
    if role and is_active_role(role):
        already_exists = True
        logger.info('Role already exists', extra={'role_id': role.id_})
        if raise_already_exists:
            raise AlreadyExists(
                Object.role,
                edrpou=valid_data.company_edrpou,
                user_id=user_id,
                is_legal=valid_data.is_legal,
            )

    return AddActiveRoleCtx(
        user=user,
        edrpou=valid_data.company_edrpou,
        activated_by=None,
        activation_source=RoleActivationSource.signature,
        company_name=valid_data.company_name,
        user_name=valid_data.name,
        first_name=valid_data.owner_first_name,
        second_name=valid_data.owner_second_name,
        last_name=valid_data.owner_last_name,
        already_exists=already_exists,
        invited_type=(valid_data.invited_type and InviteSource(valid_data.invited_type)),
        invited_edrpou=valid_data.invited_edrpou,
        signature_info=valid_data.signature_info,
    )


async def validate_company_exists(conn: DBConnection, company_id: str) -> DBRow:
    company = await select_company_by_id(conn, company_id)
    if not company:
        raise DoesNotExist(Object.company, id=company_id)
    return company


async def validate_user_email_not_exists(conn: DBConnection, email: str) -> None:
    if await is_user_email_exists(conn, email=email, exclude_placeholder=False):
        raise AlreadyExists(Object.user, email=email)


async def validate_company_by_edrpou_exists(conn: DBConnection, edrpou: str) -> DBRow:
    company = await select_company_by_edrpou(conn, edrpou=edrpou)
    if not company:
        raise DoesNotExist(Object.company, edrpou=edrpou)
    return company


def _validate_obsolete_permission(
    count: int,
    config: BillingCompanyConfig,
    limit: CompanyLimit,
    permission: CompanyPermission,
    error_message: LazyI18nString,
) -> None:
    limit_val = get_rate_limit(config, limit)
    if is_allowed_by_rate(config, permission) or limit_val is None or count <= limit_val:
        return

    if limit_val < count:
        entity = LIMIT_NAMES[limit]
        raise AccessDenied(
            reason=_(
                'У вашій компанії створена максимальна кількість '
                '{entity}, що доступна на поточному тарифі'
            ).format(entity=entity)
        )
    if not is_allowed_by_rate(config, permission):
        raise AccessDenied(reason=error_message)


async def validate_obsolete_company_permission(
    conn: DBConnection,
    company_id: str,
    config: BillingCompanyConfig,
    permission: CompanyPermission,
    *,
    error_message: LazyI18nString,
    new_entity_count: int,
) -> None:
    """
    Obsolete permissions are now regulated by limits per rate, but as we still
    have to deal with old pro rates, validate both limit and permission.
    """
    from app.tags.db import count_tags

    if not permission.is_obsolete:
        validate_company_permission_by_config(
            config=config,
            permission=permission,
            error_message=error_message,
        )
        return

    if permission == CompanyPermission.tags_enabled:
        count = await count_tags(conn, company_id)
        overall_count = count + new_entity_count
        _validate_obsolete_permission(
            count=overall_count,
            config=config,
            limit=CompanyLimit.tags,
            permission=permission,
            error_message=error_message,
        )
    elif permission == CompanyPermission.document_templates_enabled:
        count = await count_document_automation_templates(conn, company_id)
        overall_count = count + new_entity_count
        _validate_obsolete_permission(
            count=overall_count,
            config=config,
            limit=CompanyLimit.automation,
            permission=permission,
            error_message=error_message,
        )
    elif permission == CompanyPermission.custom_documents_fields_enabled:
        count = await count_document_fields(conn, company_id)
        overall_count = count + new_entity_count
        _validate_obsolete_permission(
            count=overall_count,
            config=config,
            limit=CompanyLimit.additional_fields,
            permission=permission,
            error_message=error_message,
        )


async def validate_company_permission(
    conn: DBConnection,
    *,
    company_id: str,
    company_edrpou: str,
    permission: CompanyPermission,
    new_entity_count: int = 1,
    error_message: LazyI18nString = DEFAULT_PERMISSION_ERROR_MESSAGE,
) -> None:
    # Activate free rate if company doesn't have any rate and also deactivate old
    # "pro_free" rate
    # TODO: move this to somewhere else and do not modify state on validation step
    await activate_free_companies_free_rate(
        conn=conn,
        company_id=company_id,
        company_edrpou=company_edrpou,
    )

    config = await get_billing_company_config(conn, company_id=company_id)
    await validate_obsolete_company_permission(
        conn=conn,
        company_id=company_id,
        config=config,
        permission=permission,
        error_message=error_message,
        new_entity_count=new_entity_count,
    )


def validate_company_permission_by_config(
    config: BillingCompanyConfig,
    permission: CompanyPermission,
    error_message: LazyI18nString = DEFAULT_PERMISSION_ERROR_MESSAGE,
) -> None:
    if not is_allowed_by_rate(config, permission):
        raise AccessDenied(reason=error_message)


def check_company_permission_by_config(
    config: BillingCompanyConfig, permission: CompanyPermission
) -> bool:
    return is_allowed_by_rate(config, permission)


async def validate_agree_on_roles(conn: DBConnection, data: DataDict, user: User) -> StrList:
    """Prevent from agreement on wrong user roles.

    User can agree only on his own roles. If user doesn't have any of listed
    role - attempt will be unsuccessful.
    """
    valid_data = validators.validate(AgreeOnRolesValidator, data)
    role_ids = valid_data['role_ids']

    for role_id in role_ids:
        if not await exists_role_by_user_id(conn, role_id, user.id):
            raise AccessDenied()

    return role_ids


async def validate_can_edit_role(
    conn: DBConnection,
    user: User,
    role: Role,
    new_user_role: int | None = None,
    required_permissions: set[str] | None = None,
    super_admin_permissions: set[str] | None = None,
) -> None:
    """Validate ability to edit (manage) role by given user."""
    company_config = await get_company_config(conn, company_edrpou=user.company_edrpou)
    has_super_admin_permission = has_super_admin_access(
        user,
        company_config,
        required_permissions=required_permissions,
        super_admin_permissions=super_admin_permissions,
    )
    if user.company_edrpou != role.company_edrpou and not has_super_admin_permission:
        raise AccessDenied()

    if (
        not has_super_admin_permission
        and new_user_role
        and new_user_role != role.user_role
        and not has_permission(user, {'can_edit_roles'})
    ):
        raise AccessDenied()


async def validate_delete_role(
    conn: DBConnection, data: dict[str, str], user: User
) -> tuple[StrDict, Role]:
    """Validate delete role attempt."""
    valid_data = validators.validate(DeleteRoleValidator, data)
    role_id = valid_data['role_id']

    role = await select_role_by_id(conn, role_id)
    if not role or role.status == RoleStatus.deleted:
        raise DoesNotExist(Object.role, id=role_id)

    if role.has_hrs_role:
        raise InvalidRequest(
            reason=_('Співробітник має кадрову роль. Видаліть роль із сервісу Вчасно.Кадри'),
        )

    can_delete_coworker_role = user.company_id == role.company_id and has_permission(
        user, {'can_edit_roles'}
    )
    is_current_role = user.role_id == role.id

    if can_delete_coworker_role or is_current_role:
        return valid_data, role

    raise AccessDenied(reason=_('Поточний акаунт не має прав внесення змін до вказаної компанії'))


async def validate_deny_role(conn: DBConnection, data: StrDict, user: BaseUser) -> Role:
    valid_data = validators.validate_trafaret(DENY_ROLE_TRAFARET, data)
    role_id = valid_data['role_id']

    role = await select_role_by_id(conn, role_id)
    if not role:
        raise DoesNotExist(Object.role, id=role_id)

    if user.id != role.user_id:
        raise AccessDenied(reason=_('Поточний акаунт не має прав внесення змін до вказаної ролі'))

    if role.status != RoleStatus.pending:
        raise InvalidRequest(
            reason=_('Дозволено застосовувати тільки до компаній, що очікують Вашого підтвердження')
        )

    return role


async def validate_recover_password_jwt_relevance(*, email: str, token: str) -> None:
    stored_token = await get_recover_password_jwt(email=email)
    if not stored_token or stored_token != token:
        raise Error(Code.invalid_token_recover_password)


async def validate_recover_password(request: web.Request, conn: DBConnection) -> RecoverPasswordCtx:
    """Validate recover password attempt.

    Token should contain valid email, if not user will not recover the
    password. Also unlike other tokens token for recover password valid only
    for 2 hours after sending by email
    """

    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(RecoverPasswordSchema, raw_data)
    try:
        payload = verify_jwt_token(
            data.token,
            services.config.tokens.public_key,
            verify_exp=True,
        )
    except (ValueError, jwt.InvalidTokenError):
        raise Error(Code.invalid_token_recover_password)

    email = payload.get('email')
    if not email:
        raise Error(Code.invalid_token)

    validate_email_domain_rules(email)
    validate_custom_password_rules(email, data.password)

    user = await select_base_user(conn, email=email)
    if not user:
        raise DoesNotExist(Object.user, email=email)

    await validate_recover_password_jwt_relevance(email=email, token=data.token)

    return RecoverPasswordCtx(
        user=user,
        user_email=email,
        new_password=data.password,
    )


async def validate_remind_password(conn: DBConnection, data: dict[str, str]) -> str:
    """User must exist in database to remind the password."""
    email = validators.validate(RemindPasswordValidator, data)['email']
    if not await is_user_email_exists(conn, email=email):
        raise DoesNotExist(Object.user, email=email)
    return email


def validate_update_2fa_state(data: dict[str, str], user: User | BaseUser) -> bool:
    enable = bool(data.get('enable', True))
    if enable:
        # When first factor is email, second factor must be verified phone
        if user.email and not (user.phone and user.is_phone_verified):
            raise Error(Code.phone_not_verified)

        # When first factor is phone, second factor must be verified email
        if user.auth_phone and not (user.email and user.email_confirmed):
            raise InvalidRequest(reason=_('Необхідно вказати email для активації 2FA'))

    return enable


async def validate_update_company(request: web.Request, user: User) -> UpdateCompanySchema:
    """Validate ability to update company data by regular or admin user."""
    validate_user_permission(user, {'can_edit_company'})

    raw_data = await validators.validate_json_request(request)

    data = validators.validate_pydantic(UpdateCompanySchema, raw_data)

    # We have EDRPOU in a path, but actually we don't use it. But just in case lets validate that
    # logged-in user belongs to the same company that is presented in a path.
    if user.company_edrpou != request.match_info.get('edrpou'):
        raise AccessDenied()

    return data


async def validate_permission_to_edit_role(
    conn: DBConnection,
    initiator: User,
    role: Role,
) -> PermissionEditRole:
    """
    Validate user has permission to edit role.
    User permitted if all of following options is True:
        - User is acting within the same company as role.company_id
        - User has permission to edit coworkers data
    OR:
        - User is superadmin and has can_edit_client_data as True
    """

    if initiator.company_edrpou == role.company_edrpou and has_permission(
        initiator, {'can_edit_roles'}
    ):
        return PermissionEditRole.coworker

    super_admin_permissions = await get_super_admin_permissions(
        conn=conn, user_role_id=initiator.role_id
    )
    if super_admin_permissions and 'can_edit_client_data' in super_admin_permissions:
        return PermissionEditRole.superadmin

    logger.warning(
        'Prevented an attempt to edit role without permission',
        extra={
            'initiator_company_edrpou': initiator.company_edrpou,
            'initiator_role_id': initiator.role_id,
            'company_edrpou': role.company_edrpou,
            'role_id': role.id,
        },
    )
    raise AccessDenied()


async def validate_update_hrs_role(conn: DBConnection, role: Role, data: UpdateRoleSchema) -> None:
    """
    Validate updating hrs role permissions
    """

    # If we already count role for billing limit, skip validation.
    if role.is_counted_in_billing_limit:
        return

    # If updating non-HRS fields, validate there is space in company limit for adding new role.
    if any(
        permission in USER_PERMISSIONS and permission not in HRS_NOT_BILLABLE_PERMISSIONS
        for permission in data.to_update_dict()
    ):
        await validate_add_employee(
            conn=conn,
            company_id=role.company_id,
            employee_user_id=role.user_id,
        )


async def validate_update_role(
    conn: DBConnection,
    initiator: User,
    role_id: str,
    raw_data: DataDict,
) -> UpdateRoleCtx:
    """
    Validate:
        - data for role update
        - user permissions for role update (sa/admin)
        - allowed fields to update without permission
        - SA permissions
        - etc.
    """

    role = await validate_role_exists(conn=conn, role_id=role_id)

    data = validators.validate_pydantic(schema=UpdateRoleSchema, data=raw_data)

    if data.can_receive_notifications is not None:
        data.update_can_receive_notifications()

    # If all provided fields are allowed to be updated without permission, just return ctx
    # Most common scenario - self-updating notification settings
    if data.can_update_without_permission:
        return UpdateRoleCtx(
            data=data,
            prev_role=role,
            initiator_type=PermissionEditRole.self,
        )

    initiator_type = await validate_permission_to_edit_role(
        conn=conn,
        initiator=initiator,
        role=role,
    )

    if data.user_role and initiator.role_id == role.id:
        raise AccessDenied(reason=_('Не можна змінювати свою роль в компанії'))

    if data.status:
        if not initiator.is_admin and role.is_admin:
            raise AccessDenied(reason=_('Тільки адміністратор може виконати цю дію'))

        await validate_add_employee(conn=conn, company_id=role.company_id, is_restore_coworker=True)

        if not (
            # Only activation role activation is allowed using this method
            data.status == RoleStatus.active
            # And only from "deleted" statuses
            and role.status
            in (
                RoleStatus.deleted,
                RoleStatus.user_deleted,
                RoleStatus.blocked_2fa,
                RoleStatus.rate_deleted,
            )
        ):
            raise InvalidRequest(
                reason=_('Статус може бути змінений тільки з видаленого на активний')
            )

    # Only admin can change this permission
    if data.can_view_private_document is not None and not initiator.is_admin:
        raise AccessDenied(reason=_('Тільки адміністратор може виконати цю дію'))

    if data.super_admin_permissions:
        await validate_update_super_admin_permissions(conn=conn, initiator=initiator, role=role)

    # Run specific validations against adding HRS role
    if role.has_hrs_role:
        await validate_update_hrs_role(conn=conn, role=role, data=data)

    return UpdateRoleCtx(
        data=data,
        prev_role=role,
        initiator_type=initiator_type,
    )


async def validate_update_super_admin_permissions(
    conn: DBConnection,
    initiator: User,
    role: Role,
) -> None:
    """
    Only admins in superadmin companies can change superadmin config (e.g. 41231992)
    """

    if not initiator.is_admin:
        raise AccessDenied(reason=_('Тільки адміністратор може виконати цю дію'))

    company_config = await get_company_config(conn, company_edrpou=initiator.company_edrpou)
    if not is_super_admin_company(edrpou=initiator.company_edrpou, company_config=company_config):
        raise AccessDenied()

    if initiator.company_edrpou != role.company_edrpou:
        raise AccessDenied(
            reason=_('Неможливо змінити суперадмінські дозволи для користувача іншої компанії')
        )


async def validate_update_current_password(
    conn: DBConnection,
    user: BaseUser,
    password: str | None,
) -> None:
    """Check if user can update current password"""

    # Allow to update autogenerated without checking validity of the current password
    if user.is_autogenerated_password:
        return

    # Allow to set first password without checking current password
    if not user.password:
        return

    # Check if given password is valid for given user
    await validate_is_user_current_password_valid(conn, password=password, user=user)


async def validate_update_password(
    conn: DBConnection,
    data: dict[str, str],
    user: BaseUser,
) -> UpdatePasswordCtx:
    """Validate data to update user password."""
    if user.is_autogenerated_password:
        data['current_password'] = autogenerate_password()

    valid_data = validators.validate(UpdatePasswordValidator, data)
    current_password: str | None = valid_data['current_password']
    new_password: str = valid_data['new_password']

    # New password, should not be equal to current password
    if current_password == new_password:
        raise InvalidRequest(reason=_('Новий пароль співпадає з поточним'))

    if not user.email:
        raise InvalidRequest(reason=_('Необхідно вказати електронну пошту, щоб змінити пароль'))

    # Check current password validity
    await validate_update_current_password(
        conn=conn,
        user=user,
        password=current_password,
    )

    # Check company specific rules for passwords
    validate_custom_password_rules(
        email=user.email,
        password=new_password,
    )

    return UpdatePasswordCtx(
        current_password=current_password,
        new_password=new_password,
    )


async def validate_auth_phone_not_used(
    conn: DBConnection,
    auth_phone: str,
) -> None:
    """
    Validate that auth phone is not used by another user.
    """
    phone_user = await select_base_user(
        conn=conn,
        auth_phone=auth_phone,
        only_with_email=False,
        exclude_placeholder=False,
    )
    if phone_user:
        raise AlreadyExists(
            Object.user,
            phone=auth_phone,
            reason=_(
                'Телефон вже використовується іншим користувачем для входу в систему, '
                'тому ми не можемо його використовувати для входу у ваш профіль'
            ),
        )


async def validate_update_user(
    conn: DBConnection,
    data: DataDict,
    actor: User | AuthUser | BaseUser,
) -> UpdateUserDataCtx:
    """
    Validate and convert data to update user profile.
    """
    validated = validators.validate_pydantic(UpdateUserValidator, data)

    # Update on behalf of another user
    user: User | AuthUser | BaseUser
    if validated.user_id and validated.user_id != actor.id:
        if not isinstance(actor, User):
            raise AccessDenied()

        validate_user_permission(actor, {'can_edit_roles'})

        _user = await get_user(
            conn,
            user_id=validated.user_id,
            company_id=actor.company_id,
        )
        if _user is None:
            raise AccessDenied()
        user = _user
    else:
        user = actor

    return UpdateUserDataCtx(
        first_name=validated.first_name,
        second_name=validated.second_name,
        last_name=validated.last_name,
        actor=actor,
        user=user,
    )


def _is_sensitive_phone_update(user: BaseUser) -> bool:
    """
    When user has verified phone, we consider phone update as sensitive operation
    and require password to be provided along side with OTP code for verification.
    """
    return bool(user.phone and user.is_phone_verified)


async def validate_update_existing_phone(
    conn: DBConnection,
    user: BaseUser,
    new_phone: str,
    password: str | None,
) -> None:
    # Auth phone is also updated when we update user phone, so we should check if there is no
    # another user with the same auth phone
    if user.auth_phone:
        await validate_auth_phone_not_used(conn, auth_phone=new_phone)

    # TODO: find out what to do when user doesn't have password (e.g. signed in with SSO)
    if password or _is_sensitive_phone_update(user):
        await validate_is_user_current_password_valid(conn, password=password, user=user)


async def validate_send_verify_phone(
    conn: DBConnection,
    request: web.Request,
    user: BaseUser,
) -> SendVerifyPhoneScheme:
    """
    Validate request to send OTP code for phone verification.
    """
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(SendVerifyPhoneScheme, raw_data)

    # When user already has phone we ask to provide password to verify that user is the owner
    # of the phone number. The same check will be done on phone change step.
    await validate_update_existing_phone(
        conn=conn,
        user=user,
        new_phone=data.phone,
        password=data.password,
    )

    return data


async def validate_update_user_phone(
    conn: DBConnection,
    raw_data: dict[str, str],
    user: BaseUser,
) -> UpdatePhoneCtx:
    data = validators.validate_pydantic(UpdateUserPhoneSchema, raw_data)

    await validate_update_existing_phone(
        conn=conn,
        user=user,
        new_phone=data.phone,
        password=data.password,
    )

    is_phone_verified: bool = False
    if data.code or _is_sensitive_phone_update(user):
        await validate_phone_verification_otp(user, otp=data.code, phone=data.phone)
        is_phone_verified = True

    return UpdatePhoneCtx(
        phone=data.phone,
        is_phone_verified=is_phone_verified,
    )


async def validate_verify_phone(
    request: web.Request,
    user: BaseUser,
) -> str:
    """
    Check OTP code for phone verification.
    """
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(VerifyPhoneSchema, raw_data)

    if not user.phone:
        raise InvalidRequest(
            reason=_('Для підтвердження телефону потрібно спочатку встановити його'),
        )

    await validate_phone_verification_otp(user, otp=data.code, phone=user.phone)

    return data.code


async def validate_update_auth_phone(
    conn: DBConnection,
    request: web.Request,
    user: BaseUser,
) -> str | None:
    """
    Validate phone update request for auth user.
    """

    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(UpdateAuthPhoneSchema, raw_data)

    # Auth phone will be removed
    if not data.enable:
        # Check that user has at least one authentication method when we disable phone auth
        if not user.email:
            raise InvalidRequest(
                reason=_(
                    'Необхідно вказати електронну пошту, щоб вимкнути вхід за номером телефону'
                )
            )

        return None

    if not (user.phone and user.is_phone_verified):
        raise InvalidRequest(
            reason=_(
                'Неможливо змінити телефон, якщо він не верифікований. Будь ласка, спочатку '
                'виконайте верифікацію телефону.'
            )
        )

    # From time to time we might have invalid phone numbers for users, so we need to validate
    # and normalize phone number before using it for authentication
    auth_phone = validators.validate_pydantic_adapter(pv.PhoneAdapter, value=user.phone)

    await validate_auth_phone_not_used(conn, auth_phone=auth_phone)

    return auth_phone


async def validate_phone_verification_otp(
    user: BaseUser,
    otp: str | None,
    phone: str,
) -> None:
    """
    Validate a phone verification code provided by the user

    This function works in a pair with "send_phone_verification_otp" that
    sends the code to the user's phone number and stores it in Redis to be
    checked here.
    """
    if not otp:
        raise Error(Code.invalid_totp_code)

    await validate_rate_limit(
        key=f'phone_verification_code:{user.id}',
        limit=15,
        delta=ONE_HOUR_DELTA,
    )

    stored_otp = await get_phone_verification_otp(user_id=user.id, email=user.email, phone=phone)
    if not stored_otp or otp != stored_otp:
        raise Error(Code.invalid_totp_code)


async def validate_sa_update_company_config(
    conn: DBConnection, request: web.Request
) -> tuple[UpdateCompanyConfigSchemaSA, Company]:
    """
    Validate ability to update company data by super admin user.
    """
    json_data = await validators.validate_json_request(request)
    raw_data = {'company_id': request.match_info.get('company_id'), **json_data}
    data = validators.validate_pydantic(UpdateCompanyConfigSchemaSA, raw_data)

    company_row = await validate_company_exists(conn, company_id=data.company_id)
    company = Company.from_row(company_row)

    return data, company


async def validate_update_billing_company_config(
    conn: DBConnection, request: web.Request
) -> UpdateBillingCompanyConfigSchema:
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(UpdateBillingCompanyConfigSchema, raw_data)

    await validate_company_exists(conn, company_id=data.company_id)

    return data


async def validate_update_default_recipient(
    conn: DBConnection, request: web.Request, user: User
) -> str | None:
    """
    When None - we reset existing default recipient
    """
    validate_user_permission(user, {'can_edit_roles'})

    data = await validators.validate_json_request(request)
    role_id: str | None = validators.validate(UpdateDefaultRecipientValidator, data)['role_id']
    if not role_id:
        return role_id

    role = await select_role_by_id(conn, role_id)
    if not role:
        raise DoesNotExist(Object.role)
    if role.company_id != user.company_id:
        raise AccessDenied()
    return role_id


async def validate_subscribe_to_esputnik(
    request: web.Request,
) -> EsputnikSubscriptionCtx:
    raw_data = await validators.validate_json_request(request)
    data = validators.validate(EsputnikSubscriptionValidator, raw_data)

    await validate_rate_limit(
        key=f'esp_sub:{data["email"]}',
        limit=15,
        delta=ONE_HOUR_DELTA,
    )

    return EsputnikSubscriptionCtx(
        name=data['name'],
        email=data['email'],
        source=SubscriptionSource(data['source']),
    )


def validate_custom_password_rules(email: str, password: str) -> None:
    """
    Apply additional rules for password validation for some companies.
    """
    password_mapping = {
        'ua.nestle.com': 16,
        'ua.bosch.com': 12,
        'boschrexroth.com.ua': 12,
        'bosch.com': 12,
    }
    domain = email.split('@')[-1]

    if domain in password_mapping and len(password) < password_mapping[domain]:
        err = _(
            'Пароль має містити щонайменше {number} символів, хоча б одну цифру і один символ.'
        ).format(number=password_mapping[domain])
        raise InvalidRequest(password=err)


def validate_email_domain_rules(email: str) -> None:
    """
    Apply additional rules for email validation.
    """

    # Do not allow ru domain for registration
    if email.endswith('.ru'):
        raise InvalidRequest(reason=_('Реєстрація користувачів з домену .ru заборонена'))

    # Do not allow some misspelled domains
    if email.endswith(
        (
            'gmal.com',
            'outlok.com',
            'gmial.com',
            'gmil.com',
            'gimail.com',
            'urk.net',
        )
    ):
        raise InvalidRequest(
            reason=_(
                'Ви ввели неправильний домен. Будь ласка, перевірте правильність '
                'введення домену та спробуйте ще раз.'
            )
        )


async def validate_update_language(request: web.Request) -> Language:
    raw_data = await validators.validate_json_request(request)
    data = validators.validate(UpdateLanguageValidator, raw_data)
    return data['language']


async def validate_user_email_change_company_domains(
    *,
    companies: list[Company],
    new_email: str,
) -> None:
    """
    Check that new email has a valid domain for each company.
    """

    invalid_companies: list[Company] = []
    for company in companies:
        if not is_valid_email_domains(email=new_email, domains=company.email_domains):
            invalid_companies.append(company)

    if invalid_companies:
        raise AccessDenied(
            reason=_(
                'Неможливо змінити електронну пошту на вказану, оскільки в одній або декількох '
                'компаніях використовується корпоративна електронна пошта, яка не співпадає з '
                'новою електронною поштою',
            ),
            details={
                'invalid_companies': [
                    {
                        'name': company.name,
                        'edrpou': company.edrpou,
                        'email_domains': company.email_domains,
                    }
                    for company in invalid_companies
                ]
            },
        )


async def validate_user_email_change__start(
    request: web.Request,
    conn: DBConnection,
    user: BaseUser,
) -> StartUserEmailChangeSchema:
    """
    Before changing user email we need should verify new email for uniqueness and validity
    """
    raw_data = await validators.validate_json_request(request)

    data = validators.validate_pydantic(StartUserEmailChangeSchema, raw_data)
    new_email: str = data.new_email

    await validate_user_email_not_exists(conn, email=new_email)

    companies = await select_user_companies(conn, user_id=user.id)
    await validate_user_email_change_company_domains(companies=companies, new_email=new_email)

    validate_email_domain_rules(email=new_email)

    return data


def _check_user_email_change_token(*, token: str, expected_action: str) -> str:
    """
    Check that JWT token sent during an email change process is valid. Also, check token is
    intended for the expected action.
    """
    try:
        payload = verify_jwt_token(
            token=token,
            public_key_path=services.config.tokens.public_key,
            verify_exp=True,
        )
    except jwt.InvalidTokenError:
        raise Error(Code.invalid_token)

    token_action = payload['action']
    if token_action != expected_action:
        raise Error(Code.invalid_token)

    return payload['change_id']


def _check_user_email_change_status(
    change: UserEmailChange,
    expected_status: UserEmailChangeStatus,
) -> None:
    """
    Check that user email change request has expected status
    """
    if change.status != expected_status:
        logger.info(
            msg='Invalid user email change status',
            extra={
                'change_id': change.id,
                'old_email': change.old_email,
                'new_email': change.new_email,
                'status': change.status,
                'expected_status': expected_status,
            },
        )
        raise Error(
            code=Code.invalid_request,
            reason=_(
                'Запит на зміну електронної пошти не може бути виконаний, оскільки він має '
                'неправильний статус. Сформуйте новий запит на зміну електронної пошти'
            ),
        )


async def _validate_user_email_change_id(
    conn: DBConnection,
    change_id: str,
    user: BaseUser,
) -> UserEmailChange:
    """
    Validate user email change by ID (usually extracted from JWT token).
    """
    change = await select_user_email_change(conn, change_id=change_id)
    if not change:
        raise Error(
            code=Code.object_does_not_exist,
            reason=_('Не знайдено запит на зміну електронної пошти'),
        )

    _validate_user_email_change_is_valid(change=change)

    if change.user_id != user.id:
        raise AccessDenied(
            reason=_(
                'Немає доступу до запиту на зміну електронної пошти. '
                'Увійдіть у систему як користувач з яким був створений запит або '
                'сформуйте новий запит на зміну електронної пошти'
            )
        )

    return change


async def _validate_user_email_change_latest(
    conn: DBConnection,
    user: BaseUser,
) -> UserEmailChange:
    """
    Validate that latest email change request from user is not cancled or expired
    """
    latest_change = await select_latest_user_email_changes(conn, user_id=user.id)
    if not latest_change:
        raise Error(
            code=Code.invalid_token,
            reason=_('Не знайдено запит на зміну електронної пошти'),
        )

    _validate_user_email_change_is_valid(change=latest_change)

    return latest_change


async def validate_user_email_change__verify_email(
    request: web.Request,
    conn: DBConnection,
    user: BaseUser,
) -> UserEmailChange:
    """
    Validate user identity by token that we sent to the old email.
    """
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(VerifyUserEmailChangeSchema, raw_data)

    change_id = _check_user_email_change_token(
        token=data.token,
        expected_action=USER_EMAIL_CHANGE_VERIFY_EMAIL_TOKEN_ACTION,
    )

    change = await _validate_user_email_change_id(conn, change_id=change_id, user=user)

    _check_user_email_change_status(
        change=change,
        expected_status=UserEmailChangeStatus.verify_email,
    )

    return change


async def validate_user_email_change__verify_password(
    request: web.Request,
    conn: DBConnection,
    user: BaseUser,
) -> UserEmailChange:
    """
    Validate user identity by password
    """
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(VerifyUserEmailChangePasswordSchema, raw_data)

    await validate_is_user_current_password_valid(
        conn=conn,
        password=data.password,
        user=user,
    )

    change = await _validate_user_email_change_latest(conn, user=user)

    _check_user_email_change_status(
        change=change,
        expected_status=UserEmailChangeStatus.verify_password,
    )

    return change


async def validate_user_email_change__verify_2fa(
    request: web.Request,
    conn: DBConnection,
    user: BaseUser,
) -> UserEmailChange:
    """
    Verify OTP code sent to the user phone number
    """
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(VerifyUserEmailChange2FASchema, raw_data)

    user_phone = user.phone
    if not user_phone:
        raise Error(
            code=Code.invalid_request,
            reason=_('Користувач не має підтвердженого номера телефону'),
        )

    await validate_phone_verification_otp(user=user, otp=data.code, phone=user_phone)

    change = await _validate_user_email_change_latest(conn, user=user)

    _check_user_email_change_status(
        change=change,
        expected_status=UserEmailChangeStatus.verify_2fa,
    )

    return change


def _validate_user_email_change_is_valid(change: UserEmailChange) -> None:
    """
    Check that user email change has valid status and user access to it.
    """
    if change.status == UserEmailChangeStatus.cancelled:
        logger.info('User email change is cancelled', extra={'change_id': change.id})
        raise Error(
            code=Code.invalid_token,
            reason=_(
                'Вами було скасовано даний запит на зміну електронної пошти. '
                'Щоб виконати заміну, будь ласка, розпочніть новий процес зміни пошти'
            ),
        )

    if utc_now() > (change.date_created + timedelta(hours=24)):
        logger.info('User email change is expired', extra={'change_id': change.id})
        raise Error(
            code=Code.invalid_token,
            reason=_(
                'Цей запит на зміну електронної пошти застарів, будь ласка, сформуйте новий запит'
            ),
        )


async def validate_user_email_change__confirm(
    request: web.Request,
    conn: DBConnection,
    user: BaseUser,
) -> UserEmailChange:
    """
    Check token from the new email to finally confirm email change.
    """
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(ConfirmUserEmailChangeSchema, raw_data)

    change_id = _check_user_email_change_token(
        token=data.token,
        expected_action=USER_EMAIL_CHANGE_CONFIRM_TOKEN_ACTION,
    )

    change = await _validate_user_email_change_id(conn, change_id=change_id, user=user)

    _check_user_email_change_status(
        change=change,
        expected_status=UserEmailChangeStatus.pending,
    )

    # We already checked that on start email change, but let's double-check before moving forward
    # to avoid 500+ errors
    await validate_user_email_not_exists(conn, email=change.new_email)

    return change


async def validate_user_email_change__update_contacts(
    conn: DBConnection,
    user: BaseUser,
) -> UserEmailChange:
    """
    Validate that user can update contacts after confirming email change.
    """
    change = await _validate_user_email_change_latest(conn, user=user)

    # We expect that user update contacts only after email change is confirmed
    if change.status != UserEmailChangeStatus.confirmed:
        raise Error(
            code=Code.invalid_token,
            reason=_('Запит на зміну електронної пошти не був підтверджений'),
        )

    if change.date_contacts_synced is not None:
        raise Error(
            code=Code.invalid_token,
            reason=_('Синхронізація контактів вже була розпочата'),
        )

    return change
