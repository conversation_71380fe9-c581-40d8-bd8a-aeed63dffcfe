"""add seqnum for events

Revision ID: 271e11fade11
Revises: ddb0ea4d8744
Create Date: 2022-09-16 10:06:18.337077+00:00

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '271e11fade11'
down_revision = 'ddb0ea4d8744'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("CREATE SEQUENCE events_seqnum")
    op.execute('LOCK TABLE events IN SHARE MODE')
    op.add_column(
        'events',
        sa.Column(
            'seqnum',
            sa.BigInteger(),
            server_default=sa.text("nextval('events_seqnum')"),
            nullable=True,
        ),
    )
    op.execute(
        """
        UPDATE events
        SET seqnum = rn
        FROM (
           SELECT id, row_number() OVER (ORDER BY date_created) rn
           FROM events
        ) t
        WHERE events.id = t.id;
        SELECT setval(
            'events_seqnum',
            (SELECT MAX(seqnum) FROM events)
        );
    """
    )
    op.alter_column(
        'events',
        'seqnum',
        nullable=False,
        server_default=sa.text("nextval('events_seqnum')"),
    )
    op.execute('ALTER SEQUENCE events_seqnum OWNED BY events.seqnum;')

    op.execute('COMMIT;')
    op.create_index(
        op.f('ix_events_seqnum'),
        'events',
        ['seqnum'],
        unique=False,
        postgresql_concurrently=True,
    )


def downgrade():
    op.drop_index(op.f('ix_events_seqnum'), table_name='events')
    op.drop_column('events', 'seqnum')
