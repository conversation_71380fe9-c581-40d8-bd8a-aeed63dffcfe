"""Add document_actions.document_edrpou_owner

Revision ID: 9dfc4a2e822a
Revises: 936b65a283e8
Create Date: 2024-06-17 11:15:34.782966+00:00

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '9dfc4a2e822a'
down_revision = '936b65a283e8'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'document_actions',
        sa.Column(
            'document_edrpou_owner',
            sa.String(length=64),
            nullable=True,
        ),
    )
    op.add_column(
        'document_actions',
        sa.Column(
            'company_edrpou',
            sa.String(length=64),
            nullable=True,
        ),
    )


def downgrade():
    op.drop_column('document_actions', 'document_edrpou_owner')
    op.drop_column('document_actions', 'company_edrpou')
