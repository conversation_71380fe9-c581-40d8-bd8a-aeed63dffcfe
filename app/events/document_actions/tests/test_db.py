import pytest

from app.events.document_actions import db, types
from app.lib import datetime_utils as dt_utils
from app.services import services
from app.tests import common

TEST_DOCUMENT_ID = '4a7b67c3-e732-4f41-a199-f647a30d572d'
TEST_DOCUMENT_ID_2 = '5b8c78d4-e843-5f52-b2a0-f758a41e683e'
TEST_DOCUMENT_EDRPOU = '12345678'
TEST_DOCUMENT_TITLE = 'Test title'
TEST_FAKE_DOCUMENT_ID = common.TEST_UUID

TEST_COMPANY_ID = '3eba89c6-71f9-4285-a579-737681c76b61'
TEST_COMPANY_ID_2 = '4fcb90d7-82f0-5396-b680-848792c87c72'
TEST_FAKE_COMPANY_ID = common.TEST_UUID
TEST_COMPANY_EDRPOU = '87654321'

TEST_ROLE_ID = '54123ba8-fa25-48f7-bb23-388e07ce28c2'
TEST_FAKE_ROLE_ID = common.TEST_UUID

TEST_UUID_1 = '10000000-0000-0000-0000-000000000001'
TEST_UUID_2 = '10000000-0000-0000-0000-000000000002'
TEST_UUID_3 = '10000000-0000-0000-0000-000000000003'
TEST_UUID_4 = '10000000-0000-0000-0000-000000000004'
TEST_UUID_5 = '10000000-0000-0000-0000-000000000005'
TEST_UUID_6 = '10000000-0000-0000-0000-000000000006'


@pytest.mark.parametrize(
    """
    document_actions,
    expected_document_actions,
    filters
    """,
    [
        pytest.param(
            # Test date_from / date_to filters
            [
                # Shouldn't be included - date_created not in filters gap
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=2),
                    extra={'test': 'document uploaded'},
                ),
                # Should be included
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=4),
                    extra={'test': 'document version uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=5),
                    extra={'test': 'document deleted'},
                ),
                # Shouldn't be included - date_created not in filters gap
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=6),
                    extra={'test': 'document deleted'},
                ),
            ],
            [
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=4),
                    extra={'test': 'document version uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=5),
                    extra={'test': 'document deleted'},
                ),
            ],
            {
                'company_id': TEST_COMPANY_ID,
                'last_date_from': dt_utils.midnight().replace(day=3),
                'last_record_id': None,
                'date_to': dt_utils.midnight().replace(day=5),
            },
            id='date_from_date_to',
        ),
        pytest.param(
            # Test pagination with last_record_id
            [
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document uploaded'},
                    id=TEST_UUID_1,  # Adding IDs to test pagination
                ),
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),  # Same date as previous
                    extra={'test': 'document version uploaded'},
                    id=TEST_UUID_2,
                ),
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=4),
                    extra={'test': 'document deleted'},
                    id=TEST_UUID_3,
                ),
            ],
            [
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document version uploaded'},
                    id=TEST_UUID_2,
                ),
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=4),
                    extra={'test': 'document deleted'},
                    id=TEST_UUID_3,
                ),
            ],
            {
                'company_id': TEST_COMPANY_ID,
                'last_date_from': dt_utils.midnight().replace(day=3),
                'last_record_id': TEST_UUID_1,  # Pagination from after the first record
                'date_to': dt_utils.midnight().replace(day=5),
            },
            id='pagination_with_last_record_id',
        ),
        pytest.param(
            # Test limit parameter (should only return the first 2 when limit=2)
            [
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=4),
                    extra={'test': 'document version uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=5),
                    extra={'test': 'document deleted'},
                ),
            ],
            [
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=4),
                    extra={'test': 'document version uploaded'},
                ),
            ],
            {
                'company_id': TEST_COMPANY_ID,
                'last_date_from': dt_utils.midnight().replace(day=3),
                'last_record_id': None,
                'date_to': dt_utils.midnight().replace(day=5),
                'limit': 2,  # Only return 2 records
            },
            id='limit_parameter',
        ),
        pytest.param(
            # Test multiple actions with the same date_created (should be ordered by id)
            [
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document uploaded 1'},
                    id=TEST_UUID_2,  # Higher ID but same date
                ),
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),  # Same date
                    extra={'test': 'document version uploaded 1'},
                    id=TEST_UUID_1,  # Lower ID should come first
                ),
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),  # Same date
                    extra={'test': 'document deleted'},
                    id=TEST_UUID_3,  # Highest ID
                ),
            ],
            [
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document version uploaded 1'},
                    id=TEST_UUID_1,
                ),
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document uploaded 1'},
                    id=TEST_UUID_2,
                ),
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document deleted'},
                    id=TEST_UUID_3,
                ),
            ],
            {
                'company_id': TEST_COMPANY_ID,
                'last_date_from': dt_utils.midnight().replace(day=3),
                'last_record_id': None,
                # Same as date_from to only get actions on that day
                'date_to': dt_utils.midnight().replace(day=3),
            },
            id='same_date_created_ordering',
        ),
        pytest.param(
            # Test company_id filter (should only return actions for TEST_COMPANY_ID)
            [
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID_2,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=4),
                    extra={'test': 'document version uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=5),
                    extra={'test': 'document deleted'},
                ),
            ],
            [
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=5),
                    extra={'test': 'document deleted'},
                ),
            ],
            {
                'company_id': TEST_COMPANY_ID,
                'last_date_from': dt_utils.midnight().replace(day=3),
                'last_record_id': None,
                'date_to': dt_utils.midnight().replace(day=5),
            },
            id='company_id_filter',
        ),
        pytest.param(
            # Test boundary conditions for dates
            [
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),  # Exactly at last_date_from
                    extra={'test': 'document uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=4),
                    extra={'test': 'document version uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=5),  # Exactly at date_to
                    extra={'test': 'document deleted'},
                ),
            ],
            [
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=4),
                    extra={'test': 'document version uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=5),
                    extra={'test': 'document deleted'},
                ),
            ],
            {
                'company_id': TEST_COMPANY_ID,
                # Exactly matches first record
                'last_date_from': dt_utils.midnight().replace(day=3),
                'last_record_id': None,
                'date_to': dt_utils.midnight().replace(day=5),  # Exactly matches last record
            },
            id='date_boundary_conditions',
        ),
        pytest.param(
            # Test empty result set (no matching records)
            [
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document uploaded'},
                ),
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=4),
                    extra={'test': 'document version uploaded'},
                ),
            ],
            [],  # Expect an empty result
            {
                'company_id': TEST_FAKE_COMPANY_ID,
                'last_date_from': dt_utils.midnight().replace(day=3),
                'last_record_id': None,
                'date_to': dt_utils.midnight().replace(day=5),
            },
            id='no_matching_records',
        ),
        pytest.param(
            # Test complex pagination scenario
            [
                types.DocumentAction(
                    action=types.Action.document_delete,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),
                    extra={'test': 'document uploaded 1'},
                    id=TEST_UUID_1,
                ),
                types.DocumentAction(
                    action=types.Action.document_send,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=3),  # Same date as previous
                    extra={'test': 'document version uploaded 1'},
                    id=TEST_UUID_2,
                ),
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=4),
                    extra={'test': 'document deleted 1'},
                    id=TEST_UUID_3,
                ),
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=5),
                    extra={'test': 'document field deleted'},
                    id=TEST_UUID_4,
                ),
            ],
            [
                types.DocumentAction(
                    action=types.Action.document_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=4),
                    extra={'test': 'document deleted 1'},
                    id=TEST_UUID_3,
                ),
                types.DocumentAction(
                    action=types.Action.document_version_upload,
                    document_id=TEST_DOCUMENT_ID,
                    document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
                    document_title=TEST_DOCUMENT_TITLE,
                    company_id=TEST_COMPANY_ID,
                    company_edrpou=TEST_COMPANY_EDRPOU,
                    role_id=TEST_ROLE_ID,
                    date_created=dt_utils.midnight().replace(day=5),
                    extra={'test': 'document field deleted'},
                    id=TEST_UUID_4,
                ),
            ],
            {
                'company_id': TEST_COMPANY_ID,
                'last_date_from': dt_utils.midnight().replace(day=3),
                'last_record_id': TEST_UUID_2,  # Skip the first day (day 3) entirely after record 2
                'date_to': dt_utils.midnight().replace(day=5),
            },
            id='complex_pagination',
        ),
    ],
)
async def test_select_document_actions_for_report(
    aiohttp_client,
    document_actions: list[types.DocumentAction],
    expected_document_actions: list[types.DocumentAction],
    filters: dict,
):
    """
    Given document actions
    When selecting document actions by filter
    Expected to return proper amount of actions in the right asc order
    """

    # Prepare
    await common.prepare_client(aiohttp_client)
    await db.insert_document_actions(document_actions=document_actions)

    # Act
    async with services.events_db.acquire() as conn:
        stream = db.stream_document_actions_for_report(
            conn=conn,
            company_id=filters['company_id'],
            last_date_from=filters['last_date_from'],
            last_record_id=filters['last_record_id'],
            date_to=filters['date_to'],
            limit=filters.get('limit', 100_000),
            chunk_size=2,
        )
        result_document_actions = [document_action async for ch in stream for document_action in ch]

    # Assert
    assert len(result_document_actions) == len(expected_document_actions)

    for document_action, expected_document_action in zip(
        result_document_actions, expected_document_actions
    ):
        assert document_action.action == expected_document_action.action
        assert document_action.document_id == expected_document_action.document_id
        assert document_action.company_id == expected_document_action.company_id
        assert document_action.role_id == expected_document_action.role_id
        assert (
            dt_utils.to_local_datetime(document_action.date_created)
            == expected_document_action.date_created
        )
        assert document_action.extra == expected_document_action.extra
        assert (
            document_action.document_edrpou_owner == expected_document_action.document_edrpou_owner
        )
        assert document_action.company_edrpou == expected_document_action.company_edrpou


async def test_insert_document_action(aiohttp_client):
    """
    Given a document action
    When calling insert_document_action db query
    Expected creating record in db
    """

    # Prepare
    await common.prepare_client(aiohttp_client)

    document_action = types.DocumentAction(
        action=types.Action.document_send,
        document_id=TEST_DOCUMENT_ID,
        document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
        document_title=TEST_DOCUMENT_TITLE,
        company_id=TEST_COMPANY_ID,
        company_edrpou=TEST_COMPANY_EDRPOU,
        role_id=TEST_ROLE_ID,
        extra={'test': 'some extra for testing purposes'},
    )

    # Act
    db_document_action = await db.insert_document_action(document_action=document_action)

    # Assert
    assert db_document_action.action == types.Action.document_send
    assert db_document_action.document_id == TEST_DOCUMENT_ID
    assert db_document_action.document_edrpou_owner == TEST_DOCUMENT_EDRPOU
    assert db_document_action.company_id == TEST_COMPANY_ID
    assert db_document_action.company_edrpou == TEST_COMPANY_EDRPOU
    assert db_document_action.role_id == TEST_ROLE_ID
    assert db_document_action.extra == {'test': 'some extra for testing purposes'}

    db_document_actions = await db.select_document_actions_for(
        document_id=document_action.document_id
    )
    db_document_action = db_document_actions[0]

    assert db_document_action.action == types.Action.document_send
    assert db_document_action.document_id == TEST_DOCUMENT_ID
    assert db_document_action.document_edrpou_owner == TEST_DOCUMENT_EDRPOU
    assert db_document_action.company_id == TEST_COMPANY_ID
    assert db_document_action.company_edrpou == TEST_COMPANY_EDRPOU
    assert db_document_action.role_id == TEST_ROLE_ID
    assert db_document_action.extra == {'test': 'some extra for testing purposes'}


async def test_insert_document_actions(aiohttp_client):
    """
    Given multiple document actions
    When calling add_document_actions
    Expected creating record for each action in database
    """

    # Prepare
    await common.prepare_client(aiohttp_client)

    document_actions = [
        types.DocumentAction(
            action=types.Action.document_send,
            document_id=TEST_DOCUMENT_ID,
            document_edrpou_owner=TEST_DOCUMENT_EDRPOU,
            document_title=TEST_DOCUMENT_TITLE,
            company_id=TEST_COMPANY_ID,
            company_edrpou=TEST_COMPANY_EDRPOU,
            role_id=TEST_ROLE_ID,
            extra={'test': 'some extra for testing purposes'},
        )
        for _ in range(0, 40)
    ]

    # Act
    db_document_actions = await db.insert_document_actions(document_actions=document_actions)

    # Assert
    for db_document_action in db_document_actions:
        assert db_document_action.action == types.Action.document_send
        assert db_document_action.document_id == TEST_DOCUMENT_ID
        assert db_document_action.company_id == TEST_COMPANY_ID
        assert db_document_action.role_id == TEST_ROLE_ID
        assert db_document_action.extra == {'test': 'some extra for testing purposes'}

    db_document_actions = await db.select_document_actions_for(document_id=TEST_DOCUMENT_ID)

    for db_document_action in db_document_actions:
        assert db_document_action.action == types.Action.document_send
        assert db_document_action.document_id == TEST_DOCUMENT_ID
        assert db_document_action.company_id == TEST_COMPANY_ID
        assert db_document_action.role_id == TEST_ROLE_ID
        assert db_document_action.extra == {'test': 'some extra for testing purposes'}
