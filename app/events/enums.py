import enum

from app.lib.enums import NamedEnum


@enum.unique
class EventSource(enum.Enum):
    api_blackbox = 'api_blackbox'
    api_internal = 'api_internal'
    api_mobile = 'api_mobile'
    api_public = 'api_public'
    plugin_1c = 'plugin_1c'


@enum.unique
class EventType(enum.Enum):
    """Types of events."""

    LOGIN_SUCCESSFUL = 'login_successful'
    LOGIN_UNSUCCESSFUL = 'login_unsuccessful'

    LOGIN_TO_COMPANY = 'LOGIN_TO_COMPANY'
    LOGOUT_FROM_COMPANY = 'LOGOUT_FROM_COMPANY'

    LOGOUT_SUCCESSFUL = 'logout_successful'

    UPDATE_ROLE_SUCCESSFUL = 'update_role_successful'
    ROLE_DELETE = 'role_delete'
    ROLE_CREATE = 'role_create'

    GENERATE_TOKEN = 'generate_token'
    DELETE_TOKEN = 'delete_token'

    UPDATE_ANTIVIRUS = 'update_antivirus'

    TEMPLATE_CREATE = 'template_create'
    TEMPLATE_UPDATE = 'template_update'
    TEMPLATE_DELETE = 'template_delete'
    TEMPLATE_TOGGLE = 'template_toggle'

    FIELD_CREATE = 'field_create'
    FIELD_UPDATE = 'field_update'
    FIELD_DELETE = 'field_delete'
    FIELD_ACCESS_CREATE = 'field_access_create'
    FIELD_ACCESS_DELETE = 'field_access_delete'

    REQUIRED_FIELD_CREATE = 'required_field_create'
    REQUIRED_FIELD_UPDATE = 'required_field_update'
    REQUIRED_FIELD_DELETE = 'required_field_delete'

    COMPANY_UPDATE = 'company_update'

    ROLE_TAG_CREATE = 'role_tag_create'
    ROLE_TAG_DELETE = 'role_tag_delete'

    GROUP_CREATE = 'group_create'
    GROUP_DELETE = 'group_delete'
    GROUP_RENAME = 'group_rename'

    GROUP_MEMBER_CREATE = 'group_member_create'
    GROUP_MEMBER_DELETE = 'group_member_delete'


class ActionsReportType(NamedEnum):
    documents = 'documents'
    users = 'users'
