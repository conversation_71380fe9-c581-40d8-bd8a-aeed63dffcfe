"""Alter delete_document_requests.document_id

Revision ID: f8fd0363e2c3
Revises: 709e234e3d29
Create Date: 2021-12-08 16:02:08.052795


Author: <PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f8fd0363e2c3'
down_revision = '709e234e3d29'
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
        ALTER TABLE delete_document_requests
        ALTER COLUMN document_id TYPE text USING document_id::text;
    """
    )
    op.create_index(
        op.f('ix_delete_document_requests_document_id'),
        'delete_document_requests',
        ['document_id'],
        unique=False,
    )


def downgrade():
    op.drop_index(
        op.f('ix_delete_document_requests_document_id'),
        table_name='delete_document_requests',
    )
    op.execute(
        """
        ALTER TABLE delete_document_requests
        ALTER COLUMN document_id TYPE uuid USING document_id::uuid;
    """
    )
