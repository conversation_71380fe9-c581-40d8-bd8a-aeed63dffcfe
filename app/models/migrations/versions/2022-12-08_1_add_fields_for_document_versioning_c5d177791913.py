"""add fields for document versioning

Revision ID: c5d177791913
Revises: b3ede2d56912
Create Date: 2022-12-08 16:32:01.387039

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.models.migrations.utils import DummyEnum
from app.models.types import SoftEnum


# revision identifiers, used by Alembic.
revision = 'c5d177791913'
down_revision = 'b3ede2d56912'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'document_antivirus_check',
        sa.Column('document_version_id', postgresql.UUID(), nullable=True),
    )
    op.create_foreign_key(
        None,
        'document_antivirus_check',
        'document_versions',
        ['document_version_id'],
        ['id'],
        ondelete='CASCADE',
    )
    op.add_column('document_versions', sa.Column('s3_key', sa.Text(), nullable=True))
    op.add_column(
        'document_versions',
        sa.Column('type', SoftEnum(DummyEnum), nullable=True),
    )
    op.add_column('documents', sa.Column('s3_key', sa.Text(), nullable=True))
    op.add_column(
        'reviews', sa.Column('document_version_id', postgresql.UUID(), nullable=True)
    )
    op.create_foreign_key(
        None,
        'reviews',
        'document_versions',
        ['document_version_id'],
        ['id'],
        ondelete='CASCADE',
    )


def downgrade():
    op.drop_constraint(None, 'reviews', type_='foreignkey')
    op.drop_column('reviews', 'document_version_id')
    op.drop_column('documents', 's3_key')
    op.drop_column('document_versions', 'type')
    op.drop_column('document_versions', 's3_key')
    op.drop_constraint(None, 'document_antivirus_check', type_='foreignkey')
    op.drop_column('document_antivirus_check', 'document_version_id')
