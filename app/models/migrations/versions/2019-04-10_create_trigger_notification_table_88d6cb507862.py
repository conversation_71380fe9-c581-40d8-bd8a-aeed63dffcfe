""" Create trigger notification table

Revision ID: df6458138792
Revises: cb6ba7ee20c9
Create Date: 2019-04-10 11:57:32.028072


Author: <PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.models.types import SoftEnum
from app.trigger_notifications.enums import TriggerNotificationStatus

revision = 'df6458138792'
down_revision = 'cb6ba7ee20c9'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'trigger_notifications',
        sa.Column(
            'id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False,
        ),
        sa.Column(
            'role_id',
            postgresql.UUID(),
            nullable=True,
        ),
        sa.Column('title', sa.Text(), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('url', sa.Text(), nullable=True),
        sa.Column('status', SoftEnum(TriggerNotificationStatus), nullable=False),
        sa.Column(
            'display_date',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'date_updated',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('created_by', postgresql.UUID(), nullable=True),
        sa.ForeignKeyConstraint(('created_by',), ['roles.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(('role_id',), ['roles.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(
        'idx_trigger_notifications_role_id_date',
        'trigger_notifications',
        ['role_id', sa.text('display_date DESC')],
        unique=False,
    )
    op.create_index(
        'idx_trigger_notifications_role_id_new_status_date',
        'trigger_notifications',
        ['role_id', 'status', sa.text('display_date DESC')],
        unique=False,
        postgresql_where=sa.text("status = 'new'"),
    )


def downgrade():
    op.drop_index(
        'idx_trigger_notifications_role_id_new_status_date',
        table_name='trigger_notifications',
    )
    op.drop_index(
        'idx_trigger_notifications_role_id_date',
        table_name='trigger_notifications',
    )
    op.drop_table('trigger_notifications')
