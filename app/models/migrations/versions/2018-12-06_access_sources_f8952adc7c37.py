""" Add column sources to access_to_doc table

Revision ID: f8952adc7c37
Revises: a05cf99400c6
Create Date: 2018-12-06 19:15:14.177193


Author: <PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f8952adc7c37'
down_revision = 'a05cf99400c6'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'access_to_doc',
        sa.Column('sources', sa.SmallInteger(), nullable=True),
    )


def downgrade():
    op.drop_column('access_to_doc', 'sources')
