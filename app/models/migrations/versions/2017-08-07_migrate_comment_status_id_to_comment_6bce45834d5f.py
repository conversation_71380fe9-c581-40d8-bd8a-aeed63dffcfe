"""Migrate comment.status_id to comment.type.

Revision ID: 6bce45834d5f
Revises: 6d854101fc18
Create Date: 2017-08-07 15:32:11.552575


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6bce45834d5f'
down_revision = '6d854101fc18'
branch_labels = None
depends_on = None

def upgrade():
    op.execute("""
UPDATE comments
SET type = 'comment'
WHERE status_id = 9000;
UPDATE comments
SET type = 'rejection'
WHERE status_id = 9001;
    """)


def downgrade():
    pass
