"""Hash tokens

Revision ID: 4e03d5ba642a
Revises: 6e882d9f3ccf
Create Date: 2018-10-08 17:38:54.063944

Author: <PERSON>
"""

import hashlib
import sqlalchemy as sa
from alembic import op

revision = '4e03d5ba642a'
down_revision = '6e882d9f3ccf'
branch_labels = None
depends_on = None


def upgrade():
    try:
        conn = op.get_bind()
        tokens = conn.execute('select * from tokens')
        for t in tokens:
            token = str(t.token)
            token_hash = hashlib.sha512(token.encode('utf8')).hexdigest()
            conn.execute(f"update tokens set token_hash = '{token_hash}' where token = '{token}'")

    except Exception as err:
        print(str(err))


def downgrade():
    pass
