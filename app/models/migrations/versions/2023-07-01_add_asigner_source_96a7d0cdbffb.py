"""add_asigner_source

Revision ID: 96a7d0cdbffb
Revises: 305318c97063
Create Date: 2023-07-01

Author: <PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa

from app.models.types import SoftEnum
from app.lib.enums import SignersSource


# revision identifiers, used by Alembic.
revision = '96a7d0cdbffb'
down_revision = '305318c97063'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('document_signers', sa.Column('source', SoftEnum(SignersSource), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('document_signers', 'source')
    # ### end Alembic commands ###
