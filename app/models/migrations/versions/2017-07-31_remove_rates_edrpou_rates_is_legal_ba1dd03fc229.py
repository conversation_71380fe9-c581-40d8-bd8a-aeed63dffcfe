"""Remove rates.edrpou, rates.is_legal fields.

Revision ID: ba1dd03fc229
Revises: a869e36d5207
Create Date: 2017-07-31 06:25:34.460871


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ba1dd03fc229'
down_revision = 'a869e36d5207'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('rates', 'company_id',
               existing_type=postgresql.UUID(),
               nullable=False)
    op.create_index('uix_rates_company_id_iscurrent_true', 'rates', ['company_id'], unique=True, postgresql_where=sa.text('is_current IS true'))
    op.drop_index('uix_rates_edrpou_islegal_iscurrent_true', table_name='rates')
    op.drop_column('rates', 'is_legal')
    op.drop_column('rates', 'edrpou')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rates', sa.Column('edrpou', sa.VARCHAR(length=64), autoincrement=False, nullable=False))
    op.add_column('rates', sa.Column('is_legal', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=False))
    op.create_index('uix_rates_edrpou_islegal_iscurrent_true', 'rates', ['edrpou', 'is_legal'], unique=True)
    op.drop_index('uix_rates_company_id_iscurrent_true', table_name='rates')
    op.alter_column('rates', 'company_id',
               existing_type=postgresql.UUID(),
               nullable=True)
    # ### end Alembic commands ###
