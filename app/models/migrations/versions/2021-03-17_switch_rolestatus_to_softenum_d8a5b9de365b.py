"""Switch RoleStatus to SoftEnum

Revision ID: d8a5b9de365b
Revises: 422cc02cc2da
Create Date: 2021-03-17 16:20:51.291620


Author: a<PERSON>k<PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.auth.enums import RoleStatus
from app.models.types import SoftEnum

# revision identifiers, used by Alembic.
revision = 'd8a5b9de365b'
down_revision = '422cc02cc2da'
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column('roles', 'status',
               existing_type=postgresql.ENUM('active', 'pending', 'denied', 'deleted', name='rolestatus'),
               type_=SoftEnum(RoleStatus),
               existing_nullable=False,
               existing_server_default=sa.text("'pending'::rolestatus"))


def downgrade():
    conn = op.get_bind()
    values = conn.execute("SELECT enum_range(NULL::rolestatus);").fetchall()
    values = values[0][0] if values else ''
    op.execute('COMMIT')  # cannot modify enum during transaction, skip it
    for e in RoleStatus:
        if e.value in values:
            continue
        query = sa.text(f"ALTER TYPE rolestatus ADD VALUE '{e.value}';")
        op.execute(query)
    op.execute("ALTER TABLE roles ALTER COLUMN status TYPE rolestatus USING status::rolestatus;")
