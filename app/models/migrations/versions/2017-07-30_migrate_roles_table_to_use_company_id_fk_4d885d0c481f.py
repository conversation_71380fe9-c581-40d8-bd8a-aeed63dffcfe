"""Migrate roles table to use company_id FK.

Revision ID: 4d885d0c481f
Revises: 78bfa54d0bb1
Create Date: 2017-07-30 17:55:35.297421


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4d885d0c481f'
down_revision = '78bfa54d0bb1'
branch_labels = None
depends_on = None

def upgrade():
    op.execute("""
UPDATE roles
SET company_id = companies.id
FROM companies
WHERE companies.edrpou = roles.company_edrpou
AND companies.is_legal = roles.is_legal
    """)


def downgrade():
    pass
