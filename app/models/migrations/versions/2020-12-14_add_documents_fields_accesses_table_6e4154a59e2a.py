"""Add documents_fields_accesses table

Revision ID: 6e4154a59e2a
Revises: fd22500a78e5
Create Date: 2020-12-14 11:18:56.677211


Author: a<PERSON><PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6e4154a59e2a'
down_revision = 'fd22500a78e5'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'documents_fields_accesses',
        sa.Column(
            'id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False,
        ),
        sa.Column('field_id', postgresql.UUID(), nullable=False),
        sa.Column('role_id', postgresql.UUID(), nullable=False),
        sa.Column('updated_by', postgresql.UUID(), nullable=False),
        sa.Column(
            'date_updated',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('date_deleted', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ('field_id',), ['documents_fields.id'], ondelete='CASCADE'
        ),
        sa.ForeignKeyConstraint(('role_id',), ['roles.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(('updated_by',), ['roles.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
    )
    op.create_index(
        op.f('ix_documents_fields_accesses_role_id'),
        'documents_fields_accesses',
        ['role_id'],
        unique=False,
    )
    op.create_index(
        'uix_documents_fields_accesses_field_id_role_id',
        'documents_fields_accesses',
        ['field_id', 'role_id'],
        unique=True,
    )


def downgrade():
    op.drop_index(
        'uix_documents_fields_accesses_field_id_role_id',
        table_name='documents_fields_accesses',
    )
    op.drop_index(
        op.f('ix_documents_fields_accesses_role_id'),
        table_name='documents_fields_accesses',
    )
    op.drop_table('documents_fields_accesses')
