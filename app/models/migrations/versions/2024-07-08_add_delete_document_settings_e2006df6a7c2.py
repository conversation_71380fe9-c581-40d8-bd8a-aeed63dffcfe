"""add delete_document_settings

Revision ID: e2006df6a7c2
Revises: debba23c89f7
Create Date: 2024-07-08 18:48:45.695612

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e2006df6a7c2'
down_revision = 'debba23c89f7'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'delete_document_settings',
        sa.Column('document_id', sa.String(length=64), nullable=False),
        sa.Column('is_delete_request_required', sa.<PERSON>(), server_default='0', nullable=False),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'date_updated',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('document_id'),
    )


def downgrade():
    op.drop_table('delete_document_settings')
