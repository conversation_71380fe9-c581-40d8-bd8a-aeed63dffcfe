"""Update user_meta table [2]

Revision ID: 47d93f81f4a7
Revises: 48965f051123
Create Date: 2023-02-09 08:24:55.686828

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '47d93f81f4a7'
down_revision = '48965f051123'
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
        ALTER TABLE user_meta
        RENAME COLUMN has_invited_user to has_invited_coworker;
        """
    )


def downgrade():
    op.execute(
        """
        ALTER TABLE user_meta
        RENAME COLUMN has_invited_coworker to has_invited_user;
        """
    )
