"""Add user_meta table

Revision ID: 422cc02cc2da
Revises: 350adecc5752
Create Date: 2021-03-03 21:48:24.580375


Author: a<PERSON><PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '422cc02cc2da'
down_revision = '350adecc5752'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_meta',
    sa.Column('user_id', sa.String(length=64), nullable=True),
        sa.Column('invalid_password_count', sa.SmallInteger(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.UniqueConstraint('user_id', name='uix_meta_user_id')
    )


def downgrade():
    op.drop_table('user_meta')
