"""add extension to document_versions

Revision ID: 141c76f577b0
Revises: 9d612c06e104
Create Date: 2023-04-24 13:53:23.678080

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '141c76f577b0'
down_revision = '9d612c06e104'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('document_versions', sa.Column('extension', sa.String(length=64), nullable=True))


def downgrade():
    op.drop_column('document_versions', 'extension')
