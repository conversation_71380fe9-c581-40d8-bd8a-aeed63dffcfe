"""Add documents count esputnik metadata

Revision ID: 79439bbf1aca
Revises: 8ef7cb6a5ee0
Create Date: 2021-08-30 18:07:28.087305


Author: m.dodonchuk
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '79439bbf1aca'
down_revision = '8ef7cb6a5ee0'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('user_meta', sa.Column('has_signed_incoming_document', sa.<PERSON>(), server_default='0', nullable=False))
    op.add_column('user_meta', sa.Column('sent_documents_count_synced', sa.BIGINT(), nullable=True))


def downgrade():
    op.drop_column('user_meta', 'sent_documents_count_synced')
    op.drop_column('user_meta', 'has_signed_incoming_document')
