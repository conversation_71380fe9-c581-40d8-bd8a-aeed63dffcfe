"""add template_id to draft

Revision ID: 7f1baee097db
Revises: 8ffc9de82765
Create Date: 2024-10-10 15:50:43.459687

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7f1baee097db'
down_revision = '8ffc9de82765'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('drafts', sa.Column('template_id', postgresql.UUID(), nullable=True))
    op.create_index(op.f('ix_drafts_template_id'), 'drafts', ['template_id'], unique=False)
    op.create_foreign_key(None, 'drafts', 'templates', ['template_id'], ['id'], ondelete='CASCADE')


def downgrade():
    op.drop_constraint(None, 'drafts', type_='foreignkey')
    op.drop_index(op.f('ix_drafts_template_id'), table_name='drafts')
    op.drop_column('drafts', 'template_id')
