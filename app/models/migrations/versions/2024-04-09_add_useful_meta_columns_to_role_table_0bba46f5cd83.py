"""add_useful_meta_columns_to_role_table

Revision ID: 0bba46f5cd83
Revises: 12e9584d240b
Create Date: 2024-04-09 15:17:11.391891

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0bba46f5cd83'
down_revision = '12e9584d240b'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'roles', sa.Column('has_few_signatures', sa.<PERSON>(), server_default='1', nullable=False)
    )
    op.add_column(
        'roles', sa.Column('has_few_reviews', sa.<PERSON>(), server_default='1', nullable=False)
    )


def downgrade():
    op.drop_column('roles', 'has_few_reviews')
    op.drop_column('roles', 'has_few_signatures')
