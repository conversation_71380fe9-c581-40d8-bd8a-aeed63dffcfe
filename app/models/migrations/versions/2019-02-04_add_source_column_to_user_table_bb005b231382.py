"""Add source column to user table

Revision ID: bb005b231382
Revises: 25ab39e6b404
Create Date: 2019-02-04 17:06:23.878100


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'bb005b231382'
down_revision = '25ab39e6b404'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('users', sa.Column('source', sa.String(length=64), nullable=True))


def downgrade():
    op.drop_column('users', 'source')
