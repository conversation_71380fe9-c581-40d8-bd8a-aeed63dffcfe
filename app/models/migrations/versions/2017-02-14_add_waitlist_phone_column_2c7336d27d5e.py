"""Add waitlist.phone column

Revision ID: 2c7336d27d5e
Revises: 232ce3454cf3
Create Date: 2017-02-14 19:45:03.643397


Author: <PERSON>
"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = '2c7336d27d5e'
down_revision = '232ce3454cf3'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'waitlist',
        sa.Column(
            'phone',
            sa.String(length=16),
            nullable=True
        )
    )


def downgrade():
    op.drop_column('waitlist', 'phone')
