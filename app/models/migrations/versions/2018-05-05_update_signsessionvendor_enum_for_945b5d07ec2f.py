"""Update SignSessionVendor enum for Nemiroff.

Revision ID: 945b5d07ec2f
Revises: c485297b2137
Create Date: 2018-05-05 04:28:13.395259


Author: lequan
"""
from alembic import op

from app.models.migrations.utils import modify_db_enum


# revision identifiers, used by Alembic.
revision = '945b5d07ec2f'
down_revision = 'c485297b2137'
branch_labels = None
depends_on = None


def upgrade():
    modify_db_enum(
        op,
        'sign_sessions',
        'vendor',
        'signsessionvendor',
        "('agroyard', 'caparol', 'delivery', 'intertelecom', 'nemiroff', 'novaposhta', 'sovtes', 'vchasno', 'x3', 'zakupki')",
    )


def downgrade():
    modify_db_enum(
        op,
        'sign_sessions',
        'vendor',
        'signsessionvendor',
        "('agroyard', 'caparol', 'delivery', 'intertelecom', 'novaposhta', 'sovtes', 'vchasno', 'x3', 'zakupki')",
    )
