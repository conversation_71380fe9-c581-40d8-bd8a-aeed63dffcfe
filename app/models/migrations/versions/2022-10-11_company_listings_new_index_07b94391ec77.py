"""company_listings_new_index

Revision ID: 07b94391ec77
Revises: 6add345f189f
Create Date: 2022-10-11 15:15:11.062184

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "07b94391ec77"
down_revision = "6add345f189f"
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT;')
    op.execute(
        """
        CREATE INDEX
        CONCURRENTLY
        IF NOT EXISTS ix_edrpou_date_created_document_num
        ON company_first_access_to_doc (edrpou, date_created DESC, document_num DESC);
        """
    )


def downgrade():
    op.drop_index(
        "ix_edrpou_date_created_document_num", table_name="company_first_access_to_doc"
    )
