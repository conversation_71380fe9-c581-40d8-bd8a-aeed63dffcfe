"""signatures_drop_status

Revision ID: 95791b776553
Revises: c182af5ecb33
Create Date: 2023-01-04 13:52:24.669092

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '95791b776553'
down_revision = 'c182af5ecb33'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column('signatures', 'status')


def downgrade():
    op.add_column(
        'signatures',
        sa.Column(
            'status',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=False,
        ),
    )
