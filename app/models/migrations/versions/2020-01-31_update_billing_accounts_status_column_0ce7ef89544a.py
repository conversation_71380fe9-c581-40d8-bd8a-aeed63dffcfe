"""Update billing_accounts.status column type

Revision ID: 0ce7ef89544a
Revises: c4591b92d773
Create Date: 2020-01-31 16:00:57.218078


Author: <PERSON>
"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
from app.billing.enums import AccountStatus
from app.models.migrations.utils import modify_db_enum
from app.models.types import SoftEnum

revision = '0ce7ef89544a'
down_revision = 'c4591b92d773'
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        'billing_accounts',
        'status',
        existing_type=postgresql.TIMESTAMP(timezone=True),
        type_=SoftEnum(AccountStatus),
        existing_nullable=True,
    )
    modify_db_enum(
        op,
        'billing_transactions',
        'type',
        'transactiontype',
        "('invoice_payment', 'invoice_payment_cancel', "
        "'debit_charge_off', 'bonus_income', 'bonus_cancel', "
        "'bonus_charge_off', 'credit_use', 'credit_repay', "
        "'debt_clearance', 'cross_company', "
        "'rate_payment', 'rate_payment_cancel')"
    )


def downgrade():
    op.drop_column('billing_accounts', 'status')
    op.add_column(
        'billing_accounts',
        sa.Column('status', SoftEnum(AccountStatus), nullable=True),
    )
    modify_db_enum(
        op,
        'billing_transactions',
        'type',
        'transactiontype',
        "('invoice_payment', 'invoice_payment_cancel', "
        "'debit_charge_off', 'bonus_income', 'bonus_cancel', "
        "'bonus_charge_off', 'credit_use', 'credit_repay', "
        "'debt_clearance', 'cross_company')"
    )
