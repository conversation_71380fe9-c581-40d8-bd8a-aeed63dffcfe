"""Add unique constraint to review_requests

Revision ID: 363b232cb2f8
Revises: 077593e208db
Create Date: 2017-12-21 00:54:04.431395

Author: <PERSON>
"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '363b232cb2f8'
down_revision = '077593e208db'
branch_labels = None
depends_on = None


def upgrade():
    op.create_unique_constraint(
        'uix_review_requests_document_id_from_role_id_to_role_id',
        'review_requests',
        ['document_id', 'from_role_id', 'to_role_id']
    )


def downgrade():
    op.drop_constraint(
        'uix_review_requests_document_id_from_role_id_to_role_id',
        'review_requests',
        type_='unique'
    )
