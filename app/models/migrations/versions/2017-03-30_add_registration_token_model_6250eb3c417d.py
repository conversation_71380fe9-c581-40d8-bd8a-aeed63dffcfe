"""Add registration token model.

Revision ID: 6250eb3c417d
Revises: 64fc0a928dc5
Create Date: 2017-03-30 15:45:56.131221


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6250eb3c417d'
down_revision = '64fc0a928dc5'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('registration_tokens',
    sa.Column('token', sa.Text(), nullable=False),
        sa.Column('registered_user_id', sa.String(length=64), nullable=True),
        sa.Column('invited_by_companies', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('invited_by_users', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('invited_with_documents', postgresql.JSON(astext_type=sa.Text()), server_default=sa.text("'[]'"), nullable=True),
        sa.Column('is_verified_inviter', sa.Boolean(), server_default='0', nullable=False),
        sa.Column('date_created', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('date_updated', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('date_registered', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['registered_user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('token')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('registration_tokens')
    # ### end Alembic commands ###
