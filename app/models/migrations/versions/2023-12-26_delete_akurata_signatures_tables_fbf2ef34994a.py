"""delete-akurata-signatures-tables

Revision ID: fbf2ef34994a
Revises: bea8759d2355
Create Date: 2023-12-26 12:09:52.601161

"""
import citext
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'fbf2ef34994a'
down_revision = 'bea8759d2355'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index(
        'ix_signature_templates_user_id',
        table_name='signature_templates',
    )
    op.drop_table('signature_templates')
    op.drop_index(
        'ix_signatures_placeholders_document_id',
        table_name='signatures_placeholders',
    )
    op.drop_table('signatures_placeholders')
    op.drop_column('signatures', 'fields')


def downgrade():
    op.add_column(
        'signatures',
        sa.Column(
            'fields',
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.create_table(
        'signatures_placeholders',
        sa.Column(
            'id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            'document_id',
            sa.VARCHAR(length=64),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            'x',
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            'y',
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            'width',
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            'height',
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column('page', sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column('edrpou', sa.VARCHAR(length=64), autoincrement=False, nullable=False),
        sa.Column('email', citext.CIText(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(
            ('document_id',),
            ['documents.id'],
            name='signatures_placeholders_document_id_fkey',
        ),
        sa.PrimaryKeyConstraint('id', name='signatures_placeholders_pkey'),
    )
    op.create_index(
        'ix_signatures_placeholders_document_id',
        'signatures_placeholders',
        ['document_id'],
        unique=False,
    )
    op.create_table(
        'signature_templates',
        sa.Column(
            'id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column('user_id', sa.VARCHAR(length=64), autoincrement=False, nullable=False),
        sa.Column('type', sa.VARCHAR(length=54), autoincrement=False, nullable=False),
        sa.Column(
            'meta',
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            'date_created',
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text('now()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ('user_id',),
            ['users.id'],
            name='signature_templates_user_id_fkey',
        ),
        sa.PrimaryKeyConstraint('id', name='signature_templates_pkey'),
    )
    op.create_index(
        'ix_signature_templates_user_id',
        'signature_templates',
        ['user_id'],
        unique=False,
    )
