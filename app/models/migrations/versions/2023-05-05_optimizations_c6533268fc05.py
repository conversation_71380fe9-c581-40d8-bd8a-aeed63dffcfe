"""optimizations

Revision ID: c6533268fc05
Revises: 141c76f577b0
Create Date: 2023-05-05 08:34:22.439986

"""
from alembic import op


# revision identifiers, used by Alembic.
revision = 'c6533268fc05'
down_revision = '141c76f577b0'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT;')
    op.execute(
        """
        DROP INDEX
        CONCURRENTLY
        IF EXISTS
        idx_documents_is_invalid_signed;
        """
    )
    op.execute(
        """
        CREATE INDEX
        CONCURRENTLY
        IF NOT EXISTS
           idx_document_id_is_invalid_signed
        ON documents (id, is_invalid_signed)
        WHERE is_invalid_signed is TRUE;
        """
    )
    op.execute(
        """
        CREATE INDEX
        CONCURRENTLY
        IF NOT EXISTS
           idx_signatures_document_id_algo
        ON signatures (document_id, algo)
        WHERE algo is not null;
        """
    )


def downgrade():
    op.drop_index(
        'idx_signatures_document_id_algo',
        table_name='signatures',
    )
    op.drop_index(
        'idx_document_id_is_invalid_signed',
        table_name='documents',
    )
    op.create_index(
        'idx_documents_is_invalid_signed', 'documents', ['is_invalid_signed'], unique=False
    )
