"""Add youcontrol companies table

Revision ID: ba97a6b33c9f
Revises: 939de102f1fa
Create Date: 2024-06-24 12:13:24.676957

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ba97a6b33c9f'
down_revision = '939de102f1fa'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'youcontrol_companies',
        sa.Column('company_id', postgresql.UUID(), nullable=False),
        sa.Column('is_company_active', sa.<PERSON>(), server_default='0', nullable=False),
        sa.Column('contractor_type', sa.Text(), nullable=True),
        sa.Column('response_edr', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('date_sync_edr', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(('company_id',), ['companies.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('company_id'),
    )


def downgrade():
    op.drop_table('youcontrol_companies')
