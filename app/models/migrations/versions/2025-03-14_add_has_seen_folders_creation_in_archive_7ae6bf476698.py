"""add has_seen_folders_creation_in_archive

Revision ID: 7ae6bf476698
Revises: 9cb4feb4f36c
Create Date: 2025-03-14 10:52:31.653987

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7ae6bf476698'
down_revision = '9cb4feb4f36c'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'user_onboarding',
        sa.Column(
            'has_seen_folders_creation_in_archive', sa.<PERSON><PERSON>an(), server_default='0', nullable=False
        ),
    )


def downgrade():
    op.drop_column('user_onboarding', 'has_seen_folders_creation_in_archive')
