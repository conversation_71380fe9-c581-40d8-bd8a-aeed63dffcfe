"""Add temp signature migration table

Revision ID: 582b08e2adcc
Revises: ad278bf5f065
Create Date: 2024-04-08 15:18:09.073719

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '582b08e2adcc'
down_revision = 'ad278bf5f065'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'signature_migration_errors',
        sa.Column(
            'id',
            sa.String(length=64),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False,
        ),
        sa.Column('signature_id', postgresql.UUID(), nullable=False),
        sa.Column('error', sa.Text(), nullable=False),
        sa.Column('context', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint('id'),
    )


def downgrade():
    op.drop_table('signature_migration_errors')
