"""add document_cloud_signer

Revision ID: 220c7374bdc3
Revises: 7450c0c18866
Create Date: 2021-10-12 10:50:51.525782


Author: a<PERSON><PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '220c7374bdc3'
down_revision = '************'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'document_cloud_signer',
        sa.Column(
            'id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False,
        ),
        sa.Column('document_id', sa.String(length=64), nullable=False),
        sa.Column('role_id', postgresql.UUID(), nullable=False),
        sa.Column('operation_id', sa.String(length=88), nullable=False),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ['document_id'], ['documents.id'], ondelete='NO ACTION'
        ),
        sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ondelete='NO ACTION'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('operation_id'),
    )
    op.create_index(
        op.f('ix_document_cloud_signer_document_id'),
        'document_cloud_signer',
        ['document_id'],
        unique=False,
    )


def downgrade():
    op.drop_index(
        op.f('ix_document_cloud_signer_document_id'), table_name='document_cloud_signer'
    )
    op.drop_table('document_cloud_signer')
