"""Add document_versions table

Revision ID: e82139737bdc
Revises: f6f27657cfc9
Create Date: 2020-06-30 15:04:17.602632


Author: <PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e82139737bdc'
down_revision = 'f6f27657cfc9'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'document_versions',
        sa.Column(
            'id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False,
        ),
        sa.Column('document_id', sa.String(length=64), nullable=True),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ('document_id',), ['documents.id'], ondelete='NO ACTION'
        ),
        sa.PrimaryKeyConstraint('id'),
    )
    op.create_index(
        op.f('ix_document_versions_document_id'),
        'document_versions',
        ['document_id'],
        unique=False,
    )


def downgrade():
    op.drop_index(
        op.f('ix_document_versions_document_id'), table_name='document_versions'
    )
    op.drop_table('document_versions')
