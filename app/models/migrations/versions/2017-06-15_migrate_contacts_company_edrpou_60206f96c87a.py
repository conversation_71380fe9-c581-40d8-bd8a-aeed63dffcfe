"""Migrate contacts.company_edrpou.

Revision ID: 60206f96c87a
Revises: 1474adbf449d
Create Date: 2017-06-15 11:46:17.101612


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '60206f96c87a'
down_revision = '1474adbf449d'
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # 1) Action (user or document) identifies EDRPOU, which uploaded contacts
    # 2) If contacts were created before roles were implemented in Vchasno -
    # use 1st role EDRPOU
    for row in conn.execute("""
        WITH user_action AS (
            SELECT
              r.user_id,
              ua.date_created,
              ua.edrpou as company_edrpou
            FROM user_actions AS ua
              LEFT JOIN roles r ON ua.role_id = r.id
            GROUP BY
              r.user_id,
              ua.date_created,
              ua.edrpou
        ),
            document_action AS (
              SELECT
                r.user_id,
                da.date_created,
                da.edrpou as company_edrpou
              FROM document_actions AS da
                LEFT JOIN roles r ON da.role_id = r.id
              GROUP BY
                r.user_id,
                da.date_created,
                da.edrpou
          ),
            user_roles AS (
              SELECT
                r.user_id,
                r.date_created,
                r.company_edrpou,
                row_number() OVER (
                  PARTITION BY r.user_id
                  ORDER BY r.date_created ) AS role_num
              FROM roles r
          )
        SELECT
          cc.id,
          CASE
              WHEN r1.company_edrpou IS NOT NULL THEN r1.company_edrpou
              WHEN r2.company_edrpou IS NOT NULL THEN r2.company_edrpou
          ELSE
              r3.company_edrpou
          END AS company_edrpou
        FROM contragent_companies AS cc
          LEFT JOIN (
                      SELECT
                        id,
                        company_edrpou
                      FROM (
                             SELECT
                               cc.id,
                               r.company_edrpou,
                               row_number() OVER (
                                 PARTITION BY cc.id, cc.user_id, cc.date_created
                                 ORDER BY r.date_created DESC
                                 ) AS row_num
                             FROM contragent_companies cc
                               LEFT JOIN user_action r
                                 ON cc.user_id = r.user_id AND
                                    cc.date_created >= r.date_created
                           ) sub1
                      WHERE sub1.row_num = 1
                    ) r1 ON cc.id = r1.id
          LEFT JOIN (
                      SELECT
                        id,
                        company_edrpou
                      FROM (
                             SELECT
                               cc.id,
                               r.company_edrpou,
                               row_number() OVER (
                                 PARTITION BY cc.id, cc.user_id, cc.date_created
                                 ORDER BY r.date_created DESC
                                 ) AS row_num
                             FROM contragent_companies cc
                               LEFT JOIN document_action r
                                 ON cc.user_id = r.user_id AND
                                    cc.date_created >= r.date_created
                           ) sub2
                      WHERE sub2.row_num = 1
                    ) r2 ON cc.id = r2.id
          LEFT JOIN (
                      SELECT
                        user_id,
                        company_edrpou
                      FROM user_roles
                      WHERE role_num = 1
                    ) r3 ON cc.user_id = r3.user_id
        WHERE cc.company_edrpou is NULL;
    """):
        conn.execute(
            sa.text("""
                UPDATE contragent_companies
                SET company_edrpou = :company_edrpou
                WHERE id = :id"""),
            company_edrpou=row.company_edrpou,
            id=row.id)


def downgrade():
    pass
