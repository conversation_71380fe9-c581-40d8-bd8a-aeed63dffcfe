"""Add document_required_fields.is_amount_required field

Revision ID: 55baeb484e87
Revises: 4ac0b19fbe16
Create Date: 2023-11-16 18:10:38.884626

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '55baeb484e87'
down_revision = '4ac0b19fbe16'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'document_required_fields',
        sa.Column('is_amount_required', sa.<PERSON>(), server_default='0', nullable=False),
    )


def downgrade():
    op.drop_column('document_required_fields', 'is_amount_required')
