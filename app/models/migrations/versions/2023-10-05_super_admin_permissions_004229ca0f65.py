"""super_admin_permissions

Revision ID: 004229ca0f65
Revises: 18cac4ce9255
Create Date: 2023-03-23 17:04:48.056393

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '004229ca0f65'
down_revision = '18cac4ce9255'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'super_admin_permissions',
        sa.<PERSON>umn('role_id', postgresql.UUID(), index=True, unique=True, nullable=False),
        sa.<PERSON>umn('can_view_client_data', sa.<PERSON>(), server_default='0', nullable=False),
        sa.<PERSON>umn('can_edit_client_data', sa.<PERSON>(), server_default='0', nullable=False),
        sa.Column(
            'can_edit_special_features',
            sa.<PERSON>(),
            server_default='0',
            nullable=False,
        ),
        sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ondelete='CASCADE'),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('super_admin_permissions')
    # ### end Alembic commands ###
