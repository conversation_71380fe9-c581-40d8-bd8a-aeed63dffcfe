"""Update User model.

Revision ID: 22350c7dace6
Revises: 8236606c473a
Create Date: 2017-03-05 13:39:57.007101


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '22350c7dace6'
down_revision = '8236606c473a'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('registration_completed', sa.<PERSON>(), server_default='0', nullable=False))
    op.execute('UPDATE users SET registration_completed = true')
    op.execute('UPDATE users SET ecp_confirmed = false WHERE ecp_confirmed IS NULL')
    op.alter_column('users', 'ecp_confirmed',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               server_default='0')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'ecp_confirmed',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               server_default=None)
    op.execute('UPDATE users SET ecp_confirmed = NULL WHERE ecp_confirmed = false')
    op.drop_column('users', 'registration_completed')
    # ### end Alembic commands ###
