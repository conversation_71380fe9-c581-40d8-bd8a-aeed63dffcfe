"""Add date_received index

Revision ID: 1de482a92702
Revises: 179a5e03ebd8
Create Date: 2025-04-28 13:28:39.674758

"""

from alembic import op
import sqlalchemy as sa

from app.config import is_prod_environ

# revision identifiers, used by Alembic.
revision = '1de482a92702'
down_revision = '179a5e03ebd8'
branch_labels = None
depends_on = None


def upgrade():
    # on production, execute this migration manually
    if is_prod_environ():
        return

    op.execute('COMMIT;')
    op.execute(
        """
        CREATE INDEX CONCURRENTLY
        IF NOT EXISTS ix_document_recipients_edrpou_id_not_received
        ON document_recipients (edrpou, id)
        WHERE date_sent is not null and date_received is null;
        """
    )


def downgrade():
    op.drop_index(
        'ix_document_recipients_edrpou_id_not_received',
        table_name='document_recipients',
        postgresql_where=sa.text('date_sent is not null and date_received is null'),
        postgresql_concurrently=True,
    )
