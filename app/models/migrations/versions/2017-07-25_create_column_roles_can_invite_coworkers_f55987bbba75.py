"""Create column roles.can_invite_coworkers.

Revision ID: f55987bbba75
Revises: a60736d92a11
Create Date: 2017-07-25 08:24:38.793564


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f55987bbba75'
down_revision = 'a60736d92a11'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('roles', sa.Column('can_invite_coworkers', sa.<PERSON>(), server_default='1', nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('roles', 'can_invite_coworkers')
    # ### end Alembic commands ###
