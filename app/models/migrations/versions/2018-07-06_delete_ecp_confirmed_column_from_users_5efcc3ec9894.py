"""Delete ecp_confirmed column from users table.

Revision ID: 5efcc3ec9894
Revises: e4acaed81eb4
Create Date: 2018-07-06 13:52:56.350963


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5efcc3ec9894'
down_revision = 'e4acaed81eb4'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column('users', 'ecp_confirmed')


def downgrade():
    op.add_column(
        'users',
        sa.Column(
            'ecp_confirmed',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=False,
        ),
    )
