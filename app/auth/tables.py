import sqlalchemy as sa

from app.auth.enums import (
    InviteSource,
    RoleActivationSource,
    RoleStatus,
    UserEmailChangeStatus,
)
from app.esputnik.enums import Event
from app.lib.enums import DocumentListSortDate, Language
from app.models import columns, metadata
from app.registration.enums import RegistrationMethod

company_table = sa.Table(
    'companies',
    metadata,
    columns.UUID(),
    columns.EDRPOU(nullable=False, index=True),
    columns.IPN(),
    columns.CompanyName(),
    columns.CompanyName('full_name'),
    columns.<PERSON>olean('is_legal', True),
    columns.EDRPOU('dealt_by_company', nullable=True),
    columns.ForeignKey('dealt_by_user', 'users.id', ondelete='SET NULL'),
    columns.Boolean('is_dealer', False),
    columns.Boolean('render_signature_in_interface', True),
    columns.Boolean('render_signature_on_print_document', True),
    columns.Counter('upload_documents_left'),
    columns.Boolean('is_unlimited_upload'),
    # Examples: "Послуги у сфері краси", "ІТ сфера"
    columns.Text('activity_field', nullable=True),
    # Examples: "20","11-50", "більше 1000"
    sa.Column('employees_number', sa.String(64)),
    columns.Phone(),
    # =====================
    # Security settings
    # =====================
    # Allow to add users only with email from this domains
    columns.JSONArrayLegacy('email_domains'),
    # Trusted IPs for login
    # apply to all roles in the company (except for admin)
    # Applied to all requests from this role even for API
    columns.JSONB('allowed_ips', server_default=sa.text("'[]'")),
    # Trusted IPs for using some public API methods
    # Such as cloud-signer
    columns.JSONB('allowed_api_ips', server_default=sa.text("'[]'")),
    # Logout user after inactivity in (seconds)
    # default 30 days (30 * 24 * 60 * 60) = 2592000
    sa.Column('inactivity_timeout', sa.Integer, nullable=True),
    # =====================
    # Dates
    # =====================
    columns.DateCreated(),
    columns.DateUpdated(),
    # =====================
    # Indexes
    # =====================
    sa.UniqueConstraint('edrpou', 'is_legal', name='uix_edrpou_is_legal'),
)

company_statistic_table = sa.Table(
    'companies_statistics',
    metadata,
    columns.ForeignKey(
        name='company_id',
        mixed='companies.id',
        primary_key=True,
        ondelete='CASCADE',
        nullable=False,
    ),
    sa.Column('roles_count', sa.Integer, nullable=True),
    sa.Column('tags_count', sa.Integer, nullable=True),
    sa.Column('templates_count', sa.Integer, nullable=True),
    sa.Column('document_fields_count', sa.Integer, nullable=True),
)

company_meta_table = sa.Table(
    'company_meta',
    metadata,
    columns.ForeignKey(
        name='company_id',
        mixed='companies.id',
        primary_key=True,
        ondelete='CASCADE',
        index=True,
        nullable=False,
    ),
    # stores last documents count threshold synced with esputnik
    sa.Column('sent_documents_count_synced', sa.BIGINT()),
    # stores last event sent about documents run out to esputnik
    columns.SoftEnum('documents_expires_last_event', Event, nullable=True),
    # stores the newest billing_account's id when event sent
    columns.UUID(
        'documents_expires_last_billing_account_id',
        nullable=True,
        primary_key=False,
        server_default=None,
    ),
    # {{ stores info in which way company has been invited to service
    # https://evocompany.atlassian.net/wiki/spaces/vchasno/pages/*********
    columns.SoftEnum('invited_type', InviteSource, nullable=True),
    columns.EDRPOU('invited_edrpou', nullable=True),
    # }}
)

company_config_table = sa.Table(
    'companies_config',
    metadata,
    columns.ForeignKey(
        name='company_id',
        mixed='companies.id',
        primary_key=True,
        ondelete='CASCADE',
        nullable=False,
    ),
    columns.JSONB('config'),
    columns.JSONB('admin_config'),
)

role_table = sa.Table(
    'roles',
    metadata,
    columns.UUID(),
    columns.ForeignKey('company_id', 'companies.id', nullable=False, ondelete='NO ACTION'),
    columns.ForeignKey('deleted_by', 'roles.id', nullable=True, ondelete='NO ACTION'),
    # Who invited this role? It can bу either a coworker or a recipient
    columns.ForeignKey('invited_by', 'roles.id', nullable=True, ondelete='NO ACTION'),
    columns.ForeignKey('activated_by', 'roles.id', nullable=True, ondelete='NO ACTION'),
    columns.SoftEnum('activation_source', RoleActivationSource, nullable=True),
    columns.ForeignKey('user_id', 'users.id', nullable=False, ondelete='NO ACTION', index=True),
    sa.Column('user_role', sa.Integer(), nullable=False),
    columns.Boolean('can_view_document'),
    columns.Boolean('can_comment_document'),
    columns.Boolean('can_upload_document'),
    columns.Boolean('can_download_document'),
    columns.Boolean('can_print_document'),
    columns.Boolean('can_delete_document'),
    # deprecated use `can_sign_and_reject_document_external`
    # and `can_sign_and_reject_document_internal`
    # TODO: remove after migration to new permissions
    columns.Boolean('can_sign_and_reject_document'),
    columns.Boolean('can_sign_and_reject_document_external'),
    columns.Boolean('can_sign_and_reject_document_internal'),
    columns.Boolean('can_invite_coworkers'),
    columns.Boolean('can_edit_company'),
    columns.Boolean('can_edit_roles'),
    columns.Boolean('can_create_tags', server_default='1'),
    columns.Boolean('can_edit_document_automation'),
    columns.Boolean('can_edit_document_fields'),
    columns.Boolean('can_edit_document_category'),
    columns.Boolean('can_extract_document_structured_data'),
    columns.Boolean('can_edit_document_structured_data'),
    columns.Boolean('can_archive_documents'),
    columns.Boolean('can_delete_archived_documents'),
    columns.Boolean('can_edit_templates'),
    columns.Boolean('can_edit_directories'),
    columns.Boolean('can_remove_itself_from_approval'),
    columns.Boolean('can_edit_security'),
    columns.Boolean('can_download_actions'),
    # Зміна розпочатого процесу підписання/погодження - можливість замінити в
    # учасників підписання/погодження, що ще не виконали свою дію. Це право не
    # дає можливість прибирати тих користувачів, що вже підписали/погодили документ
    columns.Boolean('can_change_document_signers_and_reviewers'),
    # More rights for document deletion
    # extended right for document deletion
    columns.Boolean('can_delete_document_extended'),
    columns.Boolean('can_edit_company_contact'),
    columns.Boolean('can_edit_required_fields'),
    columns.Boolean('can_view_private_document'),
    columns.Boolean('can_view_coworkers', default=True),
    # User can control document versions
    # which documents can be versioned
    columns.Boolean('can_edit_version_settings', default=False),
    # User can upload create versioned document or upload new version
    columns.Boolean('can_add_document_versions', default=True),
    # User can delete version from versioned document not created by him
    columns.Boolean('can_delete_other_versions', default=True),
    # Email management
    columns.Boolean('can_receive_inbox'),
    columns.Boolean('can_receive_inbox_as_default'),
    columns.Boolean('can_receive_comments'),
    columns.Boolean('can_receive_rejects'),
    columns.Boolean('can_receive_reminders'),
    columns.Boolean('can_receive_reviews'),
    # Represent other notification from previous notification
    columns.Boolean('can_receive_notifications', default=True),
    columns.Boolean('can_receive_access_to_doc', default=True),
    columns.Boolean('can_receive_delete_requests', default=True),
    columns.Boolean('can_receive_finished_docs', default=False),
    # Notification about a finished review process (document uploader)
    columns.Boolean('can_receive_review_process_finished', default=False),
    # Notification about a finished review process (user who created a review request)
    columns.Boolean('can_receive_review_process_finished_assigner', default=True),
    # Notification about a finished sign process (document uploader)
    columns.Boolean('can_receive_sign_process_finished', default=False),
    # Notification about a finished sign process (user who created a sign process)
    columns.Boolean('can_receive_sign_process_finished_assigner', default=True),
    # Notification new roles
    columns.Boolean('can_receive_new_roles', default=False),
    # Notification about integration token expiration
    columns.Boolean('can_receive_token_expiration', default=False),
    # Notification about role's email change
    columns.Boolean('can_receive_email_change', default=False),
    # Notification about admin role deletion
    columns.Boolean('can_receive_admin_role_deletion', default=False),
    # Useful meta for onboarding
    columns.Boolean('has_few_signatures', default=True),
    columns.Boolean('has_few_reviews', default=True),
    # Trusted IPs for login
    # Applied to all requests from this role even for API
    columns.JSONArrayLegacy('allowed_ips'),
    # Trusted IPs for using some public API methods
    # Such as cloud-signer
    columns.JSONArrayLegacy('allowed_api_ips'),
    columns.Enum(
        name='sort_documents',
        native_enum=DocumentListSortDate,
        nullable=True,
        enum_name='documentlistorder',
    ),
    columns.Boolean('show_invite_tooltip'),
    # show child documents in listing ?
    columns.Boolean('show_child_documents', default=True),
    columns.Text('position', nullable=True),
    # Has the role signed at least one document?
    columns.Boolean('has_signed_documents'),
    # This role was created by a user with his own signature key.
    # We use this field for informing other services via crm-proxy that it's safe to create
    # an active role in their own systems.
    #
    # NOTE: currently, this field is set to true only for explicitly added companies by that
    # user: "complete_registration", "validate_diia_action_add_role", "add_company" actions.
    # For all other cases, like adding user to company during signing, this field is set to false.
    columns.Boolean('with_signature_key', default=False),
    # will get all documents with autofilled recipient
    columns.Boolean('is_default_recipient', default=False),
    columns.SoftEnum('status', RoleStatus, default_value=RoleStatus.pending),
    # Whether role has HRS functionality
    columns.Boolean('has_hrs_role'),
    # Whether role counts towards billing company limit
    columns.Boolean('is_counted_in_billing_limit', default=True),
    columns.DateCreated(),
    columns.DateUpdated(),
    # We use this column to determine whether a user needs to see a popup informing them that
    # they have been added to the company. If this field is not null, it indicates that the user
    # has either already seen the popup or we have set it to the current date in cases where
    # showing the popup is unnecessary. If the field is null, it means the popup needs to be
    # displayed.
    # Also, we use this column to sort roles on the frontend.
    columns.DateTime('date_agreed'),
    columns.DateTime('date_deleted'),
    columns.DateTime('date_invited'),
    # Date when the role was activated
    # NOTE: this date is not updated when the role is restored from auto blocked status, like
    # "blocked_2fa" or "rate_deleted"
    columns.DateTime('date_activated'),
    sa.UniqueConstraint('company_id', 'user_id', name='uix_company_id_user_id'),
    # Indexes
    # - for sending trigger notification to everyone
    sa.Index('idx_roles_date_created_asc', sa.Column('date_created').asc()),
)

token_table = sa.Table(
    'tokens',
    metadata,
    columns.UUID(),
    sa.Column('token_hash', sa.String(128), index=True, nullable=True),
    sa.Column('role_id', None, sa.ForeignKey('roles.id', ondelete='CASCADE'), nullable=False),
    sa.Column('vendor', sa.String(32), index=True, nullable=False),  # deprecated field
    columns.DateCreated(),
    columns.DateExpired(),
)

# TODO: delete after tokens refactoring (add lifetime)
token_access_time_table = sa.Table(
    'token_access_times',
    metadata,
    columns.UUID('role_id', unique=True),
    columns.Counter(),
    columns.DateTime('date_last_access', auto_now=True, auto_now_add=True),
)

user_table = sa.Table(
    'users',
    metadata,
    columns.ID(),
    columns.ForeignKey('created_by', 'roles.id', ondelete='NO ACTION'),
    columns.ForeignKey('last_role_id', 'roles.id', ondelete='SET NULL'),
    columns.ForeignKey('pending_referrer_role_id', 'roles.id', ondelete='SET NULL'),
    columns.Email(unique=True, nullable=True),
    sa.Column('password', sa.String(255), nullable=True),
    # Phone number used for authentication, should be unique and normalized.
    # Keep in sync with "phone" column when phone number is updated
    columns.Phone('auth_phone', nullable=True, unique=True, index=True),
    # Old column for contact and 2FA phone number, it might contain duplicates and phone numbers
    # are not validated and normalized
    columns.Phone('phone', nullable=True, index=True),
    # Random salt to generate signature for "Trust this device" 2FA feature
    columns.UUID('phone_salt', primary_key=False),
    sa.Column('first_name', sa.String(64)),
    sa.Column('second_name', sa.String(64)),
    sa.Column('last_name', sa.String(64)),
    sa.Column('promo_code', sa.Text()),
    columns.Boolean('is_logged_once', default=False),
    # Is email confirmed by user via email verification email?
    columns.Boolean('email_confirmed'),
    # Is user registered/created in the system as real user or just preallocated for future
    # registration?
    columns.Boolean('is_placeholder', default_value=False),
    # Do we banned user from using the system?
    columns.Boolean('is_banned', default_value=False),
    # Does user have at least one active role?
    columns.Boolean('registration_completed'),
    columns.Boolean('is_autogenerated_password'),
    columns.Boolean('is_phone_verified'),
    columns.Boolean('is_2fa_enabled'),
    columns.Boolean('trial_auto_enabled', default_value=False),
    # ID of chat with Telegram bot, used for Telegram notification
    sa.Column('telegram_chat_id', sa.BIGINT()),
    # source possible values RegistrationSource
    sa.Column('source', sa.String(64)),
    columns.Boolean('is_subscribed_esputnik', default=False),
    # External auth providers
    columns.Text('google_id', nullable=True, unique=True, index=True),
    columns.Text('microsoft_id', nullable=True, unique=True, index=True),
    columns.Text('apple_id', nullable=True, unique=True, index=True),
    columns.SoftEnum('registration_method', enum_=RegistrationMethod, nullable=True),
    # User's preferred language
    columns.SoftEnum('language', enum_=Language, nullable=True),
    # Dates
    columns.DateCreated(),
    columns.DateUpdated(),
    columns.DateDeleted(),
    # Store info about KEP popup last displayed date etc
    columns.JSONB('extra'),
    sa.CheckConstraint(
        sqltext='email IS NOT NULL OR auth_phone IS NOT NULL',
        name='check_users_email_or_auth_phone',
    ),
)

user_meta_table = sa.Table(
    'user_meta',
    metadata,
    columns.ForeignKey('user_id', 'users.id', ondelete='CASCADE', nullable=False),
    sa.Column('invalid_password_count', sa.SmallInteger(), default=0),
    # Track whether the user has opened the document at least once
    # from a mobile device (default is False) using web version (not mobile app)
    columns.Boolean('mobile_usage', default=False),
    sa.UniqueConstraint('user_id', name='uix_meta_user_id'),
)


# In this table, we are storing information about changes in user's email address.
user_email_change_table = sa.Table(
    'user_email_changes',
    metadata,
    columns.UUID(),
    columns.ForeignKey('user_id', 'users.id', ondelete='CASCADE', nullable=False),
    columns.Email('old_email', nullable=True),
    columns.Email('new_email', nullable=False),
    columns.SoftEnum('status', UserEmailChangeStatus),
    columns.DateTime('date_password_verified'),
    columns.DateTime('date_email_verified'),
    columns.DateTime('date_2fa_verified'),
    columns.DateTime('date_confirmed'),
    columns.DateCreated(),
    columns.DateUpdated(),
    # Date when the user has started the process of syncing email in contacts
    columns.Date('date_contacts_synced', nullable=True),
    columns.Seqnum(name='seqnum', seq_name='user_email_changes_seqnum'),
)

is_active_filter = sa.and_(
    role_table.c.status == RoleStatus.active,
    user_table.c.registration_completed.is_(True),
)

role_company_join = role_table.outerjoin(
    company_table, company_table.c.id == role_table.c.company_id
)

user_role_join = user_table.outerjoin(role_table, role_table.c.user_id == user_table.c.id)

user_role_company_join = user_role_join.outerjoin(
    company_table, company_table.c.id == role_table.c.company_id
)

user_role_company_token_join = user_role_company_join.outerjoin(
    token_table, token_table.c.role_id == role_table.c.id
)


# Joins to active roles
user_active_role_join = user_table.outerjoin(
    role_table,
    sa.and_(
        role_table.c.user_id == user_table.c.id,
        is_active_filter,
    ),
)

user_active_role_company_join = user_active_role_join.outerjoin(
    company_table, company_table.c.id == role_table.c.company_id
)

# fmt: off
user_role_company_strict_join = (
    user_table
    .join(role_table, role_table.c.user_id == user_table.c.id)
    .join(company_table, company_table.c.id == role_table.c.company_id)
)
# # fmt: on

active_role_company_join = role_table.outerjoin(
    company_table,
    sa.and_(
        company_table.c.id == role_table.c.company_id,
        role_table.c.status == RoleStatus.active,
    ),
)

active_role_alias = (
    role_table.select().where(role_table.c.status == RoleStatus.active)
).alias()


# https://vchasno-group.atlassian.net/browse/DOC-7355
# Temporary table to migrate users from Vchasno.Zvit
zvit_users_table = sa.Table(
    'zvit_users',
    metadata,
    columns.ForeignKey(
        'id',
        'users.id',
        nullable=False,
        ondelete='NO ACTION',
        index=True,
        primary_key=True,
    ),
    columns.Email(unique=True),
    # Passwords are stored in PBKDF2-HMAC-SHA256 algo
    sa.Column('password', sa.String(255), nullable=True),
    columns.ForeignKey(
        'imported_by_role_id',
        'roles.id',
        nullable=False,
        ondelete='NO ACTION',
        index=False,
    ),
    columns.DateCreated(),
)
