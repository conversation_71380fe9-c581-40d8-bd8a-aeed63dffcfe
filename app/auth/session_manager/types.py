from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime

from aiohttp import web
from ua_parser import user_agent_parser

from app.lib.datetime_utils import parse_utc_timestamp, utc_now
from app.lib.helpers import get_client_ip
from app.lib.types import DataDict


@dataclass
class LastActivityDeviceInfo:
    browser: str
    browser_version: str | None
    os: str
    os_version: str | None
    device: str

    @staticmethod
    def from_header_string(header: str) -> LastActivityDeviceInfo:
        parsed = user_agent_parser.Parse(header)
        browser_version = parsed['user_agent']['major']
        if parsed['user_agent']['minor']:
            browser_version += '.' + parsed['user_agent']['minor']
        if parsed['user_agent']['patch']:
            browser_version += '.' + parsed['user_agent']['patch']
        os_version = parsed['os']['major']
        if parsed['os']['minor']:
            os_version += '.' + parsed['os']['minor']
        if parsed['os']['patch']:
            os_version += '.' + parsed['os']['patch']

        return LastActivityDeviceInfo(
            browser=parsed['user_agent']['family'],
            browser_version=browser_version,
            os=parsed['os']['family'],
            os_version=os_version,
            device=parsed['device']['family'],
        )

    @staticmethod
    def from_redis_dict(data: DataDict) -> LastActivityDeviceInfo:
        return LastActivityDeviceInfo(
            browser=data['b'],
            browser_version=data['bv'],
            os=data['os'],
            os_version=data['osv'],
            device=data['d'],
        )

    def to_redis_dict(self) -> DataDict:
        return {
            'b': self.browser,
            'bv': self.browser_version,
            'os': self.os,
            'osv': self.os_version,
            'd': self.device,
        }


@dataclass
class LastActivity:
    """
    Represents the last activity of the user.
    """

    accessed_at: datetime
    device_info: LastActivityDeviceInfo
    ip: str | None
    country: str | None = None
    city: str | None = None

    @staticmethod
    def from_request(request: web.Request) -> LastActivity:
        return LastActivity(
            accessed_at=utc_now(),
            device_info=LastActivityDeviceInfo.from_header_string(
                request.headers.get('User-Agent', '')
            ),
            ip=get_client_ip(request),
            country=request.headers.get('CF-IPCountry'),
            city=request.headers.get('CF-IPCity'),
        )

    @staticmethod
    def from_dict(session: DataDict) -> LastActivity:
        return LastActivity(
            accessed_at=parse_utc_timestamp(session['ts']),
            device_info=LastActivityDeviceInfo.from_redis_dict(session['di']),
            ip=session['ip'],
            country=session.get('country'),
            city=session.get('city'),
        )

    def to_session_dict(self) -> DataDict:
        return {
            'ts': self.accessed_at.timestamp(),
            'di': self.device_info.to_redis_dict(),
            'ip': self.ip,
            'country': self.country,
            'city': self.city,
        }


@dataclass(frozen=True)
class SessionData:
    internal_id: str  # session id (stored in redis) and used to revoke the session
    session: DataDict  # content of the session


@dataclass(frozen=True)
class ActiveSession:
    """
    Represents the active session of the user.
    """

    internal_id: str  # session id (stored in redis) and used to revoke the session
    public_session_id: str  # stored under (PUBLIC_SESSION_ID_SESSION_KEY)
    last_activity: LastActivity  # stored under (LAST_ACTIVITY_SESSION_KEY)


@dataclass(frozen=True)
class LoginSessionResponse:
    """
    Is used to represent the session data in the graphql response.
    """

    # This id is used to revoke the session.
    # It should be the public session id (stored under PUBLIC_SESSION_ID_SESSION_KEY).
    id: str

    accessed_at: str
    ip: str | None
    browser: str
    browser_version: str | None
    os: str
    os_version: str | None
    device: str
    country: str | None = None
    city: str | None = None
    is_current: bool = False

    @staticmethod
    def from_active_session(session: ActiveSession, is_current: bool) -> LoginSessionResponse:
        return LoginSessionResponse(
            id=session.public_session_id,
            accessed_at=session.last_activity.accessed_at.isoformat(),
            ip=session.last_activity.ip,
            browser=session.last_activity.device_info.browser,
            browser_version=session.last_activity.device_info.browser_version,
            os=session.last_activity.device_info.os,
            os_version=session.last_activity.device_info.os_version,
            device=session.last_activity.device_info.device,
            country=session.last_activity.country,
            city=session.last_activity.city,
            is_current=is_current,
        )
