# Session manager

Модуль відповідає за можливіть керування сесіями користувачів.

- Можливість переглянути всі сесії користувача
- Можливіть видалити сесію користувача (якщо вона твоя чи ти адмін)

Ця додаткова інформація зберігаєтсья безпосередньо в об'єкті сесії користувача. Так що кожна сесія
містить додаткову інформацію про користувача. (див. `LastActivity`)

## Особливості

При запиті на сервер в middleware до сесії користувача додається інформація про час останньої
активності користувача. (див. `store_last_user_activity`) Тому при кожному запиті на сервер - ця
інформація оновлюється.

Враховуючи те, що інформація зберігається в сесії, цей функціонал буде працювати тільки для web
сесій (тобто, якщо користувач заходить на сайт через браузер). Але не буде працювати для API запитів
таких як Public API чи Mobile API.

### PUBLIC_SESSION_ID_SESSION_KEY

В сесію також зберігаємо згенерований id (PUBLIC_SESSION_ID_SESSION_KEY). Він використовується для
керування сесію через апі.

#### Чому ж не використати звичайний id сесії?

Звичайна id сесії яка вже існує і зберігається в Redis з ключем (get_session_redis_key) не може бути
використаний для видалення сесії, оскільки використовується безпосередньо для доступу до сесії. Все
добре якщо це робить сам користувач, але якщо це робить інший користувач - виникає проблема. З цим
id можна отримати доступ до сесії і робити дії від імені користувача.
