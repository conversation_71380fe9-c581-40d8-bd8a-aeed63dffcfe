from __future__ import annotations

from enum import Enum, StrEnum, auto, unique

from app.lib.enums import NamedEnum


@unique
class AuthMethod(Enum):
    session = 'session'
    sign_session = 'sign_session'
    token = 'token'
    mobile_token = 'mobile_token'


@unique
class SystemAccountEmail(Enum):
    billing = '<EMAIL>'


class AuthFactor(Enum):
    # user is authenticated with email and password or social auth
    email = 'email'

    # user is authenticated with phone number and OTP code sent by SMS or Viber
    phone = 'phone'


@unique
class RoleStatus(StrEnum):
    active = 'active'
    denied = 'denied'
    deleted = 'deleted'
    blocked_2fa = 'blocked_2fa'  # got 2fa enabled, but no valid phone
    user_deleted = 'user_deleted'  # used when SA deleted user from system
    rate_deleted = 'rate_deleted'  # deleted by expired rate
    # Seems like this is deprecated.
    # Status was used for roles, when we invite user to other company.
    # But now we don't have such functionality,
    # so Role with this status should be created anymore
    # See: DOC-6955
    pending = 'pending'

    @property
    def is_active(self) -> bool:
        return self == RoleStatus.active

    @property
    def is_deleted(self) -> bool:
        return self == RoleStatus.deleted

    @property
    def is_denied(self) -> bool:
        return self == RoleStatus.denied

    @property
    def is_pending(self) -> bool:
        return self == RoleStatus.pending


class RoleActivationSource(StrEnum):
    # The role was activated by signing a registration token
    signature = 'signature'

    # Invited by coworker
    invite = 'invite'

    # Restored by coworker
    restore = 'restore'

    # The role was created by synchronization with crm-proxy (other products)
    sync_role = 'sync_role'

    # Activated by super admin
    super_admin = 'super_admin'


@unique
class ExternalService(Enum):
    edi = 'edi'


@unique
class StatEntity(Enum):
    # every value corresponds to company_stats table columns
    role = 'roles_count'
    tag = 'tags_count'
    template = 'templates_count'
    document_field = 'document_fields_count'


@unique
class InviteSource(StrEnum):
    """
    Represents in which way company has been invited to service if any.
    https://evocompany.atlassian.net/wiki/spaces/vchasno/pages/*********
    """

    invite_letter = 'invite_letter'
    document = 'document'


class AuthFlow(NamedEnum):
    registration = auto()
    login = auto()


class UserEmailChangeStatus(NamedEnum):
    # Initial state of the email change request
    started = auto()

    # Waiting for the user to verify the previous email address (if password is not set).
    verify_email = auto()

    # Waiting for the user to enter their password (if password is set).
    verify_password = auto()

    # Waiting for 2FA confirmation via phone (if 2FA is enabled).
    verify_2fa = auto()

    # Waiting for the user to confirm the new email via a link
    pending = auto()

    # Canceled by the user or system.
    cancelled = auto()

    # User confirmed the new email via the link.
    confirmed = auto()


@unique
class EmployeePositions(NamedEnum):
    """
    This enum represents positions that a user can choose in the registration form. The
    registration form also allows for entering a custom position. Because of this, we store this
    value in the "roles.position" database column as a free-form string in the Ukrainian
    language. See the ".name_uk" property for the Ukrainian name of the position.

    Also, check the "PositionType" enum, which represents the result of parsing the raw position
    string from the database to extract as much information as possible from it.

    How to choose which enum to use:

    - If you need to work with a position that is represented in the registration form and can
    ignore custom positions, use this enum.
    - If you need to extract as much information as possible from the raw position string and
    make decisions based on it, use the "PositionType" enum.

    NOTE: Keep positions in sync with "utilsLocalisation.js" on the frontend.
    """

    accountant = 'accountant'
    chief_accountant = 'chief_accountant'
    owner = 'owner'
    director = 'director'
    commercial_director = 'commercial_director'
    financial_director = 'financial_director'
    it_director = 'it_director'
    inspector_of_personnel_department = 'inspector_of_personnel_department'
    head_of_hr = 'head_of_hr'
    lawyer = 'lawyer'
    head_legal_department = 'head_legal_department'
    sales_specialist = 'sales_specialist'

    @classmethod
    def from_role_position(cls, raw_position: str) -> EmployeePositions | None:
        if position := EMPLOYEE_POSITION_NAMES_UK_REVERSED_LOWERCASE.get(
            raw_position.lower().strip()
        ):
            return position

        # Fall back to more general positions
        if position_type := PositionType.from_role_position(raw_position):
            return POSITION_TYPE_TO_EMPLOYEE_POSITION_MAP.get(position_type)

        return None

    @property
    def name_uk(self) -> str:
        """
        Return Ukrainian name of the position
        """
        return EMPLOYEE_POSITION_NAMES_UK[self]

    @staticmethod
    def esputnik_tov_notification_positions() -> list[str]:
        positions = [
            EmployeePositions.accountant,
            EmployeePositions.director,
            EmployeePositions.financial_director,
        ]
        return [position.name_uk for position in positions]


EMPLOYEE_POSITION_NAMES_UK = {
    EmployeePositions.accountant: 'Бухгалтер',
    EmployeePositions.chief_accountant: 'Головний бухгалтер',
    EmployeePositions.owner: 'Власник',
    EmployeePositions.director: 'Директор',
    EmployeePositions.commercial_director: 'Комерційний директор',
    EmployeePositions.financial_director: 'Фінансовий директор',
    EmployeePositions.it_director: 'IT-директор',
    EmployeePositions.inspector_of_personnel_department: 'Інспектор відділу кадрів',
    EmployeePositions.head_of_hr: 'Керівник відділу кадрів',
    EmployeePositions.lawyer: 'Юрист',
    EmployeePositions.head_legal_department: 'Керівник юридичного відділу',
    EmployeePositions.sales_specialist: 'Спеціаліст із продажів',
}

# { "Бухгалтер": "accountant", ... }
EMPLOYEE_POSITION_NAMES_UK_REVERSED: dict[str, EmployeePositions]
EMPLOYEE_POSITION_NAMES_UK_REVERSED_LOWERCASE: dict[str, EmployeePositions]
EMPLOYEE_POSITION_NAMES_UK_REVERSED = {v: k for k, v in EMPLOYEE_POSITION_NAMES_UK.items()}
EMPLOYEE_POSITION_NAMES_UK_REVERSED_LOWERCASE = {
    v.lower(): k for k, v in EMPLOYEE_POSITION_NAMES_UK.items()
}


class PositionType(NamedEnum):
    """
    This enum represents a result of parsing raw position string from the database to extract
    a general position type. It's trying to extract as much information as possible from the raw
    position string, so it can have more general positions than "EmployeePositions" enum.

    See "EmployeePositions" enum for more information.
    """

    top_manager = auto()  # Managers from both fop and tov
    top_manager_fop = auto()  # Only managers from fop
    top_manager_tov = auto()  # Only managers from tov

    accountant = auto()
    director_accountant = auto()
    lawyer = auto()
    director_lawyer = auto()
    hr = auto()
    director_hr = auto()
    it = auto()
    sales = auto()
    director_sales = auto()
    middle_manager = auto()

    @classmethod
    def from_role_position(cls, position: str) -> PositionType | None:
        """
        Convert a raw free-form string in Ukrainian, stored in the "roles.position"
        field of the database, into a predefined enum value.
        """
        return _UK_POSITION_TO_POSITION_TYPE_MAP.get(position.lower().strip())


_UK_POSITION_TO_POSITION_TYPE_MAP = {
    # Accountant
    'бухгалтер': PositionType.accountant,
    'помічник бухгалтера': PositionType.accountant,
    'економіст з бухгалтерського обліку та аналізу'
    ' господарської діяльності': PositionType.accountant,
    'обліковець з реєстрації бухгалтерських даних': PositionType.accountant,
    # Director Accountant
    'головний бухгалтер': PositionType.director_accountant,
    'заступник головного бухгалтера': PositionType.director_accountant,
    'провідний бухгалтер': PositionType.director_accountant,
    'головний бухгалтер групи компанії': PositionType.director_accountant,
    'старший бухгалтер': PositionType.director_accountant,
    # Lawyer
    'юрист': PositionType.lawyer,
    'юридичний відділ': PositionType.lawyer,
    'адвокат': PositionType.lawyer,
    'помічник юриста': PositionType.lawyer,
    'менеджер з договірної роботи': PositionType.lawyer,
    'юрисконсульт': PositionType.lawyer,
    # Director Lawyer
    'керівник юридичного департаменту': PositionType.director_lawyer,
    'керівник юридичного відділу': PositionType.director_lawyer,
    'головний юрист напрямку управління нерухомістю': PositionType.director_lawyer,
    'начальник юридичного відділу': PositionType.director_lawyer,
    # Hr
    'інспектор відділу кадрів': PositionType.hr,
    'hr': PositionType.hr,
    'інспектор з кадрів': PositionType.hr,
    'менеджер з персоналу': PositionType.hr,
    'фахівець з кадрового адміністрування': PositionType.hr,
    'hr менеджер': PositionType.hr,
    'кадровик': PositionType.hr,
    'фахівець з персоналу': PositionType.hr,
    'молодший фахівець з кадрового адміністрування': PositionType.hr,
    'hr operations': PositionType.hr,
    'начальник відділу кадрів': PositionType.hr,
    'начальник відділу по роботі з персоналом': PositionType.hr,
    # Director hr
    'керівник відділу кадрів': PositionType.director_hr,
    'керівник відділу персоналу': PositionType.director_hr,
    'директор з управління персоналом': PositionType.director_hr,
    'директор департаменту персоналу': PositionType.director_hr,
    'hrd': PositionType.director_hr,
    # It
    'it-директор': PositionType.it,
    'іт-директор': PositionType.it,
    'it директор': PositionType.it,
    'іт директор': PositionType.it,
    'it': PositionType.it,
    'іт': PositionType.it,
    'іт-спеціаліст': PositionType.it,
    'it-спеціаліст': PositionType.it,
    'іт спеціаліст': PositionType.it,
    'it спеціаліст': PositionType.it,
    'іт менеджер': PositionType.it,
    'it менеджер': PositionType.it,
    'керівник it-відділу': PositionType.it,
    'керівник іт-відділу': PositionType.it,
    'керівник іт відділу': PositionType.it,
    'керівник it відділу': PositionType.it,
    # Sales
    'менеджер з продажу': PositionType.sales,
    'спеціаліст із продажів': PositionType.sales,
    'головний фахівець відділу продажу тке': PositionType.sales,
    'адміністратор відділу продажів': PositionType.sales,
    # Director Sales
    'керівник відділу продажу': PositionType.director_sales,
    'керівник відділу продажів': PositionType.director_sales,
    'начальник відділу продажу': PositionType.director_sales,
    'начальник відділу продаж': PositionType.director_sales,
    'керівник відділу продаж': PositionType.director_sales,
    'заступник генерального директора з післяпродажного обслуговування'
    ' та продажу запасних частин': PositionType.director_sales,
    # Event.new_position_middle_manager
    'адміністратор': PositionType.middle_manager,
    'головний інженер': PositionType.middle_manager,
    'головний спеціаліст': PositionType.middle_manager,
    'довірена особа': PositionType.middle_manager,
    'уповноважена особа': PositionType.middle_manager,
    'менеджер з адміністративної діяльності': PositionType.middle_manager,
    'провідний фахівець': PositionType.middle_manager,
    'ректор': PositionType.middle_manager,
    'головний економіст': PositionType.middle_manager,
    'керівник відділу': PositionType.middle_manager,
    'директор з маркетингу': PositionType.middle_manager,
    'фінансовий директор': PositionType.top_manager,
    'директор': PositionType.top_manager,
    'керівник': PositionType.top_manager,
    'власник': PositionType.top_manager,
    'генеральний директор': PositionType.top_manager,
    'комерційний директор': PositionType.top_manager,
    'т.в.о. директора': PositionType.top_manager_tov,
    'голова наглядової ради': PositionType.top_manager_tov,
    'директор з розвитку': PositionType.top_manager_tov,
    'управитель': PositionType.top_manager_tov,
    'помічник керівника': PositionType.top_manager_tov,
    'начальник відділу збуту': PositionType.top_manager_tov,
    'директор підприємства': PositionType.top_manager_tov,
    'голова ради': PositionType.top_manager_tov,
    'заст. директора': PositionType.top_manager_tov,
    'голова': PositionType.top_manager_tov,
    'технічний директор': PositionType.top_manager_tov,
    'т.в.о.директора': PositionType.top_manager_tov,
    'голова кооперативу': PositionType.top_manager_tov,
    'сільський голова': PositionType.top_manager_tov,
    'голова громадської організації': PositionType.top_manager_tov,
    'керуючий': PositionType.top_manager_tov,
    'директор виконавчий': PositionType.top_manager_tov,
    'начальник відділу': PositionType.top_manager_tov,
    'помічник директора': PositionType.top_manager_tov,
    'заступник генерального директора': PositionType.top_manager_tov,
    'керуючий партнер': PositionType.top_manager_tov,
    'операційний директор': PositionType.top_manager_tov,
    'голова фонду': PositionType.top_manager_tov,
    'начальник управління': PositionType.top_manager_tov,
    "виконуючий обов'язки директора": PositionType.top_manager_tov,
    'виконавчий директор': PositionType.top_manager_tov,
    'директор департаменту': PositionType.top_manager_tov,
    'голова го': PositionType.top_manager_tov,
    'заступник виконавчого директора': PositionType.top_manager_tov,
    'в. о. директора': PositionType.top_manager_tov,
    'керівник структурного підрозділу': PositionType.top_manager_tov,
    'директор філії': PositionType.top_manager_tov,
    'директор технічний': PositionType.top_manager_tov,
    'директор фонду': PositionType.top_manager_tov,
    'головний фахівець': PositionType.top_manager_tov,
    'виконавча директорка': PositionType.top_manager_tov,
    'голова господарства': PositionType.top_manager_tov,
    'голова організації': PositionType.top_manager_tov,
    'президент': PositionType.top_manager_tov,
    'в.о. директора': PositionType.top_manager_tov,
    'голова правління': PositionType.top_manager_tov,
    'голова сфг': PositionType.top_manager_tov,
    'заступник директора': PositionType.top_manager_tov,
    'директорка': PositionType.top_manager_tov,
    'голова фг': PositionType.top_manager_tov,
    'голова фермерського господарства': PositionType.top_manager_tov,
    'голова осбб': PositionType.top_manager_tov,
    'в.о.директора': PositionType.top_manager_tov,
    'засновник': PositionType.top_manager_tov,
    'директор відділу закупівель': PositionType.top_manager_tov,
    'керуючий бюро': PositionType.top_manager_tov,
    'начальник': PositionType.top_manager_tov,
    'голова правління осбб': PositionType.top_manager_tov,
    'фо-п': PositionType.top_manager_fop,
    'фізична особа підприємець': PositionType.top_manager_fop,
    'приватний підприємець': PositionType.top_manager_fop,
    'фізична особа-підприємець': PositionType.top_manager_fop,
    'фізична особа - підприємець': PositionType.top_manager_fop,
    'фоп': PositionType.top_manager_fop,
    'підприємець': PositionType.top_manager_fop,
    'фізична особа': PositionType.top_manager_fop,
}

# Match a position type to employee position if it's possible
# because PositionsType has some more general positions such as top_manager or
# IT, while EmployeePositions has more specific positions like owner or it_director
POSITION_TYPE_TO_EMPLOYEE_POSITION_MAP = {
    PositionType.accountant: EmployeePositions.accountant,
    PositionType.director_accountant: EmployeePositions.chief_accountant,
    PositionType.lawyer: EmployeePositions.lawyer,
    PositionType.director_lawyer: EmployeePositions.head_legal_department,
    PositionType.hr: EmployeePositions.inspector_of_personnel_department,
    PositionType.director_hr: EmployeePositions.head_of_hr,
    PositionType.sales: EmployeePositions.sales_specialist,
    PositionType.director_sales: EmployeePositions.commercial_director,
}
