import typing as t
from uuid import uuid4

import pydantic
from aiohttp import web

from api.errors import AccessDenied, DoesNotExist, InvalidRequest, Object
from app.auth.types import User
from app.auth.validators import validate_coworkers_roles_ids, validate_user_permission
from app.billing.enums import CompanyPermission
from app.document_automation.conditions import flatten_context
from app.document_automation.db import select_automation, select_document_automation_template
from app.document_automation.enums import DocumentAutomationStatus
from app.document_automation.types import (
    ActivateAutomationCtx,
    CreateTemplateCtx,
    DocumentAutomationAutomationTemplate,
    DocumentAutomationTemplateFieldsSettings,
    DocumentAutomationTemplateReviewSettings,
    DocumentAutomationTemplateSignersSettings,
    DocumentAutomationTemplateViewersSettings,
    DocumentTemplateTagsSettings,
    SaveAssignedDocumentAutomationTemplateCtx,
    UpdateDocumentAutomationTemplateCtx,
)
from app.documents.validators import (
    validate_document_exists,
)
from app.documents_fields import validators as document_fields_validators
from app.groups.db import select_groups
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection, DBRow
from app.lib.helpers import (
    contains_duplicates,
    grouped,
)
from app.lib.types import DataDict
from app.profile.validators import validate_company_permission
from app.reviews.types import ReviewsInfoCtx
from app.tags.utils import (
    spit_existed_and_new_tags,
)
from app.tags.validators import (
    TagName,
    validate_tags_permissions,
)


class TagsSettingsSchema(pydantic.BaseModel):
    tags: pv.UniqueList[TagName] = pydantic.Field(min_length=1)


class ConditionEqualitySchema(pydantic.BaseModel):
    eq: list[str] = pydantic.Field(min_length=2, max_length=2)

    def to_dict(self) -> DataDict:
        return self.model_dump(mode='json')


class ConditionComparisonSchema(pydantic.BaseModel):
    le: list[str | int] | None = pydantic.Field(None, min_length=2, max_length=2)
    ge: list[str | int] | None = pydantic.Field(None, min_length=2, max_length=2)

    def to_dict(self) -> DataDict:
        result = {}
        if self.le is not None:
            result['le'] = self.le
        if self.ge is not None:
            result['ge'] = self.ge
        return result


# Currently we support conditions of such shape:
# { and: [
#    { or: [
#         { eq: ['#column1', 'value1'] },
#         { eq: ['#column1', 'value2'] },
#    ]}
#    { or: [
#         { eq: ['#column2', 'value1'] },
#    ]}
#    { or: [
#         { le: ['#column3', 'value1'] },
#         { ge: ['#column3', 'value2'] },
#    ]}
#    ...
# ]}
class ConditionOrSchema(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(populate_by_name=True)

    or_: list[ConditionEqualitySchema] | None = pydantic.Field(
        None, alias='or', serialization_alias='or', min_length=1, max_length=2000
    )
    and_: list[ConditionComparisonSchema] | None = pydantic.Field(
        None, alias='and', serialization_alias='and', min_length=1, max_length=2000
    )

    def to_dict(self) -> dict[str, t.Any]:
        result = {}
        if self.or_ is not None:
            result['or'] = [item.to_dict() for item in self.or_]
        if self.and_ is not None:
            result['and'] = [item.to_dict() for item in self.and_]
        return result


class ConditionsSettingsSchema(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(populate_by_name=True)

    and_: list[ConditionOrSchema] = pydantic.Field(
        alias='and', serialization_alias='and', min_length=1
    )

    def to_dict(self) -> dict[str, t.Any]:
        return {'and': [item.to_dict() for item in self.and_]}


class CreateTemplateSchema(pydantic.BaseModel):
    name: str = pydantic.Field(min_length=1, max_length=512)
    review_settings: DocumentAutomationTemplateReviewSettings | None = None
    signers_settings: DocumentAutomationTemplateSignersSettings | None = None
    viewers_settings: DocumentAutomationTemplateViewersSettings | None = None
    fields_settings: DocumentAutomationTemplateFieldsSettings | None = None
    # TODO[DOC-4378]: should be removed.
    tags_settings: DocumentTemplateTagsSettings | None = None
    conditions: ConditionsSettingsSchema | None = None
    new_tags: pv.UniqueList[TagName] | None = pydantic.Field(None, min_length=1)
    tags_ids: pv.UniqueList[pv.UUID] | None = pydantic.Field(None, min_length=1)
    assigned_to: pv.UUID | None = None

    @pydantic.model_validator(mode='after')
    def validate_tags_settings(self) -> t.Self:
        if (
            (signers_settings := self.signers_settings)
            and not signers_settings.signers_ids
            and not signers_settings.signer_entities
        ):
            raise InvalidRequest(reason=_('signers_ids чи singer_entities має бути заповнено'))

        if (
            (review_settings := self.review_settings)
            and not review_settings.reviewers_ids
            and not review_settings.reviewer_entities
        ):
            raise InvalidRequest(reason=_('reviewers_ids чи reviewer_entities має бути заповнено'))

        if (
            (viewers_settings := self.viewers_settings)
            and not viewers_settings.viewers_ids
            and not viewers_settings.viewers_group_ids
        ):
            raise InvalidRequest(reason=_('viewers_ids чи viewers_group_ids має бути заповнено'))
        return self

    def to_create_context(
        self,
        user: User,
        conditions_dict: DataDict | None = None,
        tags_ids: list[str] | None = None,
        new_tags: list[str] | None = None,
    ) -> CreateTemplateCtx:
        return CreateTemplateCtx(
            id_=str(uuid4()),
            name=self.name,
            assigned_to=self.assigned_to,
            review_settings=self.review_settings,
            signers_settings=self.signers_settings,
            viewers_settings=self.viewers_settings,
            fields_settings=self.fields_settings,
            tags_settings=self.tags_settings,
            company_id=user.company_id,
            created_by=user.role_id,
            conditions=conditions_dict if conditions_dict != {} else None,
            new_tags=new_tags or [],
            tags_ids=tags_ids or [],
        )


class UpdateTemplateSchema(CreateTemplateSchema):
    delete_tags_ids: pv.UniqueList[pv.UUID] | None = pydantic.Field(None, min_length=1)

    def to_update_context(
        self,
        user: User,
        template_id: str,
        conditions_dict: DataDict | None = None,
        tags_ids: list[str] | None = None,
        new_tags: list[str] | None = None,
        deleted_tags_ids: list[str] | None = None,
    ) -> UpdateDocumentAutomationTemplateCtx:
        return UpdateDocumentAutomationTemplateCtx(
            id_=template_id,
            name=self.name,
            assigned_to=self.assigned_to,
            review_settings=self.review_settings,
            signers_settings=self.signers_settings,
            viewers_settings=self.viewers_settings,
            fields_settings=self.fields_settings,
            tags_settings=self.tags_settings,
            company_id=user.company_id,
            conditions=conditions_dict if conditions_dict != {} else None,
            new_tags=new_tags or [],
            tags_ids=tags_ids or [],
            deleted_tags_ids=deleted_tags_ids or [],
        )


class DeleteTemplateSchema(pydantic.BaseModel):
    template_id: pv.UUID


class SaveAssignedTemplateSchema(pydantic.BaseModel):
    template_id: pv.UUID
    document_id: pv.UUID


class ActivateAutomationSchema(pydantic.BaseModel):
    automation_id: pv.UUID
    is_active: bool


def _get_template_roles_ids_from_schema(
    data: CreateTemplateSchema | UpdateTemplateSchema,
) -> list[str]:
    roles = set()
    if data.review_settings and data.review_settings.reviewers_ids:
        roles.update(data.review_settings.reviewers_ids)
    if data.signers_settings and data.signers_settings.signers_ids:
        roles.update(data.signers_settings.signers_ids)
    if data.viewers_settings and data.viewers_settings.viewers_ids:
        roles.update(data.viewers_settings.viewers_ids)
    if data.assigned_to:
        roles.add(data.assigned_to)
    return list(roles)


def _get_template_group_ids_from_schema(
    data: CreateTemplateSchema | UpdateTemplateSchema,
) -> list[str]:
    groups: set[str] = set()

    if data.signers_settings and data.signers_settings.signer_entities:
        groups.update(i.id for i in data.signers_settings.signer_entities if i.type == 'group')

    if data.review_settings and data.review_settings.reviewer_entities:
        groups.update(
            i.id
            for i in data.review_settings.reviewer_entities
            if i.type == ReviewsInfoCtx.EntityType.group
        )

    if data.viewers_settings and data.viewers_settings.viewers_group_ids:
        groups.update(data.viewers_settings.viewers_group_ids)

    return list(groups)


def _validate_non_empty_schema(data: CreateTemplateSchema | UpdateTemplateSchema) -> None:
    if (
        not data.review_settings
        and not data.signers_settings
        and not data.viewers_settings
        and not data.fields_settings
        and not data.tags_settings
    ):
        raise InvalidRequest(reason=_('Вкажіть хоча б одне налаштування для шаблону'))


async def validate_template_access(
    conn: DBConnection, template_id: str, user: User
) -> DocumentAutomationAutomationTemplate:
    template = await select_document_automation_template(conn, template_id)

    if not template or template.company_id != user.company_id:
        raise AccessDenied(reason=_('У вас немає доступу до шаблону'))

    if template.date_deleted:
        raise AccessDenied(reason=_('Шаблон видалено'))

    return template


async def validate_automation_access(conn: DBConnection, automation_id: str, user: User) -> DBRow:
    automation = await select_automation(conn, automation_id)

    if not automation or automation.company_id != user.company_id:
        raise AccessDenied(reason=_('У вас немає доступу до сценарія'))

    if automation.date_deleted:
        raise AccessDenied(reason=_('Сценарій видалено'))

    return automation


async def _validate_template_fields_schema(
    conn: DBConnection, data: CreateTemplateSchema | UpdateTemplateSchema, user: User
) -> None:
    """Validate document fields for template"""
    if not data.fields_settings:
        return  # no settings, no validations

    fields_ids = [item.field_id for item in data.fields_settings.fields]

    if contains_duplicates(fields_ids):
        raise InvalidRequest(reason=_('Дублюючі поля в сценарії'))

    await document_fields_validators.validate_accesses(conn, fields_ids, user)


async def _validate_template_settings_schema(
    conn: DBConnection, data: CreateTemplateSchema | UpdateTemplateSchema, user: User
) -> None:
    """Validate template settings data"""

    _validate_non_empty_schema(data)

    await validate_coworkers_roles_ids(
        conn=conn,
        roles_ids=_get_template_roles_ids_from_schema(data),
        company_edrpou=user.company_edrpou,
    )

    group_ids = _get_template_group_ids_from_schema(data)
    groups = await select_groups(
        conn,
        ids=group_ids,
        is_deleted=False,
        company_id=user.company_id,
    )

    active_group_ids = {group.id for group in groups if not group.date_deleted}

    if active_group_ids != set(group_ids):
        raise DoesNotExist(Object.groups, group_ids=list(set(group_ids) - active_group_ids))

    await _validate_template_fields_schema(conn, data, user)


async def _validate_template_permission(user: User) -> None:
    validate_user_permission(user, {'can_edit_document_automation'})


async def validate_create_template(
    conn: DBConnection, request: web.Request, user: User
) -> CreateTemplateCtx:
    """Validate ability to create document template"""
    await validate_company_permission(
        conn=conn,
        company_id=user.company_id,
        company_edrpou=user.company_edrpou,
        permission=CompanyPermission.document_templates_enabled,
    )
    await _validate_template_permission(user)

    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(CreateTemplateSchema, raw_data)

    conditions_dict = data.conditions.to_dict() if data.conditions else {}
    validate_automation_conditions(conditions_dict, user)

    await _validate_template_settings_schema(conn, data, user)

    # TODO[DOC-4378]: data.tags_settings should be removed and data.new_tags
    #  and data.tags_ids should be used
    if data.tags_settings:
        tags_ids, new_tags = await spit_existed_and_new_tags(
            conn=conn, tags=data.tags_settings.tags, company_id=user.company_id
        )
        if new_tags:
            await validate_tags_permissions(conn=conn, user=user, count=len(new_tags))
    else:
        tags_ids, new_tags = [], []

    return data.to_create_context(
        user=user,
        conditions_dict=conditions_dict,
        tags_ids=tags_ids,
        new_tags=new_tags,
    )


async def validate_update_template(
    conn: DBConnection, request: web.Request, user: User
) -> UpdateDocumentAutomationTemplateCtx:
    """Validate ability to update document template"""
    await validate_company_permission(
        conn=conn,
        company_id=user.company_id,
        company_edrpou=user.company_edrpou,
        permission=CompanyPermission.document_templates_enabled,
    )
    await _validate_template_permission(user)

    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(UpdateTemplateSchema, raw_data)

    template_id = request.match_info['template_id']
    template = await validate_template_access(conn, template_id, user)

    conditions_dict = data.conditions.to_dict() if data.conditions else {}
    validate_automation_conditions(conditions_dict, user)
    await _validate_template_settings_schema(conn, data, user)

    return data.to_update_context(
        user=user,
        template_id=template.id,
        conditions_dict=conditions_dict,
        tags_ids=data.tags_ids,
        new_tags=data.new_tags,
        deleted_tags_ids=data.delete_tags_ids,
    )


async def validate_delete_template(
    conn: DBConnection, request: web.Request, user: User
) -> DocumentAutomationAutomationTemplate:
    """Validate ability to delete document template"""
    await _validate_template_permission(user)

    template_id = request.match_info['template_id']
    data = validators.validate_pydantic(DeleteTemplateSchema, {'template_id': template_id})

    return await validate_template_access(conn, data.template_id, user)


async def validate_save_assigned_template(
    conn: DBConnection, request: web.Request, user: User
) -> SaveAssignedDocumentAutomationTemplateCtx:
    """Validate ability to save assigned template"""
    await _validate_template_permission(user)

    template_id = request.match_info['template_id']
    document_id = request.match_info['document_id']
    data = validators.validate_pydantic(
        SaveAssignedTemplateSchema, {'template_id': template_id, 'document_id': document_id}
    )

    template = await validate_template_access(conn, data.template_id, user)
    document = await validate_document_exists(conn, document_id=data.document_id)

    return SaveAssignedDocumentAutomationTemplateCtx(
        document=document,
        template=template,
        user=user,
    )


async def validate_activate_automation(
    conn: DBConnection, request: web.Request, user: User
) -> ActivateAutomationCtx:
    """Validate ability to activate/deactivate automation"""
    await _validate_template_permission(user)

    raw_data = await validators.validate_json_request(request)
    raw_data['automation_id'] = request.match_info.get('automation_id')
    data = validators.validate_pydantic(ActivateAutomationSchema, raw_data)

    automation = await validate_automation_access(conn, data.automation_id, user)

    template = await validate_template_exists(
        conn=conn,
        template_id=automation.template_id,
        company_id=user.company_id,
    )

    return ActivateAutomationCtx(
        automation_id=automation.id,
        template=template,
        status=(
            DocumentAutomationStatus.enabled
            if data.is_active
            else DocumentAutomationStatus.disabled
        ),
    )


async def validate_template_exists(
    conn: DBConnection, *, template_id: str, company_id: str | None = None
) -> DocumentAutomationAutomationTemplate:
    template = await select_document_automation_template(conn, template_id, company_id=company_id)
    if not template:
        raise DoesNotExist(Object.template, template_id=template_id)

    return template


def validate_automation_conditions(conditions: DataDict, user: User) -> None:
    """Validate new automation conditions.

    `flatten_context` will return an iterator with pairs of condition and value
    consequentially, so every N element is the key and every N+1 element
    is its value. Each value is a dictionary with only 1 key/value pair,
    so extract this dict values to get condition/value pair and then validate it.
    """
    if not conditions:
        return

    flattened = flatten_context(conditions)
    has_inbox_condition = False
    has_self_recipient = False

    value_dict: DataDict
    condition_dict: DataDict
    for condition_dict, value_dict in grouped(flattened, 2):  # type: ignore
        condition, *__ = condition_dict.values()
        value, *__ = value_dict.values()
        if condition and 'document_recipients.edrpou' in condition and value == user.company_edrpou:
            has_self_recipient = True
        elif condition and 'document_side' in condition and value == 'inbox':
            has_inbox_condition = True

    if has_inbox_condition and has_self_recipient:
        raise InvalidRequest(
            reason=_('Для вхідних документів можна вказувати лише ЄДРПОУ ваших контрагентів'),
        )
