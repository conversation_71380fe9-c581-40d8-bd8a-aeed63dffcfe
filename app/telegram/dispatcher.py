import logging

from aiohttp import web

from app.telegram.commands import (
    on_code,
    on_contact,
    on_other,
    on_phone,
    on_start,
    on_unsubscribe,
)
from app.telegram.enums import DispatcherStatus
from app.telegram.utils import (
    get_chat_id,
    get_message,
    get_text,
    has_contact,
    has_phone,
    has_text,
    has_verification_code,
    is_command,
)
from app.telegram.validators import validate_chat_id

logger = logging.getLogger(__name__)
COMMANDS = {
    '/start': on_start,
    '/unsubscribe': on_unsubscribe,
}


async def dispatcher(request: web.Request) -> web.Response:
    """Function select which view to use based on telegram message
    It tries always return 200 code with a response, because on failure
    Telegram sends the message again several times.
    """
    app = request.app
    json = await request.json()
    try:
        message = get_message(json)

        if not message or not get_chat_id(message):
            return web.json_response({'status': DispatcherStatus.error.value})

        validate_chat_id({'chat_id': get_chat_id(message)})

        if is_command(message) and get_text(message) in COMMANDS:
            command = COMMANDS[get_text(message)]
            return await command(app, message)

        if has_contact(message):
            return await on_contact(app, message)

        if has_text(message) and has_phone(message):
            return await on_phone(app, message)

        if has_text(message) and has_verification_code(message):
            return await on_code(app, message)

        return await on_other(message)

    except Exception:
        logger.exception('Error in Telegram dispatcher', extra={'json': json})
        return web.json_response({'status': DispatcherStatus.error.value})
