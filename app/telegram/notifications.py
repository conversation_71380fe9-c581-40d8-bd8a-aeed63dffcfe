import logging
from typing import cast

from app.auth.constants import COMPANY_ID_REQUEST_KEY
from app.auth.types import User
from app.comments.emailing import can_receive_comment
from app.comments.types import CommentRecipientForNotification
from app.documents.types import Document, DocumentForEmailBase
from app.lib import utm_params
from app.lib.database import DBRow
from app.lib.emailing import build_company_label, can_receive_notification
from app.lib.enums import DocumentFolder, NotificationType
from app.lib.types import DataDict, StrDict
from app.lib.urls import build_url
from app.services import services
from app.telegram.utils import (
    get_unique_recipients,
    make_inline_button,
    render_telegram_message,
    send_message,
)

logger = logging.getLogger(__name__)


async def send_low_balance_notification(
    recipient: User,
    total_units_left: int,
) -> None:
    """
    Send telegram low balance billing notification to recipient

    Input:
        `recipient`: User - notification recipient
        `total_units_left`: int - amount of documents left
    """
    if not recipient.telegram_chat_id:
        logger.info(
            'No telegram chat_id found for recipient',
            extra={'role_id': recipient.role_id},
        )
        return

    if not can_receive_notification(recipient):
        logger.info(
            'Recipient disabled notifications.',
            extra={'role_id': recipient.role_id},
        )
        return

    # Build utm params depending on whether balance is zero or low
    utm = (
        utm_params.TELEGRAM_FINISHING_DOCS
        if total_units_left
        else utm_params.TELEGRAM_FINISHED_DOCS
    )

    # Build company name
    company_name = f'ЄДРПОУ/ІПН: {recipient.company_edrpou}'
    if recipient.company_name:
        company_name = f'{recipient.company_name}, {company_name}'

    await send_message(
        chat_id=recipient.telegram_chat_id,
        text=render_telegram_message(
            template_name='about_low_balance',
            context={
                'left_units': total_units_left,
                'company_name': company_name,
            },
        ),
        reply_markup=make_inline_button(
            'Перейти до документа', build_url('bill_generation', get=utm)
        ),
    )


async def send_document_review_request_telegram(recipient: User, document: DBRow) -> None:
    """Notify user about adding him to document reviewing"""
    if recipient.telegram_chat_id is None or not can_receive_notification(
        recipient, NotificationType.review
    ):
        return

    params = utm_params.TELEGRAM_DOCUMENT_REVIEW_REQUEST.copy()
    params[COMPANY_ID_REQUEST_KEY] = recipient.company_id
    url = build_url('app', tail=f'/documents/{document.id}', get=params)

    context = {
        'doc_title': document.title,
        'doc_url': url,
    }

    message = render_telegram_message('document_review_request', context)

    await send_message(
        chat_id=recipient.telegram_chat_id,
        text=message,
        reply_markup=make_inline_button('Перейти до документа', url),
    )


async def send_document_rejected(
    user: User,
    document: Document,
    recipients: list[User],
    comment: str | None,
) -> None:
    """
    Notify user after document has been rejected by partner.

    NOTE: that function sends telegram notification about a rejected document
    only to recipients companies, so it doesn't work for internal documents.
    To add support for internal documents, update the filter below and add
    a different message.
    """

    recipients = get_unique_recipients(
        [
            recipient
            for recipient in recipients
            if (
                can_receive_notification(recipient, NotificationType.reject)
                and recipient.company_edrpou != user.company_edrpou
            )
        ]
    )
    for recipient in recipients:
        # Build url
        params = utm_params.TELEGRAM_DOCUMENT_REJECTED.copy()
        params[COMPANY_ID_REQUEST_KEY] = recipient.company_id
        url = build_url('app', tail=f'/documents/{document.id}', get=params)

        # Build context
        context = {
            'partner': {
                'company_name': user.company_name,
                'reason': comment,
                'doc_title': document.title,
                'doc_url': url,
            }
        }
        message = render_telegram_message('document_rejected', context)

        await send_message(
            # recipients with empty telegram_chat_id was filtered out by
            # function get_unique_recipients
            chat_id=cast(str, recipient.telegram_chat_id),
            text=message,
            reply_markup=make_inline_button('Перейти до документа', url),
        )


async def send_document_comment(
    user: User,
    document: Document,
    recipients: list[CommentRecipientForNotification],
    comment: str,
) -> None:
    """This functions send notification about new comments to document"""

    # Filter only recipient that can receive comments and has telegram_chat_id
    recipients = get_unique_recipients(
        [recipient for recipient in recipients if can_receive_comment(recipient)]
    )

    for recipient in recipients:
        by_coworker = recipient.company_edrpou == user.company_edrpou
        partner_type = 'Співробітник компанії' if by_coworker else 'Ваш Партнер'

        # Build url
        params = utm_params.TELEGRAM_DOCUMENT_COMMENTED.copy()
        params[COMPANY_ID_REQUEST_KEY] = recipient.company_id
        url = build_url('app', tail=f'/documents/{document.id}', get=params)

        # Build context
        context = {
            'partner': {
                'company_name': user.company_name,
                'comment': comment,
                'doc_title': document.title,
                'doc_url': url,
                'type': partner_type,
            }
        }
        message = render_telegram_message('document_commented', context)

        await send_message(
            chat_id=cast(int, recipient.telegram_chat_id),
            text=message,
            reply_markup=make_inline_button('Перейти до коментарів', url),
        )


async def send_about_inbox_document(
    recipient: User,
    document: DocumentForEmailBase,
    recipient_has_multiple_roles: bool,
) -> None:
    """This function sends telegram message about incoming documents.
    Note: there is no logic of retrieving data from database."""
    # Circular import problem
    from app.documents.utils import is_one_sign_document

    if not can_receive_notification(recipient, NotificationType.inbox):
        return

    chat_id = recipient.telegram_chat_id
    if chat_id is None or not recipient.can_receive_inbox:
        return

    params = utm_params.TELEGRAM_RECEIVED_DOCUMENTS_TO_SIGN.copy()
    subject = 'Ви отримали документ на підпис'
    if is_one_sign_document(document=document._row):
        subject = 'Ви отримали документ'
        params = utm_params.TELEGRAM_RECEIVED_DOCUMENTS_BILL.copy()

    if recipient_has_multiple_roles:
        recipient_label = build_company_label(
            recipient.company_edrpou,
            company_name=recipient.company_name,
        )
        subject = f'{subject} для компанії {recipient_label}'

    params[COMPANY_ID_REQUEST_KEY] = recipient.company_id
    url = build_url('app', tail=f'/documents/{document.id}', get=params)
    partner = f'ЄДРПОУ/ІПН: {document.sender_company_edrpou}'
    if document.sender_company_name:
        partner = f'{document.sender_company_name}, {partner}'

    context = {
        'subject': subject,
        'partner': partner,
        'document_title': document.title,
        'document_url': url,
    }

    message = render_telegram_message('inbox_document', context)
    await send_message(
        chat_id=chat_id,
        text=message,
        reply_markup=make_inline_button('Перейти до документа', url),
    )


async def send_documents_to_signer(
    signer: StrDict,
    documents: list[Document],
    more_documents_count: int | None = None,
) -> None:
    """This functions send telegram notification to signer about assigning
    him to document.
    """
    can_receive = can_receive_notification(signer, NotificationType.inbox)
    if not signer.get('telegram_chat_id') or not can_receive:
        return

    one_document = len(documents) == 1

    url_options: DataDict = utm_params.TELEGRAM_DOC_COORDINATION_INVITE_COLLEAGUE.copy()
    url_options['folder_id'] = DocumentFolder.wait_my_sign.value
    url_options[COMPANY_ID_REQUEST_KEY] = signer['company_id']

    documents_info = [
        {
            'id': doc.id,
            'title': doc.title,
            'url': build_url('app', tail=f'/documents/{doc.id}', get=url_options),
        }
        for doc in documents
    ]
    more_url = build_url('app', tail='/documents', get=url_options)

    document_word = 'документ' if one_document else 'документи'
    subject = f'Ви отримали {document_word} на підпис:'

    context = {
        'site_url': services.config.app.domain,
        'signer': signer,
        'subject': subject,
        'more_files_count': more_documents_count,
        'files': documents_info,
        'more_url': more_url,
    }

    message = render_telegram_message('document_to_signers', context)

    tail = f'/documents/{documents[0].id}' if one_document else '/documents'
    to_documents_url = build_url('app', tail=tail, get=url_options)

    document_word = 'документу' if one_document else 'документів'
    button_message = f'Перейти до {document_word}'
    await send_message(
        chat_id=signer['telegram_chat_id'],
        text=message,
        reply_markup=make_inline_button(button_message, to_documents_url),
    )


async def send_invite_role_by_document(
    user: User,
    company_edrpou: str,
    company_name: str | None,
) -> None:
    """Notify about incoming document with button to add company to
    user profile
    """
    if user.telegram_chat_id is None:
        return

    if not can_receive_notification(user, NotificationType.inbox):
        return

    full_name = f'ЄДРПОУ/ІПН: {company_edrpou}'
    if company_name:
        full_name = f'{company_name}, {full_name}'

    context = {'company_name': full_name}
    message = render_telegram_message('invite_role_by_document', context)

    button_message = 'Додайте компанію через повну (десктопну) версію сайту'
    utm = utm_params.TELEGRAM_INVITE_ROLE_BY_DOC
    url = build_url('app', tail='/settings', get=utm)

    await send_message(
        chat_id=user.telegram_chat_id,
        text=message,
        reply_markup=make_inline_button(button_message, url),
    )
