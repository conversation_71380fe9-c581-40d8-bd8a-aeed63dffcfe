import logging
from typing import Any, TypeVar

import aiohttp
import jinja2
import pydantic
from aiohttp import web

from app.lib.sender.utils import generate_otp_code
from app.lib.types import DataDict
from app.services import services
from app.telegram.enums import DispatcherStatus
from app.telegram.validators import TelegramCodeVerificationSchema, validate_phone_for_telegram

logger = logging.getLogger(__name__)

UserT = TypeVar('UserT', bound=Any)


def has_contact(message: DataDict) -> bool:
    return 'contact' in message


def has_phone(message: DataDict) -> bool:
    text = get_text(message)
    return bool(validate_phone_for_telegram(phone=text))


def has_verification_code(message: DataDict) -> bool:
    text = get_text(message)
    try:
        TelegramCodeVerificationSchema.model_validate({'code': text})
        return True
    except pydantic.ValidationError:
        return False


def has_text(message: DataDict) -> bool:
    return 'text' in message


def is_command(message: DataDict) -> bool:
    return 'text' in message and message['text'].startswith('/')


def get_text(message: DataDict) -> str:
    return message['text']


def get_message(request: dict[str, DataDict]) -> DataDict | None:
    return request.get('message')


def get_chat_id(message: dict[str, DataDict]) -> int:
    return message.get('chat', {})['id']


def get_contact_phone(message: dict[str, DataDict]) -> str | None:
    return message.get('contact', {}).get('phone_number')


def render_telegram_message(template_name: str, context: DataDict) -> str:
    """Helper function for rendering telegram messages"""
    if not template_name.endswith('.jinja2'):
        template_name = f'{template_name}.jinja2'

    directory = 'jinja_templates/telegram'
    env = jinja2.Environment(
        loader=jinja2.PackageLoader('app', directory),
        autoescape=True,
    )

    template = env.get_template(template_name)
    return template.render(**context)


def make_inline_button(text: str, url: str) -> DataDict:
    """Helper function for creating button under post"""
    return {
        'inline_keyboard': [
            [
                {
                    'text': text,
                    'url': url,
                }
            ]
        ],
    }


def get_unique_recipients(recipients: list[UserT]) -> list[UserT]:
    """Filter uniquer and with not None telegram_chat_id attribute"""
    chat_ids: set[int] = set()
    result = []
    for recipient in recipients:
        chat_id = recipient.telegram_chat_id
        if chat_id is None or chat_id in chat_ids:
            continue
        chat_ids.add(chat_id)
        result.append(recipient)
    return result


def build_telegram_url(app: web.Application) -> str:
    """Build correct url for telegram bot api"""
    config = services.config
    if telegram_config := config.telegram:
        return f'{telegram_config.url}{telegram_config.token}'
    return ''


async def send_request_to_telegram(
    command: str,
    data: DataDict,
) -> web.Response:
    """Low-level function for sending request to telegram"""
    telegram_url = build_telegram_url(app=services.app)

    if not telegram_url or not data.get('chat_id'):
        return web.json_response({'status': DispatcherStatus.error.value})

    async with aiohttp.ClientSession() as session:
        url = f'{telegram_url}/{command}'
        async with session.post(url, json=data) as resp:
            json = await resp.json()
            if not json['ok']:
                logger.info(
                    msg='Not valid Telegram send message request',
                    extra={
                        'data': data,
                        'error_description': json.get('description'),
                        'error_params': json.get('parameters'),
                    },
                )
                return web.json_response(
                    {
                        'status': DispatcherStatus.error.value,
                    }
                )

    return web.json_response({'status': DispatcherStatus.ok.value})


async def send_photo(
    chat_id: int | str,
    photo: Any,
    caption: str = '',
) -> web.Response:
    """Function for sending images to telegram"""
    data: DataDict = {
        'chat_id': chat_id,
        'photo': photo,
        'caption': caption,
    }
    return await send_request_to_telegram('sendPhoto', data)


async def send_message(
    chat_id: int | str,
    text: str,
    parse_mode: str = 'HTML',
    disable_web_page_preview: bool = True,
    disable_notification: bool = False,
    reply_markup: DataDict | None = None,
) -> web.Response:
    """Function for sending message to chat with user"""
    data: DataDict = {
        'chat_id': chat_id,
        'text': text,
        'parse_mode': parse_mode,
        'disable_web_page_preview': disable_web_page_preview,
        'disable_notification': disable_notification,
    }
    if reply_markup:
        data['reply_markup'] = reply_markup

    return await send_request_to_telegram('sendMessage', data)


async def get_telegram_code(
    app: web.Application,
    *,
    chat_id: int,
    phone: str,
) -> str | None:
    key = _get_telegram_code_key(chat_id=chat_id, phone=phone)
    return await app['redis'].get(key)


async def reset_telegram_code(
    app: web.Application,
    *,
    chat_id: int,
    phone: str,
) -> None:
    key = _get_telegram_code_key(chat_id=chat_id, phone=phone)
    await app['redis'].delete(key)


def _get_telegram_code_key(chat_id: int, phone: str) -> str:
    return f'telegram-{chat_id}-{phone}'


async def create_telegram_code(
    app: web.Application,
    *,
    chat_id: int,
    phone: str,
) -> str:
    code = generate_otp_code()
    await save_telegram_code(app, chat_id=chat_id, phone=phone, code=code)
    return code


async def save_telegram_code(
    app: web.Application,
    *,
    chat_id: int,
    phone: str,
    code: str,
) -> None:
    config = services.config
    ttl = config.auth.totp_interval
    key = _get_telegram_code_key(chat_id=chat_id, phone=phone)
    await app['redis'].setex(key, value=code, time=ttl)
