import pydantic

from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.types import DataDict


class TelegramCodeVerificationSchema(pydantic.BaseModel):
    code: str = pydantic.Field(min_length=6, max_length=6)


class ChatIDSchema(pydantic.BaseModel):
    chat_id: int


class TelegramPhoneSchema(pydantic.BaseModel):
    phone: pv.Phone


def append_phone_prefix(phone: str) -> str:
    if isinstance(phone, str):
        phone = ''.join(phone.strip().split())
        if phone.startswith('380') and len(phone) == 12:
            phone = f'+{phone}'
        elif phone.startswith('0') and len(phone) == 10:
            phone = f'+38{phone}'
    return phone


def validate_phone_for_telegram(phone: str) -> str | None:
    # Straight way to prepossess different type of phone format that doesn't
    # accept by phone validator
    phone = append_phone_prefix(phone)
    try:
        valid_data = validators.validate_pydantic(TelegramPhoneSchema, {'phone': phone})
        return valid_data.phone
    except Exception:
        return None


def validate_code_verification(code: str) -> str | None:
    valid_data = validators.validate_pydantic(TelegramCodeVerificationSchema, {'code': code})
    return valid_data.code


def validate_chat_id(data: DataDict) -> None:
    validators.validate_pydantic(ChatIDSchema, data)
