from http import HTTPStatus

import pytest

from app.auth.db import update_company_config
from app.auth.schemas import VersionSettings, VersionSettingsCategoryConfig
from app.auth.utils import get_company_config
from app.auth.validators import UpdateCompanyConfigSchema
from app.document_categories import db
from app.document_categories.types import PublicDocumentCategory
from app.document_versions.enums import VersionReviewFlow
from app.services import services
from app.tests import common

LIST_DOCUMENT_CATEGORIES_PUBLIC_API_URL = '/api/v2/document-categories'
CREATE_DOCUMENT_CATEGORY_PUBLIC_API_URL = '/api/v2/document-categories'
UPDATE_DOCUMENT_CATEGORY_PUBLIC_API_URL = '/api/v2/document-categories/{document_category_id}'
DELETE_DOCUMENT_CATEGORY_PUBLIC_API_URL = '/api/v2/document-categories/{document_category_id}'


@pytest.mark.parametrize(
    'active_categories_count, deleted_categories_count',
    [
        (10, 5),
        (0, 3),
    ],
)
async def test_list_document_categories(
    aiohttp_client,
    active_categories_count,
    deleted_categories_count,
):
    """
    Given a user
    When requests to list document categories via public API
    Expected to list them in correct format
    """

    # Prepare
    _, client, user = await common.prepare_client(aiohttp_client, is_admin=True)

    async with services.db.acquire() as conn:
        # Prepare active categories
        for i in range(0, active_categories_count):
            await db.insert_document_category(
                conn=conn,
                title=f'active_title_{i}',
                company_id=user.company_id,
            )

        # Prepare deleted categories
        for i in range(0, deleted_categories_count):
            document_category = await db.insert_document_category(
                conn=conn,
                title=f'active_title_{i}',
                company_id=user.company_id,
            )
            await db.delete_document_category(
                conn=conn,
                document_category_id=document_category.id,
            )

    # Act
    response = await client.get(
        path=LIST_DOCUMENT_CATEGORIES_PUBLIC_API_URL,
        headers=common.prepare_auth_headers(user=user),
    )

    # Assert
    assert response.status == HTTPStatus.OK

    data = await response.json()
    assert len(data) == active_categories_count
    assert (
        len([category for category in data if 'active' in category['category_title']])
        == active_categories_count
    )


async def test_create_internal_document_category(aiohttp_client):
    """
    Given an admin of a company
    When trying to create internal document category via public API
    Expected document category to create successfully
    """

    # Prepare
    _, client, user = await common.prepare_client(aiohttp_client, is_admin=True)

    headers = common.prepare_auth_headers(user=user)

    # Act
    title = 'Test document category'
    response = await client.post(
        path=CREATE_DOCUMENT_CATEGORY_PUBLIC_API_URL,
        json={'title': title},
        headers=headers,
    )

    # Assert
    assert response.status == HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        document_categories = await db.select_available_document_categories(
            conn=conn,
            company_id=user.company_id,
        )

    assert len([category for category in document_categories if category.company_id]) == 1

    document_category = document_categories[0]
    assert document_category.title == title
    assert document_category.is_internal
    assert document_category.date_deleted is None


async def test_update_internal_document_category(aiohttp_client):
    """
    Given an admin of a company
    When trying to update internal document category via public API
    Expected document category to update successfully
    """

    # Prepare
    _, client, user = await common.prepare_client(aiohttp_client, is_admin=True)
    headers = common.prepare_auth_headers(user=user)

    # Prepare document category
    async with services.db.acquire() as conn:
        document_category = await db.insert_document_category(
            conn=conn,
            title='Old document category',
            company_id=user.company_id,
        )

    # Act
    new_title = 'New document category'
    response = await client.patch(
        path=UPDATE_DOCUMENT_CATEGORY_PUBLIC_API_URL.format(
            document_category_id=document_category.id,
        ),
        json={'title': new_title},
        headers=headers,
    )

    # Assert
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        document_category = await db.select_document_category(
            conn=conn,
            document_category_id=document_category.id,
            company_id=user.company_id,
        )

    assert document_category.title == new_title


async def test_delete_internal_document_category(aiohttp_client):
    """
    Given an admin of a company
    When trying to delete internal document category via public API
    Expected document category to delete successfully
    """

    # Prepare
    _, client, user = await common.prepare_client(aiohttp_client, is_admin=True)
    headers = common.prepare_auth_headers(user=user)

    # Prepare document category
    async with services.db.acquire() as conn:
        document_category = await db.insert_document_category(
            conn=conn,
            title='Test document category',
            company_id=user.company_id,
        )

    # Act
    response = await client.delete(
        path=DELETE_DOCUMENT_CATEGORY_PUBLIC_API_URL.format(
            document_category_id=document_category.id,
        ),
        headers=headers,
    )

    # Assert
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        document_category = await db.select_document_category(
            conn=conn,
            document_category_id=document_category.id,
            company_id=user.company_id,
            is_active=False,
        )

    assert document_category.date_deleted is not None


async def test_delete_internal_document_category_version_config_updated(aiohttp_client):
    """
    Given an admin of a company and some config with allowed categories for version
    When trying to delete internal document category via public API
    Expected document category deleted from config successfully
    """

    # Prepare
    _, client, user = await common.prepare_client(aiohttp_client, is_admin=True)
    headers = common.prepare_auth_headers(user=user)

    # Prepare document category
    async with services.db.acquire() as conn:
        document_category = await db.insert_document_category(
            conn=conn,
            title='Test document category',
            company_id=user.company_id,
        )
        await update_company_config(
            conn=conn,
            company_id=user.company_id,
            config=UpdateCompanyConfigSchema(
                version_settings=VersionSettings(
                    review_flow=VersionReviewFlow.restarted,
                    category_config=VersionSettingsCategoryConfig(
                        type='allow',
                        category_ids=[
                            document_category.id,
                            PublicDocumentCategory.sales_invoice.value,
                        ],
                    ),
                )
            ).to_db_config(),
        )
        config = await get_company_config(
            conn=conn,
            company_id=user.company_id,
        )

    # Act
    response = await client.delete(
        path=DELETE_DOCUMENT_CATEGORY_PUBLIC_API_URL.format(
            document_category_id=document_category.id,
        ),
        headers=headers,
    )

    # Assert
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        document_category = await db.select_document_category(
            conn=conn,
            document_category_id=document_category.id,
            company_id=user.company_id,
            is_active=False,
        )
        config = await get_company_config(
            conn=conn,
            company_id=user.company_id,
        )

    assert document_category.date_deleted is not None
    assert config.version_settings.category_config.category_ids == [
        PublicDocumentCategory.sales_invoice.value
    ]
