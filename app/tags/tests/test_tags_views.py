from http import H<PERSON><PERSON><PERSON>us
from unittest.mock import ANY

import pytest
import sqlalchemy as sa

from api import errors
from app.documents.db import select_document_listings
from app.documents.enums import AccessSource
from app.documents.tables import listing_table
from app.documents.tests.utils import prepare_private_document
from app.documents.validators import validate_document_access
from app.lib.enums import User<PERSON><PERSON>
from app.models import select_all
from app.services import services
from app.tags.db import select_tags_by_names
from app.tags.tables import (
    contact_tag_table,
    document_tag_table,
    role_tag_table,
    tag_table,
)
from app.tests.common import (
    CONNECT_MULTIPLE_TAGS_URL,
    CONNECT_TAG_ROLES_URL,
    CREATE_TAG_FOR_ROLES_URL,
    CREATE_TAG_MULTIPLE_URL,
    DISCONNECT_TAG_ROLES_URL,
    TEST_TAG_NAME,
    TEST_TAG_NAME1,
    prepare_auth_headers,
    prepare_client,
    prepare_contact,
    prepare_document_access_via_tag,
    prepare_document_data,
    prepare_user_data,
    request_connect_tags_and_contacts,
    request_connect_tags_and_documents,
    request_connect_tags_and_roles,
    request_create_tags_for_contacts,
    request_create_tags_for_documents,
    request_disconnect_roles_and_tags,
    request_disconnect_tags_and_contacts,
    request_disconnect_tags_and_documents,
    update_company_config,
)

TEST_UUID_1 = '1daf4f89-df5c-4e1e-8dd5-8992439cc410'
ANOTHER_TAG_NAME = 'ANOTHER TAG'


@pytest.mark.parametrize(
    'soft_access_to_tags, expected_status',
    [
        (
            True,
            HTTPStatus.OK,
        ),
        (
            False,
            HTTPStatus.FORBIDDEN,
        ),
    ],
)
async def test_connect_tag_and_documents(
    aiohttp_client,
    soft_access_to_tags,
    expected_status,
):
    app, client, admin = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=admin.company_edrpou,
        user_role=UserRole.user.value,
        can_view_document=False,
    )
    user2 = await prepare_user_data(app, email='a@t', company_edrpou='10101010')
    doc1 = await prepare_document_data(app, owner=admin)
    doc2 = await prepare_document_data(app, owner=coworker)
    alien_doc = await prepare_document_data(app, owner=user2)

    tags = await request_create_tags_for_documents(
        client=client, user=admin, names=[TEST_TAG_NAME], documents_ids=[doc1.id]
    )
    tag1_id = tags[0]['id']

    # Alien tag
    alien_tags = await request_create_tags_for_documents(
        client=client,
        user=user2,
        names=[ANOTHER_TAG_NAME],
        documents_ids=[alien_doc.id],
    )
    alien_tag_id = alien_tags[0]['id']

    # Can not connect document with not existed `TEST_UUID_1`
    await request_connect_tags_and_documents(
        client=client,
        user=admin,
        tags_ids=[TEST_UUID_1, tag1_id],
        documents_ids=[doc2.id],
        status=HTTPStatus.FORBIDDEN,
    )

    # Can not connect document with with alien tag `alien_tag_id`
    await request_connect_tags_and_documents(
        client=client,
        user=admin,
        tags_ids=[alien_tag_id, tag1_id],
        documents_ids=[doc2.id],
        status=HTTPStatus.FORBIDDEN,
    )

    # Can not connect alien document with with tag
    await request_connect_tags_and_documents(
        client=client,
        user=admin,
        tags_ids=[tag1_id],
        documents_ids=[alien_doc.id],
        status=HTTPStatus.FORBIDDEN,
    )

    # Can not connect not existed document with tag
    await request_connect_tags_and_documents(
        client=client,
        user=admin,
        tags_ids=[tag1_id],
        documents_ids=[TEST_UUID_1],
        status=HTTPStatus.FORBIDDEN,
    )

    async with app['db'].acquire() as conn:
        await update_company_config(
            conn=conn,
            company_id=admin.company_id,
            soft_access_to_tags=soft_access_to_tags,
        )

        await request_connect_tags_and_documents(
            client=client,
            user=coworker,
            tags_ids=[tag1_id],
            documents_ids=[doc2.id],
            status=expected_status,
        )

        rows = await select_all(conn, document_tag_table.select())
        result = [(row.tag_id, row.document_id) for row in rows]
        expected = [
            (tag1_id, doc1.id),
            (alien_tag_id, alien_doc.id),
        ]
        if expected_status == HTTPStatus.OK:
            expected.append((tag1_id, doc2.id))

        assert set(result) == set(expected)

    await request_connect_tags_and_documents(
        client=client,
        user=admin,
        tags_ids=[tag1_id],
        documents_ids=[doc2.id, doc1.id],
        status=HTTPStatus.OK,
    )


@pytest.mark.parametrize('remove_from_profile', [True, False])
async def test_disconnect_documents_and_tags(aiohttp_client, remove_from_profile):
    app, client, user1 = await prepare_client(aiohttp_client, can_view_document=False)

    user2 = await prepare_user_data(app, email='a@t', company_edrpou='10101010')
    doc1 = await prepare_document_data(app, owner=user1)
    doc2 = await prepare_document_data(app, owner=user1)
    admin = await prepare_user_data(app=app, email='a@c', user_role=UserRole.admin.value)
    # alien document
    alien_doc = await prepare_document_data(app, owner=user2)

    tags = await request_create_tags_for_documents(
        client=client,
        user=user1,
        names=[TEST_TAG_NAME],
        documents_ids=[doc1.id, doc1.id],
    )
    tag1_id = tags[0]['id']
    async with app['db'].acquire() as conn:
        rows = await select_all(conn, role_tag_table.select())
        assert [row.role_id for row in rows] == [user1.role_id]

    if remove_from_profile:
        await request_disconnect_roles_and_tags(
            client=client,
            user=admin,
            tags_ids=[tag1_id],
            roles_ids=[user1.role_id],
            expected_status=HTTPStatus.OK,
        )

    # Alien tag
    alien_tags = await request_create_tags_for_documents(
        client=client,
        user=user2,
        names=[ANOTHER_TAG_NAME],
        documents_ids=[alien_doc.id],
    )
    alien_tag_id = alien_tags[0]['id']

    await request_disconnect_tags_and_documents(
        client=client,
        user=user1,
        tags_ids=[TEST_UUID_1, tag1_id],
        documents_ids=[doc2.id],
        status=HTTPStatus.FORBIDDEN,
    )

    await request_disconnect_tags_and_documents(
        client=client,
        user=user1,
        tags_ids=[alien_tag_id, tag1_id],
        documents_ids=[doc2.id],
        status=HTTPStatus.FORBIDDEN,
    )

    await request_disconnect_tags_and_documents(
        client=client,
        user=user1,
        tags_ids=[alien_tag_id, tag1_id],
        documents_ids=[doc2.id],
        status=HTTPStatus.FORBIDDEN,
    )

    await request_disconnect_tags_and_documents(
        client=client,
        user=user1,
        tags_ids=[alien_tag_id],
        documents_ids=[alien_doc.id],
        status=HTTPStatus.FORBIDDEN,
    )

    await request_disconnect_tags_and_documents(
        client=client,
        user=user1,
        tags_ids=[tag1_id],
        documents_ids=[TEST_UUID_1],
        status=HTTPStatus.FORBIDDEN,
    )

    if remove_from_profile:
        # Such as we remove tag from role, this user lose access to tag
        await request_disconnect_tags_and_documents(
            client=client,
            user=user1,
            tags_ids=[tag1_id],
            documents_ids=[doc1.id, doc2.id],
            status=HTTPStatus.FORBIDDEN,
        )

    await request_disconnect_tags_and_documents(
        client=client,
        user=admin,
        tags_ids=[tag1_id],
        documents_ids=[doc1.id, doc2.id],
        status=HTTPStatus.OK,
    )

    async with app['db'].acquire() as conn:
        rows = await select_all(conn, document_tag_table.select())
        result = [(row.tag_id, row.document_id) for row in rows]
        expected = [(alien_tag_id, alien_doc.id)]
        assert sorted(result) == sorted(expected)

        rows = await select_all(conn, tag_table.select())
        tags_ids = [row.id for row in rows]
        if remove_from_profile:
            assert tags_ids == [alien_tag_id]
        else:
            assert len(tags_ids) == 2
            assert set(tags_ids) == {alien_tag_id, tag1_id}

        listings = await select_document_listings(
            conn,
            document_id=doc1.id,
            edrpou=user1.company_edrpou,
        )
        assert len(listings) == 1
        assert not listings[0].sources.contains(AccessSource.tag)


@pytest.mark.parametrize(
    'names, status, created',
    [
        ([TEST_TAG_NAME, TEST_TAG_NAME1], HTTPStatus.CREATED, 2),
        ([TEST_TAG_NAME, '1' * 520], HTTPStatus.BAD_REQUEST, 0),
        ([TEST_TAG_NAME, TEST_TAG_NAME], HTTPStatus.CREATED, 1),
    ],
)
async def test_create_tags_for_document(aiohttp_client, names, status, created):
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='a@t', company_edrpou='10101010')
    doc1 = await prepare_document_data(app, owner=user1)

    async with app['db'].acquire() as conn:
        result = await select_all(conn, tag_table.select())
        assert len(result) == 0

        await request_create_tags_for_documents(
            client=client,
            user=user1,
            documents_ids=[doc1.id],
            names=names,
            status=status,
        )

        result = await select_all(conn, tag_table.select())
        assert len(result) == created

        # new tags assigned to user role
        role_tags = await select_all(conn, role_tag_table.select())
        assert len(role_tags) == created
        if created > 0:
            assert {role_tag.role_id for role_tag in role_tags} == {user1.role_id}

        # other user is forbid to crate tag for document without access
        await request_create_tags_for_documents(
            client=client,
            user=user2,
            documents_ids=[doc1.id],
            names=[TEST_TAG_NAME],
            status=HTTPStatus.FORBIDDEN,
        )

        if created > 0:
            # New tags create tag access to user
            listings = await select_all(conn, listing_table.select())
            assert len(listings) == 1
            assert listings[0].sources.contains(AccessSource.tag)
            # not remove previous access given as document uploader
            assert listings[0].sources.contains(AccessSource.default)

            # Check errors on tags duplications
            await request_create_tags_for_documents(
                client=client,
                user=user1,
                documents_ids=[doc1.id],
                names=[TEST_TAG_NAME],
                status=HTTPStatus.BAD_REQUEST,
            )


@pytest.mark.parametrize('access_mode', [2, 1, 0])
async def test_create_tag_for_roles_and_connect_with_document(aiohttp_client, access_mode):
    """
    - create tag for roles
    - connect tag with document
    - check access role to document
    """
    if access_mode == 0:
        params = {'is_admin': True, 'can_view_document': True}
    elif access_mode == 1:
        params = {'is_admin': True, 'can_view_document': False}
    elif access_mode == 2:
        params = {'is_admin': False, 'can_view_document': True}
    else:
        return

    app, client, user = await prepare_client(aiohttp_client, **params)
    coworker = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=user.company_edrpou,
    )
    alien = await prepare_user_data(
        app,
        email='a@t',
        company_edrpou='10101010',
    )

    document = await prepare_document_data(app, user)

    tag_names = [TEST_TAG_NAME, TEST_TAG_NAME1, TEST_TAG_NAME1]
    roles_ids = [user.role_id, coworker.role_id]
    response = await client.post(
        CREATE_TAG_FOR_ROLES_URL,
        headers=prepare_auth_headers(user),
        json={'names': tag_names, 'roles_ids': roles_ids},
    )
    assert response.status == HTTPStatus.CREATED
    async with app['db'].acquire() as conn:
        tags = await select_tags_by_names(
            conn,
            tags_names=tag_names,
            company_id=user.company_id,
        )
        assert len(tags) == 2
        assert {tag.name for tag in tags} == set(tag_names)
        tags_ids = {tag.id for tag in tags}

        # Connect both tags with document
        response = await client.post(
            CONNECT_MULTIPLE_TAGS_URL,
            headers=prepare_auth_headers(user),
            json={'tags_ids': list(tags_ids), 'documents_ids': [document.id]},
        )

        assert response.status == HTTPStatus.OK, await response.json()

        role_tags = await select_all(
            conn,
            (sa.select([role_tag_table]).where(role_tag_table.c.tag_id.in_(tags_ids))),
        )

        expected_role_tags = {(role_id, tag_id) for role_id in roles_ids for tag_id in tags_ids}
        assert expected_role_tags == {(role_tag.role_id, role_tag.tag_id) for role_tag in role_tags}

        await validate_document_access(conn, user, document.id)
        await validate_document_access(conn, coworker, document.id)
        with pytest.raises(errors.AccessDenied):
            await validate_document_access(conn, alien, document.id)

        listings = await select_document_listings(
            conn,
            document.id,
            user.company_edrpou,
        )

        for item in listings:
            assert item.role_id in roles_ids
            if item.role_id == user.role_id:
                assert item.sources == AccessSource.default | AccessSource.tag
            if item.role_id == coworker.role_id:
                assert item.sources == AccessSource.tag


async def test_connect_tag_documents_respects_privacy(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    coworker1 = await prepare_user_data(app, email='<EMAIL>', company_edrpou=user.company_edrpou)

    recipient1 = await prepare_user_data(app, email='<EMAIL>', company_edrpou='9876543210')

    document1 = await prepare_document_data(app, user)
    document2 = await prepare_document_data(app, user)
    document3 = await prepare_document_data(app, user, another_recipients=[recipient1])

    await prepare_private_document(document_id=document2.id, company_edrpou=user.company_edrpou)
    await prepare_private_document(
        document_id=document3.id,
        company_edrpou=recipient1.company_edrpou,
    )

    tag1_name = 'privacy_doc_test_tag'
    response = await client.post(
        CREATE_TAG_FOR_ROLES_URL,
        headers=prepare_auth_headers(user),
        json={'names': [tag1_name], 'roles_ids': [coworker1.role_id]},
    )
    assert response.status == HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        tags = await select_tags_by_names(
            conn,
            tags_names=[tag1_name],
            company_id=user.company_id,
        )
        tag1_id = tags[0].id

        response = await client.post(
            CONNECT_MULTIPLE_TAGS_URL,
            headers=prepare_auth_headers(user),
            json={
                'tags_ids': [tag1_id],
                'documents_ids': [document1.id, document2.id, document3.id],
            },
        )
        assert response.status == HTTPStatus.OK, await response.json()

        async def get_access_map(document_id: str):
            _listings = await select_document_listings(
                conn=conn,
                document_id=document_id,
                edrpou=user.company_edrpou,
            )
            return {row.role_id: row for row in _listings}

        # Coworker 1 received access to document1 (not private)
        listings = await get_access_map(document_id=document1.id)
        assert len(listings) == 2
        assert listings[user.role_id].sources == AccessSource.default
        assert listings[coworker1.role_id].sources == AccessSource.tag

        # Coworker 1 didn't receive access to document2 (private)
        listings = await get_access_map(document_id=document2.id)
        assert len(listings) == 1
        assert listings[user.role_id].sources == AccessSource.default
        assert coworker1.role_id not in listings

        # Coworker 1 received access to document3 (private, but in another company)
        listings = await get_access_map(document_id=document3.id)
        assert len(listings) == 2
        assert listings[user.role_id].sources == AccessSource.default
        assert listings[coworker1.role_id].sources == AccessSource.tag


async def test_create_tag_for_documents_and_connect_with_roles(aiohttp_client):
    """
    - create tag for documents
    - connect tag with roles
    - check access role to document
    """
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    coworker = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=user.company_edrpou,
    )
    alien = await prepare_user_data(
        app,
        email='a@t',
        company_edrpou='10101010',
    )

    document = await prepare_document_data(app, user)

    tag_names = [TEST_TAG_NAME, TEST_TAG_NAME1, TEST_TAG_NAME1]
    roles_ids = [user.role_id, coworker.role_id]

    # Create both tags with document
    response = await client.post(
        CREATE_TAG_MULTIPLE_URL,
        headers=prepare_auth_headers(user),
        json={'names': tag_names, 'documents_ids': [document.id]},
    )
    assert response.status == HTTPStatus.CREATED, await response.json()

    async with app['db'].acquire() as conn:
        tags = await select_tags_by_names(
            conn,
            tags_names=tag_names,
            company_id=user.company_id,
        )
        assert len(tags) == 2
        assert {tag.name for tag in tags} == set(tag_names)
        tags_ids = {tag.id for tag in tags}

        response = await client.post(
            CONNECT_TAG_ROLES_URL,
            headers=prepare_auth_headers(user),
            json={'tags_ids': list(tags_ids), 'roles_ids': roles_ids},
        )
        assert response.status == HTTPStatus.CREATED, await response.json()

        role_tags = await select_all(
            conn,
            (sa.select([role_tag_table]).where(role_tag_table.c.tag_id.in_(tags_ids))),
        )

        expected_role_tags = {(role_id, tag_id) for role_id in roles_ids for tag_id in tags_ids}
        assert expected_role_tags == {(role_tag.role_id, role_tag.tag_id) for role_tag in role_tags}

        await validate_document_access(conn, user, document.id)
        await validate_document_access(conn, coworker, document.id)
        with pytest.raises(errors.AccessDenied):
            await validate_document_access(conn, alien, document.id)

        listings = await select_document_listings(
            conn,
            document.id,
            user.company_edrpou,
        )

        for item in listings:
            assert item.role_id in roles_ids
            if item.role_id == user.role_id:
                assert item.sources == AccessSource.default | AccessSource.tag
            if item.role_id == coworker.role_id:
                assert item.sources == AccessSource.tag


async def test_connect_tag_and_role_access(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    coworker1 = await prepare_user_data(app, email='<EMAIL>', company_edrpou=user.company_edrpou)
    coworker2 = await prepare_user_data(app, email='<EMAIL>', company_edrpou=user.company_edrpou)
    coworker3 = await prepare_user_data(app, email='<EMAIL>', company_edrpou=user.company_edrpou)

    document1 = await prepare_document_data(app, coworker2)
    document2 = await prepare_document_data(app, coworker2)
    document3 = await prepare_document_data(app, coworker2)
    document4 = await prepare_document_data(app, coworker2)
    document5 = await prepare_document_data(app, coworker2)

    tag1_name = 'test1'
    tag2_name = 'test2'
    tag3_name = 'test3'
    tag4_name = 'test4'

    await request_create_tags_for_documents(
        client=client,
        user=user,
        names=[tag1_name, tag2_name],
        documents_ids=[document1.id, document2.id],
    )
    await request_create_tags_for_documents(
        client=client,
        user=user,
        names=[tag3_name],
        documents_ids=[document3.id],
    )
    await request_create_tags_for_documents(
        client=client,
        user=user,
        names=[tag4_name],
        documents_ids=[document4.id],
    )

    async with services.db.acquire() as conn:
        tags = await select_tags_by_names(
            conn=conn,
            tags_names=[tag1_name, tag2_name, tag3_name, tag4_name],
            company_id=user.company_id,
        )
        tags_map = {tag.name: tag.id for tag in tags}
        tag1_id = tags_map[tag1_name]
        tag3_id = tags_map[tag2_name]

        response = await client.post(
            path=CONNECT_TAG_ROLES_URL,
            headers=prepare_auth_headers(user),
            # should open new tag access for coworker1 to document1, document2 and document3
            json={'tags_ids': [tag1_id, tag3_id], 'roles_ids': [coworker1.role_id]},
        )
        assert response.status == HTTPStatus.CREATED, await response.json()

        async def get_access_map(document_id: str):
            _listings = await select_document_listings(
                conn=conn,
                document_id=document_id,
                edrpou=user.company_edrpou,
            )
            return {row.role_id: row for row in _listings}

        listings_1 = await get_access_map(document_id=document1.id)
        assert len(listings_1) == 3
        assert listings_1[user.role_id].sources == AccessSource.tag  # user created tag
        assert listings_1[coworker1.role_id].sources == AccessSource.tag  # was connected to tag
        assert listings_1[coworker2.role_id].sources == AccessSource.default  # was uploader
        assert coworker3.role_id not in listings_1

        listings_2 = await get_access_map(document_id=document2.id)
        assert len(listings_2) == 3
        assert listings_2[user.role_id].sources == AccessSource.tag  # user created tag
        assert listings_2[coworker1.role_id].sources == AccessSource.tag  # was connected to tag
        assert listings_2[coworker2.role_id].sources == AccessSource.default  # was uploader
        assert coworker3.role_id not in listings_2

        listings_3 = await get_access_map(document_id=document3.id)
        assert len(listings_3) == 2
        assert listings_3[user.role_id].sources == AccessSource.tag  # user created tag
        assert listings_3[coworker2.role_id].sources == AccessSource.default  # was uploader
        assert coworker1.role_id not in listings_3
        assert coworker3.role_id not in listings_3

        listings_4 = await get_access_map(document_id=document4.id)
        assert len(listings_4) == 2
        assert listings_4[user.role_id].sources == AccessSource.tag  # user created tag
        assert listings_4[coworker2.role_id].sources == AccessSource.default  # was uploader
        assert coworker1.role_id not in listings_4
        assert coworker3.role_id not in listings_4

        listings_5 = await get_access_map(document_id=document5.id)
        assert len(listings_5) == 1
        assert listings_5[coworker2.role_id].sources == AccessSource.default  # was uploader
        assert user.role_id not in listings_5
        assert coworker1.role_id not in listings_5
        assert coworker3.role_id not in listings_5


async def test_connect_tag_and_roles_access__with_private_documents(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    company1_edrpou = user.company_edrpou

    coworker1 = await prepare_user_data(app, email='<EMAIL>', company_edrpou=company1_edrpou)

    recipient1 = await prepare_user_data(app, email='<EMAIL>', company_edrpou='9876543210')
    company2_edrpou = recipient1.company_edrpou

    document1 = await prepare_document_data(app, user)
    document2 = await prepare_document_data(app, user)
    document3 = await prepare_document_data(app, user, another_recipients=[recipient1])

    await prepare_private_document(document_id=document2.id, company_edrpou=company1_edrpou)
    await prepare_private_document(document_id=document3.id, company_edrpou=company2_edrpou)

    tag1_name = 'private_test_tag'
    await request_create_tags_for_documents(
        client=client,
        user=user,
        names=[tag1_name],
        documents_ids=[document1.id, document2.id, document3.id],
    )

    async with services.db.acquire() as conn:
        tags = await select_tags_by_names(
            conn=conn,
            tags_names=[tag1_name],
            company_id=user.company_id,
        )
        tag1_id = tags[0].id

        response = await client.post(
            path=CONNECT_TAG_ROLES_URL,
            headers=prepare_auth_headers(user),
            json={'tags_ids': [tag1_id], 'roles_ids': [coworker1.role_id]},
        )
        assert response.status == HTTPStatus.CREATED, await response.json()

        async def get_access_map(document_id: str, edrpou: str):
            _listings = await select_document_listings(
                conn=conn,
                document_id=document_id,
                edrpou=edrpou,
            )
            return {row.role_id: row for row in _listings}

        # Coworker 1 received access to document1 (not private)
        listings = await get_access_map(document_id=document1.id, edrpou=company1_edrpou)
        assert len(listings) == 2
        assert listings[user.role_id].sources == AccessSource.tag | AccessSource.default
        assert listings[coworker1.role_id].sources == AccessSource.tag

        # Coworker 1 didn't receive access to document2 (private)
        listings = await get_access_map(document_id=document2.id, edrpou=company1_edrpou)
        assert len(listings) == 1
        assert listings[user.role_id].sources == AccessSource.tag | AccessSource.default
        assert coworker1.role_id not in listings

        # Coworker 1 received access to document3 (private, but in another company)
        listings = await get_access_map(document_id=document3.id, edrpou=company1_edrpou)
        assert len(listings) == 2
        assert listings[user.role_id].sources == AccessSource.tag | AccessSource.default
        assert listings[coworker1.role_id].sources == AccessSource.tag


async def test_disconnect_tag_and_role(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    coworker = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=user.company_edrpou,
    )

    document = await prepare_document_data(app, user)

    tags_names = [TEST_TAG_NAME, TEST_TAG_NAME1, TEST_TAG_NAME1]
    roles_ids = [user.role_id, coworker.role_id]
    async with app['db'].acquire() as conn:
        tags_ids = await prepare_document_access_via_tag(
            client,
            conn=conn,
            user=user,
            document_ids=[document.id],
            roles_ids=roles_ids,
            tags_names=tags_names,
        )
        tag1_id, tag2_id = tags_ids

        # Remove all tags from user, such as user also have default access
        # access must be have access with sources equal to AccessSource.default
        await client.delete(
            DISCONNECT_TAG_ROLES_URL,
            headers=prepare_auth_headers(user),
            json={'tags_ids': tags_ids, 'roles_ids': [user.role_id]},
        )

        user_access = await select_document_listings(
            conn=conn,
            document_id=document.id,
            edrpou=user.company_edrpou,
            clause=sa.and_(listing_table.c.role_id == user.role_id),
        )
        assert len(user_access) == 1
        assert user_access[0].sources == AccessSource.default

        # Remove one tag for user, such as document and user one more common
        # document, user must have tag access after disconnecting
        await client.delete(
            DISCONNECT_TAG_ROLES_URL,
            headers=prepare_auth_headers(user),
            json={'tags_ids': [tag1_id], 'roles_ids': [coworker.role_id]},
        )

        coworker_access = await select_document_listings(
            conn=conn,
            document_id=document.id,
            edrpou=user.company_edrpou,
            clause=sa.and_(listing_table.c.role_id == coworker.role_id),
        )
        assert len(coworker_access) == 1
        assert coworker_access[0].sources == AccessSource.tag

        # Remove last tag for user, such as document and user doesnot have
        # more common document, user must lose access to document
        await client.delete(
            DISCONNECT_TAG_ROLES_URL,
            headers=prepare_auth_headers(user),
            json={'tags_ids': [tag2_id], 'roles_ids': [coworker.role_id]},
        )

        coworker_access = await select_document_listings(
            conn=conn,
            document_id=document.id,
            edrpou=user.company_edrpou,
            clause=sa.and_(listing_table.c.role_id == coworker.role_id),
        )
        assert len(coworker_access) == 0


async def test_create_tags_for_contacts(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    alien = await prepare_user_data(app, email='a@t', company_edrpou='10101010')

    async with app['db'].acquire() as conn:
        contact1_id = await prepare_contact(conn, user, contact={'edrpou': '00000001'})
        contact2_id = await prepare_contact(conn, user, contact={'edrpou': '00000002'})
        contact3_id = await prepare_contact(conn, alien, contact={'edrpou': '00000003'})
        contact4_id = await prepare_contact(conn, user, contact={'edrpou': '00000004'})

    tags = await request_create_tags_for_contacts(
        client=client,
        user=user,
        names=['1', '2'],
        contacts_ids=[contact1_id, contact2_id],
    )
    tags_mapping = {tag['name']: tag['id'] for tag in tags}
    tag1_id = tags_mapping['1']
    tag2_id = tags_mapping['2']

    # Already exists
    await request_create_tags_for_contacts(
        client=client,
        user=user,
        names=['1', '2'],
        contacts_ids=[contact4_id],
        status=HTTPStatus.BAD_REQUEST,
    )
    # Doesn't have access
    await request_create_tags_for_contacts(
        client=client,
        user=user,
        names=['3'],
        contacts_ids=[contact3_id],
        status=HTTPStatus.NOT_FOUND,
    )

    # Doesn't exists
    await request_create_tags_for_contacts(
        client=client,
        user=user,
        names=['3'],
        contacts_ids=[TEST_UUID_1],
        status=HTTPStatus.NOT_FOUND,
    )

    tags = await request_create_tags_for_contacts(
        client=client,
        user=user,
        names=['4'],
        contacts_ids=[contact1_id, contact2_id, contact4_id],
    )
    tags_mapping = {tag['name']: tag['id'] for tag in tags}
    tag4_id = tags_mapping['4']

    async with app['db'].acquire() as conn:
        role_tags = await select_all(conn, role_tag_table.select())
        assert len(role_tags) == 3
        assert {
            (user.role_id, tag1_id),
            (user.role_id, tag2_id),
            (user.role_id, tag4_id),
        } == {(role_tag.role_id, role_tag.tag_id) for role_tag in role_tags}

        contact_tags = await select_all(conn, contact_tag_table.select())
        tags = await select_all(conn, tag_table.select())
        assert {tag.name for tag in tags} == {'1', '2', '4'}
        assert len(contact_tags) == 7
        assert {
            (tag1_id, contact1_id),
            (tag1_id, contact2_id),
            (tag2_id, contact1_id),
            (tag2_id, contact2_id),
            (tag4_id, contact1_id),
            (tag4_id, contact2_id),
            (tag4_id, contact4_id),
        } == {(contact_tag.tag_id, contact_tag.contact_id) for contact_tag in contact_tags}


async def test_connect_contact_tags(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    alien = await prepare_user_data(app, email='a@t', company_edrpou='10101010')
    coworker = await prepare_user_data(
        app,
        can_view_document=False,
        user_role=UserRole.user.value,
        email='<EMAIL>',
        company_edrpou=user.company_edrpou,
    )

    async with app['db'].acquire() as conn:
        contact1_id = await prepare_contact(conn, user, contact={'edrpou': '00000001'})
        contact2_id = await prepare_contact(conn, user, contact={'edrpou': '00000002'})
        contact3_id = await prepare_contact(conn, alien, contact={'edrpou': '00000003'})

    # Create tag1 and tag2 for contact1
    tags = await request_create_tags_for_contacts(
        client=client, user=user, names=['1', '2'], contacts_ids=[contact1_id]
    )
    tags_mapping = {tag['name']: tag['id'] for tag in tags}
    tag1_id = tags_mapping['1']
    tag2_id = tags_mapping['2']

    # Create tag3 owned by non admin coworker
    tags = await request_create_tags_for_contacts(
        client=client, user=coworker, names=['3'], contacts_ids=[contact2_id]
    )
    tag3_id = tags[0]['id']

    # with random UUUID
    await request_connect_tags_and_contacts(
        client=client,
        user=user,
        tags_ids=[TEST_UUID_1],
        contacts_ids=[contact1_id, contact2_id],
        status=HTTPStatus.FORBIDDEN,
    )

    # with alien contact
    await request_connect_tags_and_contacts(
        client=client,
        user=user,
        tags_ids=[tag1_id],
        contacts_ids=[contact3_id],
        status=HTTPStatus.NOT_FOUND,
    )

    # non admin coworker cannot user not own tags
    await request_connect_tags_and_contacts(
        client=client,
        user=coworker,
        tags_ids=[tag1_id],
        contacts_ids=[contact1_id],
        status=HTTPStatus.FORBIDDEN,
    )

    await request_connect_tags_and_contacts(
        client=client,
        user=user,
        tags_ids=[tag3_id],
        contacts_ids=[contact1_id, contact2_id],
    )

    await request_connect_tags_and_contacts(
        client=client,
        user=user,
        tags_ids=[tag1_id, tag2_id, tag3_id],
        contacts_ids=[contact1_id, contact2_id],
    )

    async with app['db'].acquire() as conn:
        contact_tags = await select_all(conn, contact_tag_table.select())
        tags = await select_all(conn, tag_table.select())
    assert {tag.name for tag in tags} == {'1', '2', '3'}
    assert len(contact_tags) == 6
    assert {
        (tag1_id, contact1_id),
        (tag2_id, contact1_id),
        (tag3_id, contact1_id),
        (tag1_id, contact2_id),
        (tag2_id, contact2_id),
        (tag3_id, contact2_id),
    } == {(contact_tag.tag_id, contact_tag.contact_id) for contact_tag in contact_tags}


async def test_disconnect_contacts_tags(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    async with app['db'].acquire() as conn:
        contact1_id = await prepare_contact(conn, user, contact={'edrpou': '00000001'})
        await prepare_contact(conn, user, contact={'edrpou': '00000002'})

    # Create tag1 and tag2 for contact1
    tags = await request_create_tags_for_contacts(
        client=client, user=user, names=['1', '2'], contacts_ids=[contact1_id]
    )
    tags_mapping = {tag['name']: tag['id'] for tag in tags}
    tag1_id = tags_mapping['1']
    tag2_id = tags_mapping['2']

    # Disconnect one tag from role
    await request_disconnect_roles_and_tags(
        client=client,
        user=user,
        tags_ids=[tag1_id],
        roles_ids=[user.role_id],
    )

    await request_disconnect_tags_and_contacts(
        client=client,
        user=user,
        tags_ids=[tag1_id, tag2_id],
        contacts_ids=[contact1_id],
    )

    async with app['db'].acquire() as conn:
        tags = await select_all(conn, tag_table.select())
        # Tag1 removed because there is no any role or document connected with tags
        assert {tag.name for tag in tags} == {'2'}

        contact_tags = await select_all(conn, contact_tag_table.select())
        assert len(contact_tags) == 0


@pytest.mark.parametrize(
    'soft_access_to_tags, expected_status',
    [
        (True, HTTPStatus.CREATED),
        (False, HTTPStatus.FORBIDDEN),
    ],
)
async def test_soft_access_to_tags(aiohttp_client, soft_access_to_tags, expected_status):
    app, client, admin = await prepare_client(aiohttp_client, is_admin=True)
    coworker = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=admin.company_edrpou,
        user_role=UserRole.user.value,
        can_view_document=False,
    )

    tag_names = [TEST_TAG_NAME, TEST_TAG_NAME1]
    roles_ids = [admin.role_id]
    response = await client.post(
        CREATE_TAG_FOR_ROLES_URL,
        headers=prepare_auth_headers(admin),
        json={'names': tag_names, 'roles_ids': roles_ids},
    )
    assert response.status == HTTPStatus.CREATED
    data = await response.json()
    tags_mapping = {item['name']: item['id'] for item in data}
    tag_1 = tags_mapping[TEST_TAG_NAME]
    tag_2 = tags_mapping[TEST_TAG_NAME1]

    async with app['db'].acquire() as conn:
        contact1_id = await prepare_contact(conn, admin, contact={'edrpou': '00000001'})
        document = await prepare_document_data(app, owner=coworker)

        await update_company_config(
            conn=conn,
            company_id=admin.company_id,
            soft_access_to_tags=soft_access_to_tags,
        )

    await request_connect_tags_and_contacts(
        client=client,
        user=coworker,
        tags_ids=[tag_1, tag_2],
        contacts_ids=[contact1_id],
        status=expected_status,
    )

    response = await request_connect_tags_and_documents(
        client=client,
        user=coworker,
        tags_ids=[tag_1, tag_2],
        documents_ids=[document.id],
        raw_response=True,
    )
    expected = expected_status
    if 200 <= expected_status < 300:
        expected = HTTPStatus.OK
    assert response.status == expected

    await request_connect_tags_and_roles(
        client=client,
        user=coworker,
        tags_ids=[tag_1, tag_2],
        roles_ids=[admin.role_id],
        status=HTTPStatus.FORBIDDEN,
    )


@pytest.mark.parametrize(
    'expected_status, is_admin',
    [
        (HTTPStatus.CREATED, True),
        (HTTPStatus.FORBIDDEN, False),
    ],
)
async def test_access_to_tags_improve(aiohttp_client, expected_status, is_admin):
    """
    Test case for https://tabula-rasa.atlassian.net/browse/DOC-6048
    Only admin can add tags to recipient company
    """
    app, client, admin = await prepare_client(aiohttp_client, is_admin=True)
    coworker = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=admin.company_edrpou,
        user_role=UserRole.admin.value if is_admin else UserRole.user.value,
        can_view_document=False,
    )

    response = await client.post(
        CREATE_TAG_FOR_ROLES_URL,
        headers=prepare_auth_headers(admin),
        json={'names': [TEST_TAG_NAME], 'roles_ids': [admin.role_id]},
    )
    assert response.status == HTTPStatus.CREATED
    data = await response.json()
    assert data[0]['name'] == TEST_TAG_NAME
    tag_id = data[0]['id']

    async with app['db'].acquire() as conn:
        contact1_id = await prepare_contact(conn, admin, contact={'edrpou': '00000001'})

    await request_connect_tags_and_contacts(
        client=client,
        user=coworker,
        tags_ids=[tag_id],
        contacts_ids=[contact1_id],
        status=expected_status,
    )


async def test_get_tags(aiohttp_client):
    app, client, admin = await prepare_client(aiohttp_client, is_admin=True)

    tag_names = [f'Tag{i}' for i in range(1, 6)]
    roles_ids = [admin.role_id]

    response = await client.post(
        path=CREATE_TAG_FOR_ROLES_URL,
        headers=prepare_auth_headers(admin),
        json={'names': tag_names, 'roles_ids': roles_ids},
    )
    assert response.status == HTTPStatus.CREATED
    created_tags = await response.json()
    assert len(created_tags) == len(tag_names)

    tags_mapping = {item['name']: item for item in created_tags}

    # Fetch tags without pagination
    response = await client.get(
        path='/api/v2/tags',
        headers=prepare_auth_headers(admin),
    )
    assert response.status == HTTPStatus.OK
    response_tags = await response.json()
    response_tags = sorted(response_tags['tags'], key=lambda x: x['name'])
    assert response_tags == sorted(
        [
            {
                'id': tags_mapping['Tag1']['id'],
                'name': 'Tag1',
                'date_created': ANY,
            },
            {
                'id': tags_mapping['Tag2']['id'],
                'name': 'Tag2',
                'date_created': ANY,
            },
            {
                'id': tags_mapping['Tag3']['id'],
                'name': 'Tag3',
                'date_created': ANY,
            },
            {
                'id': tags_mapping['Tag4']['id'],
                'name': 'Tag4',
                'date_created': ANY,
            },
            {
                'id': tags_mapping['Tag5']['id'],
                'name': 'Tag5',
                'date_created': ANY,
            },
        ],
        key=lambda x: x['name'],
    )

    # Fetch tags with pagination
    params = {'limit': 2, 'offset': 1}
    response = await client.get(
        path='/api/v2/tags',
        headers=prepare_auth_headers(admin),
        params=params,
    )
    assert response.status == HTTPStatus.OK
    data = await response.json()
    assert len(data['tags']) == 2


async def test_get_tag_roles(aiohttp_client):
    app, client, admin = await prepare_client(aiohttp_client, is_admin=True)

    user = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=admin.company_edrpou,
        user_role=UserRole.user.value,
        can_view_document=False,
    )
    await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=admin.company_edrpou,
        user_role=UserRole.user.value,
        can_view_document=False,
    )

    roles_ids = [admin.role_id, user.role_id]
    tag_name = 'MultiRoleTestTag'

    response = await client.post(
        path=CREATE_TAG_FOR_ROLES_URL,
        headers=prepare_auth_headers(admin),
        json={'names': [tag_name], 'roles_ids': roles_ids},
    )
    assert response.status == HTTPStatus.CREATED

    created_tags = await response.json()
    assert len(created_tags) == 1
    tag_id = created_tags[0]['id']

    # Fetch roles associated with the tag
    response = await client.get(
        path=f'/api/v2/tags/{tag_id}/roles',
        headers=prepare_auth_headers(admin),
    )
    assert response.status == HTTPStatus.OK

    data = await response.json()
    response_roles = data['roles']
    assert len(response_roles) == len(roles_ids)
    response_roles = sorted(response_roles, key=lambda x: x['role_id'])
    expected_roles = [
        {
            'tag_id': tag_id,
            'role_id': admin.role_id,
            'assigner_id': admin.role_id,
            'date_created': ANY,
        },
        {
            'tag_id': tag_id,
            'role_id': user.role_id,
            'assigner_id': admin.role_id,
            'date_created': ANY,
        },
    ]

    expected_roles = sorted(expected_roles, key=lambda x: x['role_id'])
    assert response_roles == expected_roles


async def test_get_tag_roles_no_access(aiohttp_client):
    app, client, admin = await prepare_client(aiohttp_client, is_admin=True)
    user = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=admin.company_edrpou,
        user_role=UserRole.user.value,
        can_view_document=False,
    )

    tag_name = 'NoAccessTag'
    roles_ids = [admin.role_id]

    response = await client.post(
        path=CREATE_TAG_FOR_ROLES_URL,
        headers=prepare_auth_headers(admin),
        json={'names': [tag_name], 'roles_ids': roles_ids},
    )
    assert response.status == HTTPStatus.CREATED

    created_tags = await response.json()
    assert len(created_tags) == 1
    tag_id = created_tags[0]['id']

    # Regular user attempts to fetch roles for the tag they don't have access to
    response = await client.get(
        path=f'/api/v2/tags/{tag_id}/roles',
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.FORBIDDEN
