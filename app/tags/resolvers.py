from collections import defaultdict

from hiku.engine import (
    Context,
    pass_context,
)

from api.graph.constants import (
    DB_ENGINE_KEY,
)
from api.graph.types import OptionalStr
from api.graph.utils import (
    get_graph_user,
)
from app.lib.types import (
    DataDict,
    StrList,
)
from app.tags.db import select_role_tags_by_roles, select_tags_for_graph
from app.tags.utils import get_tags_access_filter


@pass_context
async def resolve_tags(ctx: Context, options: DataDict) -> StrList:
    """Resolve a tag list for current company"""
    user = get_graph_user(ctx)
    if not user:
        return []

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        tags_ids = await select_tags_for_graph(
            conn=conn,
            user=user,
            has_roles=options['hasRoles'],
            search=options['search'],
            limit=options['limit'],
            offset=options['offset'],
        )

    return tags_ids


@pass_context
async def resolve_tags_by_roles(ctx: Context, ids_list: StrList) -> list[OptionalStr]:
    """Resolve role_tag_table.tag_id list for given ids of documents"""

    user = get_graph_user(ctx)
    if not user:
        return [None for _ in ids_list]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        tags_access_filters = await get_tags_access_filter(conn, user)

        role_tags = await select_role_tags_by_roles(
            conn=conn, roles_ids=ids_list, filters=[tags_access_filters]
        )

    tags_map: defaultdict[str, StrList] = defaultdict(list)
    for role_tag in role_tags:
        tags_map[role_tag.role_id].append(role_tag.tag_id)

    return [tags_map.get(role_id, []) for role_id in ids_list]
