from http import HTTPStatus

import yarl

from app.analytics import tables, types
from app.analytics.utils import get_event_tracking_url, to_email_open
from app.models import select_all
from app.services import services
from app.tests import common

ADD_CABINET_EVENT_URL = '/analytics/cabinet/event'


async def test_email_tracking(aiohttp_client):
    app, client, user = await common.prepare_client(aiohttp_client)
    subject = 'Test subject'
    template_name = 'template_name'

    url = get_event_tracking_url(
        to_email_open(
            email=user.email,
            user_id=user.id,
            subject=subject,
            template=template_name,
        )
    )
    url = yarl.URL(url)

    response = await client.get(url.path_qs)
    assert response.status == 200

    # Check that event was saved only once
    response = await client.get(url.path_qs)
    assert response.status == 200

    async with app['db'].acquire() as conn:
        rows = await select_all(conn=conn, query=tables.analytics_events_table.select())

    assert len(rows) == 1
    assert rows[0]['type'] == types.AnalyticsEventType.email_open
    assert rows[0]['email'] == user.email
    assert rows[0]['user_id'] == user.id
    assert rows[0]['data'] == {
        'subject': subject,
        'template': template_name,
    }


async def test_add_cabinet_event(aiohttp_client):
    """
    Given a regular user
    When calling an endpoint to add new cabinet event
    Cabinet event expected to be added successfully
    """

    # Arrange
    app, client, user = await common.prepare_client(aiohttp_client)
    event_type = types.AnalyticsEventType.employees_limit_with_trial_proposal_popup_shown
    data = {'test_data': True}

    # Act
    response = await client.post(
        path=ADD_CABINET_EVENT_URL,
        json={'type': event_type, 'data': data},
        headers=common.prepare_auth_headers(user),
    )

    # Assert
    assert response.status == HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        rows = await select_all(conn=conn, query=tables.analytics_events_table.select())

    assert len(rows) == 1

    row = rows[0]
    assert row.type == event_type
    assert row.email == user.email
    assert row.role_id == user.role_id
    assert row.company_id == user.company_id
    assert row.data == data
