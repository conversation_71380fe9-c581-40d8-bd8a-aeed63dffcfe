import datetime
import random

import pytest

from app.lib.datetime_utils import local_now
from app.tests.common import (
    fetch_graphql,
    prepare_auth_headers,
    prepare_client,
    prepare_trigger_notification,
    prepare_user_data,
)
from app.trigger_notifications.enums import (
    TriggerNotificationStatus,
    TriggerNotificationType,
)

TEST_UUID_1 = '1397a003-92ff-470d-93ee-42562e2f1da1'
TEST_UUID_2 = '2e5d0e37-6762-49c3-9397-98a4ac37681f'
TEST_UUID_3 = '339427e1-f046-4a96-82fe-7c8a8e7a9db9'
TEST_UUID_4 = '44de4100-8a02-4d7b-adf6-fd6d3bbace97'
TEST_UUID_5 = '5162667e-bda8-4e67-8e0c-6ba6418bda5d'
TEST_UUID_6 = '2c60fc07-353e-41ea-b338-afdb06b8d805'


@pytest.mark.parametrize(
    'options, expected_keys',
    [
        ('', ['NEW_FOR_USER_2', 'SEEN_BY_USER', 'NEW_FOR_USER_1']),
        ('(status: "new")', ['NEW_FOR_USER_2', 'NEW_FOR_USER_1']),
        ('(limit: 1)', ['NEW_FOR_USER_2']),
        ('(offset: 1)', ['SEEN_BY_USER', 'NEW_FOR_USER_1']),
        ('(status: "new", limit: 1, offset: 1)', ['NEW_FOR_USER_1']),
    ],
)
async def test_resolve_trigger_notifications(aiohttp_client, options, expected_keys):
    app, client, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    alien = await prepare_user_data(app, email='<EMAIL>', company_edrpou='11223344')
    notifications_mapping = {
        'NEW_FOR_USER_1': {
            'id': TEST_UUID_1,
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.new,
            'role_id': user.role_id,
            'display_date': datetime.datetime(year=2019, month=1, day=1),
        },
        'NEW_FOR_USER_2': {
            'id': TEST_UUID_2,
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.new,
            'role_id': user.role_id,
            'display_date': datetime.datetime(year=2019, month=1, day=3),
        },
        'SEEN_BY_USER': {
            'id': TEST_UUID_3,
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.seen,
            'role_id': user.role_id,
            'display_date': datetime.datetime(year=2019, month=1, day=2),
        },
        'NOT_USER_NOTIFICATION_1': {
            'id': TEST_UUID_4,
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.new,
            'role_id': alien.role_id,
            'display_date': datetime.datetime(year=2019, month=1, day=1),
        },
        'NOT_USER_NOTIFICATION_2': {
            'id': TEST_UUID_5,
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.new,
            'role_id': alien.role_id,
            'display_date': datetime.datetime(year=2019, month=1, day=1),
        },
    }

    db_data = list(notifications_mapping.values())
    random.seed(local_now().day)
    random.shuffle(db_data)
    await prepare_trigger_notification(app, db_data)

    query = (
        f'{{ '
        f'   triggerNotifications {options} {{ '
        f'      id, title, description, type, status, displayDate, context '
        f'   }} '
        f'}}'
    )
    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    notifications = data['triggerNotifications']
    assert len(notifications) == len(expected_keys)
    for key, result in zip(expected_keys, notifications):
        expected = notifications_mapping[key]
        assert expected['id'] == result['id']
        expected_date = expected['display_date'].strftime('%Y-%m-%dT%H:%M:%S+00:00')
        assert expected_date == result['displayDate']
        assert expected['type'].value == result['type']
        assert expected['status'].value == result['status']


async def test_resolve_trigger_notifications_title_and_description(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    notifications_db = [
        # Title and description is already exists, just return as is
        {
            'id': TEST_UUID_1,
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.new,
            'role_id': user.role_id,
            'url': 'URL_1',
            'context': None,
        },
        {
            'id': TEST_UUID_2,
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.new,
            'role_id': user.role_id,
            'url': None,
            'context': None,
        },
        {
            'id': TEST_UUID_3,
            'type': TriggerNotificationType.check_recipients,
            'status': TriggerNotificationStatus.new,
            'role_id': user.role_id,
            'url': None,
            'context': None,
        },
        {
            'id': TEST_UUID_4,
            'type': TriggerNotificationType.check_recipients_contacts,
            'status': TriggerNotificationStatus.new,
            'role_id': user.role_id,
            'url': None,
            'context': None,
        },
        {
            'id': TEST_UUID_5,
            'type': TriggerNotificationType.company_registration,
            'status': TriggerNotificationStatus.new,
            'role_id': user.role_id,
            'url': None,
            'context': {'company_title': 'COMPANY_1'},
        },
        {
            'id': TEST_UUID_6,
            'type': TriggerNotificationType.invite_companies,
            'status': TriggerNotificationStatus.new,
            'role_id': user.role_id,
            'url': None,
            'context': None,
        },
    ]
    await prepare_trigger_notification(app, notifications_db)

    query = '{ triggerNotifications(limit: 20) { id, title, description, url } }'
    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    notifications = data['triggerNotifications']
    result_map = {notification.pop('id'): notification for notification in notifications}

    expected_map = {
        TEST_UUID_1: {
            'title': 'Надсилайте безкоштовно перші 25 документів!',
            'description': 'Спробуйте прямо зараз!',
            'url': 'URL_1',
        },
        TEST_UUID_2: {
            'title': 'Надсилайте безкоштовно перші 25 документів!',
            'description': 'Спробуйте прямо зараз!',
            'url': 'https://help.vchasno.com.ua/starttosend',
        },
        TEST_UUID_3: {
            'title': 'Перевірте, хто з ваших контрагентів вже зареєстрований у Вчасно.',
            'description': ('Вони точно готові обмінюватись електронними документами з вами'),
            'url': 'https://help.vchasno.com.ua/checkcontractors',
        },
        TEST_UUID_4: {
            'title': (
                'Завантажте перелік ваших контрагентів та отримуйте сповіщення, '
                'коли вони реєструються!'
            ),
            'description': '',
            'url': 'https://help.vchasno.com.ua/checkcontractors',
        },
        TEST_UUID_5: {
            'title': (
                'Ваш контрагент COMPANY_1 зареєструвався у Вчасно '
                'та готовий обмінюватись з вами електронними документами!'
            ),
            'description': (
                'За нашим досвідом це допомагає швидше та легше почати обмінюватись '
                'електронними документами.'
            ),
            'url': 'https://help.vchasno.com.ua/invitecontractor',
        },
        TEST_UUID_6: {
            'title': 'Запросіть контрагентів у Вчасно',
            'description': (
                'За нашим досвідом це допомагає швидше та легше почати обмінюватись '
                'електронними документами.'
            ),
            'url': 'https://help.vchasno.com.ua/invitecontractor',
        },
    }
    assert result_map == expected_map
