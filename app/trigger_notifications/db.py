import datetime

import sqlalchemy as sa

from app.lib.database import DBConnection, DBRow
from app.lib.types import DataDict
from app.models import exists, select_all
from app.trigger_notifications.enums import (
    TriggerNotificationStatus,
    TriggerNotificationType,
)
from app.trigger_notifications.tables import trigger_notification_table
from app.trigger_notifications.types import TriggerNotification


async def update_trigger_notifications(
    conn: DBConnection, data: DataDict, notifications_ids: list[str]
) -> None:
    if not notifications_ids or not data:
        return

    await conn.execute(
        trigger_notification_table.update()
        .values({**data, 'date_updated': sa.text('now()')})
        .where(trigger_notification_table.c.id.in_(notifications_ids))
    )


async def select_trigger_notifications(
    conn: DBConnection,
    ids: list[str],
    *,
    status: TriggerNotificationStatus | None = None,
    role_id: str | None = None,
) -> list[TriggerNotification]:
    filters = [trigger_notification_table.c.id.in_(ids)]
    if role_id:
        filters.append(trigger_notification_table.c.role_id == role_id)
    if status:
        filters.append(trigger_notification_table.c.status == status)

    notifications = await select_all(
        conn=conn,
        query=sa.select([trigger_notification_table]).where(sa.and_(*filters)),
    )
    return [TriggerNotification.from_db(notification) for notification in notifications]


async def select_seen_trigger_notifications_for_delete(
    conn: DBConnection,
    limit: int,
    seen_date_to: datetime.datetime,
    old_date_to: datetime.datetime,
) -> list[DBRow]:
    """Select seen trigger notification that must be deleted"""

    return await select_all(
        conn=conn,
        query=(
            sa.select([trigger_notification_table.c.id])
            .select_from(trigger_notification_table)
            .where(
                sa.or_(
                    sa.and_(
                        trigger_notification_table.c.status == TriggerNotificationStatus.seen,
                        trigger_notification_table.c.display_date >= old_date_to,
                        trigger_notification_table.c.display_date <= seen_date_to,
                    )
                )
            )
            .limit(limit)
        ),
    )


async def select_old_trigger_notifications_for_delete(
    conn: DBConnection,
    limit: int,
    old_date_to: datetime.datetime,
) -> list[DBRow]:
    """Select old trigger notification that must be deleted"""

    return await select_all(
        conn=conn,
        query=(
            sa.select([trigger_notification_table.c.id])
            .select_from(trigger_notification_table)
            .where(trigger_notification_table.c.display_date < old_date_to)
            .order_by(trigger_notification_table.c.display_date.desc())
            .limit(limit)
        ),
    )


async def exist_trigger_notification(
    conn: DBConnection, role_id: str, type_: TriggerNotificationType
) -> bool:
    return await exists(
        conn=conn,
        select_from=trigger_notification_table,
        clause=sa.and_(
            trigger_notification_table.c.type == type_,
            trigger_notification_table.c.role_id == role_id,
        ),
    )


async def insert_trigger_notification(conn: DBConnection, data: list[DataDict]) -> None:
    await conn.execute(trigger_notification_table.insert().values(data))


async def delete_trigger_notification(conn: DBConnection, notifications_ids: list[str]) -> None:
    await conn.execute(
        trigger_notification_table.delete().where(
            trigger_notification_table.c.id.in_(notifications_ids)
        )
    )


async def select_all_trigger_notifications(conn: DBConnection) -> list[DBRow]:
    """Only for tests use"""
    return await select_all(conn, trigger_notification_table.select())


async def select_trigger_notifications_for_graph(
    conn: DBConnection,
    role_id: str,
    status: TriggerNotificationStatus | None,
    limit: int,
    offset: int,
) -> list[DBRow]:
    """
    Resolve trigger notification for GraphQL.
    Notification ordered by display date, for selecting using index
    """

    filters = [trigger_notification_table.c.role_id == role_id]
    if status is not None:
        filters.append(trigger_notification_table.c.status == status)

    return await select_all(
        conn=conn,
        query=(
            sa.select([trigger_notification_table.c.id])
            .select_from(trigger_notification_table)
            .where(sa.and_(*filters))
            .order_by(trigger_notification_table.c.display_date.desc())
            .limit(limit)
            .offset(offset)
        ),
    )


async def delete_trigger_notification_by_role_and_type(
    conn: DBConnection, role_id: str, type_: TriggerNotificationType
) -> None:
    clause = sa.and_(
        trigger_notification_table.c.type == type_,
        trigger_notification_table.c.role_id == role_id,
    )
    await conn.execute(trigger_notification_table.delete().where(clause))
