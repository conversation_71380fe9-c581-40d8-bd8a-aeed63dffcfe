import logging

from hiku.engine import Context, pass_context
from hiku.expr.core import define
from hiku.types import Any as HikuAny

from api.graph.constants import DB_READONLY_KEY
from api.graph.types import OptionalStr
from api.graph.utils import get_graph_user
from app.lib.types import DataDict, StrList
from app.trigger_notifications import db, utils
from app.trigger_notifications.enums import (
    TriggerNotificationStatus,
    TriggerNotificationType,
)

logger = logging.getLogger(__name__)


@define(HikuAny, HikuAny)
def get_trigger_notification_title(
    type_: TriggerNotificationType | None,
    context: DataDict | None,
) -> str:
    """
    Generate trigger notification title by translating a message and applying context
    """

    if not type_:
        return ''

    config = utils.get_type_config(type_=type_)
    return config.get_display_title(context=context).value


@define(HikuAny, HikuAny)
def get_trigger_notification_description(
    type_: TriggerNotificationType | None,
    context: DataDict | None,
) -> str:
    """
    Generate trigger notification description by translating a message
    and applying context
    """

    if not type_:
        return ''

    config = utils.get_type_config(type_=type_)
    return config.get_display_description(context=context).value


@define(HikuAny, HikuAny, HikuAny)
def get_trigger_notification_url(
    url: str | None,
    type_: TriggerNotificationType | None,
    context: DataDict | None,
) -> str:
    """Generate trigger notification URL by applying context"""

    if not type_:
        return ''

    # Some old notifications, which still were not expired by TTL (31 days),
    # already have URL that we can return
    if url is not None:
        return url

    config = utils.get_type_config(type_=type_)
    return config.get_display_url(context=context)


@pass_context
async def resolve_trigger_notifications(ctx: Context, options: DataDict) -> StrList:
    user = get_graph_user(ctx)
    if not user:
        return []

    role_id = user.role_id
    raw_status: OptionalStr = options['status']
    status = TriggerNotificationStatus(raw_status) if raw_status else None
    limit: int = options['limit']
    offset: int = options['offset']
    async with ctx[DB_READONLY_KEY].acquire() as conn:
        notifications = await db.select_trigger_notifications_for_graph(
            conn=conn,
            role_id=role_id,
            status=status,
            limit=limit,
            offset=offset,
        )
    return [notification.id for notification in notifications]
