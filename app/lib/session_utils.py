"""Utilities for working with sessions."""

import typing as t
import uuid

import aiohttp.web
import aiohttp_session

from app.lib.types import Pipeline, Redis
from app.services import services

REDIS_TTL_KEY_DOES_NOT_EXIST: t.Final = -2
REDIS_TTL_FOREVER: t.Final = -1


def generate_session_identity() -> str:
    """Generate an identity for a session."""
    return uuid.uuid4().hex


def get_session_id(
    session: aiohttp_session.Session,
    *,
    session_id_factory: t.Callable[[], str] = generate_session_identity,
) -> str:
    """Return the ID of the session.

    If no session ID is set, the function will set the ID to a value generated
    by `id_factory` (hex UUID value by default).
    """
    if not session.identity:
        session.set_new_identity(session_id_factory())
    return t.cast(str, session.identity)


def session_max_age_to_redis_ttl(session: aiohttp_session.Session) -> int:
    """Return a Redis TTL that mateches a given sessions max age."""
    return REDIS_TTL_FOREVER if session.max_age is None else session.max_age


def get_active_sessions_key_for(user_id: str) -> str:
    """Return the key that holds a list of active sessions."""
    return f'session_id_{user_id}'


def get_session_redis_key(session_identity: str) -> str:
    """Return the key that stores the `session` in Redis."""
    cookie_name = services.config.app.cookie_name

    # Since `aiohttp_session` hides the logic of how a Redis key for a given
    # session will be generated inside the storage's functions, and does not
    # allow to override the logic, we have to follow the internal format
    # defined in:
    # https://github.com/aio-libs/aiohttp-session/blob/6246167b4dc030d7db46c285f1d0b87b203fcce1/aiohttp_session/redis_storage.py#L95
    return f'{cookie_name}_{session_identity}'


async def new_session(request: aiohttp.web.Request) -> aiohttp_session.Session:
    """Return a new `aiohttp_session` Session."""
    session = await aiohttp_session.new_session(request)

    # Every new session should have non-empty identity
    identity = generate_session_identity()
    session.set_new_identity(identity)

    # Force session middleware to store new session
    session.changed()

    return session


async def destroy_session(session: aiohttp_session.Session) -> None:
    """Destroys the passed session.

    A session is considered destroyed if it is deleted from Redis and
    invalidated.
    """
    if session.identity is None:
        raise ValueError(
            f'Cannot destroy session {session} that has no identity set'
            f'(sesison.identity is {session.identity}).'
        )

    session_key = get_session_redis_key(session.identity)
    await services.redis.delete(session_key)

    session.invalidate()


async def ensure_new_session(request: aiohttp.web.Request) -> aiohttp_session.Session:
    """Return a new session for a given request.

    The function destroys any previous session bound to the request and ensures
    that a new valid session is issued.
    """
    current_session = await aiohttp_session.get_session(request)
    session = await new_session(request)
    session.update(current_session)

    # `aiohttp_session` quirk: when no previous sessions were bound to a
    # request, `load_session()` returns a new session. And if there was no
    # previous session, there is nothing to destroy
    if not current_session.new:
        await destroy_session(current_session)

    return session


def copy_session_data(
    source: aiohttp_session.Session, destination: aiohttp_session.Session
) -> None:
    """Copy session data from `source` to `destination`."""
    destination.update(source)


def _ttl_update_required(key_ttl: int, session_ttl: int) -> bool:
    """Return `True` if the current TTL must be updated, else `False`.

    An update to the key's TTL is required if the session outlives the key.
    That is, the session has a greater time-to-live (max age) than the key that
    would hold the session.

    """
    if key_ttl < REDIS_TTL_KEY_DOES_NOT_EXIST:
        raise ValueError(
            f'{key_ttl} is not a valid Redis key TTL '
            f'({REDIS_TTL_KEY_DOES_NOT_EXIST} <= TTL < Infinity).'
        )
    if session_ttl < REDIS_TTL_FOREVER:
        raise ValueError(f'{session_ttl} is not a valid session TTL.')
    return key_ttl != REDIS_TTL_FOREVER and key_ttl < session_ttl


def _schedule_ttl_update(
    pipe: Pipeline,
    key: str,
    new_ttl: int,
) -> Pipeline:
    """Schedule a TTL update for a `key` inside a `transaction`.

    The purpose of this function is to only update the key's TTL. It explicitly
    prohibits deleting the key by using `EXPIRE` with a negative value, since
    it can be a pitfall.

    Raises:
        ValueError: when a negative non-forever TTL is provided as a new TTL.
    """
    if new_ttl > 0:
        pipe = pipe.expire(key, new_ttl)
    elif new_ttl == REDIS_TTL_FOREVER:
        pipe = pipe.persist(key)
    else:
        raise ValueError(
            f'TTL {new_ttl} is not a valid value for a TTL update, '
            f'since negative TTLs would delete the key.'
        )
    return pipe


async def add_active_user_session(
    redis: Redis,
    user_id: str,
    session: aiohttp_session.Session,
) -> None:
    """Add a session to a list of the user's active sessions."""
    active_user_sessions_key = get_active_sessions_key_for(user_id)
    new_session_id = get_session_id(session)

    current_key_ttl = await redis.ttl(active_user_sessions_key)
    session_ttl = session_max_age_to_redis_ttl(session)

    async with redis.pipeline(transaction=True) as pipe:
        pipe = pipe.rpush(active_user_sessions_key, new_session_id)

        if _ttl_update_required(current_key_ttl, session_ttl):
            pipe = _schedule_ttl_update(pipe, active_user_sessions_key, session_ttl)

        await pipe.execute()


async def remove_active_user_session(
    redis: Redis, user_id: str, session: aiohttp_session.Session
) -> None:
    """Remove an active user session from the user's active sessions.

    Args:
        redis: a Redis instance.
        user_id: an ID of a user for whom the session will be removed.
        session: the session to be removed.
    """
    active_sessions_key = get_active_sessions_key_for(user_id)
    identity = t.cast(str, session.identity)
    await redis.lrem(active_sessions_key, 1, identity)


async def get_session_cookie_value(request: aiohttp.web.Request) -> str | None:
    """
    Get the session cookie value for current request, the ID that can be used
    for locating the session in session storage
    """
    cookie_name: str = services.config.app.cookie_name

    # Try to get cookie value from request cookies
    if cookie_value := request.cookies.get(cookie_name):
        return cookie_value

    # Try to get cookie value from the session. It allows getting the cookie value
    # for cases when the user was logged in during the current request.
    # If session.identity is None, that session was not created for storing
    # login information.
    session = await aiohttp_session.get_session(request)
    if identity := session.identity:
        return identity

    return None
