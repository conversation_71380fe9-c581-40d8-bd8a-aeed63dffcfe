import pytest

from app.lib.chunks import Async<PERSON>hunkReader, iter_bytes_by_chunks


@pytest.mark.parametrize('content', [b'ab', b'foo', b'bar', b'baz', b'hello' * 10])
async def test_async_chunk_reader(content: bytes) -> None:
    iterable = iter_bytes_by_chunks(content, chunk_size=1)
    chunk_reader = AsyncChunkReader(iterable, chunk_size=2)

    result = bytearray()
    is_last_set_to_true = False
    async for chunk, is_last in chunk_reader:
        result.extend(chunk)
        is_last_set_to_true = is_last

    assert bytes(result) == content
    assert chunk_reader.total_size == len(content)
    assert is_last_set_to_true is True
