"""
Module to implement higher order functions for upload/download/delete files
to/from S3. As this is higher order functions they support encryption right out
of box, no extra code required.

Usage
=====

::

    import uuid

    from app.app import create_app
    from app.lib import s3_utils

    async def s3_upload_download_delete_file():
        app = create_app()

        upload_item = s3_utils.UploadFile(
            str(uuid.uuid4()),
            b'Hello, world!',
            '00000000')
        await s3_utils.upload(app, upload_item)

        download_item = s3_utils.DownloadFile(
            upload_item.key,
            upload_item.uploaded_by_edrpou)
        assert await s3_utils.download(app, download_item) == b'Hello, world!'

        await s3_utils.delete(app, download_item.key)

"""

from __future__ import annotations

import asyncio
import io
import logging
import typing as t
from contextlib import suppress
from dataclasses import dataclass
from typing import TYPE_CHECKING, Any

import charset_normalizer
from aiobotocore.response import StreamingBody
from aiohttp import ClientPayloadError, web
from botocore.exceptions import ClientError
from charset_normalizer.constant import CHARDET_CORRESPONDENCE
from cryptography.hazmat.primitives.ciphers import CipherContext

from api.downloads.constants import BUFFER_CHUNK_SIZE
from api.errors import Code, Error, ServerError
from app.config.schemas import S3Config
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import s3_cse, tracking
from app.lib.chunks import AsyncChunkReader, iter_by_chunks
from app.lib.s3_cse import AES_BLOCK_SIZE
from app.lib.types import DataDict
from app.services import services

if TYPE_CHECKING:
    from types_aiobotocore_s3.type_defs import (
        CompletedPartTypeDef,
        GetObjectOutputTypeDef,
        HeadObjectOutputTypeDef,
        ListObjectVersionsOutputTypeDef,
    )

logger = logging.getLogger(__name__)

# Upload threshold in seconds. All S3 uploads that takes longer then X seconds
# should be tracked
UPLOAD_THRESHOLD = 2.5

BYTES_PER_CHUNK = 10 * 1024 * 1024  # 10 MB

DEFAULT_S3_UPLOAD_TIMEOUT_SECONDS = 60


class GetObjectOptionalDef(t.TypedDict, total=False):
    VersionId: str


@dataclass
class DownloadFile:
    key: str

    # This version of the file on S3. Don't confuse with the document version functionality.
    s3_version_id: str | None = None


@dataclass
class CopyFile:
    source_key: str
    destination_key: str


@dataclass(kw_only=True)
class _BaseUploadFile:
    # S3 key where the file will be uploaded.
    # NOTE: Never use it directly to access s3, always use "add_object_key_prefix" before using it.
    key: str

    # Should we encrypt the file before uploading it to S3?
    # Try to avoid using this option, as it is not secure.
    encrypt: bool = True

    # Extra metadata to be stored with the file. Should be a header-safe dict.
    extra_metadata: dict[str, Any] | None = None

    def merge_metadata(self, metadata: DataDict) -> DataDict:
        """
        Safely merge extra metadata with the provided metadata.
        """
        if extra := self.extra_metadata:
            # Validate that there are no overlapping keys
            assert not set(metadata.keys()) & set(extra.keys())

            return {**metadata, **extra}

        return metadata


@dataclass(kw_only=True)
class UploadFile(_BaseUploadFile):
    # Actual file content to upload
    body: bytes

    @property
    def body_size(self) -> int:
        return len(self.body)


@dataclass(kw_only=True)
class UploadFileIterable(_BaseUploadFile):
    # Actual file content to upload
    body: t.AsyncIterable[bytes]


def get_object_key(*, config: S3Config, key: str) -> str:
    """
    Add bucket prefix to the key:
     - <key> → <bucket>/<key>

    Historically, we have always set the host to a non-None value, which automatically
    enables path-style addressing for buckets.

    Path-style addressing looks like this: "https://<endpoint>/<bucket>/<key>". When it is
    enabled, the bucket name is automatically added to the path regardless of the hostname.
    Here are a few examples:
    1. loss.olympus.evo -> loss.olympus.evo/<bucket>/<key>
    2. s3.amazonaws.com -> s3.amazonaws.com/<bucket>/<key>
    3. !!! <bucket>.s3.amazonaws.com -> <bucket>.s3.amazonaws.com/<bucket>/<key>

    In the first two cases, the bucket name is not considered a part of the key and serves
    as an indicator for the S3 (or S3-compatible storage) to determine the bucket.

    However, in the last case, when a request is sent to S3, the bucket name in a path will be
    considered as part of the key itself, leading to all keys being prefixed with the bucket
    name.

    That is why we always add the bucket prefix to the key on AWS S3. Other S3-compatible
    services may not need that, but we add it anyway to be consistent.

    Here is also some additional information about this:
     - https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/142147616/S3+evodoc
    """

    bucket = config.bucket
    assert not key.startswith(bucket), 'Key already prefixed with bucket'

    return f'{bucket}/{key}'


def remove_object_key_prefix(*, config: S3Config, key: str) -> str:
    """
    Remove bucket prefix from the key:
     - <bucket>/<key> → <key>
    Ref: see get_object_key
    """
    bucket = config.bucket
    assert key.startswith(bucket), 'Key is not prefixed with bucket'

    return key[len(bucket) + 1 :]


def _get_encryption_key(s3_config: S3Config) -> str:
    """
    Returns global key for encryption/decryption.
    """
    assert len(s3_config.encryption_keys) == 1
    return s3_config.encryption_keys[0]


class _FakeAsyncStream:
    def __init__(self, content: bytes) -> None:
        self.bytes_obj = io.BytesIO(content)

    async def read(self, n: int) -> bytes:
        return self.bytes_obj.read(n)

    def close(self) -> None:
        self.bytes_obj.close()


class Decrypter:
    """
    Class implements methods for decrypt content from S3 by chunks
    """

    def __init__(
        self,
        file_item: DownloadFile,
        s3_config: S3Config,
        chunk_size: int = BUFFER_CHUNK_SIZE,
    ) -> None:
        self._s3_config = s3_config
        self._file_item = file_item
        self._chunk_size = chunk_size
        self._read_bytes_length = 0
        self._is_last_chunk = False

        # Set values inside async init() method
        self._encrypted_stream: StreamingBody | None = None
        self._encrypted_content_length = 0
        self._envelope: s3_cse.EncryptionEnvelope | None = None
        self._aes_decrypter: CipherContext | None = None
        self._first_chunk: bytes | None = None
        self._encoding: str | None = None

    @property
    def can_decrypt(self) -> bool:
        """
        Decrypter successfully initialised and has configured
        aes_decrypter with correct decryption_key
        """
        return self._aes_decrypter is not None

    @property
    def has_encryption_metadata(self) -> bool:
        """
        Decrypter has necessary decryption metadata (provided with s3_object)
        """
        return self._envelope is not None

    @property
    def content_length(self) -> int:
        """Returns content_length of result data stream"""
        if self.can_decrypt and self._envelope:
            return self._envelope.content_length
        assert self._encrypted_content_length > 0, 'Expected Content length greater than 0'
        return self._encrypted_content_length

    async def init(self) -> None:
        """
        Perform async init operations for Decrypter
        """
        if get_flag(FeatureFlags.ENABLE_FILES_FULL_DOWNLOAD):
            # Temporary, remove after resolving issue with streaming from S3
            # https://tabula-rasa.atlassian.net/browse/DOC-6509
            content, metadata = await _download_raw(self._file_item)
            self._encrypted_stream = t.cast(StreamingBody, _FakeAsyncStream(content))
            self._encrypted_content_length = len(content)
        else:
            s3_object = await get_object(self._file_item)
            self._encrypted_stream = s3_object['Body']
            metadata = s3_object['Metadata']
            self._encrypted_content_length = s3_object['ContentLength']

        self._envelope = s3_cse.envelope_from_metadata(
            s3_config=self._s3_config,
            metadata=metadata,
        )
        if not self._encrypted_content_length and self._envelope:
            self._encrypted_content_length = self._envelope.content_length + s3_cse.get_pad_length(
                self._envelope.content_length
            )

        # Process first chunk for initializing aes_decrypter
        await self._process_first_chunk()

    async def _process_first_chunk(self) -> None:
        encrypted_chunk = await self._read_chunk()
        self._first_chunk = encrypted_chunk

        if not self.has_encryption_metadata:
            # Don't have encryption metadata, skip aes_decrypter init
            return

        assert self._envelope, 'Encryption Envelope expected'

        decryption_key = _get_encryption_key(self._s3_config)
        try:
            self._aes_decrypter = s3_cse.aes_decrypter(self._envelope, decryption_key)
            decrypted_chunk = self._decrypt(encrypted_chunk)
        except ValueError as err:
            s3_cse.logger.info(
                'Failed attempt to decrypt content of encrypted S3 file.', exc_info=True
            )
            raise ServerError(
                reason=_('Виникла помилка при завантаженні документа, спробуйте повторити спробу')
            ) from err

        if decrypted_chunk:
            self._first_chunk = decrypted_chunk

    async def stream_to_response(self, response: web.StreamResponse) -> None:
        """Method for streaming decrypted file content to aiohttp web_response"""
        async for chunk in self._iter_chunks():
            await response.write(chunk)
        if not self._is_last_chunk:
            logger.error(
                'Original stream not completed',
                extra={
                    'streamed_content_len': self._read_bytes_length,
                    'full_content_len': self._encrypted_content_length,
                },
            )

    async def get_content_encoding(self) -> str | None:
        """Guess decrypted content encoding"""
        if self._encoding:
            return self._encoding
        if not self._first_chunk:
            return None

        self._encoding = self.detect_content_encoding(self._first_chunk)
        return self._encoding

    async def _iter_chunks(self) -> t.AsyncIterator[bytes]:
        try:
            if self._first_chunk:
                yield self._first_chunk

            while encrypted := await self._read_chunk():
                yield self._decrypt(encrypted)

        finally:
            assert self._encrypted_stream is not None, 'expected encrypted stream'
            self._encrypted_stream.close()

    def _decrypt(self, data: bytes) -> bytes:
        # Unable to decrypt, just stream input data
        if not self.can_decrypt:
            return data

        assert self._aes_decrypter, 'expected cipher decrypter'

        with tracking.s3_decryption_time.process_time():
            decrypted = self._aes_decrypter.update(data)
            if self._is_last_chunk:
                decrypted = s3_cse.unpad(decrypted + self._aes_decrypter.finalize())

        return decrypted

    async def _read_chunk(self) -> bytes:
        assert self._encrypted_stream is not None, 'expected encrypted stream'

        chunk = await self._encrypted_stream.read(self._chunk_size)
        self._read_bytes_length += len(chunk)

        # Due to AES encryption details, we don't want to receive last chunk
        # less than s3_cse.AES_BLOCK_SIZE, because it can lead to errors during decryption
        while 0 < self._encrypted_content_length - self._read_bytes_length < s3_cse.AES_BLOCK_SIZE:
            additional_chunk = await self._encrypted_stream.read(self._chunk_size)

            if len(additional_chunk) == 0:
                raise ServerError(
                    reason=_(
                        'Виникла помилка при завантаженні документа, спробуйте повторити спробу'
                    ),
                    log_extra={
                        'encrypted_content_len': self._encrypted_content_length,
                        'read_bytes_len': self._read_bytes_length,
                        'current_chunk_len': len(chunk),
                    },
                )

            chunk += additional_chunk
            self._read_bytes_length += len(additional_chunk)

        if self._read_bytes_length == self._encrypted_content_length:
            self._is_last_chunk = True
        return chunk

    @staticmethod
    def detect_content_encoding(content: bytes) -> str | None:
        """
        It should be fast, because we read max 512*5 bytes from content
        """
        encoding = None
        with suppress(Exception):
            normalizer_result = charset_normalizer.from_bytes(content, threshold=0.1).best()
            if not normalizer_result:
                return None

            encoding = normalizer_result.encoding
            encoding = CHARDET_CORRESPONDENCE.get(encoding, encoding)
        return encoding


def _get_chunk_encrypter(s3_config: S3Config) -> s3_cse.ChunkEncrypter:
    """
    Get chunk encrypter for file upload to S3.
    """
    return s3_cse.ChunkEncrypter(
        key_encryption_key=_get_encryption_key(s3_config),
        s3_config=s3_config,
    )


async def decrypt_content(
    item: DownloadFile,
    content_encrypted: bytes,
    envelope: s3_cse.EncryptionEnvelope | None,
    s3_config: S3Config,
) -> bytes:
    """Decrypt encrypted content using global encryption key"""

    # in case when envelope not exists, just return original
    if not envelope:
        return content_encrypted

    try:
        return await s3_cse.decrypt(
            content=content_encrypted,
            envelope=envelope,
            encryption_key=_get_encryption_key(s3_config),
        )
    except ValueError as err:
        s3_cse.logger.info('Failed attempt to decrypt content of encrypted S3 file.', exc_info=True)
        raise ServerError(
            reason=_('Виникла помилка при завантаженні документа, спробуйте повторити спробу'),
            log_extra={
                'content_length': len(content_encrypted),
                's3_key': get_object_key(config=s3_config, key=item.key),
            },
        ) from err


async def delete_batch(
    keys: list[str],
    chunk_size: int = 50,
) -> None:
    """Delete multiple files from S3."""
    for ch in iter_by_chunks(keys, size=chunk_size):
        coroutines = [delete(key=key) for key in ch]
        await asyncio.gather(*coroutines)


async def delete(key: str) -> None:
    """Delete file from S3."""

    s3_config = services.config.s3
    object_key_new = get_object_key(config=s3_config, key=key)
    await services.s3_client.delete_object(Bucket=s3_config.bucket, Key=object_key_new)

    logger.info(
        msg='File deleted from S3',
        extra={'key': object_key_new},
    )


async def exists(key: str) -> bool:
    """Check if file exists on S3"""

    s3_config = services.config.s3
    object_key = get_object_key(config=s3_config, key=key)

    try:
        await services.s3_client.head_object(Bucket=s3_config.bucket, Key=object_key)
        return True
    except ClientError as err:
        if err.response['ResponseMetadata']['HTTPStatusCode'] == 404:
            return False
        raise err


async def get_keys_with_prefix(key_prefix: str) -> list[str]:
    """Get keys from S3 with a given prefix."""
    assert key_prefix.endswith('/'), 'Key prefix should end with "/"'

    s3_config = services.config.s3
    object_key = get_object_key(config=s3_config, key=key_prefix)
    response = await services.s3_client.list_objects_v2(Bucket=s3_config.bucket, Prefix=object_key)

    return [
        remove_object_key_prefix(config=s3_config, key=content['Key'])
        for content in response.get('Contents', [])
    ]


async def _download_raw(
    item: DownloadFile,
) -> tuple[bytes, DataDict]:
    """
    Download file content from S3.
    """

    try:
        response = await get_object(item)
        async with response['Body'] as stream:
            content = await stream.read()
    except ClientPayloadError:
        logger.info('Unexpected ClientPayloadError on download file from S3', exc_info=True)

        # yield control to the event loop before next try
        await asyncio.sleep(0)

        # Make additional try
        response = await get_object(item)
        async with response['Body'] as stream:
            content = await stream.read()

    return content, response['Metadata']


@tracking.s3_download.time()
async def download(item: DownloadFile) -> tuple[bytes, dict[Any, Any]]:
    """Download file content from S3. Decrypt content if needed."""

    s3_config = services.config.s3
    content_encrypted, metadata = await _download_raw(item)
    envelope = s3_cse.envelope_from_metadata(s3_config=s3_config, metadata=metadata)

    content_decrypted = await decrypt_content(
        item=item,
        content_encrypted=content_encrypted,
        envelope=envelope,
        s3_config=s3_config,
    )

    return content_decrypted, metadata


async def encrypt_content(
    body: bytes,
    s3_config: S3Config,
) -> tuple[bytes, s3_cse.MetaData]:
    """Encrypt content before uploading document to S3."""

    body_encrypted, envelope = await s3_cse.encrypt(
        content=body, key_encryption_key=_get_encryption_key(s3_config)
    )
    metadata = s3_cse.envelope_to_metadata(s3_config=s3_config, envelope=envelope)
    return body_encrypted, metadata


async def upload_batch(items: list[UploadFile]) -> None:
    """Upload multiple files to S3."""
    coroutines = [upload(item=item) for item in items]
    await asyncio.gather(*coroutines)


async def upload(item: UploadFile) -> None:
    """
    Uploads the file contents to S3. Encrypt it with global key by default
    (can be disabled by item.encrypt parameter)
    """

    client = services.s3_client
    config = services.config.s3

    if item.encrypt:
        body, metadata = await encrypt_content(
            body=item.body,
            s3_config=config,
        )
    else:
        logger.warning('Upload file without encryption', extra={'key': item.key})
        body, metadata = item.body, {}

    metadata = item.merge_metadata(metadata)

    object_key = get_object_key(config=config, key=item.key)

    await client.put_object(
        Bucket=config.bucket,
        Key=object_key,
        Body=body,
        Metadata=metadata,
    )

    logger.info(
        msg='File uploaded to S3',
        extra={
            'bucket': config.bucket,
            'key': object_key,
            'metadata': metadata,
        },
    )


async def get_object(item: DownloadFile) -> GetObjectOutputTypeDef:
    """Get object from S3, without decryption."""

    s3_client = services.s3_client
    s3_config = services.config.s3

    # You can pass only an existing version ID or omit this parameter entirely.
    # Passing "None" will cause a ParamValidationError.
    optional_kwargs: GetObjectOptionalDef = {}
    if item.s3_version_id:
        optional_kwargs['VersionId'] = item.s3_version_id

    object_key = get_object_key(config=s3_config, key=item.key)

    try:
        return await s3_client.get_object(
            Bucket=s3_config.bucket, Key=object_key, **optional_kwargs
        )
    except ClientError as error:
        if error.response['Error']['Code'] == 'NoSuchKey':
            logger.warning(
                msg='File not found on S3',
                extra={'key': object_key},
            )
        raise


async def list_object_versions(
    *,
    prefix: str,
) -> ListObjectVersionsOutputTypeDef:
    """
    Get deleted object. This works only when the bucket has a versioning enabled.

    Useful links:
    - https://repost.aws/knowledge-center/s3-undelete-configuration
    - https://docs.aws.amazon.com/AmazonS3/latest/userguide/RestoringPreviousVersions.html
    - https://docs.aws.amazon.com/AmazonS3/latest/userguide/RetrievingObjectVersions.html
    """

    s3_config = services.config.s3
    if not s3_config.is_aws:
        raise ServerError(
            raw_reason=(
                'Завантаження видаленого документа можливе тільки з AWS S3. '
                'Інші S3-сумісні сервіси наразі не підтримуються'
            ),
            details={'key': prefix},
        )

    _prefix = get_object_key(config=s3_config, key=prefix)

    response = await services.s3_client.list_object_versions(
        Bucket=s3_config.bucket,
        Prefix=_prefix,
    )

    logger.info(msg='ListObjectVersions response', extra={'response': str(response)})
    return response


async def head_object(item: DownloadFile) -> HeadObjectOutputTypeDef:
    """Get object metadata from S3."""
    s3_config = services.config.s3
    object_key = get_object_key(config=s3_config, key=item.key)
    return await services.s3_client.head_object(Bucket=s3_config.bucket, Key=object_key)


async def copy(item: CopyFile) -> None:
    """
    Copy file from one location to another on S3.
    """

    s3_client = services.s3_client
    s3_config = services.config.s3

    # Bucket is the same for source and destination
    source_bucket = destination_bucket = s3_config.bucket

    source_key = get_object_key(config=s3_config, key=item.source_key)
    destination_key = get_object_key(config=s3_config, key=item.destination_key)

    res = await s3_client.copy_object(
        Bucket=destination_bucket,
        CopySource={
            'Bucket': source_bucket,
            'Key': source_key,
        },
        Key=destination_key,
        # Copy all metadata from the source object to preserve encryption properties
        MetadataDirective='COPY',
    )
    result = res.get('CopyObjectResult', {})
    etag = result.get('ETag')
    last_modified = result.get('LastModified')

    logger.info(
        msg='File copied on S3',
        extra={
            'source_bucket': source_bucket,
            'source_key': source_key,
            'destination_bucket': destination_bucket,
            'destination_key': destination_key,
            'etag': etag,
            'last_modified': last_modified,
        },
    )


async def upload_iterable(item: UploadFileIterable) -> int:
    """
    Uploads AsyncIterable content to S3, returns total content_length in bytes.
    Process iterable by chunks without keeping all data in memory.

    Use regular upload for files less than 10MB and multipart upload for large files.
    """

    if not item.encrypt:
        raise NotImplementedError('Unencrypted upload is not supported')

    # Check that constant is valid
    assert BYTES_PER_CHUNK % AES_BLOCK_SIZE == 0, (
        'Chunk size must be divisible by AES_BLOCK_SIZE for proper encryption'
    )
    chunk_reader = AsyncChunkReader(item.body, BYTES_PER_CHUNK)

    # Read the first chunk to see if it's the only one
    first_chunk, is_last = await chunk_reader.read()
    if not first_chunk:
        return 0

    # If this is the only chunk, use regular_upload
    if is_last:
        upload_item = UploadFile(
            key=item.key,
            body=first_chunk,
        )
        try:
            async with asyncio.timeout(DEFAULT_S3_UPLOAD_TIMEOUT_SECONDS):
                await upload(upload_item)
        except TimeoutError:
            logger.exception(
                msg='Timeout error on uploading files to S3',
                extra={
                    'key': item.key,
                    'body_size': upload_item.body_size,
                },
            )
            raise Error(Code.s3_error)

        return len(first_chunk)

    # If we have more than one chunk, proceed with multipart upload
    s3_client = services.s3_client
    s3_config = services.config.s3

    bucket = s3_config.bucket
    object_key = get_object_key(config=s3_config, key=item.key)

    logger.info(
        msg='Multipart upload to S3 started',
        extra={
            'bucket': bucket,
            'key': object_key,
        },
    )

    encrypter = _get_chunk_encrypter(s3_config=s3_config)

    async with asyncio.timeout(DEFAULT_S3_UPLOAD_TIMEOUT_SECONDS):
        multipart_upload_response = await s3_client.create_multipart_upload(
            Bucket=bucket,
            Key=object_key,
            Metadata={},  # Will be set after uploading all chunks
        )
        upload_id: str = multipart_upload_response['UploadId']

    parts: list[CompletedPartTypeDef] = []
    part_num: int = 1  # Start from 1 since we already have the first chunk

    try:
        # Upload the first chunk we already read
        part_encrypted = encrypter.encrypt(first_chunk, is_last_chunk=False)
        response = await s3_client.upload_part(
            Bucket=bucket,
            # part number should start from 1
            PartNumber=part_num,
            UploadId=upload_id,
            Body=part_encrypted,
            Key=object_key,
            ContentLength=len(part_encrypted),
        )
        logger.info(
            'Part upload response',
            extra={
                's3_upload_response': str(response),
                'is_last_chunk': False,
                'part_number': part_num,
            },
        )
        parts.append({'ETag': response['ETag'], 'PartNumber': part_num})

        # Process remaining chunks
        while True:
            chunk, is_last = await chunk_reader.read()
            if not chunk:
                break

            part_num += 1
            part_encrypted = encrypter.encrypt(chunk, is_last_chunk=is_last)

            response = await s3_client.upload_part(
                Bucket=bucket,
                PartNumber=part_num,
                UploadId=upload_id,
                Body=part_encrypted,
                Key=object_key,
                ContentLength=len(part_encrypted),
            )

            parts.append({'ETag': response['ETag'], 'PartNumber': part_num})
            logger.info(
                'Part upload response',
                extra={
                    's3_upload_response': str(response),
                    'is_last_chunk': is_last,
                    'part_number': part_num,
                },
            )

        metadata = encrypter.get_metadata(chunk_reader.total_size)
        metadata = item.merge_metadata(metadata)

        # Complete the multipart upload
        async with asyncio.timeout(DEFAULT_S3_UPLOAD_TIMEOUT_SECONDS):
            final_resp = await s3_client.complete_multipart_upload(
                Bucket=bucket,
                Key=object_key,
                UploadId=upload_id,
                MultipartUpload={'Parts': parts},
            )
            logger.info('Complete multipart upload response', extra=final_resp)

            # Update S3 object metadata with actual
            await s3_client.copy_object(
                Bucket=bucket,
                CopySource={'Bucket': bucket, 'Key': object_key},
                Key=object_key,
                Metadata=metadata,
                MetadataDirective='REPLACE',
            )

    except Exception as e:
        # Ensure we abort the multipart upload if anything goes wrong
        try:
            await s3_client.abort_multipart_upload(
                Bucket=bucket,
                Key=object_key,
                UploadId=upload_id,
            )
        except Exception as abort_error:
            logger.exception(
                'Failed to abort multipart upload',
                extra={'error': str(abort_error)},
            )
        raise Error(Code.s3_error) from e

    return chunk_reader.total_size
