import logging
import time
import typing as t
from contextlib import contextmanager
from functools import wraps

from aiohttp import web
from prometheus_client import Counter, Gauge
from prometheus_client import Summary as BaseSummary

REQUEST_TIME_THRESHOLD = 5000  # ms

DecoratedFunction = t.Callable[..., t.Any]
P = t.ParamSpec('P')
R = t.TypeVar('R')

logger = logging.getLogger(__name__)


def get_resource_path(request: web.Request) -> str:
    return request.match_info.route.resource.canonical if request.match_info.route.resource else ''


class TimerContextManager:
    def __init__(self, callback: t.Callable[..., t.Any]) -> None:
        self._callback = callback

    def _new_timer(self) -> 'TimerContextManager':
        return self.__class__(self._callback)

    def __enter__(self) -> t.Self:
        self._start_time = time.monotonic()
        return self

    def __exit__(self, *args: t.Any, **kwargs: t.Any) -> None:
        self.duration = time.monotonic() - self._start_time

        # write prometheus metric
        self._callback(self.duration)

    def __call__(self, function: t.Callable[P, t.Awaitable[R]]) -> t.Callable[P, t.Awaitable[R]]:
        @wraps(function)
        async def async_wrapper(*args: P.args, **kwargs: P.kwargs) -> R:
            with self._new_timer():
                return await function(*args, **kwargs)

        return async_wrapper


class Summary(BaseSummary):
    def time(self, **kwargs: t.Any) -> TimerContextManager:  # type: ignore
        """
        A decorator and context manager that measures elapsed time, regardless of what the
        process is doing (CPU or waiting)

        Use it to measure the duration of a function call or a block of code.
        """
        func = self.labels(**kwargs).observe if kwargs else self.observe
        return TimerContextManager(callback=func)


class ProcessTimeCounter(Counter):
    @contextmanager
    def process_time(self) -> t.Iterator[None]:
        """
        A context manager that measures process time, which is the time spent by the CPU executing
        a current thread(main or another), excluding time spent waiting for input/output.

        Use it to measure CPU-bound tasks
        """
        start_time = time.thread_time()
        try:
            yield
        finally:
            duration = time.thread_time() - start_time
            self.inc(duration)


http_requests = Counter(
    name='http_requests',
    documentation='HTTP Requests',
    labelnames=['path'],
    namespace='app',
)
http_requests_duration = Summary(
    name='http_requests_duration',
    documentation='HTTP Requests Duration',
    labelnames=['path'],
    namespace='app',
)
http_requests_responses = Counter(
    name='http_requests_response',
    documentation='HTTP Requests Response',
    labelnames=['path', 'code'],
    namespace='app',
)

prepare_archive = Summary(
    name='prepare_archive',
    documentation='Prepare archive',
    namespace='downloads',
)
generate_archive = Summary(
    name='generate_archive',
    documentation='Prepare archive',
    namespace='downloads',
)
get_pdf_content_from_xml = Summary(
    name='get_pdf_content_from_xml',
    documentation='Get PDF content from XML',
    namespace='downloads',
)
generate_print_file = Summary(
    name='generate_print_file',
    documentation='Generate print file',
    namespace='downloads',
)
generate_signed_file = Summary(
    name='generate_signed_file',
    documentation='Generate signed file',
    namespace='downloads',
)
unarchive_content = Summary(
    name='unarchive_content',
    documentation='Unarchive content',
    namespace='downloads',
)
stream_file_buffer = Summary(
    name='stream_file_buffer',
    documentation='Stream file buffer',
    namespace='downloads',
)
stream_s3_object = Summary(
    name='stream_s3_object',
    documentation='Stream s3 object',
    namespace='downloads',
)
s3_download = Summary(
    name='s3_download',
    documentation='S3 Download',
    namespace='downloads',
)
get_available_documents = Summary(
    name='get_available_documents',
    documentation='Get available document for viewing in web',
    namespace='app',
)
s3_encryption_time = ProcessTimeCounter(
    name='s3_encryption_time',
    documentation='S3 encryption time',
)
s3_decryption_time = ProcessTimeCounter(
    name='s3_decryption_time',
    documentation='S3 decryption time',
)
verify_signature_time = ProcessTimeCounter(
    name='verify_signature_time',
    documentation='Verify signature time',
)
generate_hash_time = ProcessTimeCounter(
    name='generate_hash_time',
    documentation='Generate hash time',
)

# Upload counters documents/signatures
documents_count_web = Counter('documents_count_web', 'Documents count')
signatures_count_web = Counter('signatures_count_web', 'Signatures count')

documents_count_api = Counter('documents_count_api', 'Documents count')
signatures_count_api = Counter('signatures_count_api', 'Signatures count')

documents_count_mobile = Counter('documents_count_mobile', 'Documents count')

documents_count_vb = Counter('documents_count_vb', 'Documents count')
signatures_count_vb = Counter('signatures_count_vb', 'Signatures count')


token_requests_old_token_format_err = Counter(
    'token_requests_old_token_format_err', 'Old token format usage count'
)

indexing_fast_size = Gauge('indexing_fast_size', 'Indexing Fast Queue Size')
indexing_slow_size = Gauge('indexing_slow_size', 'Indexing Slow Queue Size')
indexing_listing_size = Gauge('indexing_listing_size', 'Indexing Listing Queue Size')
indexing_archive_size = Gauge('indexing_archive_size', 'Indexing Archive Queue Size')
indexing_dead_size = Gauge('indexing_dead_size', 'Indexing Dead Queue Size')
indexing_temp_size = Gauge('indexing_temp_size', 'Indexing Temp Queue Size')
indexing_in_progress_size = Gauge('indexing_in_progress_size', 'Indexing InProgress Size')


es_requests_counter = Counter(
    name='requests_count',
    documentation='Requests to Elasticsearch counter',
    labelnames=['index'],
    namespace='es',
)
es_get_documents = Summary(
    name='es_get_documents',
    documentation='Search documents in Elasticsearch',
    labelnames=['source'],
    namespace='es',
)
es_get_contact_recipients = Summary(
    name='es_get_contact_recipients',
    documentation='Search contact_recipients in Elasticsearch',
    labelnames=['source'],
    namespace='es',
)
indexator_data_fetch_duration = Summary(
    name='indexator_data_fetch_duration',
    documentation='Indexator data fetch duration',
    labelnames=['entity'],
    namespace='es',
)
indexator_data_send_duration = Summary(
    name='indexator_data_old_es_duration',
    documentation='Indexator ES data processing duration',
    labelnames=['version'],
    namespace='es',
)
indexator_actions_builder_duration = Summary(
    name='indexator_actions_builder_duration',
    documentation='Indexator preparing data for ES duration',
    namespace='es',
)
indexator_count_failed = Counter('indexator_count_failed', 'Indexation failed')


worker_job_duration = Summary(
    name='worker_job_duration',
    documentation='Worker: job duration',
    namespace='worker',
    labelnames=['topic'],
)
worker_job_started_count = Counter(
    name='worker_job_started_count',
    documentation='Worker: job started',
    namespace='worker',
    labelnames=['topic'],
)
worker_job_timed_out_count = Counter(
    name='worker_job_timed_out_count',
    documentation='Worker: job timed out',
    namespace='worker',
    labelnames=['topic'],
)
worker_job_failed_count = Counter(
    name='worker_job_failed_count',
    documentation='Worker: job failed',
    namespace='worker',
    labelnames=['topic'],
)


concierge_updates_started_count = Counter(
    name='concierge_updates_started_count',
    documentation='Concierge: updates started',
)
concierge_updates_failed_count = Counter(
    name='concierge_updates_failed_count',
    documentation='Concierge: updates failed',
)


delete_documents_from_db = Summary(
    name='delete_documents_from_db',
    documentation='Delete docouments from db',
    namespace='documents',
)


events_persistence_duration = Summary(
    name='events_persistance_duration',
    documentation='The duration of persisting user events to the Events DB (seconds)',
    namespace='events',
)

graphp_resolve_field_duration = Summary(
    name='graphp_resolve_field_duration',
    documentation='The duration of resolving a field in the graphp query (seconds)',
    labelnames=['function'],
    namespace='graphp',
)
