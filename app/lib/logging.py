import logging
import time
import typing as t
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)


MAX_LENGTH = 200
PREFIX_LENGTH = 200 // 2 - 10
SUFFIX_LENGTH = 200 // 2 - 10


def _shorten_string(value: str) -> str:
    before = value[:PREFIX_LENGTH]
    after = value[-SUFFIX_LENGTH:]
    return f'{before}... ({len(value)}) ...{after}'


def _shorten_bytes(value: bytes) -> bytes:
    before = value[:PREFIX_LENGTH]
    after = value[-SUFFIX_LENGTH:]
    return before + b'... (' + str(len(value)).encode() + b') ...' + after


def truncate_logging_extra[T](value: T) -> T:
    """
    Recursively shorten the values of a dictionary or a list to MAX_LENGTH.

    TODO: find a better way to handle it automatically on global level.
    """
    if isinstance(value, str):
        return _shorten_string(value)  # type: ignore
    if isinstance(value, bytes) and len(value) > MAX_LENGTH:
        return _shorten_bytes(value)  # type: ignore
    if isinstance(value, dict):
        return {key: truncate_logging_extra(item) for key, item in value.items()}  # type: ignore
    if isinstance(value, list):
        return [truncate_logging_extra(item) for item in value]  # type: ignore
    return value


@asynccontextmanager
async def log_duration(message: str) -> t.AsyncIterator[None]:
    """
    Logs the duration of the context manager's body execution.
    Only logs actions that exceed 1 second
    """

    from app.flags import FeatureFlags
    from app.flags.utils import get_flag

    start_time = time.time()
    try:
        yield
    finally:
        end_time = time.time()
        duration = end_time - start_time  # Duration in seconds

        if not get_flag(FeatureFlags.DISABLE_DURATION_LOGS_HELPER) and duration > 1:
            logger.info(f'[Duration]: {message}', extra={'duration': duration})
