import typing as t

import elasticmagic
from elasticmagic.result import ActionResult, ErrorReason

from app.lib.types import DataDict

DEFAULT_DOC_TYPE = '_doc'


class PatchedActionResult(ActionResult):
    def __init__(self, raw_result: DataDict):
        super(ActionResult, self).__init__(raw_result)
        self.name = next(iter(raw_result.keys()))
        data = next(iter(raw_result.values()))
        self.status = data['status']
        self.found = data.get('found')
        raw_error = data.get('error')
        if raw_error:
            if isinstance(raw_error, str):
                self.error: t.Any = raw_error
            else:
                self.error = ErrorReason(raw_error)
        else:
            self.error = None
        self._index = data['_index']
        self._type = data.get('_type')
        self._id = data['_id']
        self._version = data.get('_version')


class PatchedBulkResult:
    def __init__(self, raw_result: DataDict):
        self.raw = raw_result
        self.took = raw_result['took']
        self.errors = raw_result['errors']
        self.items = list(map(PatchedActionResult, raw_result['items']))

    def __iter__(self) -> t.Iterable[t.Any]:
        return iter(self.items)


def custom_get_doc_type_for_hit(*_: t.Any, **__: t.Any) -> str:
    return DEFAULT_DOC_TYPE


def monkeypatch_elasticmagic() -> None:
    """
    Monkeypatch elasticmagic to add support for elasticsearch8
    """
    elasticmagic.compiler.BulkResult = PatchedBulkResult
    elasticmagic.result.get_doc_type_for_hit = custom_get_doc_type_for_hit
