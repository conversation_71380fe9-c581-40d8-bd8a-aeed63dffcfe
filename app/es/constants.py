INDEXATOR_KEY = 'es:indexator:fast:zset'
INDEXATOR_SLOW_KEY = 'es:indexator:slow:zset'
INDEXATOR_LISTING_KEY = 'es:indexator:listing:zset'
INDEXATOR_ARCHIVE_KEY = 'es:indexator:archive:zset'
INDEXATOR_DEAD_LETTER_KEY = 'es:indexator:dead:zset'

# Used for temp index actions,
# like reindex some bulk of documents to add new fields
INDEXATOR_TEMP_KEY = 'es:indexator:temp:zset'


DOCUMENTS_LOCK_KEY = 'es:indexator:lock:documents'

# Duration limit for Elasticsearch slow operation in seconds
ES_SLOW_DURATION_LIMIT = 1.0

ALL_INDEXING_QUEUES = [
    INDEXATOR_KEY,
    INDEXATOR_SLOW_KEY,
    INDEXATOR_LISTING_KEY,
    INDEXATOR_ARCHIVE_KEY,
    INDEXATOR_DEAD_LETTER_KEY,
]
