from aiohttp import web
from elasticmagic.compiler import Compiler_7_0

from api.public.decorators import api_super_admin_permission_required
from app.auth.types import User
from app.es.models.comment import Comment
from app.es.models.contact_recipient import ContactRecipient
from app.es.models.document import Document
from app.i18n import _
from app.openapi.decorators import openapi_docs
from app.services import services

API_TAG = 'Elasticsearch'


@openapi_docs(
    summary=_('Повернути схему маппінгу Elasticsearch'),
    description=_('Повертає схему маппінгу Elasticsearch для основних моделей проєкту'),
    tags=[API_TAG],
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def show_mapping(_: web.Request, __: User) -> web.Response:
    data = {
        'document': Document.to_mapping(Compiler_7_0),
        'comment': Comment.to_mapping(Compiler_7_0),
        'contact_recipient': ContactRecipient.to_mapping(Compiler_7_0),
    }
    return web.json_response(data)


@openapi_docs(
    summary=_('Синхронізувати маппінг Elasticsearch з моделями в коді'),
    tags=[API_TAG],
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def update_mapping(_: web.Request, __: User) -> web.Response:
    """
    Update mapping of Elasticsearch indexes with models in code.

    Allowed changes:
    - add new fields to existing indices
    - add multi-fields to existing fields
    - add new properties to existing object fields
    - change search settings of existing fields

    Not allowed changes:
    - change the field type of existing field
    - rename an existing field

    Refers to Elasticsearch documentation:
     - https://www.elastic.co/docs/api/doc/elasticsearch/v8/operation/operation-indices-put-mapping
    """
    await services.es.documents.put_mapping(Document)
    await services.es.comments.put_mapping(Comment)
    await services.es.contact_recipients.put_mapping(ContactRecipient)
    return web.Response()


@openapi_docs(
    summary=_('Синхронізувати маппінг Elasticsearch з моделями в коді'),
    tags=[API_TAG],
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def update_contact_recipient_mapping(_: web.Request, __: User) -> web.Response:
    await services.es.contact_recipients.put_mapping(ContactRecipient)
    return web.Response()
