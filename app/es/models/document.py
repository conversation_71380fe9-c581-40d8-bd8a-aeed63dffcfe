from elasticmagic import Document as Model
from elasticmagic import Field
from elasticmagic import types as t

from app.es import fields


class _BaseParameter(Model):
    field_id = Field(t.Keyword)


class ParameterText(_BaseParameter):
    value = Field(t.Keyword)


class ParameterNumber(_BaseParameter):
    value = Field(t.Float)


class ParameterDate(_BaseParameter):
    value = Field(fields.Date)


class Viewer(Model):
    # List or role_ids with same dates
    role_ids = Field(t.List(t.Keyword))
    date_created = Field(fields.Date)
    date_document = Field(fields.Date)


def _suggest_field(type_: t.Type = t.Text) -> Field:
    """
    A specialized text field designed for supporting search suggestions. This field
    uses the 'ngram_analyzer' analyzer for indexing, which tokenizes the text and converts
    it to lowercase, facilitating case-insensitive searches

    Norms are disabled for this field to reduce index size and improve performance,
    as they are not necessary for the type of queries expected on suggestion fields.
    The 'index_options' are set to 'docs', indicating that only document IDs are
    stored, optimizing the field for use cases where only the existence of terms
    in a document is relevant, not their frequency or position.
    """

    return Field(
        type_,
        analyzer='ngram_analyzer',
        search_analyzer='standard',
        norms=False,
        index_options='docs',
    )


class Document(Model):
    """
    Model of a document in Elasticsearch which represents document
    available for company (access_edrpou).
    That means we can have several documents with same ID in index,
    but that documents differ by access_edrpou.
    Remember to specify filter by access_edrpou in each query.

    Also, access_edrpou used as _routing parameter to reduce number of shards
    involved in searching/indexing documents by access_edrpou.
    """

    __doc_type__ = '_doc'

    __mapping_options__ = {
        'dynamic': False,
        'date_detection': False,
    }

    _routing: Field = Field(required=True)

    # _id = company_listing_table.c.id
    # Its internal id for elastic document, should be unique across index
    # We put it equals to company_listing_table.c.id, because it gives unique value for
    # (document.id, access_edrpou) pair. We manually set this value,
    # because we need it for Update/Delete API

    # document.id from DB
    document_id = Field(t.Keyword)

    # Always set this field to True during indexing.
    # We can manually set it to False for all documents,
    # then reindex all documents and find documents that only exists in elastic
    is_exists = Field(t.Boolean)

    access_edrpou = Field(t.Keyword)

    # Field for wide search by different text fields from document
    all_text = Field(
        t.Text,
        norms=False,
        analyzer='text_delimit',
    )

    # Deprecated field, replaced by 'all_text'
    meta_data = Field(
        t.Text,
        norms=False,
        index_options='docs',
        fields={'suggest': _suggest_field()},
    )

    title = Field(
        t.Keyword,
        copy_to='all_text',
        fields={'suggest': _suggest_field()},
    )
    number = Field(
        t.Keyword,
        copy_to='all_text',
        fields={'suggest': _suggest_field()},
    )
    owner_edrpou = Field(
        t.Keyword,
        copy_to='all_text',
        fields={'suggest': _suggest_field()},
    )
    owner_email = Field(
        t.Keyword,
        copy_to='all_text',
        normalizer='lowercase_normalizer',
        fields={'suggest': _suggest_field()},
    )
    owner_company_name = Field(
        t.Keyword,
        copy_to='all_text',
        normalizer='lowercase_normalizer',
        fields={'suggest': _suggest_field()},
    )
    amount = Field(t.Long, copy_to='all_text')
    # used for prefix+term search
    display_title = Field(
        t.Keyword,
        normalizer='lowercase_normalizer',
    )
    recipient_edrpou = Field(t.Keyword)
    # Used for sorting only
    recipient_email = Field(
        t.Keyword,
        normalizer='lowercase_normalizer',
    )
    # Used for sorting only
    recipient_company_name = Field(
        t.Keyword,
        normalizer='lowercase_normalizer',
    )
    status = Field(t.Keyword)
    first_sign_by = Field(t.Keyword)
    is_internal = Field(t.Boolean)
    is_multilateral = Field(t.Boolean)
    expected_owner_signatures = Field(t.Keyword)
    expected_recipient_signatures = Field(t.Keyword)
    pending_owner_signatures = Field(t.Keyword)
    pending_recipient_signatures = Field(t.Keyword)
    seqnum = Field(t.Long)
    date_created = Field(fields.Date)
    date_document = Field(fields.Date)
    date_delivered = Field(fields.Date)
    date_finished = Field(fields.Date)
    date_review_approved = Field(fields.Date)
    date_index = Field(fields.Date)
    # doesn't have any linked parent documents
    is_root = Field(t.Boolean)
    extension = Field(t.Keyword)
    category = Field(t.Keyword)
    vendor_id = Field(t.Keyword)
    vendor = Field(t.Keyword)
    # documents has changed (for API)
    has_changed = Field(t.Boolean)
    # Document signed with signatures than no longer valid (by one or more EDRPOUs).
    # Such documents should be signed again to be fixed.
    is_invalid_signed = Field(t.Boolean)
    # Represents the latest version of the document.
    last_version_uploaded_by_edrpou = Field(t.Keyword)
    parameters_text = Field(t.List(t.Nested(ParameterText)))
    parameters_number = Field(t.List(t.Nested(ParameterNumber)))
    parameters_date = Field(t.List(t.Nested(ParameterDate)))
    # Has comments visible for company
    has_comments = Field(t.Boolean)
    # Specifies if document processed by company
    is_processed = Field(t.Boolean)
    # Tag names for document available within company
    tag_names = Field(
        t.List(t.Keyword),
        normalizer='lowercase_normalizer',
        fields={'suggest': _suggest_field(t.List(t.Text))},
        copy_to='all_text',
    )
    # Tag ids for document available within company
    tag_ids = Field(t.List(t.Keyword))
    # List of role_ids(not admins) for which document is available within company
    viewer_roles = Field(t.List(t.Keyword))
    # Object with document_dates specific to some role_id, used for sorting only
    viewer_dates = Field(t.List(t.Nested(Viewer)))
    # True if document has all expected signatures from company
    is_signed = Field(t.Boolean)
    # Date when add latest signature from current company to document
    date_latest_sign = Field(fields.Date)
    # List of role_ids which have signatures on document from company
    signed_roles = Field(t.List(t.Keyword))
    # List of role_ids which have invalid signatures on document from company
    signed_invalid_roles = Field(t.List(t.Keyword))
    # Document has invalid signatures from roles within company
    is_own_invalid_signed = Field(t.Boolean)
    # Document has invalid signatures from roles outside company
    is_partner_invalid_signed = Field(t.Boolean)
    # List of signers emails within company
    signer_emails = Field(
        t.List(t.Keyword),
        normalizer='lowercase_normalizer',
        fields={'suggest': _suggest_field(t.List(t.Text))},
    )
    # List of roles within company that can apply signature next
    next_signer_roles = Field(t.List(t.Keyword))
    is_current_flow = Field(t.Boolean)
    # List of roles within company with reviews on document
    reviewed_role_ids = Field(t.List(t.Keyword))
    # List of role_ids within company which
    # created review_requests in status='active'
    review_requests_from_roles = Field(t.List(t.Keyword))
    # List of role_ids within company which
    # received review_requests in status='active'
    review_requests_to_roles = Field(t.List(t.Keyword))
    # List of group_ids within company which
    # received review_requests in status='active'
    review_requests_to_groups = Field(t.List(t.Keyword))
    # Which review statuses present on document within company
    review_statuses = Field(t.List(t.Keyword))
    # Document has required reviews in not 'Approved' status
    is_review_required = Field(t.Boolean)
    # List of not_hidden emails within company
    current_recipient_emails = Field(
        t.List(t.Keyword),
        normalizer='lowercase_normalizer',
    )
    recipients_edrpous = Field(
        t.List(t.Keyword),
        fields={'suggest': _suggest_field(t.List(t.Text))},
        copy_to='all_text',
    )
    # List of all not_hidden emails for all recipients
    recipients_emails = Field(
        t.List(t.Keyword),
        normalizer='lowercase_normalizer',
        fields={'suggest': _suggest_field(t.List(t.Text))},
        copy_to='all_text',
    )
    recipients_company_names = Field(
        t.List(t.Keyword),
        normalizer='lowercase_normalizer',
        fields={'suggest': _suggest_field(t.List(t.Text))},
        copy_to='all_text',
    )
    is_archived = Field(t.Boolean)
    directory_id = Field(t.Long)

    # Is document private inside company?
    # See "document_access_settings_private" table for reference
    is_private = Field(t.Boolean)
