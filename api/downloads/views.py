import io
import logging

from aiohttp import web
from botocore.exceptions import ClientError

from api.downloads import utils
from api.downloads.archives import (
    extract_p7s_container,
    generate_documents_archive,
    prepare_archive_files,
)
from api.downloads.constants import TTL_GENERATE_ARCHIVE_LOCK_IN_SEC
from api.downloads.enums import SignatureArchiveFormat
from api.downloads.utils import (
    download_asic_container_content,
    download_file,
    download_file_as_bytes,
    download_review_history_content,
    get_pdf_content_from_xml,
    stream_file_buffer,
    stream_file_original,
    stream_signature_content,
)
from api.downloads.validators import (
    validate_create_xml_to_pdf,
    validate_download_archive,
    validate_download_archived_documents,
    validate_download_file_document,
    validate_download_last_internal_signature,
    validate_download_multi_archive,
    validate_download_original,
    validate_download_review_history,
    validate_download_xml_to_json,
    validate_download_xml_to_pdf,
    validate_multi_download_documents,
)
from api.downloads.viewer import (
    generate_json_file,
    generate_print_file,
    generate_signed_file,
    prepare_viewer_file,
)
from api.downloads.xml_utils import count_rows_xml_to_json, generate_xml_to_json
from api.errors import Code, DoesNotExist, Error, Object
from api.utils import (
    api_response,
    gen_archive_generator_lock_key,
    is_public_api_request,
)
from app.auth.decorators import login_required, redirect_to_auth
from app.auth.types import AuthUser, BaseUser, User
from app.auth.utils import (
    get_company_config,
)
from app.auth.validators import validate_user_permission
from app.document_antivirus.validators import validate_antivirus_check_download_document
from app.events import document_actions
from app.i18n import _
from app.lib import s3_utils, tracking, validators
from app.lib.helpers import json_response, not_none, translit
from app.services import services
from worker import topics

logger = logging.getLogger(__name__)

REDIS_TTL_RESULT__EXPIRE = -1

GQL_CREATE_XML_TO_PDF = """
{{
    document(id: "{document_id}") {{
        title
        extension
        s3XmlToPdfKey
    }}
}}
"""


async def asic(request: web.Request, user: AuthUser | User) -> web.StreamResponse:
    """Download ASiC-e container with document + ECDSA signatures."""

    validate_user_permission(user, {'can_download_document'})

    async with services.db.acquire() as conn:
        document = await validate_download_file_document(request, conn, user)

    buffer = await download_asic_container_content(document.id_)
    if not buffer:
        raise DoesNotExist(Object.signature, document_id=document.id_)

    return await stream_file_buffer(
        request=request,
        file_name=f'{document.title}.sce',
        raw_buffer=io.BytesIO(buffer),
        content_type='Application/vnd.etsi.asic-e+zip',
    )


async def p7s_container(request: web.Request, user: AuthUser | User) -> web.StreamResponse:
    """Download P7S container with document + signatures."""

    validate_user_permission(user, {'can_download_document'})

    async with services.db.acquire() as conn:
        document = await validate_download_file_document(request, conn, user)

    signature_format = SignatureArchiveFormat.internal_appended

    # minimal configuration to get archive files with p7s container
    with tracking.prepare_archive.time():
        options = await prepare_archive_files(
            user=user,
            document=document,
            with_original=True,
            with_xml_original=True,
            with_signatures=True,
            with_xml_preview=False,
            with_signatures_preview=False,
            with_revoke_original=False,
            with_revoke_signatures=True,
            signature_format=signature_format,
        )

    file = await extract_p7s_container(options)
    if not file:
        raise DoesNotExist(Object.signature, document_id=document.id_)

    return await stream_file_buffer(
        request=request,
        file_name=file.file_name,
        raw_buffer=io.BytesIO(file.content),
        content_type='Application/pkcs7-signature',
    )


async def archive(request: web.Request, user: AuthUser | User) -> web.StreamResponse:
    """Download archive of document with digital signatures.

    In total it may result in archive with 5 files, one for original document
    from S3 and one ``.p7s`` file with:

    - Owner signature
    - Owner stamp signature
    - Contragent signature
    - Contragent stamp signature

    If signature or stamp not ready, they will not be added to the archive.

    TODO: Generate archive for document and store it at S3.
    """

    validate_user_permission(user, {'can_download_document'})

    async with services.db.acquire() as conn:
        download_options = await validate_download_archive(request, conn, user)

    document = download_options.document
    archive_config = download_options.archive_config

    with tracking.prepare_archive.time():
        options = await prepare_archive_files(
            user=user,
            document=document,
            with_original=archive_config.with_original,
            with_signatures_preview=archive_config.with_signatures_preview,
            with_xml_preview=archive_config.with_xml_preview,
            with_xml_original=archive_config.with_xml_original,
            with_signatures=archive_config.with_signatures,
            signature_format=archive_config.signature_format,
            with_revoke_original=archive_config.with_revoke_original,
            with_revoke_signatures=archive_config.with_revoke_signatures,
        )

    with tracking.generate_archive.time():
        output = await generate_documents_archive(
            archive_filename=options.archive_filename,
            archives=[options],
            in_one_folder=archive_config.in_one_folder,
            with_instruction=archive_config.with_instruction,
            filenames_mode=archive_config.filenames_mode,
            filenames_max_length=archive_config.filenames_max_length,
        )

    if not isinstance(user, AuthUser) and not is_public_api_request(request):
        await document_actions.add_document_action(
            document_action=document_actions.DocumentAction(
                action=document_actions.Action.document_download,
                document_id=document.id_,
                document_edrpou_owner=document.owned_by_edrpou,
                document_title=document.title,
                company_id=user.company_id,
                company_edrpou=user.company_edrpou,
                email=user.email,
                role_id=user.role_id,
            ),
        )

    return await stream_file_buffer(
        request=request,
        file_name=output.filename,
        raw_buffer=output.buffer,
    )


async def create_xml_to_pdf(request: web.Request, user: AuthUser | User) -> web.StreamResponse:
    """Generate PDF from XML document via URL2PDF service.

    This is an optional and test way to pre-generate PDF from XML document. It
    will be replaced with worker service.
    """
    validate_user_permission(user, {'can_download_document'})

    options = validate_create_xml_to_pdf(
        dict(
            await validators.validate_json_request(request, allow_blank=True),
            document_id=request.match_info['document_id'],
        )
    )
    document_id, force = options.document_id, options.force

    async with services.db.acquire() as conn:
        await validate_antivirus_check_download_document(
            conn=conn, document_id=document_id, company_id=user.company_id
        )
        download_options = await validate_download_original(request, conn, user)

    if not download_options.document.is_xml:
        raise Error(Code.invalid_preview_as_pdf)

    s3_xml_to_pdf_key = download_options.document.s3_xml_to_pdf_key
    status = 201 if (not s3_xml_to_pdf_key or force) else 200

    with tracking.get_pdf_content_from_xml.time():
        content = await get_pdf_content_from_xml(
            user=user,
            document_id=document_id,
            s3_xml_to_pdf_key=s3_xml_to_pdf_key,
            force=force,
        )

    return await stream_file_buffer(
        request=request,
        file_name=translit(f'{download_options.document.title}.pdf'),
        raw_buffer=io.BytesIO(content),
        status=status,
    )


async def original(request: web.Request, user: AuthUser | User) -> web.StreamResponse:
    """Download original document content from S3."""
    async with services.db.acquire() as conn:
        options = await validate_download_original(request, conn, user)
        await validate_antivirus_check_download_document(
            conn=conn,
            document_id=options.document.id_,
            document_version_id=options.document.version_id,
            company_id=user.company_id,
        )

    return await stream_file_original(
        request=request,
        file_name=options.file_name,
        file_item=options.download_file,
    )


async def viewer_signed(request: web.Request, user: AuthUser | User) -> web.StreamResponse:
    """
    Prepare a version of the document for displaying in the UI. Historically, this has been
    called the "signed" version because we used to add a visual signature to the PDF file.
    However, we now include other visual elements such as reviews.

    Unlike the "print" version, the "signed" version typically contains fewer visual elements.
    This is because, in the UI, we try to avoid duplicating information that is already displayed
    outside the viewer.
    """

    async with services.db.acquire() as conn:
        original_options = await validate_download_original(request, conn, user)
        options = await prepare_viewer_file(
            request=request,
            conn=conn,
            company_id=user.company_id,
            original_options=original_options,
        )

        file_name = original_options.file_name

        if options.is_generate_json():
            file_name = f'{file_name}.json'
            buffer = await generate_json_file(conn=conn, options=options, user=user)
        else:
            # For PDF files, we preprocess the PDF by adding a visual signature, reviews, and other
            # visual elements. The final PDF file is then returned to the viewer.
            # Other file formats, like DOCX, XLSX, etc., are returned as is.
            content = await download_file_as_bytes(options.original_options)
            user_company_config = await get_company_config(
                conn=conn,
                company_edrpou=user.company_edrpou,
            )
            buffer = await generate_signed_file(
                content=content,
                user_company_config=user_company_config,
                options=options,
            )

        await utils.finalize_sign_session_on_viewer_download(conn, request)

    return await stream_file_buffer(request, file_name, buffer)


async def viewer_print(request: web.Request, user: AuthUser | User) -> web.StreamResponse:
    """
    Prepare a print version of the document. This version is intended for printing and contains
    additional visual elements such as a visual signature, reviews, signature details (Квитанція),
    and so on.

    This version typically contains more visual elements than the "signed" version because,
    during printing, we aim to include as much information as possible in the printed document.
    """

    validate_user_permission(user, {'can_print_document'})

    async with services.db.acquire() as conn:
        original_options = await validate_download_original(request, conn, user)
        options = await prepare_viewer_file(
            request=request,
            conn=conn,
            company_id=user.company_id,
            original_options=original_options,
        )

        file_name = original_options.file_name

        if options.is_generate_json():
            file_name = f'{file_name}.json'
            buffer = await generate_json_file(conn=conn, options=options, user=user)
        else:
            # For PDF files, we preprocess the PDF by adding a visual signature, reviews, and other
            # visual elements. The final PDF file is then returned to the viewer.
            # Other file formats, like DOCX, XLSX, etc., are returned as is.
            content = await download_file_as_bytes(options.original_options)
            user_company_config = await get_company_config(
                conn=conn,
                company_edrpou=user.company_edrpou,
            )
            buffer = await generate_print_file(
                content=content,
                user_company_config=user_company_config,
                options=options,
            )

        if user.role_id and not is_public_api_request(request):
            await document_actions.add_document_action(
                document_action=document_actions.DocumentAction(
                    action=document_actions.Action.document_export,
                    document_id=original_options.document.id_,
                    document_edrpou_owner=original_options.document.owned_by_edrpou,
                    document_title=original_options.document.title,
                    company_id=not_none(user.company_id),
                    company_edrpou=not_none(user.company_edrpou),
                    email=user.email,
                    role_id=user.role_id,
                )
            )

        await utils.finalize_sign_session_on_viewer_download(conn, request)

    return await stream_file_buffer(request, file_name, buffer)


async def xml_to_json(request: web.Request, user: AuthUser | User) -> web.StreamResponse:
    """Convert XML document to JSON.

    For all XML documents it just XML -> dict conversion, while for 1C XML
    documents we also group body rows and provide ability to limit them to
    avoid rendering large files.
    """
    async with services.db.acquire() as conn:
        options = await validate_download_xml_to_json(request, conn, user)

    buffer = await generate_xml_to_json(options)
    return await stream_file_buffer(
        request,
        options.original_options.file_name,
        buffer,
    )


async def xml_to_json_count_rows(request: web.Request, user: AuthUser | User) -> web.Response:
    """Count body lines for XML document.

    Return number of body lines for any XML document, so later whole XML
    document can be rendered to PDF, not only first 100 lines.
    """
    async with services.db.acquire() as conn:
        options = await validate_download_xml_to_json(request, conn, user)
    rows = await count_rows_xml_to_json(options)

    return api_response(request, {'rows': rows})


async def xml_to_pdf(request: web.Request, user: AuthUser | User) -> web.StreamResponse:
    """Download pre-generated PDF for XML document from S3.

    This handler returns content only in case of current document is a XML
    document and contains filled PDF name column in database. In other cases
    it will return 404 Not Found error.
    """
    validate_user_permission(user, {'can_download_document'})

    async with services.db.acquire() as conn:
        options = await validate_download_xml_to_pdf(request, conn, user)

    # After migration to new S3 we skip xml-to-pdf previews,
    # so just generate new one if its missed
    try:
        content = await download_file(options)
    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchKey':
            with tracking.get_pdf_content_from_xml.time():
                raw_bytes = await get_pdf_content_from_xml(
                    user=user,
                    document_id=options.document.id_,
                    s3_xml_to_pdf_key=options.download_file.key,
                    force=True,
                )
                content = io.BytesIO(raw_bytes)
        else:
            raise e

    return await stream_file_buffer(
        request=request,
        file_name=options.file_name,
        raw_buffer=content,
    )


async def download_archived_documents(request: web.Request, user: User) -> web.Response:
    """
    Creates async job for generating zip archive with documents including
    directories tree structure from company archive.
    Generated archive will be sent by email
    """
    redis = services.redis
    validate_user_permission(user, {'can_download_document'})

    async with services.db.acquire() as conn:
        options = await validate_download_archived_documents(
            conn=conn,
            user=user,
            request=request,
        )

        # User in company can download only one archive at the same time
        lock_key = gen_archive_generator_lock_key(user.role_id, prefix='directories')

        lock_key_ttl = await redis.ttl(lock_key)
        log_extra = {
            'role_id': user.role_id,
            'email': user.email,
            'edrpou': user.company_edrpou,
            'lock_key_ttl': lock_key_ttl,
        }

        # Key was expired or doesn't have TTL at all
        if lock_key_ttl == REDIS_TTL_RESULT__EXPIRE:
            logger.info('Previous archive lock does not have TTL', extra=log_extra)
            await redis.delete(lock_key)

        # Key not expired and lock_key_ttl will be equal to time to expire
        if lock_key_ttl > 0:
            logger.info('Previous archive generation not finished', extra=log_extra)
            message = _('Дочекайтеся закінчення генерації попереднього архіву')
            return json_response({'type': 'info', 'message': message})

        await redis.setex(lock_key, value=1, time=TTL_GENERATE_ARCHIVE_LOCK_IN_SEC)

        data = {
            'document_ids': options.document_ids,
            'directory_ids': options.directory_ids,
            'role_id': user.role_id,
            'lock_key': lock_key,
            'archive_name': options.archive_name,
            'filenames_mode': options.filenames_mode,
        }
        if options.filenames_mode:
            data['filenames_mode'] = options.filenames_mode

        await services.kafka.send_record(
            topic=topics.ARCHIVED_DOCUMENTS_DOWNLOAD,
            value=data,
        )

        return json_response(
            {
                'type': 'success',
                'message': _(
                    'Архів формується, на вашу пошту буде відправлено листа з '
                    'посиланням для завантаження'
                ),
            }
        )


async def multi_archive(request: web.Request, user: User) -> web.Response:
    redis = services.redis
    validate_user_permission(user, {'can_download_document'})

    async with services.db.acquire() as conn:
        options = await validate_multi_download_documents(
            conn=conn,
            user=user,
            request=request,
        )

        # User in company can download only one archive at the same time
        lock_key = gen_archive_generator_lock_key(user.role_id)

        lock_key_ttl: int = await redis.ttl(lock_key)
        log_extra = {
            'role_id': user.role_id,
            'email': user.email,
            'edrpou': user.company_edrpou,
            'lock_key_ttl': lock_key_ttl,
        }

        # Key was expired or doesn't have TTL at all
        if lock_key_ttl == REDIS_TTL_RESULT__EXPIRE:
            logger.info('Previous archive lock does not have TTL', extra=log_extra)
            await redis.delete(lock_key)

        # Key not expired and lock_key_ttl will be equal to time to expire
        if lock_key_ttl > 0:
            logger.info('Previous archive generation not finished', extra=log_extra)
            message = _('Дочекайтеся закінчення генерації попереднього архіву')
            return json_response({'type': 'info', 'message': message})

        await redis.setex(lock_key, value=1, time=TTL_GENERATE_ARCHIVE_LOCK_IN_SEC)

        await services.kafka.send_record(
            topic=topics.MULTI_DOWNLOAD,
            value={
                'documents_ids': options.documents_ids,
                'role_id': user.role_id,
                'lock_key': lock_key,
                'archive_name': options.archive_name,
                'request_data': options.request_data,
                **options.archive_config.to_dict(),
            },
        )

    return json_response(
        {
            'type': 'success',
            'message': _(
                'Архів формується, на вашу пошту буде відправлено листа з '
                'посиланням для завантаження'
            ),
        }
    )


@redirect_to_auth
async def download_multi_archive(request: web.Request, user: BaseUser | User) -> web.StreamResponse:
    """
    TODO: stream from S3
    """
    async with services.db.acquire() as conn:
        ctx = await validate_download_multi_archive(conn, request, user)

    download_item = s3_utils.DownloadFile(key=ctx.s3_key)

    try:
        content, meta = await s3_utils.download(download_item)
    except Exception:
        logger.exception('Fail to download archive', extra={'archive': ctx.to_log_extra()})
        return web.Response(status=404)

    file_name: str
    if ctx.archive_name:
        file_name = ctx.archive_name
    elif meta and 'filename' in meta:
        file_name = meta['filename']
    else:
        file_name = 'Doc_Vchasno.zip'

    return await stream_file_buffer(request, file_name, io.BytesIO(content))


async def last_internal_signature(
    request: web.Request, user: AuthUser | User
) -> web.StreamResponse:
    """Download content of the last internal signature"""

    async with services.db.acquire() as conn:
        signature = await validate_download_last_internal_signature(conn, request, user)

    return await stream_signature_content(
        request=request,
        document_id=signature.document_id,
        signature_id=signature.id,
    )


@login_required()
async def download_review_history(request: web.Request, user: User) -> web.StreamResponse:
    async with services.db.acquire() as conn:
        ctx = await validate_download_review_history(conn, user, request)
        return await download_review_history_content(conn, user, ctx)
