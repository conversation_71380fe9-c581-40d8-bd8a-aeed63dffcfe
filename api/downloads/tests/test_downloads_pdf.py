from pathlib import Path

import pytest

from api.downloads import pdf
from api.downloads.pdf.common import (
    generate_signed_file,
)
from api.downloads.tests.conftest import assert_pdf_file
from api.downloads.types import (
    Document,
    Review,
    ViewerSignatureEusign,
    to_document,
)
from app.auth.schemas import ReviewRenderConfig
from app.document_revoke.enums import DocumentRevokeStatus
from app.document_revoke.types import DocumentRevoke
from app.documents.db import select_document_by_id, select_document_recipients
from app.documents.enums import FirstSignBy
from app.i18n import _
from app.lib.enums import DocumentStatus, RenderSignatureAtPage, SignatureFormat, SignatureType
from app.reviews.enums import ReviewType
from app.reviews.types import ReviewHistory, ReviewHistoryItem
from app.services import services
from app.signatures.db import select_signatures
from app.signatures.enums import CertificatePowerType, SignaturePowerType
from app.tests.common import (
    datetime_test,
    prepare_app_client,
    prepare_client,
    prepare_uploaded_signed_documents,
)

DATA_DIR = Path(__file__).parent / 'data'


OWNER_EDRPOU = '12345678'
RECIPIENT_EDRPOU = '98765432'
TEST_DOCUMENT_TITLE_SPECIAL_CHARS = 'test<a'
TEST_DOCUMENT_NUMBER_SPECIAL_CHARS = 'C<D-0000847'

TEST_DOCUMENT_1 = Document(
    id_='a3d8f7a0-4c1e-4b1f-9c7d-1c8b3b9d9b2d',
    created_by_edrpou=OWNER_EDRPOU,
    owned_by_edrpou=OWNER_EDRPOU,
    raw_file_name='test.pdf',
    date_created=datetime_test('2022-01-01 00:00:00'),
    date_delivered=datetime_test('2022-01-02 00:00:00'),
    date_rejected=None,
    recipient_edrpou=RECIPIENT_EDRPOU,
    type_='pdf',
    s3_xml_to_pdf_key=None,
    first_sign_by=FirstSignBy.owner,
    version=None,
    number='123456',
    status=DocumentStatus.uploaded,
)
TEST_REVOKE_1 = DocumentRevoke(
    id='ab38e611-3b10-4a58-961b-b67db5638a52',
    document_id=TEST_DOCUMENT_1.id_,
    initiator_role_id='initiator_role_id',
    initiator_company_id='initiator_company_id',
    reason='Test reason',
    status=DocumentRevokeStatus.completed,
    signature_format=SignatureFormat.internal_wrapped,
)


VIEWER_EUSIGN_SIGNATURE_1 = ViewerSignatureEusign(
    id_='a3d8f7a0-4c1e-4b1f-9c7d-1c8b3b9d9b2d',
    date_created=datetime_test('2022-01-01 00:00:00'),
    is_owner=True,
    type_=SignatureType.signature,
    company_name='Company Name 1',
    edrpou=OWNER_EDRPOU,
    name='User Name 1',
    position='Position 1',
    serial_number='1234567890',
    is_legal=True,
    time_mark=datetime_test('2022-01-02 00:00:00'),
    signature_power_type=SignaturePowerType.qualified,
    certificate_power_type=CertificatePowerType.qualified,
)
VIEWER_EUSIGN_SIGNATURE_2 = ViewerSignatureEusign(
    id_='4a3d8f7a-1e4c-1f4b-9c7d-1c8b3b9d9b2d',
    date_created=datetime_test('2022-01-01 00:00:00'),
    is_owner=False,
    type_=SignatureType.signature,
    company_name='Company Name 2',
    edrpou=RECIPIENT_EDRPOU,
    name='User Name 2',
    position='Position 2',
    serial_number='0987654321',
    is_legal=True,
    time_mark=datetime_test('2022-01-02 00:00:00'),
    signature_power_type=SignaturePowerType.qualified,
    certificate_power_type=CertificatePowerType.qualified,
)


@pytest.mark.parametrize(
    'filename, render_at_page, expected_pages',
    [
        ('pdf-sample.pdf', RenderSignatureAtPage.first, 1),
        ('pdf-sample-multipage.pdf', RenderSignatureAtPage.first, 8),
        ('pdf-sample-multipage.pdf', RenderSignatureAtPage.last, 8),
        ('pdf-sample-multipage.pdf', RenderSignatureAtPage.all, 8),
    ],
)
async def test_generate_signed_file(aiohttp_client, filename, render_at_page, expected_pages):
    app, client, owner = await prepare_client(aiohttp_client)
    documents_ids = await prepare_uploaded_signed_documents(app, owner, extension='.pdf')

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, documents_ids[0])
        signatures = await select_signatures(conn, documents_ids)

    original_content = (DATA_DIR / filename).read_bytes()

    document = to_document(document)

    viewer_signatures: list[ViewerSignatureEusign] = []
    for signature in signatures:
        viewer_signatures.append(
            ViewerSignatureEusign.key_from_db(
                signature=signature,
                document_owner_edrpou=document.owned_by_edrpou,
            )
        )

    signed_file = generate_signed_file(
        document=document,
        original_content=original_content,
        signatures=viewer_signatures,
        reviews=[],
        review_render_config=ReviewRenderConfig(),
        render_at_page=render_at_page,
        render_signature=True,
        render_review=False,
        locale_code=None,
        use_pdf_17=True,
    )

    assert_pdf_file(
        output=signed_file,
        expected_pages=expected_pages,
        # __output_path=DATA_DIR / f'__{filename}.{render_at_page.value}.pdf',
    )

    signed_file.seek(0)
    signed_content = signed_file.read()
    assert len(original_content) < len(signed_content)


async def test_generate_signed_file_with_corrupted_eof(aiohttp_client):
    """
    Add to the end of the file a lot of data after EOF and check if it will handle it correctly

    Originally, we had the function "remove_data_after_pdf_oef" that was developed to fix an issue
    where some of our clients automatically added too much extra data after %%EOF in PDF files.
    By specification, PDF files should end with %%EOF within the last 1024 bytes of the file,
    and everything that comes after that marker should be ignored. At the time the bug was
    reported, we used PyPDF to parse PDF files, and it had problems parsing malformed PDF files
    if more than 1024 bytes were present after %%EOF [1]. To fix this issue, we manually
    truncated the PDF file to the last %%EOF marker.

    Now, we use pikepdf to parse PDF files. It also tries to find the %%EOF marker within the
    last 1024 bytes and, by default, fails if it can't find it. However, it has a special option
    to enable a recovery mechanism that allows it to parse malformed PDF files, including those
    where the %%EOF marker is not present within the last 1024 bytes:

    > pikepdf.Pdf.open(..., attempt_recovery=True)

    1. https://stackoverflow.com/questions/45390608/eof-marker-not-found-while-use-pypdf2-merge-pdf-file-in-python
    """
    app, client, owner = await prepare_client(aiohttp_client)
    documents_ids = await prepare_uploaded_signed_documents(app, owner, extension='.pdf')

    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, documents_ids[0])
        signatures = await select_signatures(conn, documents_ids)

    original_content = (DATA_DIR / 'pdf-sample.pdf').read_bytes()
    original_content += b'q' * 100_000  # 100 kb of extra data

    document = to_document(document)

    viewer_signatures: list[ViewerSignatureEusign] = []
    for signature in signatures:
        viewer_signatures.append(
            ViewerSignatureEusign.key_from_db(
                signature=signature,
                document_owner_edrpou=document.owned_by_edrpou,
            )
        )

    signed_file = generate_signed_file(
        document=document,
        original_content=original_content,
        signatures=viewer_signatures,
        reviews=[],
        render_at_page=RenderSignatureAtPage.first,
        review_render_config=ReviewRenderConfig(),
        render_signature=True,
        render_review=False,
        locale_code=None,
        use_pdf_17=True,
    )

    # Result file should be already without extra data after EOF
    assert signed_file.getvalue().endswith(b'%%EOF\n')

    assert_pdf_file(
        output=signed_file,
        expected_pages=1,
        # __output_path=DATA_DIR / '__pdf-sample-large-extra.pdf',
    )


async def test_generate_review_history_file():
    history = ReviewHistory(
        document_id='a3d8f7a0-4c1e-4b1f-9c7d-1c8b3b9d9b2d',
        document_title=TEST_DOCUMENT_TITLE_SPECIAL_CHARS,
        document_number=TEST_DOCUMENT_NUMBER_SPECIAL_CHARS,
        document_date=datetime_test('2022-01-01 00:00:00'),
        items=[
            ReviewHistoryItem(
                user_name='User Name 1',
                user_email='<EMAIL>',
                action=_('Погоджує'),
                date=datetime_test('2022-01-02 00:00:00'),
            ),
            ReviewHistoryItem(
                user_name='User Name 2',
                user_email='<EMAIL>',
                action=_('Погоджує'),
                date=datetime_test('2022-01-03 00:00:00'),
            ),
        ],
    )
    output = pdf.generate_review_history_file_sync(history=history, use_pdf_17=True)
    assert_pdf_file(
        output=output,
        expected_pages=1,
        # __output_path=DATA_DIR / '__review_history.pdf',
    )


@pytest.mark.parametrize(
    'debug_name, signatures, expected_pages',
    (('eusign', [VIEWER_EUSIGN_SIGNATURE_1, VIEWER_EUSIGN_SIGNATURE_2], 2),),
)
async def test_generate_print_file(signatures, debug_name, expected_pages):
    original = (DATA_DIR / 'pdf-sample.pdf').read_bytes()
    reviews = [
        Review(
            type_=ReviewType.approve,
            email='<EMAIL>',
            first_name='Test',
            second_name='Test',
            last_name='Test',
            position='Head',
        ),
        Review(
            type_=ReviewType.approve,
            email='<EMAIL>',
            first_name='Test',
            second_name='Test',
            last_name='Test',
            position='Head',
        ),
    ]
    # change status to add revoke information to the PDF
    # and add special characters to test escape
    test_document_2 = TEST_DOCUMENT_1._replace(
        status=DocumentStatus.revoked,
        raw_file_name=f'{TEST_DOCUMENT_TITLE_SPECIAL_CHARS}.pdf',
        number=TEST_DOCUMENT_NUMBER_SPECIAL_CHARS,
    )
    output = pdf.generate_print_file(
        original_content=original,
        document=test_document_2,
        signatures=signatures,
        reviews=reviews,
        review_render_config=ReviewRenderConfig(),
        revoke_signatures=signatures,
        revoke=TEST_REVOKE_1,
        render_at_page=RenderSignatureAtPage.first,
        render_signature=True,
        render_review=True,
        locale_code=None,
        use_pdf_17=True,
    )
    assert_pdf_file(
        output=output,
        expected_pages=expected_pages,
        # __output_path=DATA_DIR / f'__pdf-sample-print-{debug_name}.pdf',
    )


@pytest.mark.parametrize(
    'debug_name, signatures, expected_pages',
    (('eusign', [VIEWER_EUSIGN_SIGNATURE_1, VIEWER_EUSIGN_SIGNATURE_2], 1),),
)
async def test_generate_signatures_details_file(
    aiohttp_client, signatures, debug_name, expected_pages
):
    # prepare app context with process_pool
    await prepare_app_client(aiohttp_client)

    async with services.db.acquire() as conn:
        recipients = await select_document_recipients(conn, document_id=TEST_DOCUMENT_1.id)

    output = await pdf.generate_signatures_details_file(
        document=TEST_DOCUMENT_1,
        recipients=recipients,
        signatures=signatures,
        revoke_signatures=[],
        revoke=None,
    )
    assert_pdf_file(
        output=output,
        expected_pages=expected_pages,
        # __output_path=DATA_DIR / f'__pdf-sample-signatures-details-{debug_name}.pdf',
    )
