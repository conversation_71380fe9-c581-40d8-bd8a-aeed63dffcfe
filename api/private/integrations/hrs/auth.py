import functools
import logging
import typing as t

import jwt
import pydantic
from aiohttp import web

from api import errors
from app.auth import constants as auth_constants
from app.auth import db as auth_db
from app.auth import types as auth_types
from app.lib import integrations
from app.lib import types as core_types
from app.lib import validators_pydantic as pv
from app.services import services

JWT_ALGORITHM = 'HS256'
HRSHandler = t.Callable[[web.Request, auth_types.User], t.Awaitable[web.StreamResponse]]

logger = logging.getLogger(__name__)


class HrsUserAuthSchema(pydantic.BaseModel):
    edrpou: pv.EDRPOU
    email: pv.Email


def _validate_user_auth(request: web.Request) -> HrsUserAuthSchema:
    token = request.headers.get('X-HRS-User-Token')

    if not token:
        logger.warning('Attempt to access HRS integration API without token')
        raise errors.InvalidRequest()

    jwt_data = jwt.decode(token, verify=False, algorithms=[JWT_ALGORITHM])
    try:
        auth_schema = HrsUserAuthSchema.model_validate(jwt_data)
    except pydantic.ValidationError:
        logger.info(msg='HRS user validation failed', extra={'jwt_data': jwt_data})
        raise errors.InvalidRequest()

    return auth_schema


def hrs_auth_required(handler: HRSHandler) -> core_types.Handler:
    """
    Decoraror for HRS authentication.

    Used for covering HRS view handlers.
    """

    @functools.wraps(handler)
    async def wrapper(request: web.Request) -> web.StreamResponse:
        integrations.validate_private_integration_token(request, integration='hrs')
        authenticated_user = _validate_user_auth(request)

        async with services.db.acquire() as conn:
            user = await auth_db.select_user(
                conn=conn,
                company_edrpou=authenticated_user.edrpou,
                email=authenticated_user.email,
            )

        if not user:
            logger.warning(
                'User from HRS not found',
                extra={
                    'email': authenticated_user.email,
                    'edrpou': authenticated_user.edrpou,
                },
            )
            raise errors.AccessDenied()

        request[auth_constants.USER_APP_KEY] = user
        return await handler(request, user)

    return wrapper
