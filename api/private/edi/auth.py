import logging
import typing as t
from functools import wraps

from aiohttp import web

from api.errors import AccessDenied
from api.private.edi.utils import get_user_and_source
from api.private.edi.validators import validate_user
from app.auth.constants import USER_APP_KEY
from app.auth.types import User
from app.lib.types import Handler
from app.services import services

EDIHandler = t.Callable[[web.Request, User, str], t.Awaitable[web.StreamResponse]]

logger = logging.getLogger(__name__)


def _validate_edi_integration_token(request: web.Request) -> None:
    """
    Check that proper EDI integration token is provided in the request.
    """
    request_token = request.headers.get('X-EDI-Authorization')
    config = services.config.edi
    if not config:
        raise AccessDenied()

    token = config.rpc_auth_token

    if request_token != token:
        raise AccessDenied()


def integration_required(
    handler: t.Callable[[web.Request], t.Awaitable[web.StreamResponse]],
) -> Handler:
    """
    Decorator for EDI integration handlers. It checks only the integration token.

    Used for requests that are not related to a specific user.
    """

    @wraps(handler)
    async def wrapper(request: web.Request) -> web.StreamResponse:
        _validate_edi_integration_token(request)
        return await handler(request)

    return wrapper


def auth_required(handler: EDIHandler) -> Handler:
    """
    Decorator for EDI integration handlers.

    It checks the integration token and extracts the user to perform action on behalf of it.
    """

    @wraps(handler)
    async def wrapper(request: web.Request) -> web.StreamResponse:
        _validate_edi_integration_token(request)

        by_edrpou, by_email = validate_user(request)

        async with request.app['db'].acquire() as conn:
            user, source = await get_user_and_source(
                conn=conn, by_edrpou=by_edrpou, by_email=by_email
            )
        if not user:
            logger.warning(
                'User from EDI not found',
                extra={'email': by_email, 'edrpou': by_edrpou},
            )
            raise AccessDenied()

        request[USER_APP_KEY] = user
        return await handler(request, user, source)

    return wrapper
