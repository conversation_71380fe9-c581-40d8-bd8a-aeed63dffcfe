import base64
import io
import logging
from collections import defaultdict
from http import HTT<PERSON>tatus

from aiohttp import web
from aiohttp.web_request import FileField

from api.downloads.archives import (
    generate_documents_archive,
    prepare_archive_files,
)
from api.downloads.enums import ArchiveFilenamesMode
from api.downloads.utils import (
    download_external_key_signature,
    download_external_stamp_signature,
    download_internal_signature,
    stream_file_buffer,
)
from api.downloads.validators import (
    validate_download_archive,
    validate_download_file_document,
)
from api.errors import DoesNotExist, InvalidRequest, Object
from api.private.edi.auth import auth_required, integration_required
from api.private.integrations import handlers as integration_handlers
from api.private.integrations import handlers as private_handlers
from api.private.integrations.enums import PrivateIntegration
from api.public import views as public_views
from api.public.validators import DocumentIdSchema
from app.auth.types import User
from app.document_revoke.utils import (
    add_document_revoke,
    add_document_revoke_signature,
    add_document_revoke_signature_async_jobs,
    reject_document_revoke,
)
from app.document_revoke.validators import (
    validate_create_document_revoke,
    validate_document_revoke_exists,
    validate_document_revoke_reject,
    validate_document_revoke_sign,
)
from app.documents import views as documents
from app.documents import views as documents_views
from app.documents.enums import DocumentSource
from app.documents.validators import validate_document_access
from app.es.utils import send_to_indexator
from app.events.document_actions import Action, DocumentAction, add_document_action
from app.i18n import _
from app.lib import tracking
from app.lib.enums import SignatureFormat
from app.lib.validators import (
    validate_json_request,
    validate_post_request,
    validate_pydantic,
)
from app.registration.enums import RegistrationSource
from app.services import services
from app.sign_sessions import views as sign_sessions
from app.sign_sessions.enums import SignSessionSource
from app.signatures.enums import SignatureSource
from app.signatures.utils import get_signature_source_from_request
from app.signatures.validators import validate_signature_access
from app.uploads.utils import (
    form_field_to_json,
    prepare_upload_form_parameters,
    process_web_upload,
)

logger = logging.getLogger(__name__)


@auth_required
async def upload(request: web.Request, user: User, source: str) -> web.Response:
    """
    Process documents upload from EDI
    """
    # Process uploading and indexing the documents
    documents, _ = await process_web_upload(request, user, DocumentSource(source))
    await send_to_indexator(document_ids=[d.id for d in documents], to_slow_queue=True)

    # Trigger prometheus tracking
    tracking.documents_count_web.inc(len(documents))

    return web.json_response(data={'document_id': documents[0].id}, status=201)


@auth_required
async def send_document(request: web.Request, user: User, _: str) -> web.Response:
    return await documents.send_document(request, user)


@auth_required
async def get_document(request: web.Request, user: User, _: str) -> web.Response:
    document_id = request.match_info['document_id']
    async with request.app['db'].acquire() as conn:
        document = await validate_document_access(conn, user, document_id)
    return web.json_response({'status': document.status_id})


@auth_required
async def delete(request: web.Request, user: User, _: str) -> web.Response:
    return await documents.delete(request, user, delete_from_edi=False)


@auth_required
async def create_sign_session(request: web.Request, user: User, _: str) -> web.Response:
    return await sign_sessions.create_sign_session(
        request=request,
        user=user,
        source=SignSessionSource.edi,
    )


@auth_required
async def add_signature(request: web.Request, user: User, _: str) -> web.Response:
    return await public_views.add_signature(request, user)


@auth_required
async def get_signature(request: web.Request, user: User, _: str) -> web.StreamResponse:
    signature_id = request.match_info['signature_id']

    async with request.app['db'].acquire() as conn:
        signature = await validate_signature_access(conn, signature_id, user)

    content: bytes
    if signature.is_internal:
        content = await download_internal_signature(
            document_id=signature.document_id,
            signature_id=signature.id,
        )
    else:
        # External signatures are stored in key/stamp fields in DB (old way)
        # or on S3 (new way)
        if signature.key_exists:
            content = await download_external_key_signature(signature=signature)
        elif signature.stamp_exists:
            content = await download_external_stamp_signature(signature=signature)

        else:
            raise DoesNotExist(Object.signature, signature_id=signature_id)

    return await stream_file_buffer(request, 'signature.p7s', io.BytesIO(content))


@auth_required
async def get_signatures(request: web.Request, user: User, _: str | None = None) -> web.Response:
    async with request.app['db'].acquire() as conn:
        document = await validate_download_file_document(request, conn, user)

    with tracking.prepare_archive.time():
        options = await prepare_archive_files(
            user=user,
            document=document,
            with_xml_preview=False,
            with_signatures=True,
            with_original=True,
            with_signatures_preview=False,
            with_xml_original=True,
            signature_format=None,
            # do not include revokes into response
            # because it will break logic on the edi side
            # to get revoke files use separate endpoint
            with_revoke_original=False,
            with_revoke_signatures=False,
        )

    signatures = defaultdict(list)
    for file in options.files:
        signatures[document.id_].append(
            {'file_name': file.file_name, 'file': base64.b64encode(file.content).decode()}
        )

    return web.json_response(signatures)


@auth_required
async def get_document_archive(request: web.Request, user: User, _: str) -> web.StreamResponse:
    async with request.app['db'].acquire() as conn:
        download_options = await validate_download_archive(request, conn, user)

    document = download_options.document
    archive_config = download_options.archive_config

    with tracking.prepare_archive.time():
        options = await prepare_archive_files(
            user=user,
            document=document,
            with_xml_preview=archive_config.with_xml_preview,
            with_signatures=archive_config.with_signatures,
            with_xml_original=archive_config.with_xml_original,
            with_original=True,
            with_signatures_preview=True,
            signature_format=None,
            with_revoke_original=archive_config.with_xml_original,
            with_revoke_signatures=archive_config.with_signatures,
        )

    with tracking.generate_archive.time():
        output = await generate_documents_archive(
            archive_filename=options.archive_filename,
            archives=[options],
            in_one_folder=archive_config.in_one_folder,
            with_instruction=archive_config.with_instruction,
            filenames_mode=ArchiveFilenamesMode.default,
            filenames_max_length=archive_config.filenames_max_length,
        )

    return await stream_file_buffer(
        request=request,
        file_name=output.filename,
        raw_buffer=output.buffer,
    )


@integration_required
async def invite_user_edi(request: web.Request) -> web.Response:
    """Auto generates user with given email."""
    return await integration_handlers.invite_user_common(
        request=request,
        source=RegistrationSource.edi,
    )


@auth_required
async def document_reject(request: web.Request, user: User, _: str) -> web.Response:
    return await documents_views.reject(request, user)


@auth_required
async def list_document_comments(request: web.Request, user: User, _: str) -> web.Response:
    """
    Retrieve available comments
    """

    return await public_views.list_document_comments(request, user)


@integration_required
async def create_vchasno_user_edi(request: web.Request) -> web.Response:
    return await private_handlers.create_vchasno_user_common(
        request=request,
        source=PrivateIntegration.edi,
    )


@auth_required
async def document_revoke_create(request: web.Request, user: User, source: str) -> web.Response:
    """
    Create document revoke.

    Form data should contain:
    - original_file: file
    - data: field with signature fields in json (may be as string, bytes, or FileField)
    - p7s: file with signature (optional)
    - params: JSON string with additional parameters
      - reason: string
      - format: string
    """

    params, form_data = await prepare_upload_form_parameters(request)
    original_file: str | bytes | bytearray | FileField | None = form_data.get('original_file')
    if not original_file:
        raise InvalidRequest(reason=_('Missing original_file'))

    if not isinstance(original_file, FileField):
        raise InvalidRequest(reason=_('Use multipart/form-data content-type'))

    async with services.db.acquire() as conn:
        async with conn.begin():
            ctx_create = await validate_create_document_revoke(
                conn=conn,
                user=user,
                raw_data={
                    'reason': params.pop('reason', None),
                    'document_id': request.match_info['document_id'],
                },
            )
            ctx_create.content = original_file.file.read()
            # set as default (usually edi sends internal wrapped signatures)
            # but we can add other
            # formats on validate_document_revoke_sign|add_document_revoke_signature
            ctx_create.signature_format = (
                params.get('format')
                and SignatureFormat(params['format'])
                or SignatureFormat.internal_wrapped
            )

            revoke = await add_document_revoke(
                conn=conn,
                ctx=ctx_create,
            )
            ctx_sign = await validate_document_revoke_sign(
                conn=conn,
                user=user,
                source=SignatureSource.api,
                revoke=revoke,
                raw_data={
                    **params,
                    **form_field_to_json(form_data.get('data', None)),
                    'p7s': form_data.get('p7s'),
                    'revoke_id': revoke.id,
                },
            )
            await add_document_revoke_signature(conn=conn, ctx=ctx_sign)

        await add_document_revoke_signature_async_jobs(ctx=ctx_sign)
        await add_document_action(
            DocumentAction(
                action=Action.revoke_sign,
                document_id=ctx_sign.document.id,
                document_edrpou_owner=ctx_sign.document.edrpou_owner,
                document_title=ctx_sign.document.title,
                company_id=ctx_sign.user.company_id,
                company_edrpou=ctx_sign.user.company_edrpou,
                email=ctx_sign.user.email,
                role_id=ctx_sign.user.role_id,
            )
        )

        return web.json_response(status=HTTPStatus.CREATED)


@auth_required
async def document_revoke_get(request: web.Request, user: User, _: str) -> web.Response:
    """
    Get document revoke
    """

    async with services.db.acquire() as conn:
        valid_data = validate_pydantic(
            DocumentIdSchema, {'document_id': request.match_info['document_id']}
        )
        revoke = await validate_document_revoke_exists(
            conn=conn,
            document_id=valid_data.document_id,
        )
        await validate_document_access(
            conn=conn,
            user=user,
            document_id=revoke.document_id,
        )

        return web.json_response(
            {
                'status': revoke.status,
            }
        )


@auth_required
async def document_revoke_add_signature(
    request: web.Request,
    user: User,
    _: str,
) -> web.Response:
    """
    Add signature to document revoke
    """
    async with services.db.acquire() as conn:
        revoke = await validate_document_revoke_exists(
            conn=conn,
            document_id=request.match_info['document_id'],
        )

        ctx = await validate_document_revoke_sign(
            conn=conn,
            user=user,
            source=get_signature_source_from_request(request),
            revoke=revoke,
            raw_data={
                **dict(await validate_post_request(request)),
                'revoke_id': revoke.id,
            },
        )
        await add_document_revoke_signature(conn=conn, ctx=ctx)

    await add_document_revoke_signature_async_jobs(ctx=ctx)
    await add_document_action(
        DocumentAction(
            action=Action.revoke_sign,
            document_id=ctx.document.id,
            document_edrpou_owner=ctx.document.edrpou_owner,
            document_title=ctx.document.title,
            company_id=ctx.user.company_id,
            company_edrpou=ctx.user.company_edrpou,
            email=ctx.user.email,
            role_id=ctx.user.role_id,
        )
    )
    return web.json_response(status=HTTPStatus.CREATED)


@auth_required
async def document_revoke_reject(
    request: web.Request,
    user: User,
    _: str,
) -> web.Response:
    """
    Reject document revoke
    """
    async with services.db.acquire() as conn:
        revoke = await validate_document_revoke_exists(
            conn=conn,
            document_id=request.match_info['document_id'],
        )

        ctx = await validate_document_revoke_reject(
            conn=conn,
            user=user,
            revoke=revoke,
            raw_data={
                **(await validate_json_request(request)),
                'revoke_id': revoke.id,
            },
        )
        await reject_document_revoke(
            conn=conn,
            ctx=ctx,
        )

    return web.json_response()


@auth_required
async def get_revoke_signatures(
    request: web.Request, user: User, _: str | None = None
) -> web.Response:
    async with request.app['db'].acquire() as conn:
        document = await validate_download_file_document(request, conn, user)

    with tracking.prepare_archive.time():
        options = await prepare_archive_files(
            user=user,
            document=document,
            with_xml_preview=False,
            with_signatures=False,
            with_original=False,
            with_signatures_preview=False,
            with_xml_original=False,
            signature_format=None,
            with_revoke_original=True,
            with_revoke_signatures=True,
        )

    signatures = defaultdict(list)
    for file in options.files:
        signatures[document.id_].append(
            {'file_name': file.file_name, 'file': base64.b64encode(file.content).decode()}
        )

    return web.json_response(signatures)
