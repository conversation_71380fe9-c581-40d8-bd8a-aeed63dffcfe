import base64
import datetime
import json
import logging
import smtplib
import uuid
from pathlib import Path
from unittest import mock

import pytest
import sqlalchemy as sa
import ujson

from api.enums import Vendor
from api.private.blackbox.utils import BLACKBOX_DOCUMENTS_NOTIFICATION_REDIS_KEY
from api.public.tests.common import TEST_RECIPIENT_EDRPOU, TEST_RECIPIENT_EMAIL
from api.uploads import utils as uploads_utils
from app.auth.db import select_company_by_id
from app.billing.db import select_company_accounts, select_company_transactions
from app.contacts.db import update_main_recipient
from app.contacts.tables import contact_person_table, contact_table
from app.contacts.types import to_contact_details
from app.delayed_task.db import select_delayed_tasks
from app.documents.db import select_bilateral_document_recipient, select_document_by_id
from app.documents.enums import DocumentSource, FirstSignBy
from app.documents.tables import document_meta_table, document_table, listing_table
from app.documents.utils import (
    get_external_key_signature_s3_key,
    get_external_stamp_signature_s3_key,
    get_internal_signature_s3_key,
)
from app.events.document_actions.db import select_document_actions_for
from app.lib import eusign_utils
from app.lib.datetime_utils import to_local_datetime
from app.lib.enums import DocumentStatus, SignatureFormat, UserRole
from app.lib.helpers import ensure_mimetype
from app.lib.s3_utils import UploadFile
from app.models import count, select_one
from app.services import services
from app.sign_sessions.enums import SignSessionType
from app.sign_sessions.tables import sign_session_table
from app.signatures.db import select_signatures
from app.signatures.tables import signature_table
from app.tests.common import (
    NOVA_POSHTA_EDRPOU,
    TEST_BILLING_ACCOUNT,
    TEST_COMPANY_EDRPOU,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    TEST_UPLOAD_LEFT_COUNTER,
    ZAKUPKI_EDRPOU,
    fetch_graphql,
    get_document,
    get_document_meta,
    prepare_auth_headers,
    prepare_client,
    prepare_company_data,
    prepare_document_data,
    prepare_form_data,
    prepare_user_data,
    set_company_config,
    with_elastic,
)
from worker import topics
from worker.emailing.reminders_for_inbox_documents.jobs import (
    send_blackbox_inbox_documents_notification,
)

logger = logging.getLogger(__name__)
BLACKBOX_URL = '/api/private/blackbox/documents'
BLACKBOX_GET_DOCUMENT_HASH_URL = '/api/private/blackbox/document/{document_id}/hash'

BLACKBOX_SIGNATURE_URL = '/api/private/blackbox/documents/{document_id}/signatures'
PUBLIC_SIGNATURE_URL = '/api/v2/documents/{document_id}/signatures'

DATA_PATH = Path(__file__).parent / 'data'

ZAKUPKI_RECIPIENT_EDRPOU = ZAKUPKI_DEED_RECIPIENT_EDRPOU = '********'
ZAKUPKI_INVOICE_RECIPIENT_EDRPOU = '********'

EDRPOU_******** = '********'

FETCH_CONTACTS_DATA = [
    {
        'accountant_email': TEST_DOCUMENT_EMAIL_RECIPIENT,
        'edrpou': ZAKUPKI_RECIPIENT_EDRPOU,
        'users': [{'email': TEST_DOCUMENT_EMAIL_RECIPIENT}],
    }
]


def check_signature(signature, path, index=0):
    with open(path, 'rb') as handler:
        data = ujson.load(handler)
        assert signature.info == data[index]


def check_uploaded_file(s3_emulation, path):
    with open(path, 'rb') as handler:
        content = handler.read()
        found = [item for item in s3_emulation if item.body == content]

        assert len(found)
        assert found[0].body_size == len(content)


def mock_fetch_contacts(data):
    async def mock_func(*args, **kwargs):
        return data

    return mock_func


@pytest.mark.parametrize(
    'company_edrpou, original, expected_uploads, expected_meta, expected_signatures',
    [
        (
            EDRPOU_********,
            'xml-sample.xml',
            2,
            {
                'external_id': None,
                'type_': None,
                'number': None,
                'date': None,
                'owner_edrpou': '********',
            },
            1,
        ),
        (
            EDRPOU_********,
            DATA_PATH / 'declar-sample.xml',
            2,
            {
                'external_id': None,
                'type_': 'Податкова Накладна',
                'number': '9647',
                'date': to_local_datetime(datetime.datetime(2017, 7, 4)),
                'owner_edrpou': '********',
            },
            1,
        ),
    ],
)
async def test_upload_document_simple(
    s3_emulation,
    aiohttp_client,
    company_edrpou,
    original,
    expected_uploads,
    expected_meta,
    expected_signatures,
):
    """Simple upload check for Blackbox API"""

    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=company_edrpou,
        create_api_account=True,
    )
    company_id = user.company_id
    session_id = str(uuid.uuid4())

    p7s = DATA_PATH / f'{original}.p7s.xz'
    original = DATA_PATH / original

    form_data = prepare_form_data(p7s=p7s)
    form_data.add_field('session_id', session_id)
    form_data.add_field(
        name='original',
        value=b'',
        filename=str(original),
        content_type=ensure_mimetype(str(original)),
    )
    headers = prepare_auth_headers(user)
    response = await client.post(BLACKBOX_URL, data=form_data, headers=headers)
    assert response.status == 201, await response.text()

    async with app['db'].acquire() as conn:
        assert await count(conn, document_meta_table) == 1
        assert await count(conn, signature_table) == expected_signatures

        document_id = await conn.scalar(sa.select([document_table.c.id]))
        document = await get_document(document_id=document_id)

        document_actions = await select_document_actions_for(document_id=document_id)
        assert len(document_actions) == 2

        # Check company upload counter
        company = await select_company_by_id(conn, company_id)
        assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1
        # Check billing account
        accounts = await select_company_accounts(conn, company_id)
        assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
        # Check billing transaction
        transactions = await select_company_transactions(conn, company_id, income=False)
        assert len(transactions) == 1

    assert document.role_id == user.role_id
    assert document.extension == '.xml'
    assert document.date_document == expected_meta.get('date')
    assert document.number == expected_meta.get('number')
    assert document.edrpou_owner == expected_meta.get('owner_edrpou')
    assert document.type == expected_meta.get('type_')
    assert document.vendor_id == expected_meta.get('external_id')

    data = await response.json()
    assert data['id'] == document_id
    assert data['created_by'] == user.role_id
    assert data['owned_by'] == user.role_id

    assert len(s3_emulation) == expected_uploads

    check_uploaded_file(s3_emulation, original)
    check_uploaded_file(s3_emulation, p7s)


@pytest.mark.parametrize(
    'invalid_data, expected_status, expected_code',
    [
        ({}, 400, 'invalid_request'),
        ({'original': DATA_PATH / 'xml-sample.xml'}, 400, 'invalid_request'),
        (
            {
                'original': DATA_PATH / 'xml-sample.xml',
                'p7s': DATA_PATH / 'xml-sample.xml.p7s.zip',
                'signs': DATA_PATH / '********.signs',
            },
            400,
            'invalid_archive_file',
        ),
        (
            {
                'original': DATA_PATH / 'xml-sample.xml',
                'p7s': DATA_PATH / 'xml-sample.xml.p7s.xz',
                'signs': DATA_PATH / '********.signs',
                'session_id': 'fake-session-id',
            },
            400,
            'invalid_request',
        ),
    ],
)
async def test_upload_document_invalid_data(
    aiohttp_client, invalid_data, expected_status, expected_code
):
    app, client, user = await prepare_client(aiohttp_client, company_edrpou=EDRPOU_********)

    session_id = invalid_data.pop('session_id', None)
    form_data = prepare_form_data(**invalid_data)
    if session_id:
        form_data.add_field('session_id', session_id)

    response = await client.post(BLACKBOX_URL, data=form_data, headers=prepare_auth_headers(user))
    assert response.status == expected_status, await response.text()

    data = await response.json()
    assert data['code'] == expected_code


@pytest.mark.parametrize(
    'company_edrpou, original_path, edrpou_recipient, create_recipient, '
    'expected_title, has_vendor_id, '
    'expected_owner_signatures, expected_recipient_signatures, '
    'expected_status, exepcted_listing_count,'
    'create_active_role_for_recipient, sent_to_recipient',
    [
        pytest.param(
            EDRPOU_********,
            DATA_PATH / 'xml-sample.xml',
            None,
            False,
            'xml-sample',
            False,
            1,
            1,
            DocumentStatus.signed.value,
            1,
            False,
            False,
            id='simple_upload',
        ),
        pytest.param(
            ZAKUPKI_EDRPOU,
            DATA_PATH / 'deed-sample.xml',
            ZAKUPKI_DEED_RECIPIENT_EDRPOU,
            False,
            'ЗК-00052660_20170831',
            True,
            1,
            1,
            DocumentStatus.signed.value,
            1,
            False,
            False,
            id='zakupki_deed',
        ),
        pytest.param(
            ZAKUPKI_EDRPOU,
            DATA_PATH / 'invoice-sample.xml',
            ZAKUPKI_INVOICE_RECIPIENT_EDRPOU,
            False,
            'ZK-00123567-11_20170831',
            True,
            1,
            0,
            DocumentStatus.approved.value,
            1,
            False,
            False,
            id='zakupki_invoice',
        ),
        pytest.param(
            ZAKUPKI_EDRPOU,
            DATA_PATH / 'deed-sample.xml',
            ZAKUPKI_DEED_RECIPIENT_EDRPOU,
            True,
            'ЗК-00052660_20170831',
            True,
            1,
            1,
            DocumentStatus.signed_and_sent.value,
            2,
            True,
            True,
            id='zakupki_deed_with_recipient',
        ),
        pytest.param(
            ZAKUPKI_EDRPOU,
            DATA_PATH / 'invoice-sample.xml',
            ZAKUPKI_INVOICE_RECIPIENT_EDRPOU,
            True,
            'ZK-00123567-11_20170831',
            True,
            1,
            0,
            DocumentStatus.finished.value,
            2,
            True,
            True,
            id='zakupki_invoice_with_recipient',
        ),
        pytest.param(
            ZAKUPKI_EDRPOU,
            DATA_PATH / 'deed-sample.xml',
            ZAKUPKI_DEED_RECIPIENT_EDRPOU,
            True,
            'ЗК-00052660_20170831',
            True,
            1,
            1,
            DocumentStatus.signed.value,
            1,
            False,
            False,
            id='zakupki_deed_with_recipient_no_send',
        ),
        pytest.param(
            ZAKUPKI_EDRPOU,
            DATA_PATH / 'invoice-sample.xml',
            ZAKUPKI_INVOICE_RECIPIENT_EDRPOU,
            True,
            'ZK-00123567-11_20170831',
            True,
            1,
            0,
            DocumentStatus.approved.value,
            1,
            False,
            False,
            id='zakupki_invoice_with_recipient_no_send',
        ),
    ],
)
async def test_upload_document_extended(
    company_edrpou,
    monkeypatch,
    aiohttp_client,
    original_path: Path,
    edrpou_recipient,
    create_recipient,
    expected_title,
    has_vendor_id,
    expected_owner_signatures,
    expected_recipient_signatures,
    expected_status,
    exepcted_listing_count,
    create_active_role_for_recipient,
    sent_to_recipient,
    s3_emulation,
):
    """Check upload for Blackbox API with extended list of checkpoints"""

    monkeypatch.setattr(uploads_utils, 'fetch_contacts', mock_fetch_contacts([]))

    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=company_edrpou,
        create_api_account=True,
    )
    monkeypatch.setattr(eusign_utils, 'get_edrpou', lambda _: company_edrpou)
    company_id = user.company_id

    if create_recipient:
        await prepare_company_data(app, edrpou=edrpou_recipient, is_legal=True)
    if create_active_role_for_recipient:
        await prepare_user_data(app, company_edrpou=edrpou_recipient, email='<EMAIL>')

    p7s_path = original_path.with_suffix('.xml.p7s.xz')
    form_data = prepare_form_data(
        original=original_path,
        p7s=p7s_path,
        signs=DATA_PATH / '********.signs',
    )
    form_data.add_field('session_id', str(uuid.uuid4()))

    headers = prepare_auth_headers(user)

    response = await client.post(BLACKBOX_URL, data=form_data, headers=headers)
    assert response.status == 201
    resp_json = await response.json()
    doc_id = resp_json['id']

    query = '{ allDocuments { count, documents { id } } }'
    async with with_elastic(app, [doc_id]):
        response = await fetch_graphql(client, query, headers)
    assert response['allDocuments']['count'] == 1
    document_id = response['allDocuments']['documents'][0]['id']

    document_key = f'{document_id}'
    assert document_key in s3_emulation
    original: UploadFile = s3_emulation[document_key]
    assert original.body == original_path.read_bytes()

    async with app['db'].acquire() as conn:
        assert await count(conn, document_table) == 1
        assert await count(conn, signature_table) == 1

        meta = await get_document_meta(document_id=document_id)
        assert meta.content_hash == await eusign_utils.generate_hash_base64(original.body)
        assert meta.content_length == original.body_size

        document = await select_document_by_id(conn, document_id)
        assert document.title == expected_title
        assert document.extension == '.xml'
        assert document.uploaded_by == user.role_id
        assert document.edrpou_owner == company_edrpou
        assert document.edrpou_recipient == edrpou_recipient
        assert document.status_id == expected_status
        assert document.role_id == user.role_id

        assert document.first_sign_by == FirstSignBy.owner
        assert document.expected_owner_signatures == expected_owner_signatures
        assert document.expected_recipient_signatures == expected_recipient_signatures

        assert document.source == DocumentSource.vchasno_container
        assert document.vendor == Vendor.onec.value
        assert (document.vendor_id is not None) is has_vendor_id
        assert document.signature_format == SignatureFormat.internal_separated

        assert await count(conn, listing_table) == exepcted_listing_count

        signatures = await select_signatures(conn, [document_id])
        assert len(signatures) == 1

        signature = signatures[0]
        assert signature.key_acsk is not None
        assert signature.key_timemark is not None
        assert signature.is_internal is True
        assert signature.internal_file_name is not None
        assert signature.internal_file_name.endswith('.xz')
        assert signature.key_exists is True

        assert signature.role_id == user.role_id
        assert signature.user_email == user.email

        sign_key = get_internal_signature_s3_key(document_id, signature.id)
        assert sign_key in s3_emulation
        assert s3_emulation[sign_key].body == p7s_path.read_bytes()

        recipient = await select_bilateral_document_recipient(
            conn=conn,
            document_id=document_id,
            document_owner_edrpou=document.edrpou_owner,
        )

        if sent_to_recipient:
            assert recipient.date_sent is not None

        if edrpou_recipient is not None:
            assert recipient is not None
            assert recipient.edrpou == edrpou_recipient
        else:
            assert recipient is None

        # Check billing account
        accounts = await select_company_accounts(conn, company_id)
        assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
        # Check company upload counter
        company = await select_company_by_id(conn, company_id)
        assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1
        # Check billing transaction
        transactions = await select_company_transactions(conn, company_id, income=False)
        assert len(transactions) == 1
        assert transactions[0].initiator_id == document.id


async def test_upload_and_prefill_contact_api(
    monkeypatch, aiohttp_client, mailbox, email_templates_renderer
):
    monkeypatch.setattr(uploads_utils, 'fetch_contacts', mock_fetch_contacts(FETCH_CONTACTS_DATA))

    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=ZAKUPKI_EDRPOU,
        create_api_account=True,
    )
    monkeypatch.setattr(eusign_utils, 'get_edrpou', lambda _: ZAKUPKI_EDRPOU)

    recipient = await prepare_user_data(
        app=app,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        company_edrpou=ZAKUPKI_RECIPIENT_EDRPOU,
    )

    response = await client.post(
        BLACKBOX_URL,
        data=prepare_form_data(
            original=DATA_PATH / 'deed-sample.xml',
            p7s=DATA_PATH / 'deed-sample.xml.p7s.xz',
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 201, await response.text()

    async with app['db'].acquire() as conn:
        document = await select_one(conn, sa.select([document_table]))
        assert document.edrpou_owner == ZAKUPKI_EDRPOU
        assert document.edrpou_recipient == ZAKUPKI_RECIPIENT_EDRPOU
        assert document.email_recipient == TEST_DOCUMENT_EMAIL_RECIPIENT
        assert document.status_id == DocumentStatus.signed_and_sent.value
        assert document.role_id == user.role_id

        assert await count(conn, contact_table) == 1
        assert await count(conn, contact_person_table) == 1
        assert (
            await count(
                conn,
                contact_person_table,
                contact_person_table.c.main_recipient.is_(True),
            )
            == 1
        )

        # Ensure delayed task was created for notification
        tasks = await select_delayed_tasks(conn=conn)
        assert len(tasks) == 1
        assert tasks[0].topic == topics.SEND_BLACKBOX_INBOX_DOCUMENTS_NOTIFICATION
        assert tasks[0].data == {'role_id': recipient.role_id}

        redis_value = await services.redis.get(
            BLACKBOX_DOCUMENTS_NOTIFICATION_REDIS_KEY.format(role_id=recipient.role_id)
        )
        document_ids = json.loads(redis_value)
        assert document_ids == [document.id]

        # No emails
        assert len(mailbox) == 0

        await send_blackbox_inbox_documents_notification(
            app, {'role_id': recipient.role_id}, logger
        )

        assert len(mailbox) == 1
        assert mailbox[0]['To'] == recipient.email

        assert len(email_templates_renderer) == 1

        document_url = email_templates_renderer[0]['context']['documents'][0].pop('url')
        assert '/app/documents/' in document_url

        assert email_templates_renderer[0] == {
            'context': {
                'button_link': 'http://localhost:8000/app/documents',
                'documents': [
                    {
                        'counterparty_company_edrpou': '40283641',
                        'counterparty_company_name': None,
                        'title': 'ЗК-00052660_20170831',
                    },
                ],
                'header_title': 'Ви отримали документи',
                'inbox_documents_count': 1,
                'recipient_company_edrpou': '********',
                'recipient_company_name': None,
                'recipient_first_name': None,
            },
            'template_name': 'inbox_documents',
        }


@pytest.mark.parametrize(
    'can_receive_notifications, raise_email_fail, expected_email_count',
    (
        (True, True, 0),
        (True, False, 1),
        (False, True, 0),
        (False, False, 0),
    ),
)
async def test_upload_and_prefill_contact_db(
    monkeypatch,
    aiohttp_client,
    mailbox,
    can_receive_notifications,
    raise_email_fail,
    expected_email_count,
    email_templates_renderer,
):
    mailbox.fail = raise_email_fail

    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=ZAKUPKI_EDRPOU,
        create_api_account=True,
    )
    monkeypatch.setattr(eusign_utils, 'get_edrpou', lambda _: ZAKUPKI_EDRPOU)

    recipient = await prepare_user_data(
        app,
        company_edrpou=ZAKUPKI_RECIPIENT_EDRPOU,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        user_role=UserRole.user.value,
        can_receive_notifications=can_receive_notifications,
    )

    async with app['db'].acquire() as conn:
        await update_main_recipient(
            conn=conn,
            company_id=user.company_id,
            details=to_contact_details(recipient),
        )

    response = await client.post(
        BLACKBOX_URL,
        data=prepare_form_data(
            original=DATA_PATH / 'deed-sample.xml',
            p7s=DATA_PATH / 'deed-sample.xml.p7s.xz',
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 201, await response.json()

    async with app['db'].acquire() as conn:
        document = await select_one(conn, sa.select([document_table]))
        assert document.edrpou_owner == ZAKUPKI_EDRPOU
        assert document.edrpou_recipient == ZAKUPKI_RECIPIENT_EDRPOU
        assert document.email_recipient == TEST_DOCUMENT_EMAIL_RECIPIENT
        assert document.status_id == DocumentStatus.signed_and_sent.value
        assert document.role_id == user.role_id

    assert len(mailbox) == 0

    try:
        await send_blackbox_inbox_documents_notification(
            app, {'role_id': recipient.role_id}, logger
        )
    except smtplib.SMTPException as e:
        if raise_email_fail:
            assert len(mailbox) == 0
            return
        raise e

    assert len(mailbox) == expected_email_count

    if can_receive_notifications:
        assert mailbox[0]['To'] == recipient.email

        assert len(email_templates_renderer) == 1

        document_url = email_templates_renderer[0]['context']['documents'][0].pop('url')
        assert '/app/documents/' in document_url

        assert email_templates_renderer[0] == {
            'context': {
                'button_link': 'http://localhost:8000/app/documents',
                'documents': [
                    {
                        'counterparty_company_edrpou': '40283641',
                        'counterparty_company_name': None,
                        'title': 'ЗК-00052660_20170831',
                    },
                ],
                'header_title': 'Ви отримали документи',
                'inbox_documents_count': 1,
                'recipient_company_edrpou': '********',
                'recipient_company_name': None,
                'recipient_first_name': None,
            },
            'template_name': 'inbox_documents',
        }


@pytest.mark.parametrize('enable_email_mock, expected_count', [(False, 0), (True, 1)])
async def test_upload_and_prefill_contact_xml(
    monkeypatch, aiohttp_client, mailbox, enable_email_mock, expected_count
):
    mailbox.fail = not enable_email_mock
    monkeypatch.setattr(eusign_utils, 'get_edrpou', lambda _: ZAKUPKI_EDRPOU)

    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=ZAKUPKI_EDRPOU,
        create_api_account=True,
    )

    response = await client.post(
        BLACKBOX_URL,
        data=prepare_form_data(
            original=DATA_PATH / 'deed-sample-with-email.xml',
            p7s=DATA_PATH / 'deed-sample-with-email.xml.p7s.xz',
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 201

    async with app['db'].acquire() as conn:
        document = await select_one(conn, sa.select([document_table]))
        assert document.edrpou_owner == ZAKUPKI_EDRPOU
        assert document.edrpou_recipient == ZAKUPKI_RECIPIENT_EDRPOU
        assert document.email_recipient == TEST_DOCUMENT_EMAIL_RECIPIENT
        assert document.status_id == DocumentStatus.signed_and_sent.value
        assert document.role_id == user.role_id

        assert await count(conn, listing_table) == 2


@pytest.mark.parametrize(
    'original',
    [
        DATA_PATH / 'deed-sample.xml',
        DATA_PATH / 'deed-sample-with-email.xml',
        DATA_PATH / 'invoice-sample.xml',
    ],
)
async def test_upload_duplicates(monkeypatch, aiohttp_client, original):
    monkeypatch.setattr(uploads_utils, 'fetch_contacts', mock_fetch_contacts([]))
    monkeypatch.setattr(eusign_utils, 'get_edrpou', lambda _: ZAKUPKI_EDRPOU)
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=ZAKUPKI_EDRPOU,
        create_api_account=True,
    )
    headers = prepare_auth_headers(user)

    response = await client.post(
        BLACKBOX_URL,
        data=prepare_form_data(original=original, p7s=original.with_suffix('.xml.p7s.xz')),
        headers=headers,
    )
    assert response.status == 201, await response.json()

    response = await client.post(
        BLACKBOX_URL,
        data=prepare_form_data(original=original, p7s=original.with_suffix('.xml.p7s.xz')),
        headers=headers,
    )
    assert response.status == 400, await response.json()

    data = await response.json()
    assert data['code'] == 'duplicated_documents_db'

    async with app['db'].acquire() as conn:
        assert await count(conn, document_table) == 1
        assert await count(conn, signature_table) == 1


async def test_upload_duplicates_no_external_id(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=EDRPOU_********,
        create_api_account=True,
    )
    headers = prepare_auth_headers(user)
    number = 3

    for _ in range(number):
        response = await client.post(
            BLACKBOX_URL,
            data=prepare_form_data(
                original=DATA_PATH / 'xml-sample.xml',
                p7s=DATA_PATH / 'xml-sample.xml.p7s.xz',
                signs=DATA_PATH / '********.signs',
            ),
            headers=headers,
        )
        assert response.status == 201, await response.json()

    async with app['db'].acquire() as conn:
        assert await count(conn, document_table) == 3
        assert await count(conn, signature_table) == 3


@pytest.mark.slow
async def test_upload_no_external_id_large_file(monkeypatch, aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=NOVA_POSHTA_EDRPOU,
        create_api_account=True,
    )
    monkeypatch.setattr(eusign_utils, 'get_edrpou', lambda _: NOVA_POSHTA_EDRPOU)
    monkeypatch.setattr(
        'api.private.blackbox.utils.generate_hash_base64_sync', mock.Mock(return_value='hashstr')
    )

    response = await client.post(
        BLACKBOX_URL,
        data=prepare_form_data(
            original=DATA_PATH / 'deed-sample-without-id-1.xml',
            p7s=DATA_PATH / 'deed-sample-without-id-1.xml.p7s.xz',
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 201, await response.json()

    async with app['db'].acquire() as conn:
        assert await count(conn, document_table, document_table.c.vendor_id.is_(None)) == 1


async def test_upload_document_view_session(monkeypatch, aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=NOVA_POSHTA_EDRPOU,
        create_api_account=True,
    )
    await set_company_config(app, company_id=user.company_id, api_vendor='novaposhta')
    monkeypatch.setattr(eusign_utils, 'get_edrpou', lambda _: NOVA_POSHTA_EDRPOU)

    response = await client.post(
        BLACKBOX_URL,
        data=prepare_form_data(
            original=DATA_PATH / 'xml-sample.xml',
            p7s=DATA_PATH / 'xml-sample.xml.p7s.xz',
            signs=DATA_PATH / '********.signs',
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 201, await response.json()

    async with app['db'].acquire() as conn:
        assert await count(conn, sign_session_table) == 1
        assert (
            await count(
                conn,
                sign_session_table,
                sign_session_table.c.type == SignSessionType.view_session,
            )
            == 1
        )


async def test_upload_document_xml_syntax_error(monkeypatch, aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    app._client_max_size = 100 * 1024 * 1024
    monkeypatch.setattr(eusign_utils, 'get_edrpou', lambda _: TEST_COMPANY_EDRPOU)

    response = await client.post(
        BLACKBOX_URL,
        data=prepare_form_data(
            original=DATA_PATH / 'invalid-xml-sample.xml',
            p7s=DATA_PATH / 'invalid-xml-sample.xml.p7s.xz',
            signs=DATA_PATH / '********.signs',
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 400

    data = await response.json()
    assert data['code'] == 'invalid_xml'
    assert data['reason'] == (
        'Неможливо розібрати завантаженний XML через помилку: '
        'Premature end of data in tag Дата line 5, line 6, column 1 (<string>, line 6)'
    )


async def test_get_document_hash(aiohttp_client):
    test_content = b'test_content'

    app, client, user = await prepare_client(aiohttp_client, company_edrpou='********')

    expected_hash = await eusign_utils.generate_hash_base64(test_content)

    document = await prepare_document_data(app, user, content=test_content)
    response = await client.get(
        BLACKBOX_GET_DOCUMENT_HASH_URL.format(document_id=document.id),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 200
    json = await response.json()
    assert json['document_hash'] == expected_hash

    meta = await get_document_meta(document_id=document.id)
    assert meta.content_hash == expected_hash
    assert meta.content_length == len(test_content)


@pytest.mark.parametrize('with_stamp', [True, False])
@pytest.mark.parametrize(
    'url_tmpl, key, is_internal, status, is_from_edi, document_signature_format',
    [
        (
            BLACKBOX_SIGNATURE_URL,
            'key',
            True,
            DocumentStatus.finished.value,
            False,
            SignatureFormat.external_separated,
        ),
        (
            BLACKBOX_SIGNATURE_URL,
            'key',
            False,
            DocumentStatus.signed_and_sent.value,
            False,
            SignatureFormat.external_separated,
        ),
        (
            PUBLIC_SIGNATURE_URL,
            'signature',
            True,
            DocumentStatus.finished.value,
            False,
            SignatureFormat.external_separated,
        ),
        (
            PUBLIC_SIGNATURE_URL,
            'signature',
            False,
            DocumentStatus.signed_and_sent.value,
            False,
            SignatureFormat.external_separated,
        ),
        # Specific cases for documents from EDI
        (
            BLACKBOX_SIGNATURE_URL,
            'key',
            False,
            DocumentStatus.signed_and_sent.value,
            True,
            SignatureFormat.external_separated,
        ),
        (
            PUBLIC_SIGNATURE_URL,
            'signature',
            False,
            DocumentStatus.signed_and_sent.value,
            True,
            SignatureFormat.external_separated,
        ),
        (
            BLACKBOX_SIGNATURE_URL,
            'key',
            False,
            DocumentStatus.signed_and_sent.value,
            True,
            SignatureFormat.internal_wrapped,
        ),
        (
            PUBLIC_SIGNATURE_URL,
            'signature',
            False,
            DocumentStatus.signed_and_sent.value,
            True,
            SignatureFormat.internal_wrapped,
        ),
    ],
)
async def test_add_signature_on_hash(
    aiohttp_client,
    url_tmpl,
    key,
    is_internal,
    status,
    is_from_edi,
    document_signature_format,
    with_stamp,
    s3_emulation,
    test_flags,
):
    company_edrpou = '********'
    app, client, owner = await prepare_client(
        aiohttp_client,
        company_edrpou=company_edrpou,
        create_billing_account=True,
        feature_flags={'pass_sign_info_to_backend': False},
    )

    document = await prepare_document_data(
        app,
        owner,
        document_recipients=[{'edrpou': TEST_RECIPIENT_EDRPOU, 'emails': [TEST_RECIPIENT_EMAIL]}],
        status_id=DocumentStatus.ready_to_be_signed.value,
        title='TITLE',
        extension='PDF',
        is_internal=is_internal,
        source=DocumentSource.edi if is_from_edi else DocumentSource.vchasno,
        signature_format=document_signature_format,
    )
    s3_emulation.upload_document_content(
        document_id=document.id,
        content=b'12345',
    )
    signature_file: bytes = (DATA_PATH / '5555555.hash.p7s').read_bytes()
    data = {key: base64.b64encode(signature_file).decode('ascii')}

    stamp_file: bytes | None = None
    if with_stamp:
        stamp_file = (DATA_PATH / '5555555.stamp.hash.p7s').read_bytes()
        data['stamp'] = base64.b64encode(stamp_file).decode('ascii')

    response = await client.post(
        url_tmpl.format(document_id=document.id),
        json=data,
        headers=prepare_auth_headers(owner),
    )
    response.raise_for_status()
    async with app['db'].acquire() as conn:
        signatures = await select_signatures(conn, [document.id])
        document = await select_document_by_id(conn, document.id)

    assert document.status_id == status
    assert len(signatures) == 1
    signature = signatures[0]
    assert signature.key_owner_edrpou == '********'
    assert signature.role_id == owner.role_id
    assert signature.user_email == owner.email

    if with_stamp:
        assert signature.stamp_owner_edrpou == '********'
    else:
        assert not signature.stamp_owner_edrpou

    if is_from_edi:
        if document_signature_format != SignatureFormat.external_separated:
            assert signature.format == document.signature_format
            assert signature.is_internal is True

            signature_s3_key = f'{document.id}_{signature.id}'
            assert signature_s3_key in s3_emulation
            file = s3_emulation[signature_s3_key]
            # the content of the signature is generated on the fly
            # by converting external signature to internal
            assert file.body is not None
            assert file.encrypt is True
        else:
            assert signature.format == SignatureFormat.external_separated
            assert signature.is_internal is False

            key_s3_key = get_external_key_signature_s3_key(
                document_id=document.id,
                signature_id=signature.id,
            )
            assert key_s3_key in s3_emulation
            file = s3_emulation[key_s3_key]
            assert file.body == signature_file
            assert file.encrypt is True

            if with_stamp:
                stamp_s3_key = get_external_stamp_signature_s3_key(
                    document_id=document.id,
                    signature_id=signature.id,
                )
                assert stamp_s3_key in s3_emulation
                file = s3_emulation[stamp_s3_key]
                assert file.body == stamp_file
                assert file.encrypt is True
