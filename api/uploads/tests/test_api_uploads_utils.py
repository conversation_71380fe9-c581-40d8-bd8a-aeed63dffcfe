import datetime
import io

import pytest

from api.uploads.types import MetaData
from api.uploads.utils import (
    blake2b_hash,
    handle_encoding,
    handle_encoding_bytes,
    is_one_sign_type,
    parse_date,
    parse_declaration_date,
    parse_title,
    unarchive_file_name,
)
from app.lib.datetime_utils import to_local_datetime

TEXT_SAMPLES = {
    'spanish': (
        'El hardware inalámbrico no autorizado se puede introducir fácilmente.'
        'Los puntos de acceso inalámbricos son relativamente poco costosos y'
        'se implementan fácilmente. Un equipo de asesores bienintencionado que'
        'trabaja en una sala de conferencias podría instalar un punto de '
        'acceso inalámbrico para compartir un solo puerto cableado en la sala'
    ),
    'french': (
        'Maître Corbeau, sur un arbre perché'
        'Ma<PERSON><PERSON>, par l’odeur alléché'
        'Lui tint à peu près ce langage :'
        '« Hé ! Que vous êtes joli !'
        'Se rapporte à votre plumage,'
        'Vous êtes le Phénix des hôtes de ces bois. »'
        'Il ouvre un large bec, laisse tomber sa proie.'
        'Le Renard s’en saisit, et dit : « Mon bon Monsieur,'
        'Vit aux dépens de celui qui l’écoute :'
        'Cette leçon vaut bien un fromage, sans doute. »'
        'Jura, mais un peu tard, qu’on ne l’y prendrait plus.'
    ),
    'polish': (
        'Obraz wisi na ścianie. Obok obrazu wiszą tabliczki z informacją.'
        'Jedna tabliczka jest po polsku a druga jest po angielsku.'
        'Ściana jest biała a obraz jest kolorowy. Na obrazie są:'
        'niebieskie niebo, szare chmury i zielona łąka pokryta'
        'kolorowymi plamami. Kolorowe plamy to kwiaty. Kwiaty są wszędzie i'
        'mają różne kolory: czerwony, żółty, pomarańczowy, fioletowy i różowy.'
        'Pośrodku łąki rośnie drzewo.'
    ),
    'ukrainian': (
        'Акт_виконаних_робіт.pdf. Всі люди народжуються вільними і рівними у '
        'своїй гідності та правах. Вони наділені розумом і совістю і повинні '
        'діяти у відношенні один до одного в дусі братерства.'
    ),
    'russian': '50 миль к Владивостоку',
    'chinese': '和毛泽东-到处群魔乱舞.pdf',
    'armenian': 'Կյաեբս չտայի կասկածի',
    'japanese': '経意責家方家閉.pdf',
    'croatian': 'Dodigović. Kako se Vi',
    'turkish': 'Yukarda mavi gök, asağıda',
    'english': 'pdf-sample',
}


@pytest.mark.parametrize(
    'content, expected',
    [
        (
            io.BytesIO(b''),
            '786a02f742015903c6c6fd852552d272912f4740e15847618a86e217f71f5419d25e1'
            '031afee585313896444934eb04b903a685b1448b755d56f701afe9be2ce',
        ),
        (
            io.BytesIO(b'Hello, world!'),
            'a2764d133a16816b5847a737a786f2ece4c148095c5faa73e24b4cc5d666c3e45ec27'
            '1504e14dc6127ddfce4e144fb23b91a6f7b04b53d695502290722953b0f',
        ),
        (
            io.BytesIO(b'Long long hello world' * 100),
            'bf46ed064df53fc6994cdd75ac2d237be804e5fcfdfe22138d9ad17a1f8cb2afeb37e'
            '4603177b02e8678b0d649c82bcac66692e740f63b0c471628790df447c5',
        ),
    ],
)
def test_blake2b_hash(content, expected):
    assert blake2b_hash(content) == expected


@pytest.mark.parametrize(
    'test_input, expected',
    [
        *[(sample, sample) for sample in TEXT_SAMPLES.values()],
        ('ßeta', 'ßeta'),
        ('math×doc.pdf', 'math×doc.pdf'),
        # 1C -> Win
        (
            '40283641_14725837_20160920_СчетНаОплатуПокупателю_ZK-0031321-2',
            '40283641_14725837_20160920_СчетНаОплатуПокупателю_ZK-0031321-2',
        ),
        # 1C -> Win -> Linux
        (
            '40283641_32756392_20161212_¥ «¨§ æ¨ï®¢ à®¢á«ã£_-00024052',
            '40283641_32756392_20161212_РеализацияТоваровУслуг_ЗК-00024052',
        ),
        # Mac
        ('ÐÑÐ´Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸Ì_Deal_white.pdf', 'Підписаний_Deal_white.pdf'),
        # Win -> Linux
        (
            '40283641_16300840_20160921_RTU_-00010163.pdf',
            '40283641_16300840_20160921_RTU_ЗК-00010163.pdf',
        ),
        # Linux
        ('Bo_меню.pdf', 'Bo_меню.pdf'),
        # Archive name Mac
        ('MAC_Archive.zip', 'MAC_Archive.zip'),
        # Archive name Linux
        ('Архив.zip', 'Архив.zip'),
        # 1C files in xml format
        ('RTU_çè-00017446   _20170418.xml', 'RTU_ЗК-00017446   _20170418.xml'),
    ],
)
def test_handle_encoding(test_input, expected):
    assert handle_encoding(test_input) == expected


@pytest.mark.parametrize(
    'test_input, expected',
    [
        (b'Hello, world!', 'Hello, world!'),
        (
            b'33945923_23243188_20240408_\x80\xaa\xe2 \xae\xaa\xa0\xa7\xa0\xad\xa8\xef'
            b' \xe3\xe1\xab\xe3\xa3_\x80\<EMAIL>'
            b'_[ 621  6IZG   ]_3275596.PDF',
            '33945923_23243188_20240408_Акт оказания услуг_АД<EMAIL>'
            '_[ 621  6IZG   ]_3275596.PDF',
        ),
    ],
)
def test_handle_encoding_bytes(test_input, expected):
    assert handle_encoding_bytes(test_input) == expected


@pytest.mark.parametrize(
    'document_type, expected',
    [
        ('Счет', True),
        ('СчетНаОплатуПокупателю', True),
        ('Рахунок', True),
        ('Рахунок-Фактура', True),
        ('Invoice', True),
        ('АктНаданняПослуг', False),
        ('РеализацияТоваровУслуг', False),
    ],
)
def test_is_one_sign_type(document_type, expected):
    assert is_one_sign_type(document_type) == expected


@pytest.mark.parametrize(
    'test_input, expected',
    [
        (None, None),
        ('not a date', None),
        ('20170801', to_local_datetime(datetime.datetime(2017, 8, 1))),
        ('2017-08-01', to_local_datetime(datetime.datetime(2017, 8, 1))),
        ('01082017', to_local_datetime(datetime.datetime(2017, 8, 1))),
    ],
)
def test_parse_date(test_input, expected):
    assert parse_date(test_input) == expected


@pytest.mark.parametrize(
    'test_input, expected',
    [
        (None, None),
        ('not a date', None),
        ('01082017', to_local_datetime(datetime.datetime(2017, 8, 1))),
        ('10102010', to_local_datetime(datetime.datetime(2010, 10, 10))),
    ],
)
def test_parse_declaration_date(test_input, expected):
    assert parse_declaration_date(test_input) == expected


@pytest.mark.parametrize(
    'test_input, expected',
    [
        (
            '12345678_1234567890_20170228_Акт_00000001258',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou='1234567890',
                type_='Акт',
                number='00000001258',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_1234567890_20170228_Акт******************************',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou='1234567890',
                recipient_emails=['<EMAIL>'],
                type_='Акт',
                number='00000001258',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_1234567890_20170228_Акт*********************************',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou='1234567890',
                recipient_emails=['<EMAIL>'],
                type_='Акт',
                number='00000001258',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_1234_20170228_Акт*****************************',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou=None,
                type_='Акт',
                number='00000001258',
                recipient_emails=['<EMAIL>'],
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_20170228_Акт_00000001258',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou=None,
                type_='Акт',
                number='00000001258',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678  _1234567890  _20170228_Акт_00000001258',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou='1234567890',
                type_='Акт',
                number='00000001258',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_1234_20170228_Акт_00000001258',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou=None,
                type_='Акт',
                number='00000001258',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_1234567_20170228_Акт_00000001258',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou=None,
                type_='Акт',
                number='00000001258',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_1234567890_20170228_Акт**********************************',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou='1234567890',
                type_='Акт',
                number='00000001258',
                recipient_emails=['<EMAIL>'],
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_20170228_Акт***********************,admin.com,<EMAIL>',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou=None,
                type_='Акт',
                number='00000001258',
                recipient_emails=['<EMAIL>', '<EMAIL>'],
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_20170228_Акт***********************,admin.com,<EMAIL>',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou=None,
                type_='Акт',
                number='00000001258',
                recipient_emails=['<EMAIL>', '<EMAIL>'],
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_1234567890_20170228_Акт_00000001258_admin,<EMAIL>',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou='1234567890',
                type_='Акт',
                number='00000001258',
                recipient_emails=['<EMAIL>'],
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_20170228_Акт_00000001258_3efb7937-5c53-4080-9c64-87066d7d9184',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou=None,
                type_='Акт',
                number='00000001258',
                external_id='3efb7937-5c53-4080-9c64-87066d7d9184',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_1234567890_20170228_Акт_00000001258'
            '<EMAIL>,<EMAIL>, some sort of junk,'
            '<EMAIL>,other-type-of-junk'
            '_3efb7937-5c53-4080-9c64-87066d7d9184',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou='1234567890',
                type_='Акт',
                number='00000001258',
                recipient_emails=[
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                ],
                external_id='3efb7937-5c53-4080-9c64-87066d7d9184',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '12345678_ КС123456 _20170228_Акт_00000001258'
            '<EMAIL>,<EMAIL>, some sort of junk,'
            '<EMAIL>,other-type-of-junk'
            '_3efb7937-5c53-4080-9c64-87066d7d9184',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou='КС123456',
                type_='Акт',
                number='00000001258',
                recipient_emails=[
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                ],
                external_id='3efb7937-5c53-4080-9c64-87066d7d9184',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '   КА098765_КГ123456_20170228_Акт_00000001258'
            '<EMAIL>,<EMAIL>, some sort of junk,'
            '<EMAIL>,other-type-of-junk'
            '_3efb7937-5c53-4080-9c64-87066d7d9184',
            MetaData(
                owner_edrpou='КА098765',
                recipient_edrpou='КГ123456',
                type_='Акт',
                number='00000001258',
                recipient_emails=[
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                ],
                external_id='3efb7937-5c53-4080-9c64-87066d7d9184',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
            ),
        ),
        (
            '0000146088_20210414_12_42_30',
            MetaData(
                owner_edrpou='0000146088',
                external_id='30',
                type_='12',
                number='42',
                date=to_local_datetime(datetime.datetime(2021, 4, 14)),
            ),
        ),
        (
            'R0000146088_20210414_12_42_30',
            MetaData(),
        ),
        (
            '12345678_1234567890_20170228_Акт*********************************_42_322',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou='1234567890',
                recipient_emails=['<EMAIL>'],
                type_='Акт',
                number='00000001258',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
                external_id='42',
                amount=322,
            ),
        ),
        (
            '12345678_1234567890_20170228_Акт*********************************_42_-322',
            MetaData(
                owner_edrpou='12345678',
                recipient_edrpou='1234567890',
                recipient_emails=['<EMAIL>'],
                type_='Акт',
                number='00000001258',
                date=to_local_datetime(datetime.datetime(2017, 2, 28)),
                external_id='42',
                amount=-322,
            ),
        ),
        (
            '55555555_6879876431_20110403_ПоступлениеТоваровУслуг_НО000000002_'
            '_c7b62771-4595-11dc-8e55-0019d171d567_15200',
            MetaData(
                owner_edrpou='55555555',
                recipient_edrpou='6879876431',
                recipient_emails=None,
                type_='ПоступлениеТоваровУслуг',
                number='НО000000002',
                date=to_local_datetime(datetime.datetime(2011, 4, 3)),
                external_id='c7b62771-4595-11dc-8e55-0019d171d567',
                amount=15200,
            ),
        ),
        # external_id is null, amount not null
        (
            '55555555_1234567890_20221107_test_1_senya_kr@example.com__1200',
            MetaData(
                owner_edrpou='55555555',
                recipient_edrpou='1234567890',
                recipient_emails=['<EMAIL>'],
                type_='test',
                number='1',
                date=to_local_datetime(datetime.datetime(2022, 11, 7)),
                external_id=None,
                amount=1200,
            ),
        ),
        # recipient_email and external_id is null, amount not null
        (
            '55555555_1234567890_20221107_test_1___1200',
            MetaData(
                owner_edrpou='55555555',
                recipient_edrpou='1234567890',
                recipient_emails=None,
                type_='test',
                number='1',
                date=to_local_datetime(datetime.datetime(2022, 11, 7)),
                external_id=None,
                amount=1200,
            ),
        ),
        # recipient_email is null, external_is and amount not null
        (
            '55555555_1234567890_20221107_test_1__external-id_1200',
            MetaData(
                owner_edrpou='55555555',
                recipient_edrpou='1234567890',
                recipient_emails=None,
                type_='test',
                number='1',
                date=to_local_datetime(datetime.datetime(2022, 11, 7)),
                external_id='external-id',
                amount=1200,
            ),
        ),
    ],
)
def test_parse_title(test_input, expected):
    assert parse_title(test_input) == expected


@pytest.mark.parametrize(
    'file_name, expected',
    [
        ('file.xml.bz2', 'file.xml'),
        ('file.xml.xz', 'file.xml'),
        ('file.xml.zip', 'file.xml.zip'),
        ('file.xml', 'file.xml'),
    ],
)
def test_unarchive_file_name(file_name, expected):
    assert unarchive_file_name(file_name) == expected
