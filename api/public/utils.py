import logging
from collections import defaultdict
from collections.abc import Iterable

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import insert

from api.errors import InvalidRequest
from api.public.db import (
    select_available_document_additional_fields,
    select_incoming_documents_by_ids,
)
from api.public.types import (
    CreateCoworkerOptions,
    CreateTokenOptions,
    ListDocumentsOptions,
    ListIncomingDocumentsOptions,
    UploaderInfo,
)
from app.auth.constants import HRS_NOT_BILLABLE_PERMISSIONS, USER_PERMISSIONS
from app.auth.db import (
    insert_token,
    select_role_by_id,
    select_token_by,
    update_role,
    upsert_super_admin_permissions,
)
from app.auth.enums import RoleActivationSource, RoleStatus
from app.auth.helpers import autogenerate_password, generate_hash_sha512
from app.auth.tables import (
    company_table,
    role_table,
    token_table,
    user_table,
)
from app.auth.types import (
    AuthUser,
    Role,
    UpdateRoleDict,
    User,
)
from app.auth.utils import create_autogenerated_coworker, update_user
from app.contacts import utils as contacts
from app.document_categories.db import select_document_categories
from app.document_categories.types import DocumentCategory
from app.document_versions.types import DocumentVersion
from app.document_versions.utils import (
    get_document_version_available_for_document_and_company,
)
from app.documents.db import (
    select_children_documents_links,
    select_delete_requests_by_docs_ids,
    select_documents_recipients,
    select_owner_documents_for_api,
    select_parents_documents_links,
    update_documents,
)
from app.documents.tables import document_table, documents_processed_table, listing_table
from app.documents.utils import get_documents_access_filters
from app.es.enums import ESQuerySource
from app.es.types import AsyncIndex
from app.es.utils import build_incoming_api_query, build_outgoing_api_query, fetch_es_document_ids
from app.i18n import _
from app.lib.database import DBConnection, DBRow
from app.lib.enums import DocumentStatus
from app.lib.logging import log_duration
from app.models import select_all
from app.profile.types import PermissionEditRole
from app.profile.utils import (
    generate_esputnik_registration_event,
    update_esputnik_position,
)
from app.profile.validators import UpdateRoleSchema
from app.registration import utils as registration
from app.registration.utils import (
    send_jobs_about_autogenerated_user,
)
from app.services import services

logger = logging.getLogger(__name__)

DEFAULT_QUERY_LIMIT = 500


async def create_token(conn: DBConnection, options: CreateTokenOptions) -> tuple[DBRow, str]:
    """Create new token via public API.

    This is just a wrapper on top of token model functions from ``app.auth``.
    """
    raw_token = await insert_token(
        conn, {'role_id': options.role_id, 'vendor': options.vendor.value}
    )
    row = await select_token_by(conn, token_table.c.token_hash == generate_hash_sha512(raw_token))
    return row, raw_token  # type: ignore[return-value]


async def create_coworker(conn: DBConnection, options: CreateCoworkerOptions) -> User:
    """Create new coworker via public API"""

    # Create new user
    password = autogenerate_password()
    user = await create_autogenerated_coworker(
        conn=conn,
        edrpou=options.edrpou,
        email=options.email,
        password=password,
        first_name=options.first_name,
        second_name=options.second_name,
        last_name=options.last_name,
        phone=options.phone,
        is_phone_verified=False,
        position=options.position,
        created_by=options.user,
    )

    await generate_esputnik_registration_event(email=user.email)
    await update_esputnik_position(
        user_email=user.email,
        role_id=user.role_id,
        new_position=options.position,
        old_position=None,
    )

    await send_jobs_about_autogenerated_user(user)

    await registration.send_auto_welcome_email(
        email=options.email,
        password=password,
        created_by=options.user,
    )

    return user


def _set_activation_fields_on_update_role(
    prev_role: Role,
    update_dict: UpdateRoleDict,
    initiator: User,
    initiator_type: PermissionEditRole,
    new_status: RoleStatus | None,
) -> None:
    """
    Set "date_activated", "activated_by" and "activation_source" fields on role update.

    WARN: this function modifies `update_dict` in place.
    """

    # Technically, if the status is not None, it is always set to "active" because the
    # validator prevents other statuses from being assigned. However, we explicitly check it
    # here just in case
    if new_status is None or new_status != RoleStatus.active:
        return

    # Additionally, we update the activation fields only if the user’s status has been
    # manually set to "deleted" statuses. This excludes statuses like "rate_deleted" and
    # "blocked_2fa", which are automatically set by the system and restoring from them is not
    # considered as activation
    if prev_role.status not in (RoleStatus.deleted, RoleStatus.user_deleted):
        return

    update_dict['date_activated'] = sa.func.now()
    if initiator_type.is_self:
        raise ValueError('Initiator is not allowed to activate itself')

    if initiator_type.is_coworker:
        update_dict['activated_by'] = initiator.role_id
        update_dict['activation_source'] = RoleActivationSource.restore
    elif initiator_type.is_superadmin:
        update_dict['activation_source'] = RoleActivationSource.super_admin
        update_dict['activated_by'] = initiator.role_id


def _maybe_count_role_for_billing_limit(
    prev_role: Role,
    update_data: UpdateRoleDict,
) -> None:
    if prev_role.is_counted_in_billing_limit:
        return

    # If role has HRS role and we do not count it for billing limit,
    # And non-HRS field was updated, start counting this role for billing limit.
    if prev_role.has_hrs_role and any(
        permission in USER_PERMISSIONS and permission not in HRS_NOT_BILLABLE_PERMISSIONS
        for permission in update_data
    ):
        update_data['is_counted_in_billing_limit'] = True

        # Also allow to view coworkers since now it's regular EDO role
        update_data['can_view_coworkers'] = True


async def process_role_update(
    conn: DBConnection,
    prev_role: Role,
    update_data: UpdateRoleSchema,
    initiator: User,
    initiator_type: PermissionEditRole,
) -> Role:
    role_id: str = prev_role.id

    update_dict = update_data.to_update_dict()

    _set_activation_fields_on_update_role(
        prev_role=prev_role,
        update_dict=update_dict,
        initiator=initiator,
        initiator_type=initiator_type,
        new_status=update_data.status,
    )

    _maybe_count_role_for_billing_limit(
        prev_role=prev_role,
        update_data=update_dict,
    )

    async with conn.begin():
        # TAG: role_activation
        await update_role(
            conn=conn,
            role_id=role_id,
            data=update_dict,
        )

        role = await select_role_by_id(conn=conn, role_id=role_id)
        if not role:
            raise InvalidRequest(reason=_('Роль не знайдено.'))

        await contacts.add_role_to_contact_recipients_indexation(conn, role_id=role.id)

        if update_data.super_admin_permissions:
            await upsert_super_admin_permissions(
                conn=conn,
                role_id=role.id,
                super_admin_permissions=update_data.super_admin_permissions,
                request_user=initiator,
            )

        if update_data.status and update_data.status.is_active:
            await update_user(
                conn=conn,
                user_id=role.user_id,
                data={'registration_completed': True},
            )

    return role


async def prepare_recipients_for_response(
    conn: DBConnection, documents_ids: list[str]
) -> defaultdict[str, list[DBRow]]:
    recipients = await select_documents_recipients(
        conn=conn, documents_ids=documents_ids, with_names=True
    )

    mapping: defaultdict[str, list[DBRow]] = defaultdict(list)
    for recipient in recipients:
        mapping[recipient.document_id].append(recipient)
    return mapping


async def prepare_owner_info_for_response(
    conn: DBConnection, documents_ids: list[str]
) -> dict[str, UploaderInfo]:
    from_table = (
        document_table.join(
            role_table,
            role_table.c.id == document_table.c.uploaded_by,
        )
        .join(
            user_table,
            user_table.c.id == role_table.c.user_id,
        )
        .join(
            company_table,
            company_table.c.id == role_table.c.company_id,
        )
    )

    data = await select_all(
        conn,
        sa.select(
            [
                document_table.c.id,
                company_table.c.edrpou,
                company_table.c.name,
                user_table.c.email,
            ]
        )
        .select_from(from_table)
        .where(document_table.c.id.in_(documents_ids)),
    )

    return {info.id: UploaderInfo.from_db(info) for info in data}


async def prepare_delete_requests_for_response(
    conn: DBConnection, document_ids: Iterable[str]
) -> defaultdict[str, list[DBRow]]:
    """
    Prepare list of delete requests for list incoming documents response
    """

    delete_requests = await select_delete_requests_by_docs_ids(
        conn=conn, documents_ids=document_ids
    )
    mapping = defaultdict(list)
    for delete_request in delete_requests:
        mapping[delete_request.document_id].append(delete_request)
    return mapping


async def prepare_document_fields_for_response(
    conn: DBConnection, user: User, document_ids: set[str] | list[str]
) -> defaultdict[str, list[DBRow]]:
    fields = await select_available_document_additional_fields(
        conn=conn, company_id=user.company_id, document_ids=document_ids
    )

    mapping: defaultdict[str, list[DBRow]] = defaultdict(list)
    for field in fields:
        mapping[field.document_id].append(field)
    return mapping


async def prepare_parents_links_for_response(
    conn: DBConnection,
    user: User,
    documents_ids: set[str],
) -> dict[str, str]:
    """
    Return ID of the parent document for given documents (given documents are children).

    """
    parent_links = await select_parents_documents_links(
        conn=conn,
        user=user,
        documents_ids=documents_ids,
    )
    return {link.child_id: link.parent_id for link in parent_links}


async def prepare_children_links_for_response(
    conn: DBConnection,
    user: User,
    documents_ids: set[str],
) -> defaultdict[str, set[str]]:
    """
    Return IDs of the children documents for given documents (given documents are parents)
    """
    children_links = await select_children_documents_links(
        conn=conn,
        user=user,
        documents_ids=documents_ids,
    )
    mapping: defaultdict[str, set[str]] = defaultdict(set)
    for link in children_links:
        mapping[link.parent_id].add(link.child_id)
    return mapping


async def prepare_document_versions_for_response(
    conn: DBConnection, user: User, document_ids: set[str] | list[str]
) -> defaultdict[str, list[DocumentVersion]]:
    fields = await get_document_version_available_for_document_and_company(
        conn=conn,
        document_ids=document_ids,
        company_edrpou=user.company_edrpou,
    )

    mapping: defaultdict[str, list[DocumentVersion]] = defaultdict(list)
    for field in fields:
        mapping[field.document_id].append(field)
    return mapping


async def prepare_document_categories_for_response(
    conn: DBConnection,
    documents: list[DBRow],
) -> dict[str, DocumentCategory | None]:
    """
    Prepare document categories for list of documents

    Returns mapping { document_id: category }
    """
    categories_ids = [d.category for d in documents if d.category]
    categories = await select_document_categories(
        conn=conn,
        categories_ids=categories_ids,
    )
    categories_mapping = {category.id: category for category in categories}

    return {d.id: categories_mapping.get(d.category) for d in documents}


async def resolve_incoming_documents_from_es(
    *,
    es: AsyncIndex,
    conn: DBConnection,
    user: User,
    options: ListIncomingDocumentsOptions,
) -> tuple[list[DBRow], str | None]:
    es_query = await build_incoming_api_query(es=es, user=user, options=options)
    limit = options.limit or DEFAULT_QUERY_LIMIT
    documents_ids = await fetch_es_document_ids(
        query=es_query.limit(limit), source=ESQuerySource.api
    )

    non_empty_options = str(
        {k: v for k, v in options._asdict().items() if v is not None and v is not False}
    )
    if options.ids and len(options.ids) <= limit and len(documents_ids) < len(options.ids):
        logger.warning(
            'Got less documents than requested from ES',
            extra={
                'missed_es_ids': list(set(options.ids) - set(documents_ids)),
                'search_options': non_empty_options,
            },
        )

    documents = await select_incoming_documents_by_ids(
        conn=conn, ids=documents_ids, company_id=user.company_id
    )
    if len(documents) < len(documents_ids):
        logger.warning(
            'Got less documents than requested from DB',
            extra={
                'missed_db_ids': list(set(documents_ids) - {d.id for d in documents}),
                'search_options': non_empty_options,
            },
        )

    next_cursor: str | None = None
    if len(documents_ids) == limit:
        next_cursor = str(documents[-1].seqnum)

    mapping = {d.id: d for d in documents}
    documents = [mapping[id_] for id_ in documents_ids if id_ in mapping]

    return documents, next_cursor


async def list_owner_documents_es(
    conn: DBConnection,
    options: ListDocumentsOptions,
    user: User,
) -> tuple[list[DBRow], str | None]:
    es_query = await build_outgoing_api_query(es=services.es.documents, user=user, options=options)
    limit = options.limit or DEFAULT_QUERY_LIMIT
    documents_ids = await fetch_es_document_ids(
        query=es_query.limit(limit), source=ESQuerySource.api
    )

    async with log_duration('get list_owner_documents from db by ids'):
        rows = await select_owner_documents_for_api(
            conn=conn,
            documents_ids=documents_ids,
        )

    next_cursor = str(rows[-1].seqnum) if len(rows) == limit else None

    return rows, next_cursor


async def list_owner_documents_updates(
    conn: DBConnection,
    document_ids: list[str],
) -> None:
    if not document_ids:
        return

    await update_documents(
        conn,
        document_ids,
        {'has_changed_for_public_api': False},
    )


async def upsert_processed_status(
    conn: DBConnection,
    document_ids: set[str],
    processed: bool,
    user: User | AuthUser,
) -> list[str] | None:
    if not document_ids:
        return None

    query_document_access = (
        sa.select([document_table.c.id])
        .select_from(
            document_table.outerjoin(
                listing_table, listing_table.c.document_id == document_table.c.id
            )
        )
        .where(
            sa.and_(
                get_documents_access_filters(user),
                document_table.c.id.in_(document_ids),
                document_table.c.edrpou_owner != user.company_edrpou,
            )
        )
        .distinct()
    )

    access_result = await select_all(conn, query_document_access)
    document_ids = {item[0] for item in access_result}
    if not document_ids:
        return None

    rows = [
        {'document_id': doc_id, 'processed': processed, 'company_id': user.company_id}
        for doc_id in document_ids
    ]

    query = (
        insert(documents_processed_table)
        .values(rows)
        .returning(documents_processed_table.c.document_id)
    )
    query = query.on_conflict_do_update(
        index_elements=[
            documents_processed_table.c.document_id,
            documents_processed_table.c.company_id,
        ],
        set_={'processed': processed},
    )
    result = await select_all(conn, query)
    return [item[0] for item in result]


async def update_processed_status(
    conn: DBConnection,
    document_id: str,
    status_id: int | None,
    processed: bool,
    user_company_id: str,
) -> list[str] | None:
    filters = documents_processed_table.c.document_id == document_id

    # For non-finished documents, we should set processed status for
    # all companies involved in document processing, except current
    # user.
    # If a document is finished, we should set processed status for
    # all companies involved in document processing without any
    # exceptions.
    if status_id != DocumentStatus.finished.value:
        filters = sa.and_(filters, documents_processed_table.c.company_id != user_company_id)

    query = (
        documents_processed_table.update()
        .where(filters)
        .values({'processed': processed})
        .returning(documents_processed_table.c.document_id)
    )
    result = await select_all(conn, query)
    return [item[0] for item in result]
