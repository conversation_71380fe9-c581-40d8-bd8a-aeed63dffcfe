import pytest
import ujson

from api.enums import Vendor
from api.public.tests.common import TEST_COMPANY_EDRPOU, TEST_USER_EMAIL
from app.tests.common import (
    API_V1_TOKENS_URL,
    VCHASNO_EDRPOU,
    cleanup_on_teardown,
    prepare_auth_headers,
    prepare_client,
    prepare_user_data,
    set_company_config,
)


@pytest.mark.parametrize(
    'edrpou, email, vendor, expected_status',
    [
        (TEST_COMPANY_EDRPOU, TEST_USER_EMAIL, Vendor.onec.value, 200),
        (TEST_COMPANY_EDRPOU, TEST_USER_EMAIL, Vendor.api.value, 201),
    ],
)
async def test_create_token(aiohttp_client, edrpou, email, vendor, expected_status):
    app, client, master = await prepare_client(aiohttp_client, company_edrpou=VCHASNO_EDRPOU)
    await set_company_config(app, company_id=master.company_id, api_vendor='vchasno')
    await prepare_user_data(app, company_edrpou=edrpou, email=email, token_vendor=Vendor.onec.value)

    try:
        response = await client.post(
            API_V1_TOKENS_URL,
            data=ujson.dumps({'edrpou': edrpou, 'email': email, 'vendor': vendor}),
            headers=prepare_auth_headers(master),
        )
        assert response.status == expected_status

        data = await response.json()
        assert 'token' in data
        assert 'role_id' in data
        assert data['vendor'] == vendor
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'edrpou, email, vendor',
    [
        (TEST_COMPANY_EDRPOU, TEST_USER_EMAIL, Vendor.onec.value),
        (TEST_COMPANY_EDRPOU, TEST_USER_EMAIL, Vendor.api.value),
    ],
)
async def test_create_token_does_not_exist(aiohttp_client, edrpou, email, vendor):
    app, client, master = await prepare_client(aiohttp_client, company_edrpou=VCHASNO_EDRPOU)
    await set_company_config(app, company_id=master.company_id, api_vendor='vchasno')

    try:
        response = await client.post(
            API_V1_TOKENS_URL,
            data=ujson.dumps({'edrpou': edrpou, 'email': email, 'vendor': vendor}),
            headers=prepare_auth_headers(master),
        )
        assert response.status == 404

        data = await response.json()
        assert data['code'] == 'object_does_not_exist'
        assert data['details']['type'] == 'role'
    finally:
        await cleanup_on_teardown(app)


async def test_create_token_forbidden(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    try:
        response = await client.post(
            API_V1_TOKENS_URL,
            data=ujson.dumps(
                {
                    'edrpou': TEST_COMPANY_EDRPOU,
                    'email': TEST_USER_EMAIL,
                    'vendor': Vendor.api.value,
                }
            ),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 403
    finally:
        await cleanup_on_teardown(app)
