app:
  fernet_key: app_fernet_key
esputnik:
  url: 'https://url.com'
  login: '<EMAIL>'
  password: 'password'
  address_book_id: 0
  limit_insert: 0
db:
  url: ********************************
  url_readonly: ********************************
events_db:
  url: ********************************
s3:
  access_key: s3_access_key
  secret_key: s3_secret_key
  encryption_keys:
    - TEST
sync_contacts_api:
  e36507036:
    secret_key: secret_key
    url: secret_url
    param: secret_param
    limit: 0
    prefix: 'ua-'
  e40283641:
    secret_key: secret_key
    url: secret_url
    param: secret_param
    limit: 0
