; Documentation - https://iit.com.ua/download/productfiles/EUSignNIXesDescription.doc
; For AWS, we are using almost the same config as for other environments, except we are using
; UA proxy to make requests to CA servers, which blocks traffic from external countries

[\SOFTWARE\Institute of Informational Technologies\Certificate Authority-1.3\End User]
[\SOFTWARE\Institute of Informational Technologies\Certificate Authority-1.3\End User\FileStore]
ExpireTime=3600
SaveLoadedCerts=1
AutoDownloadCRLs=0
FullAndDeltaCRLs=1
OnlyOwnCRLs=1
AutoRefresh=0
CheckCRLs=1
Path=/work/eusign/dev/certificates
[\SOFTWARE\Institute of Informational Technologies\Certificate Authority-1.3\End User\Proxy]
SavePassword=0
Password=
User=
Anonymous=1
; We are using Ukraine proxy server for dev and prod environments because some of the certificate issuers (АЦСК)
; do not accept requests coming not from Ukraine. And our AWS is located in Frankfurt, Germany.
Port=3128
Address=*************
Use=1
[\SOFTWARE\Institute of Informational Technologies\Certificate Authority-1.3\End User\OCSP]
Port=80
Address=
OtherAddresses=
BeforeFStore=1
Use=1
[\SOFTWARE\Institute of Informational Technologies\Certificate Authority-1.3\End User\TSP]
Port=
Address=
GetStamps=0
[\SOFTWARE\Institute of Informational Technologies\Certificate Authority-1.3\End User\LDAP]
LookupCert=0
Password=
User=
Anonimous=1
Port=
Address=
Use=0
[\SOFTWARE\Institute of Informational Technologies\Certificate Authority-1.3\End User\Mode]
Offline=0
ResetPKey=0
MakePKeyPFXContainer=0
[\SOFTWARE\Institute of Informational Technologies\Certificate Authority-1.3\End User\Log]
System=1
ReportAgent=0
Address=
Port=10111
OnlyErrors=1
[\SOFTWARE\Institute of Informational Technologies\Certificate Authority-1.3\End User\Libraries\Sign]
[\SOFTWARE\Institute of Informational Technologies\Certificate Authority-1.3\End User\Libraries\Sign\KeyMedia]
ProtectPassword=0
Password=
Device=0
Type=3
ShowErrors=0
SourceType=2
