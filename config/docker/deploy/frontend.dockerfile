# ========== basic cached stage
FROM node:22-alpine AS cache

WORKDIR /work

COPY package.json .
COPY yarn.lock .

RUN yarn install --frozen-lockfile


# ========== build assets
FROM cache AS assets

ADD . /work

ARG VCHASNO_LEVEL
ARG VCHASNO_CONFIG
ARG TAG
ARG SENTRY_AUTH_TOKEN

ENV VCHASNO_LEVEL=${VCHASNO_LEVEL}
ENV VCHASNO_CONFIG=${VCHASNO_CONFIG}
ENV NODE_PATH /work/node_modules/
ENV NODE_ENV production
ENV WEBPACK_USE_HASH 1
ENV TAG=${TAG}
ENV SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN}

RUN node --max_old_space_size=8192 $NODE_PATH/webpack/bin/webpack.js --config webpack.config.js


# ========== upload assets
FROM python:3.12-slim AS assets_upload

RUN apt-get update && apt-get install -y \
    brotli \
    rename \
    bc \
    && rm -rf /var/lib/apt/lists/*

RUN pip3 install --no-cache s3cmd==2.4.0

ARG ACCESS_KEY_S3
ARG SECRET_KEY_S3
ARG STATIC_BUCKET

ENV ACCESS_KEY=${ACCESS_KEY_S3}
ENV SECRET_KEY=${SECRET_KEY_S3}

ENV FORCE_SYNC=1
ENV PARALLEL_SYNC=1

WORKDIR /work
COPY --from=assets /work/static /work/static
COPY cs/lib/vendor /work/static/js/lib
COPY static/css/lib /work/static/css/lib
COPY scripts/static/upload_static.bash /scripts/upload_static.bash
WORKDIR /

RUN ./scripts/upload_static.bash $STATIC_BUCKET /work/static
