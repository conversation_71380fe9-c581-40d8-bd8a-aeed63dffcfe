
# ...>braces
# Uncontrolled resource consumption in braces
# Affected versions < 3.0.3 - Patched versions 3.0.3
# de-facto yarn.lock we have braces version 3.0.3

GHSA-grv7-fg5c-xmjg

# Modules: ttag-cli>cross-spawn
# Regular Expression Denial of Service (ReDoS) in cross-spawn
# Versions of the package cross-spawn before 7.0.5 are vulnerable to Regular Expression Denial of Service (ReDoS) due to improper input sanitization.
# An attacker can increase the CPU usage and crash the program by crafting a very large and well crafted string.

GHSA-3xgq-45jj-v275


# Severity: CRITICAL
# Modules: jsdom>request>form-data
# URL: https://github.com/advisories/GHSA-fjxv-7rqg-78g4

GHSA-fjxv-7rqg-78g4
