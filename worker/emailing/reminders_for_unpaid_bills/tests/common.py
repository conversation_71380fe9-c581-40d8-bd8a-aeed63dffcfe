from app.auth import enums as auth_enums
from app.billing import db as billing_db
from app.billing import types as billing_types
from app.billing import validators as billing_validators
from app.lib import datetime_utils as dt_utils
from app.lib import enums as lib_enums
from app.services import services
from app.tests import common

USER_EMAIL_1 = '<EMAIL>'
USER_EMAIL_2 = '<EMAIL>'
USER_EMAIL_3 = '<EMAIL>'
USER_EMAIL_4 = '<EMAIL>'
USER_EMAIL_5 = '<EMAIL>'
USER_EMAIL_6 = '<EMAIL>'
USER_EMAIL_7 = '<EMAIL>'
USER_EMAIL_8 = '<EMAIL>'


async def prepare_unpaid_bill(
    conn,
    user,
    web_rate=None,
    archive_rate=None,
    documents=False,
    employees_count=None,
    date_created=None,
    services_type=billing_types.BillServicesType.rate,
) -> billing_types.Bill:
    assert documents or web_rate or archive_rate or employees_count, 'Provide any rate to the bill'

    bill_services = []
    if web_rate:
        bill_services.append(
            billing_types.AddBillServiceRateOptions(
                rate=web_rate,
                units=1,
                unit_price=1000,
                date_from=dt_utils.local_now(),
                limits_employees_count=None,
                price_per_user=None,
            )
        )

    if archive_rate:
        bill_services.append(
            billing_types.AddBillServiceRateOptions(
                rate=archive_rate,
                units=1,
                unit_price=1000,
                date_from=dt_utils.local_now(),
                limits_employees_count=None,
                price_per_user=None,
            )
        )

    if documents:
        bill_services.append(
            billing_types.AddBillServiceUnitsOptions(
                units=100,
                unit_price=100,
            )
        )

    if employees_count:
        bill_services.append(
            billing_types.AddBillServiceExtensionOptions(
                units=employees_count,
                unit_price=100,
                extension=billing_types.RateExtensionType.employees,
                date_from=None,
                company_rate=billing_types.AccountRate.latest_ultimate,
            )
        )

    options = billing_validators.AddBillOptions(
        name=user.company_name or common.TEST_COMPANY_NAME,
        edrpou=user.company_edrpou,
        email=user.email,
        role_id=user.role_id,
        company_id=user.company_id,
        user_id=user.id,
        source=billing_types.BillSource.vchasno,
        agreement='Some agreement',
        contract_basis=None,
        payment_purpose=None,
        is_card_payment=False,
        services_type=services_type,
        services=bill_services,
        custom_price=None,
        status=billing_types.BillStatus.requested,
    )

    bill = await billing_db.insert_bill(conn=conn, options=options)

    # Manually modify creation date
    if date_created:
        await billing_db.update_bill(
            conn=conn,
            bill_ids=[bill.id_],
            data={'date_created': date_created},
        )

    document = await common.prepare_document_data(app=services.app, owner=user)
    await billing_db.update_bill(
        conn=conn,
        bill_ids=[bill.id_],
        data={'document_id': document.id},
    )

    return bill


async def prepare_bill_reminder_recipients(app):
    """
    Prepare bill reminder recipients

    Returns:
        - bill_creator
        - company_admin_1
        - company_admin_2
        - accountant
        - director
        - financial_director
        - random_coworker
        - third_party
    """
    bill_creator = await common.prepare_user_data(
        app,
        email=USER_EMAIL_1,
    )
    company_admin_1 = await common.prepare_user_data(
        app,
        email=USER_EMAIL_2,
        company_id=bill_creator.company_id,
        user_role=lib_enums.UserRole.admin,
    )
    company_admin_2 = await common.prepare_user_data(
        app,
        email=USER_EMAIL_3,
        company_id=bill_creator.company_id,
        user_role=lib_enums.UserRole.admin,
    )
    accountant = await common.prepare_user_data(
        app,
        email=USER_EMAIL_4,
        company_id=bill_creator.company_id,
        role_position=auth_enums.EmployeePositions.accountant.name_uk,
        user_role=lib_enums.UserRole.user,
    )
    director = await common.prepare_user_data(
        app,
        email=USER_EMAIL_5,
        company_id=bill_creator.company_id,
        role_position=auth_enums.EmployeePositions.director.name_uk,
        user_role=lib_enums.UserRole.user,
    )
    financial_director = await common.prepare_user_data(
        app,
        email=USER_EMAIL_6,
        company_id=bill_creator.company_id,
        role_position=auth_enums.EmployeePositions.financial_director.name_uk,
        user_role=lib_enums.UserRole.user,
    )
    random_coworker = await common.prepare_user_data(
        app,
        email=USER_EMAIL_7,
        company_id=bill_creator.company_id,
        user_role=lib_enums.UserRole.user,
    )
    third_party = await common.prepare_user_data(
        app,
        email=USER_EMAIL_8,
        company_edrpou=common.TEST_COMPANY_EDRPOU_2,
    )
    return (
        bill_creator,
        company_admin_1,
        company_admin_2,
        accountant,
        director,
        financial_director,
        random_coworker,
        third_party,
    )
