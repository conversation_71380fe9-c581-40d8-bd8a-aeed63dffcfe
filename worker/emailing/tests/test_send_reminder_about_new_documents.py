import datetime
import itertools
import logging
from functools import partial

import pytest

from app.documents.enums import FirstSignBy
from app.lib.datetime_utils import local_now, utc_now
from app.lib.enums import CommentStatus, DocumentStatus
from app.notifications.db import select_notification_by
from app.notifications.enums import NotificationName, NotificationSource
from app.notifications.tables import notification_table
from app.tests.common import (
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    cleanup_on_teardown,
    prepare_client,
    prepare_comment_data,
    prepare_document_data,
    prepare_user_data,
)
from worker.emailing.jobs import send_reminder_about_new_documents_job
from worker.emailing.utils import (
    select_companies_with_activity,
    select_new_documents_details,
    select_new_documents_totals,
)

logger = logging.getLogger(__name__)


@pytest.mark.xfail
@pytest.mark.parametrize(
    'first_sign_by, document_status_id, comment_status_id, notified_party, '
    'owner_can_receive_reminders, recipient_can_receive_reminders, expected, expected_companies',
    [
        # Any flow: refused company doesn't get any notifications
        (
            FirstSignBy.owner.value,
            DocumentStatus.signed_and_sent.value,
            None,
            'nobody',
            True,
            True,
            0,
            0,
        ),
        # Standard flow
        # 1. Owner signed - waiting for recipient
        (
            FirstSignBy.owner.value,
            DocumentStatus.signed_and_sent.value,
            None,
            'recipient',
            True,
            True,
            1,
            0,
        ),
        # Owner signed - waiting for recipient,
        # but recipient's  `can_receive_reminders` is False
        (
            FirstSignBy.owner.value,
            DocumentStatus.signed_and_sent.value,
            None,
            'nobody',
            True,
            False,
            0,
            0,
        ),
        # 2. Owner signed - recipient commented
        (
            FirstSignBy.owner.value,
            DocumentStatus.finished.value,
            CommentStatus.ordinary.value,
            'owner',
            True,
            True,
            1,
            1,
        ),
        # 3. Owner signed - recipient rejected
        (
            FirstSignBy.owner.value,
            DocumentStatus.reject.value,
            CommentStatus.reject.value,
            'owner',
            True,
            True,
            1,
            1,
        ),
        # 4. Owner did not sign
        (
            FirstSignBy.owner.value,
            DocumentStatus.ready_to_be_signed.value,
            None,
            'nobody',
            True,
            True,
            0,
            0,
        ),
        # 3P Document
        # 1. owner sent - waiting for recipient
        (
            FirstSignBy.recipient.value,
            DocumentStatus.sent.value,
            None,
            'recipient',
            True,
            True,
            1,
            0,
        ),
        # 2. owner sent - recipient commented
        (
            FirstSignBy.recipient.value,
            DocumentStatus.sent.value,
            CommentStatus.ordinary.value,
            'recipient',
            True,
            True,
            2,
            1,
        ),
        # 3. owner sent - recipient rejected
        (
            FirstSignBy.recipient.value,
            DocumentStatus.reject.value,
            CommentStatus.reject.value,
            'owner',
            True,
            True,
            1,
            1,
        ),
        # 4. owner sent - recipient signed and sent back
        (
            FirstSignBy.recipient.value,
            DocumentStatus.signed_and_sent.value,
            None,
            'owner',
            True,
            True,
            1,
            1,
        ),
        # Owner sent - recipient signed and sent back,
        # but owner's  `can_receive_reminders` is False
        (
            FirstSignBy.recipient.value,
            DocumentStatus.signed_and_sent.value,
            None,
            'owner',
            False,
            True,
            0,
            1,
        ),
    ],
)
async def test_send_reminder_about_new_documents(
    mailbox,
    aiohttp_client,
    first_sign_by,
    document_status_id,
    comment_status_id,
    notified_party,
    owner_can_receive_reminders,
    recipient_can_receive_reminders,
    expected,
    expected_companies,
):
    date_3days_before = utc_now() - datetime.timedelta(days=3)

    app, client, owner = await prepare_client(
        aiohttp_client,
        can_receive_reminders=owner_can_receive_reminders,
        date_created=date_3days_before,
    )
    recipient_edrpou = '12345678'

    recipient = await prepare_user_data(
        app,
        can_receive_reminders=recipient_can_receive_reminders,
        company_edrpou=recipient_edrpou,
        email='<EMAIL>',
        date_created=date_3days_before,
    )

    document = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=True,
        first_sign_by=first_sign_by,
        another_recipients=[recipient],
        status_id=document_status_id,
    )
    document_id = document.id

    if comment_status_id:
        await prepare_comment_data(
            app,
            document_id=document_id,
            user_id=recipient.id,
            role_id=recipient.role_id,
            status_id=comment_status_id,
            text='Test Comment',
        )

    try:
        async with app['db'].acquire() as conn:
            companies = await select_companies_with_activity(
                conn, edrpou=owner.company_edrpou, date_from=date_3days_before
            )

            assert len(companies) == expected_companies

        assert len(mailbox) == 0
        await send_reminder_about_new_documents_job(app, {}, logger)
        assert len(mailbox) == expected

        if expected == 1:
            notified_email = locals()[notified_party].email
            assert mailbox[0]['To'] == notified_email
        elif expected == 2:
            emails = {owner.email, recipient.email}
            assert {item['To'] for item in mailbox} == emails

        if expected:
            notified = locals()[notified_party]

            async with app['db'].acquire() as conn:
                data = await select_notification_by(
                    conn, notification_table.c.email == notified.email
                )
                assert data.company_id == notified.company_id
                assert data.document_id == document_id
                assert data.email == notified.email

                name = NotificationName.reminder_about_new_documents
                assert data.name == name
                assert data.source == NotificationSource.email
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.xfail
@pytest.mark.parametrize(
    'days_pending, count_sent_notifications, expected',
    [
        # Pending
        (0, 0, 0),
        (1, 0, 0),
        (2, 0, 0),
        (3, 0, 1),
        (4, 0, 0),
        (5, 0, 0),
        (6, 0, 1),
        # Max notifications limit
        (3, 1, 1),
        (3, 2, 0),
        (4, 2, 0),
        (5, 2, 0),
        (6, 2, 0),
        (6, 150, 0),
    ],
)
async def test_send_reminder_about_new_documents_max_notifications(
    mailbox,
    aiohttp_client,
    days_pending,
    count_sent_notifications,
    expected,
):
    date_before = utc_now() - datetime.timedelta(days=days_pending)

    app, client, owner = await prepare_client(aiohttp_client, date_created=date_before)

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        date_created=date_before,
        email='<EMAIL>',
    )

    document = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=True,
        another_recipients=[recipient],
        status_id=DocumentStatus.signed_and_sent.value,
    )
    document_id = document.id

    try:
        send_reminder_func = partial(send_reminder_about_new_documents_job, app, {}, logger)

        async with app['db'].acquire() as conn:
            if count_sent_notifications:
                for _ in range(count_sent_notifications):
                    await send_reminder_func()
                    if mailbox:
                        mailbox.clear()

        assert len(mailbox) == 0
        await send_reminder_about_new_documents_job(app, {}, logger)
        assert len(mailbox) == expected

        if expected:
            assert mailbox[0]['To'] == recipient.email

            async with app['db'].acquire() as conn:
                data = await select_notification_by(
                    conn, notification_table.c.email == recipient.email
                )
                assert data.company_id == recipient.company_id
                assert data.document_id == document_id
                assert data.email == recipient.email

                name = NotificationName.reminder_about_new_documents
                assert data.name == name
                assert data.source == NotificationSource.email
    finally:
        await cleanup_on_teardown(app)


async def test_select_new_documents_details(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    now = local_now()

    try:
        async with app['db'].acquire() as conn:
            result = await select_new_documents_details(conn, user.company_edrpou, now)
            assert result == list(itertools.repeat({}, 4))
    finally:
        await cleanup_on_teardown(app)


async def test_select_new_documents_totals(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    now = local_now()

    try:
        async with app['db'].acquire() as conn:
            result = await select_new_documents_totals(conn, user.company_edrpou, now)
            assert result == list(itertools.repeat({'count': 0}, 4))
    finally:
        await cleanup_on_teardown(app)
