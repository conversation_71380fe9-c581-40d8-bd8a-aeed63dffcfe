import datetime

import sqlalchemy as sa

from app.auth.tables import user_table
from app.lib.database import DBConnection, DBRow
from app.models import select_all
from app.notifications.tables import unsubscription_table


async def select_users_with_incomplete_registration(
    conn: DBConnection, time_ago: datetime.datetime
) -> list[DBRow]:
    return await select_all(
        conn,
        (
            sa.select([user_table.c.first_name, user_table.c.email])
            .select_from(
                user_table.outerjoin(
                    unsubscription_table,
                    unsubscription_table.c.email == user_table.c.email,
                )
            )
            .where(
                sa.and_(
                    unsubscription_table.c.id.is_(None),
                    user_table.c.registration_completed.is_(False),
                    user_table.c.email_confirmed.is_(True),
                    user_table.c.date_updated > time_ago,
                )
            )
        ),
    )
