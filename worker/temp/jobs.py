import logging

from aiohttp import web

from app.lib import s3_utils
from app.lib.types import DataDict
from app.services import services
from worker.topics import REMOVE_OLD_S3_FILES

logger = logging.getLogger(__name__)


async def remove_old_s3_files(_: web.Application, __: DataDict, logger: logging.Logger) -> None:
    template_preview_keys = await s3_utils.get_keys_with_prefix('templates/preview/webp/400/90/')
    if not template_preview_keys:
        logger.info('No old preview files found')
        return

    await s3_utils.delete_batch(keys=template_preview_keys)

    await services.kafka.send_record(topic=REMOVE_OLD_S3_FILES)
