from datetime import datetime

import sqlalchemy as sa

from app.auth.tables import company_table
from app.auth.types import User
from app.documents.enums import AccessSource
from app.documents.tables import (
    document_recipients_table,
    document_table,
    listing_table,
)
from app.lib.database import DBConnection, DBRow
from app.models import exists, select_all, select_table_ids_for_insert
from worker.documents.enums import ESIndexationDirection


async def select_listings_with_admin_access(
    conn: DBConnection, role_id: str, limit: int
) -> list[DBRow]:
    """Select documents with document admins to given user"""

    query = (
        sa.select([listing_table.c.document_id, listing_table.c.id])
        .select_from(listing_table)
        .where(
            sa.and_(
                listing_table.c.role_id == role_id,
                listing_table.c.sources.contains(AccessSource.admin),
            )
        )
        .limit(limit)
    )
    return await select_all(conn, query)


async def select_listing_documents_ids(conn: DBConnection, listings_ids: list[str]) -> list[str]:
    listings = await select_all(
        conn=conn,
        query=(
            sa.select([listing_table.c.document_id]).where(listing_table.c.id.in_(listings_ids))
        ),
    )

    return [item.document_id for item in listings]


async def select_documents_for_indexation(
    conn: DBConnection,
    limit: int,
    direction: ESIndexationDirection,
    max_seqnum: int | None,
    min_seqnum: int | None,
    seqnum: int | None,
) -> list[DBRow]:
    filters = []

    # boundaries
    if max_seqnum is not None:
        filters.append(document_table.c.seqnum <= max_seqnum)
    if min_seqnum is not None:
        filters.append(document_table.c.seqnum >= min_seqnum)

    # cursor
    if seqnum is not None:
        if direction == ESIndexationDirection.desc:
            filters.append(document_table.c.seqnum < seqnum)
        elif direction == ESIndexationDirection.asc:
            filters.append(document_table.c.seqnum > seqnum)

    # sort direction
    order_column = document_table.c.seqnum
    if direction == ESIndexationDirection.desc:
        order_column = order_column.desc()
    elif direction == ESIndexationDirection.asc:
        order_column = order_column.asc()

    documents = await select_all(
        conn=conn,
        query=(
            sa.select([document_table.c.id, document_table.c.seqnum])
            .select_from(document_table)
            .where(sa.and_(*filters))
            .limit(limit)
            .order_by(order_column)
        ),
    )
    return documents


async def exist_sent_documents(
    conn: DBConnection, *, company_id: str, recipient_edrpou: str
) -> bool:
    """Check if company_id sent documents to recipient_edrpou"""
    return await exists(
        conn=conn,
        select_from=(
            document_table.join(
                company_table, company_table.c.edrpou == document_table.c.edrpou_owner
            ).join(
                document_recipients_table,
                document_recipients_table.c.document_id == document_table.c.id,
            )
        ),
        clause=sa.and_(
            company_table.c.id == company_id,
            document_recipients_table.c.edrpou == recipient_edrpou,
        ),
    )


async def select_documents_ids_for_recipient(
    *, conn: DBConnection, user: User, offset: int, limit: int
) -> tuple[list[str], bool]:
    # All access for documents where current user is as one of the recipients
    company_accesses = (
        sa.select([listing_table])
        .select_from(
            document_table.join(
                listing_table, listing_table.c.document_id == document_table.c.id
            ).join(
                document_recipients_table,
                document_recipients_table.c.document_id == document_table.c.id,
            )
        )
        .where(
            sa.and_(
                listing_table.c.access_edrpou == user.company_edrpou,
                document_recipients_table.c.edrpou == user.company_edrpou,
                document_recipients_table.c.emails.contains(f'{{{user.email}}}'),
            )
        )
        .alias('accesses_by_recipient')
    )

    # Select only documents that user can't access
    documents_without_access = (
        sa.select([company_accesses.c.document_id])
        .select_from(company_accesses)
        .group_by(company_accesses.c.document_id)
        .having(
            sa.func.every(
                sa.or_(
                    company_accesses.c.role_id.is_(None),
                    company_accesses.c.role_id != user.role_id,
                )
            )
        )
        .alias('document_accesses')
    )

    documents = await select_all(
        conn=conn,
        query=(
            sa.select([document_table.c.id, document_table.c.seqnum])
            .select_from(
                document_table.join(
                    documents_without_access,
                    documents_without_access.c.document_id == document_table.c.id,
                )
            )
            .order_by(document_table.c.seqnum.asc())
            .offset(offset)
            .limit(limit + 1)
            .distinct()
        ),
    )

    return [d.id for d in documents[:limit]], len(documents) > limit


async def select_documents_ids(
    *,
    conn: DBConnection,
    company_edrpous: list[str],
    excluded_edrpous: list[str],
    limit: int,
    last_document_id: str | None = None,
    date_gte: datetime | None = None,
    date_lte: datetime | None = None,
) -> tuple[list[str], bool]:
    filters = []

    if company_edrpous:
        filters.append(listing_table.c.access_edrpou.in_(company_edrpous))

    if excluded_edrpous:
        filters.append(listing_table.c.access_edrpou.notin_(excluded_edrpous))

    if last_document_id:
        filters.append(listing_table.c.document_id <= last_document_id)

    if date_gte:
        filters.append(listing_table.c.date_created >= date_gte)
    if date_lte:
        filters.append(listing_table.c.date_created <= date_lte)

    query = (
        sa.select([listing_table.c.document_id])
        .select_from(listing_table)
        .where(sa.and_(*filters))
        .order_by(listing_table.c.document_id.desc())
        .limit(limit + 1)
        .distinct()
    )

    documents = await select_all(conn, query)

    return [d.document_id for d in documents[:limit]], len(documents) > limit


async def select_documents_ids_for_insert(
    conn: DBConnection, documents_ids: list[str]
) -> list[str]:
    return await select_table_ids_for_insert(conn=conn, table=document_table, ids=documents_ids)
