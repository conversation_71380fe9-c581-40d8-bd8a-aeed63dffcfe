import logging
from typing import Any

import sqlalchemy as sa
from aiohttp import web
from sqlalchemy.dialects.postgresql import insert

from app.auth.db import update_roles
from app.auth.tables import role_table
from app.documents.tables import latest_document_recipients_table
from app.events.document_actions import tables as document_actions_tables
from app.events.user_actions import tables as user_actions_tables
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.types import DataDict
from app.models import select_all
from app.services import services
from worker import topics
from worker.migrations.utils import create_table_partition_for_next_month
from worker.utils import is_migration_duplicate_detected

logger = logging.getLogger(__name__)


async def create_document_actions_table_partition_job(
    _: web.Application, __: DataDict, logger: logging.Logger
) -> None:
    """Create document actions table partition"""
    logger.info('Try to create document actions partition for the next month')
    async with services.events_db.acquire() as conn:
        table_name = document_actions_tables.document_actions_table.name
        await create_table_partition_for_next_month(conn=conn, table_name=table_name)


async def create_user_actions_table_partition_job(
    _: web.Application, __: DataDict, logger: logging.Logger
) -> None:
    """Create user actions table partition"""
    logger.info('Try to create user actions partition for the next month')
    async with services.events_db.acquire() as conn:
        table_name = user_actions_tables.user_actions_table.name
        await create_table_partition_for_next_month(conn=conn, table_name=table_name)


async def migrate_latest_recipient(
    __: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    cursor: str = data['cursor']
    limit: int = data['limit']

    if get_flag(FeatureFlags.DISABLE_LATEST_RECIPIENTS_MIGRATION):
        logger.info('Latest recipients migration is disabled')
        return

    if await is_migration_duplicate_detected(
        key='migrate_latest_recipient',
        ttl_seconds=10 * 60,  # 10 minutes
        cursor_uuid=cursor,
        reset=data.get('reset_lock', False),
    ):
        return

    async with services.db.acquire() as conn:
        rows = await select_all(
            conn=conn,
            query=sa.text("""
                    SELECT
                        dr.id as recipient_id,
                        d.edrpou_owner as owner_edrpou,
                        dr.edrpou as recipient_edrpou,
                        dr.emails as recipient_emails,
                        dr.date_created as date_updated
                    FROM document_recipients dr
                        JOIN documents d ON dr.document_id = d.id
                    WHERE dr.id > :cursor
                        AND dr.is_emails_hidden IS FALSE
                        AND dr.emails IS NOT NULL
                    ORDER BY dr.id ASC
                    LIMIT :limit
            """).bindparams(cursor=cursor, limit=limit),
        )

        if not rows:
            logger.info('Last recipient migration is finished')
            return

        next_cursor = str(rows[-1].recipient_id)

        mapping: dict[tuple[str, str], dict[str, Any]] = {}
        for row in rows:
            key = (row.owner_edrpou, row.recipient_edrpou)
            prev_value = mapping.get(key)
            if (not prev_value) or (row.date_updated <= prev_value['date_updated']):
                owner_edrpou = row.owner_edrpou
                recipient_emails = row.recipient_emails
                recipient_edrpou = row.recipient_edrpou
                date_updated = row.date_updated
                if (
                    (not owner_edrpou)
                    or (not recipient_edrpou)
                    or (not recipient_emails)
                    or (recipient_edrpou == owner_edrpou)
                ):
                    continue

                mapping[key] = {
                    'owner_edrpou': owner_edrpou,
                    'recipient_edrpou': recipient_edrpou,
                    'recipient_emails': sorted(recipient_emails),
                    'date_updated': date_updated,
                }

        if mapping:
            # Sort items to avoid deadlocks
            items = list(mapping.values())
            items = sorted(items, key=lambda x: (x['owner_edrpou'], x['recipient_edrpou']))
            query = (
                insert(latest_document_recipients_table)
                .values(items)
                .on_conflict_do_update(
                    index_elements=[
                        latest_document_recipients_table.c.owner_edrpou,
                        latest_document_recipients_table.c.recipient_edrpou,
                    ],
                    set_={
                        'recipient_emails': sa.text('EXCLUDED.recipient_emails'),
                        'date_updated': sa.text('EXCLUDED.date_updated'),
                    },
                    where=sa.and_(
                        sa.text('EXCLUDED.date_updated')
                        > latest_document_recipients_table.c.date_updated,
                        sa.text('EXCLUDED.recipient_emails')
                        != latest_document_recipients_table.c.recipient_emails,
                    ),
                )
            )
            await conn.execute(query)

        await services.kafka.send_record(
            topic=topics.MIGRATE_LATEST_RECIPIENTS,
            value={'cursor': next_cursor, 'limit': limit},
        )


async def migrate_sign_review_permission(
    __: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    if get_flag(FeatureFlags.DISABLE_SIGN_REJECT_PERMISSION_MIGRATION):
        logger.info('Job is disabled')
        return

    last_role_id = data.get('last_role_id', '00000000-0000-0000-0000-000000000000')
    limit = data.get('limit', 100)

    if await is_migration_duplicate_detected(
        key='migrate_sign_review_permission',
        ttl_seconds=10 * 60,  # 10 minutes
        reset=data.get('reset_lock', False),
        cursor_uuid=last_role_id,
    ):
        return

    async with services.db.acquire() as conn:
        rows = await select_all(
            conn=conn,
            query=(
                sa.select(
                    [role_table.c.id.label('role_id'), role_table.c.can_sign_and_reject_document]
                )
                .where(
                    sa.and_(
                        role_table.c.id > last_role_id,
                    )
                )
                .order_by(role_table.c.id)
                .limit(limit)
            ),
        )

        if not rows:
            logger.info('Job is finished')
            return

        set_false_roles_ids = [row.role_id for row in rows if not row.can_sign_and_reject_document]
        set_true_roles_ids = [row.role_id for row in rows if row.can_sign_and_reject_document]

        await update_roles(
            conn=conn,
            role_ids=set_false_roles_ids,
            data={
                'can_sign_and_reject_document_internal': False,
                'can_sign_and_reject_document_external': False,
            },
        )
        await update_roles(
            conn=conn,
            role_ids=set_true_roles_ids,
            data={
                'can_sign_and_reject_document_internal': True,
                'can_sign_and_reject_document_external': True,
            },
        )

        if len(rows) == limit:
            await services.kafka.send_record(
                topic=topics.MIGRATE_SIGN_REVIEW_PERMISSION,
                value={'last_role_id': rows[-1].role_id},
            )
