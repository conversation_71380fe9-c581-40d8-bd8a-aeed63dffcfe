import logging

from app.documents.enums import AccessSource
from app.tests.common import (
    prepare_client,
    prepare_document_data,
    prepare_listing_access,
    prepare_user_data,
)
from app.trigger_notifications.enums import TriggerNotificationType
from app.trigger_notifications.tests.utils import get_trigger_notification
from worker.comments import jobs

logger = logging.getLogger(__name__)


async def test_send_comment_notification(
    aiohttp_client,
    mailbox,
    telegrambox,
    fcm_message_handler,
):
    app, client, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(
        app, email='<EMAIL>', create_mobile_active_session=True
    )
    document = await prepare_document_data(app, user)

    await prepare_listing_access(
        role_id=coworker.role_id,
        access_edrpou=coworker.company_edrpou,
        document_id=document.id,
        source=AccessSource.comment,
    )

    other_coworker = await prepare_user_data(
        app, email='<EMAIL>', create_mobile_active_session=True
    )

    await prepare_listing_access(
        role_id=other_coworker.role_id,
        access_edrpou=other_coworker.company_edrpou,
        document_id=document.id,
        source=AccessSource.signer,
    )

    data = {
        'is_internal': True,
        'document_id': document.id,
        'role_id': user.role_id,
        'author': {
            'email': user.email,
            'edrpou': user.company_edrpou,
        },
        'comment': {'text': 'Test comment'},
    }
    await jobs.send_comment_notification(app, data, logger)

    assert len(mailbox) == 2
    assert {mailbox[0]['To'], mailbox[1]['To']} == {
        '<EMAIL>',
        '<EMAIL>',
    }

    notifications = await get_trigger_notification(role_id=coworker.role_id)
    assert len(notifications) == 1
    assert notifications[0].type == TriggerNotificationType.document_comment

    notifications = await get_trigger_notification(role_id=other_coworker.role_id)
    assert len(notifications) == 1
    assert notifications[0].type == TriggerNotificationType.document_comment

    assert len(telegrambox) == 1

    # DOC-6830 - Temporary disabled
    # assert len(fcm_message_handler) == 2
    # assert (
    #     fcm_message_handler[0]['message']['notification']['title']
    #     == 'Ви отримали коментар до документу'
    # )
    # assert (
    #     fcm_message_handler[1]['message']['notification']['title']
    #     == 'Ви отримали коментар до документу'
    # )
