import logging

from aiohttp import web
from elasticmagic.actions import Index

from app.auth.db import select_companies_by_ids, select_user
from app.billing.utils import is_company_has_pro_or_higher_rate
from app.comments.db import (
    select_access_to_comments_map,
    select_comment_ids_for_indexation,
    select_comments_for_indexation_by_ids,
)
from app.comments.notifications import CommentNotification
from app.comments.utils import delete_es_comments
from app.documents.db import (
    select_comment_recipients,
    select_default_comment_admin_recipients,
    select_document,
)
from app.es.helpers import es_perform_bulk
from app.es.models.comment import Comment
from app.lib.chunks import iter_by_chunks
from app.lib.datetime_utils import optional_parse_raw_iso_datetime
from app.lib.types import DataDict
from app.services import services
from worker.topics import INDEX_COMMENTS, SEND_COMMENTS_TO_INDEX


async def send_comment_notification(
    __: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """Send notification about new comment in service"""

    is_internal: bool = data['is_internal']
    document_id: str = data['document_id']
    author: DataDict = data['author']
    comment: DataDict = data['comment']
    role_id: str | None = data['role_id']

    # Could not send email notification if Role ID doesn't exist (for example,
    # not registered user in sign session for example)
    if not role_id:
        return

    async with services.db_readonly.acquire() as conn:
        comment_recipients = await select_comment_recipients(
            conn=conn,
            document_id=document_id,
            company_edrpou=author['edrpou'],
            author_email=author['email'],
            is_internal=is_internal,
        )
        if not is_internal and not comment_recipients:
            # if not external recipient users for comment, then select company admins for
            # notifications
            comment_recipients = await select_default_comment_admin_recipients(
                conn=conn,
                document_id=document_id,
                current_edrpou=author['edrpou'],
            )

        if not comment_recipients:
            logger.warning(
                'No one received notification about external comment',
                extra={
                    'document_id': document_id,
                    'author_edrpou': author['edrpou'],
                    'author_email': author['email'],
                    'comment_text': comment['text'],
                },
            )
            return
        company_has_pro_mapping: dict[str, bool] = {}
        for recipient in comment_recipients:
            company_id = recipient.company_id
            if company_id in company_has_pro_mapping:
                continue
            company_has_pro = await is_company_has_pro_or_higher_rate(conn, company_id)
            company_has_pro_mapping[company_id] = company_has_pro

        user = await select_user(conn, role_id=role_id)
        document = await select_document(conn, document_id=document_id)
        if not document or not user:
            logger.warning(
                msg='Could not required objects in database',
                extra={'user': user, 'document': document},
            )
            return

    notification = CommentNotification(
        user=user,
        document=document,
        comment=comment['text'],
        comment_recipients=comment_recipients,
        company_has_pro_mapping=company_has_pro_mapping,
    )
    await notification.send()


async def index_comments(
    __: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Index comments in elasticsearch.
    """

    comment_ids: list[str] = data.get('comment_ids', [])
    use_master: bool = data.get('use_master', False)

    if not comment_ids:
        logger.info('[ES][Comment] No comments to index')
        return

    db = services.db if use_master else services.db_readonly
    async with db.acquire() as conn:
        comments = await select_comments_for_indexation_by_ids(conn, comment_ids)
        if not comments:
            logger.info('[ES][Comment] No comments to index')
            return

        comment_access_edrpous = await select_access_to_comments_map(conn, comment_ids)
        companies = await select_companies_by_ids(
            conn, [comment.access_company_id for comment in comments]
        )

    company_edrpou_mapping = {company.id: [company.edrpou] for company in companies}

    logger.info('[ES][Comment] Indexing comments', extra={'count': len(comments)})
    actions = []
    for comment in comments:
        es_comment = Comment(
            _id=comment.id,
            document_id=comment.document_id,
            document_version_id=comment.document_version_id,
            created_by=comment.role_id,
            type_=comment.type,
            date_created=comment.date_created,
            date_edited=comment.date_edited,
            seqnum=comment.seqnum,
            access_edrpous=company_edrpou_mapping.get(comment.access_company_id, [])
            if comment.access_company_id
            else comment_access_edrpous[comment.id],
        )
        actions.append(Index(es_comment))

    await es_perform_bulk(services.es.comments, actions)

    logger.info('[ES][Comment] Comments indexed')


async def send_comments_to_indexation(
    __: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Send comments to indexation.
    Group comments in batches and send to indexation.
    """

    # Might empty, but some other parameters must be specified.
    comment_ids: list[str] | None = data.get('comment_ids')
    # One or more of these parameters must be specified if comment_ids is empty
    document_ids: list[str] | None = data.get('document_ids')
    date_gte = optional_parse_raw_iso_datetime(data.get('date_gte'))
    date_lte = optional_parse_raw_iso_datetime(data.get('date_lte'))
    edrpou: str | None = data.get('edrpou')
    # system parameter
    use_master: bool = data.get('use_master', True)
    limit: int = data.get('limit', 500)
    seqnum_offset: int = data.get('seqnum_offset', 0)
    group_size = 100

    db = services.db if use_master else services.db_readonly

    if not comment_ids:
        # Think about what db should be used here
        async with db.acquire() as conn:
            comments = await select_comment_ids_for_indexation(
                conn=conn,
                document_ids=document_ids,
                date_gte=date_gte,
                date_lte=date_lte,
                edrpou=edrpou,
                limit=limit + 1,
                seqnum_offset=seqnum_offset,
            )

            comment_ids = [comment.id for comment in comments[:limit]]

            # schedule next batch
            if len(comments) > limit:
                comments = comments[:limit]
                # select next batch
                await services.kafka.send_record(
                    topic=SEND_COMMENTS_TO_INDEX,
                    value={
                        'document_ids': document_ids,
                        'date_gte': date_gte.isoformat() if date_gte else None,
                        'date_lte': date_lte.isoformat() if date_lte else None,
                        'edrpou': edrpou,
                        'use_master': use_master,
                        'limit': limit,
                        'seqnum_offset': comments[-1].seqnum,
                    },
                )

    if not comment_ids:
        logger.info('[ES][Comment] No comments to index')
        return

    await services.kafka.send_records(
        topic=INDEX_COMMENTS,
        values=[
            {
                'comment_ids': comment_ids_batch,
                'use_master': use_master,
            }
            for comment_ids_batch in iter_by_chunks(comment_ids, group_size)
        ],
    )


async def remove_comments_from_index(
    __: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Remove comments from index.
    """

    comment_ids: list[str] = data['comment_ids']

    logger.info('[ES][Comment] Removing comment from index', extra={'size': len(comment_ids)})

    try:
        await delete_es_comments(comment_ids)
    except Exception:
        logger.exception(
            '[ES][Comment] Error deleting comments from index',
        )
        raise
    logger.info('[ES][Comment] Comments removed from index')
