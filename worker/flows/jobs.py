import logging

from aiohttp import web

from app.lib.enums import Source
from app.lib.types import DataDict
from app.services import services
from worker.flows import utils
from worker.utils import retry_config


@retry_config(max_attempts=5)
async def send_multilateral_document_to_recipients(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Sending multilateral document to recipients:
     - send notification to recipients
     - create tags by recipients contacts
     - start document automation
     - update date delivered
    """

    # unpack data into variables
    document_id: str = data['document_id']
    source: Source = Source(data['source'])

    async with services.db.acquire() as conn:
        await utils.send_multilateral_document_to_recipients(
            conn=conn,
            document_id=document_id,
            source=source,
        )


@retry_config(max_attempts=5)
async def send_multilateral_document_notification(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Send notification about multilateral document to recipients. Currently, we are
    storing them in "document_recipients" table.
    """

    document_id: str = data['document_id']
    # You can use this field to send notification to specific company
    company_edrpou: str | None = data['company_edrpou']

    async with services.db.acquire() as conn:
        await utils.send_notification_about_multilateral_document(
            conn=conn,
            document_id=document_id,
            company_edrpou=company_edrpou,
        )
