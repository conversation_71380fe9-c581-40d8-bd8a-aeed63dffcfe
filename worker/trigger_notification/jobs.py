import logging
from datetime import timedelta

from aiohttp import web

from app.auth.db import select_roles_ids
from app.contacts.db import exist_unregistered_contacts, select_next_company_by_contact
from app.lib.database import DBRow
from app.lib.datetime_utils import local_now, parse_local_timestamp
from app.lib.types import DataDict
from app.trigger_notifications import db
from app.trigger_notifications.enums import (
    TriggerNotificationStatus,
    TriggerNotificationType,
)
from app.trigger_notifications.utils import (
    TriggerNotificationCheckRecipientsContacts,
    TriggerNotificationCompanyRegistration,
    TriggerNotificationInviteCompanies,
)
from worker import topics
from worker.documents.db import exist_sent_documents

DELETE_NOTIFICATION_BUCKET = 20
NOTIFICATION_TTL_IN_DAYS = 30
NOTIFICATION_HARD_TTL_IN_DAYS = 90


async def delete_old_trigger_notification(
    app: web.Application, _: DataDict, logger: logging.Logger
) -> None:
    """Job that removes trigger notifications:
    - that are seen and between 30 and 90 days old
    - that are older than 90 days
    """

    now = local_now()
    seen_date_to = now - timedelta(days=NOTIFICATION_TTL_IN_DAYS)
    old_date_to = now - timedelta(days=NOTIFICATION_HARD_TTL_IN_DAYS)

    async with app['db'].acquire() as conn:
        seen_notifications = await db.select_seen_trigger_notifications_for_delete(
            conn=conn,
            limit=DELETE_NOTIFICATION_BUCKET,
            seen_date_to=seen_date_to,
            old_date_to=old_date_to,
        )

        # select old notifications if the limit is not exceeded
        old_notifications: list[DBRow] = []
        old_notifications_limit = DELETE_NOTIFICATION_BUCKET - len(seen_notifications)
        if old_notifications_limit > 0:
            old_notifications = await db.select_old_trigger_notifications_for_delete(
                conn=conn,
                limit=old_notifications_limit,
                old_date_to=old_date_to,
            )

        notifications = seen_notifications + old_notifications
        if not notifications:
            logger.info('Finish removing seen old trigger notification')
            return

        notifications_ids = [notification.id for notification in notifications]
        await db.delete_trigger_notification(conn, notifications_ids)

        log_extra = {'ids_len': len(notifications_ids)}
        logger.info('Trigger notification was deleted', extra=log_extra)

    await app['kafka'].send_record(topics.DELETE_OLD_TRIGGER_NOTIFICATION, {})


async def create_trigger_notification(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """Job that creates record about trigger notification in database"""
    roles_ids: list[str] = data['roles_ids']
    type_: TriggerNotificationType = TriggerNotificationType(data['type'])
    display_date_timestamp = data['display_date']
    context: DataDict | None = data.get('context')
    url: str = data.get('url', None)
    # Extract time from data
    display_date = parse_local_timestamp(display_date_timestamp)

    if not roles_ids:
        logger.warning('Can not create notification for empty list of roles')
        return

    db_data = [
        {
            'role_id': role_id,
            'url': url,
            'type': type_,
            'status': TriggerNotificationStatus.new,
            'display_date': display_date,
            'context': context,
        }
        for role_id in roles_ids
    ]
    logger.info('Trigger notification data', extra={'db_data': db_data})
    async with app['db'].acquire() as conn:
        await db.insert_trigger_notification(conn, db_data)


async def create_about_company_registration(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """Job that select companies with given company_edrpou in contact list and
    sends notification to all coworkers of selected company about registration of
    company with given edrpou

    """
    edrpou: str = data['company_edrpou']
    company_name: str | None = data.get('company_name')
    offset: int = data.get('offset', 0)

    async with app['db'].acquire() as conn:
        # select next contact with new company by given edrpou
        contact = await select_next_company_by_contact(conn, edrpou, offset=offset)

        if not contact:
            logger.info('Not more companies for notifying about company registration')
            return

        # retry this job again with new offset
        await app['kafka'].send_record(
            topic=topics.NOTIFICATION_ABOUT_COMPANY_REGISTRATION,
            value={**data, 'offset': offset + 1},
        )

        # If company sent documents to given edrpou, just ignore this company
        if await exist_sent_documents(
            conn=conn, company_id=contact.company_id, recipient_edrpou=edrpou
        ):
            logger.info('Company already sent documents to given edrpou')
            return

        # select all active roles for selected company
        roles_ids = await select_roles_ids(conn, company_id=contact.company_id)

    company_name = contact.short_name or contact.name or company_name
    company_title = f'{edrpou}, {company_name}' if company_name else edrpou

    notification = TriggerNotificationCompanyRegistration()
    await notification.send(
        roles_ids=roles_ids,
        company_title=company_title,
    )


async def create_invite_companies(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """Job that sends trigger notification with call to
    action to invite companies when given role does not have this type of trigger
    notification and when company have only unregistered contacts.

    """
    role_id: str = data['role_id']
    notification_type = TriggerNotificationType.invite_companies

    async with app['db'].acquire() as conn:
        if await db.exist_trigger_notification(conn, role_id, notification_type):
            logger.info('Role already has notification', extra=data)
            return

        if not await exist_unregistered_contacts(conn, role_id):
            logger.info('All contacts in company is registered', extra=data)
            return

    notification = TriggerNotificationInviteCompanies()
    await notification.send(roles_ids=[role_id])


async def delete_fulfilled_trigger_notification(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    `type` passed to `data` must be string
    (i.e. TriggerNotificationType.type.value)
    """
    role_id = data['role_id']
    type_ = getattr(TriggerNotificationType, data['type'])
    async with app['db'].acquire() as conn:
        await db.delete_trigger_notification_by_role_and_type(conn, role_id, type_)
    logger.info('Trigger notification was deleted', extra=data)


async def update_trigger_notification_about_invite(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    role_id = data['role_id']
    type_ = TriggerNotificationType.check_recipients
    async with app['db'].acquire() as conn:
        if not await db.exist_trigger_notification(conn, role_id, type_):
            return

    data['type'] = type_.value
    await app['kafka'].send_record(topics.DELETE_FULFILLED_TRIGGER_NOTIFICATION, data)

    notification = TriggerNotificationCheckRecipientsContacts()
    await notification.send(roles_ids=[role_id])
