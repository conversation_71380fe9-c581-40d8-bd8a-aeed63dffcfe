import pytest

from worker.document_automation.utils import lowercase_email_for_conditions


@pytest.mark.parametrize(
    ('automation', 'expected'),
    [
        (
            {
                'and': [
                    {'or': [{'eq': ['#document_side', 'inbox']}]},
                    {
                        'or': [
                            {
                                'eq': [
                                    '#document_recipients.email',
                                    'B<PERSON>Dobrovolsky<PERSON>@smartweb.com.ua',
                                ]
                            }
                        ]
                    },
                ]
            },
            {
                'and': [
                    {'or': [{'eq': ['#document_side', 'inbox']}]},
                    {
                        'or': [
                            {
                                'eq': [
                                    '#document_recipients.email',
                                    '<EMAIL>',
                                ]
                            }
                        ]
                    },
                ]
            },
        ),
        (
            {
                'and': [
                    {'or': [{'eq': ['#document_side', 'inbox']}]},
                    {'or': [{'eq': ['#document_category', '2']}]},
                    {'and': [{'le': ['#document_amount', 2000000]}]},
                    {
                        'or': [
                            {
                                'eq': [
                                    '#document_recipients.email',
                                    '<EMAIL>',
                                ]
                            }
                        ]
                    },
                    {
                        'or': [
                            {'eq': ['#document_recipients.edrpou', '77777777']},
                            {'eq': ['#document_recipients.edrpou', '41231992']},
                            {'eq': ['#document_recipients.edrpou', '34554362']},
                        ]
                    },
                ]
            },
            {
                'and': [
                    {'or': [{'eq': ['#document_side', 'inbox']}]},
                    {'or': [{'eq': ['#document_category', '2']}]},
                    {'and': [{'le': ['#document_amount', 2000000]}]},
                    {
                        'or': [
                            {
                                'eq': [
                                    '#document_recipients.email',
                                    '<EMAIL>',
                                ]
                            }
                        ]
                    },
                    {
                        'or': [
                            {'eq': ['#document_recipients.edrpou', '77777777']},
                            {'eq': ['#document_recipients.edrpou', '41231992']},
                            {'eq': ['#document_recipients.edrpou', '34554362']},
                        ]
                    },
                ]
            },
        ),
        (
            {
                'and': [
                    {
                        'or': [
                            {
                                'eq': [
                                    '#document_uploaded_by',
                                    '<EMAIL>',
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                'and': [
                    {
                        'or': [
                            {
                                'eq': [
                                    '#document_uploaded_by',
                                    '<EMAIL>',
                                ]
                            }
                        ]
                    }
                ]
            },
        ),
    ],
)
def test_lowercase_document_recipients_email(automation, expected):
    res = lowercase_email_for_conditions(automation)

    assert res == expected
