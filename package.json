{"name": "evodoc", "version": "1.0.0", "description": "Please, sign this", "main": "index.js", "scripts": {"audit:code": "yarn run improved-yarn-audit --min-severity high", "audit:fix": "npx yarn-audit-fix", "build": "cross-env NODE_ENV=production webpack --progress", "predev": "yarn install", "dev:hot": "cross-env NODE_ENV=development USE_HMR=1 webpack serve --progress --config webpack.config.js --open http://localhost:8009/app", "dev": "cross-env NODE_ENV=development webpack --progress --watch --config webpack.config.js", "check-types": "tsc --project tsconfig.json", "lint": "yarn run js:lint && yarn run style:lint", "js:lint": "eslint 'cs/**/*.{js,ts,jsx,tsx}'", "js:lint:fix": "eslint 'cs/**/*.{js,ts,jsx,tsx}' --fix", "graphql:codegen": "graphql-codegen --config codegen.ts", "graphql:schema": "get-graphql-schema 'http://localhost:8000/api/debug/graphql–schema' > cs/gql/schema.gql", "test": "cross-env TZ=UTC NODE_PATH=./cs TS_NODE_TRANSPILE_ONLY=true TS_NODE_COMPILER_OPTIONS='{\"module\":\"Node16\", \"esModuleInterop\": true, \"moduleResolution\": \"Node16\"}' mocha", "codestyle:fix": "prettier 'cs/**/*.{js,ts,jsx,tsx,mjs,cjs,json}' --config .prettierrc.json --ignore-path ./.prettierignore  --write --ignore-unknown", "markdown:format": "prettier '{app,api,worker,cs,docs,translations}/**/*.md' './README.md' --config .prettierrc.json --write", "style:lint": "stylelint cs/**/*.css"}, "repository": {"type": "git", "url": "*************************:vchasno/edo/edo.git"}, "engines": {"node": ">=20", "yarn": ">=1.22"}, "author": "", "license": "ISC", "dependencies": {"@ag-grid-community/client-side-row-model": "32.3.3", "@ag-grid-community/core": "32.3.3", "@ag-grid-community/react": "32.3.3", "@ag-grid-community/styles": "33.0.3", "@azure/msal-browser": "2.39.0", "@azure/msal-react": "1.5.13", "@evo/vchasno-signer": "0.18.7", "@hookform/resolvers": "3.10.0", "@module-federation/enhanced": "0.10.0", "@popperjs/core": "2.11.8", "@reduxjs/toolkit": "2.2.8", "@sentry/integrations": "7.114.0", "@sentry/react": "8.50.0", "@tanstack/react-query": "4.36.1", "@tanstack/react-query-devtools": "4.36.1", "@tiptap/core": "2.23.1", "@tiptap/extension-document": "2.23.1", "@tiptap/extension-floating-menu": "2.23.1", "@tiptap/extension-heading": "2.23.1", "@tiptap/extension-highlight": "2.23.1", "@tiptap/extension-paragraph": "2.23.1", "@tiptap/extension-text": "2.23.1", "@tiptap/extension-text-align": "2.23.1", "@tiptap/pm": "2.23.1", "@tiptap/react": "2.23.1", "@tiptap/starter-kit": "2.23.1", "@uiw/react-markdown-preview": "5.1.4", "@vchasno/shared-components": "0.1.26", "@vchasno/ui-kit": "0.4.56", "bowser": "2.11.0", "bwip-js": "4.5.1", "classnames": "2.5.1", "clean-webpack-plugin": "4.0.0", "connected-react-router": "6.9.3", "core-js": "3.40.0", "crypto-js": "4.2.0", "date-fns": "2.29.3", "eslint-plugin-unused-imports": "4.1.4", "final-form": "4.20.7", "final-form-arrays": "3.0.2", "formdata-polyfill": "3.0.19", "framer-motion": "6", "graphql": "16.11.0", "graphql-request": "5.1.0", "history": "4.10.1", "html-to-pdfmake": "2.5.28", "immutable": "3.8.2", "input-otp": "1.4.2", "js-cookie": "3.0.5", "jschardet": "3.0.0", "jsrsasign": "11.0.0", "jszip": "3.10.1", "lodash": "4.17.21", "lottie-react": "2.4.1", "memoize-one": "5.1.1", "moment": "2.30.1", "papaparse": "5.4.1", "pdfmake": "0.2.20", "posthog-js": "1.224.1", "prop-types": "15.8.1", "qrcode.react": "4.2.0", "qs": "6.12.1", "react": "17.0.2", "react-apple-login": "1.1.6", "react-copy-to-clipboard": "5.1.0", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "17.0.2", "react-draggable": "4.4.6", "react-dropzone": "14.3.8", "react-final-form": "6.5.1", "react-final-form-arrays": "3.1.2", "react-helmet": "6.1.0", "react-hook-form": "7.54.2", "react-intersection-observer": "9.13.1", "react-joyride": "2.9.3", "react-lines-ellipsis": "0.15.4", "react-modal-sheet": "3.5.0", "react-movable": "3.2.0", "react-moveable": "0.56.0", "react-number-format": "4.9.4", "react-onclickoutside": "6.13.0", "react-pdf": "9.2.1", "react-phone-input-2": "2.15.1", "react-popper": "2.3.0", "react-redux": "7.2.9", "react-resizable-panels": "3.0.3", "react-responsive": "9.0.2", "react-router-dom": "5.3.4", "react-text-mask": "5.5.0", "redux": "4.2.1", "redux-saga": "1.1.3", "redux-thunk": "2.4.2", "reselect": "5.1.0", "sax": "1.2.4", "scroll-into-view-if-needed": "3.1.0", "stream-browserify": "3.0.0", "text-encoding": "0.7.0", "ttag": "1.8.7", "typescript": "5.9.2", "url-parse": "1.5.10", "uuid": "3.4.0", "whatwg-fetch": "3.6.20", "written-number": "0.11.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz", "yup": "1.6.1"}, "devDependencies": {"@pmmmwh/react-refresh-webpack-plugin": "0.6.1", "@sentry/webpack-plugin": "3.1.2", "@swc/core": "1.12.0", "@tanstack/eslint-plugin-query": "5.8.3", "@trivago/prettier-plugin-sort-imports": "4.3.0", "@types/chai": "4.3.20", "@types/classnames": "2.3.1", "@types/crypto-js": "3.1.40", "@types/google.accounts": "0.0.14", "@types/history": "4.7.11", "@types/html-to-pdfmake": "2.4.4", "@types/immutable": "3.8.7", "@types/js-cookie": "2.2.7", "@types/jsrsasign": "10.5.13", "@types/mocha": "10.0.9", "@types/moment": "2.13.0", "@types/node": "10.3.2", "@types/papaparse": "5.3.11", "@types/pdfmake": "0.2.11", "@types/prop-types": "15.7.11", "@types/qrcode.react": "3.0.0", "@types/qs": "6.9.7", "@types/react": "17.0.52", "@types/react-dom": "17.0.18", "@types/react-helmet": "6.1.11", "@types/react-lines-ellipsis": "0.15.5", "@types/react-loadable": "5.5.11", "@types/react-maskedinput": "4.0.9", "@types/react-redux": "7.1.16", "@types/react-responsive": "8.0.2", "@types/react-router-dom": "5.3.3", "@types/react-scrollspy": "3.3.2", "@types/react-text-mask": "5.4.14", "@types/sinon": "10.0.13", "@types/text-encoding": "0.0.36", "@types/url-parse": "1.4.3", "@types/uuid": "3.4.4", "@typescript-eslint/eslint-plugin": "6.18.0", "@typescript-eslint/parser": "6.18.0", "chai": "4.5.0", "check-engine": "1.14.0", "copy-webpack-plugin": "12.0.2", "cross-env": "7.0.3", "css-loader": "6.11.0", "css-minimizer-webpack-plugin": "7.0.0", "enzyme": "3.3.0", "eslint": "8.57.0", "eslint-config-prettier": "7.2.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-prettier": "3.3.1", "eslint-plugin-react": "7.34.1", "eslint-plugin-react-hooks": "2.5.1", "eslint-webpack-plugin": "4.0.1", "fork-ts-checker-webpack-plugin": "9.1.0", "html-webpack-plugin": "5.6.3", "improved-yarn-audit": "3.0.4", "jsdom": "11.12.0", "jsdom-global": "3.0.2", "json-loader": "0.5.7", "mini-css-extract-plugin": "2.9.3", "mocha": "10.8.2", "moment-locales-webpack-plugin": "1.2.0", "po-gettext-loader": "1.0.0", "postcss": "8.5.3", "postcss-custom-properties": "10.0.0", "postcss-loader": "7.3.4", "postcss-preset-env": "9.6.0", "prettier": "2.2.1", "prettier-plugin-sort-imports": "1.7.2", "react-refresh": "0.17.0", "sinon": "4.3.0", "speed-measure-webpack-plugin": "1.5.0", "style-loader": "4.0.0", "stylelint": "13.13.1", "stylelint-config-idiomatic-order": "8.1.0", "stylelint-config-prettier": "9.0.5", "stylelint-config-standard": "22.0.0", "svg-sprite-loader": "6.0.11", "swc-loader": "0.2.6", "terser-webpack-plugin": "5.3.11", "ts-node": "10.9.2", "ttag-cli": "1.11.2", "webpack": "5.99.9", "webpack-bundle-analyzer": "4.10.2", "webpack-cli": "6.0.1", "webpack-dev-server": "5.2.2", "yamljs": "0.3.0"}, "resolutions": {"@types/react": "17.0.50"}, "optionalDependencies": {"@graphql-codegen/cli": "5.0.7", "@graphql-codegen/typescript-operations": "4.6.1", "@graphql-codegen/typescript-react-query": "6.1.1", "get-graphql-schema": "2.1.2"}}