import React from 'react';

import 'core-js/stable';
import 'lib/yup/defaultValidationMessages';

import '../lib/i18n/i18nInit';

import { isClientRendering } from '../lib/helpers';

import Auth from '../components/auth';
import { mountPageComponent } from '../lib/reactHelpers/client';

// styles
import '../styles/public/page.css';

async function bootstrap() {
    mountPageComponent(<Auth />);
}

if (isClientRendering()) {
    bootstrap();
}
