import { PayloadAction, createSlice } from '@reduxjs/toolkit';

import { AnnulmentActProps } from './types';

const initialState: AnnulmentActProps = {
    isOpen: false,
    popupType: null,
    documentID: null,
    annulmentActID: null,
    documentTitle: null,
    reasonAnnulment: null,
};

export const annulmentActSlice = createSlice({
    name: 'annulmentAct',
    initialState: initialState,
    reducers: {
        openCreateAnnulmentActPopup: (
            state,
            {
                payload,
            }: PayloadAction<{
                documentID: string;
            }>,
        ) => {
            state.isOpen = true;
            state.popupType = 'create';
            state.documentID = payload.documentID;
        },
        openApproveAnnulmentActPopup: (
            state,
            {
                payload,
            }: PayloadAction<{
                documentID: string;
                annulmentActID: string;
                documentTitle: string;
                reasonAnnulment: string;
            }>,
        ) => {
            state.isOpen = true;
            state.popupType = 'approve';
            state.documentID = payload.documentID;
            state.annulmentActID = payload.annulmentActID;
            state.documentTitle = payload.documentTitle;
            state.reasonAnnulment = payload.reasonAnnulment;
        },
        closeAnnulmentActPopup: (state) => {
            state.isOpen = false;
            state.popupType = null;
            state.documentID = null;
            state.annulmentActID = null;
            state.documentTitle = null;
            state.reasonAnnulment = null;
        },
    },
});

export const {
    openCreateAnnulmentActPopup,
    openApproveAnnulmentActPopup,
    closeAnnulmentActPopup,
} = annulmentActSlice.actions;

export default annulmentActSlice.reducer;
