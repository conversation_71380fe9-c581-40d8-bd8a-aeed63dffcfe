import { getCurrentUserRole } from 'selectors/app.selectors';

import { StoreState } from '../types/store';
import { HasPermission, IRole, IUser } from '../types/user';

import { hasPermissionFactory } from '../records/user';

export const mapStatetoHasPermission = (
    state: StoreState,
): { hasPermission: HasPermission; currentUserRole: IRole } => ({
    hasPermission: hasPermissionFactory(state.app.currentUser),
    currentUserRole: getCurrentUserRole(state),
});

export const mapStateToCurrentUser = (
    state: StoreState,
): { currentUser: IUser } => ({
    currentUser: state.app.currentUser,
});

export const mapStateToApplicationMode = (state: StoreState) => ({
    applicationMode: state.app.applicationMode,
});
