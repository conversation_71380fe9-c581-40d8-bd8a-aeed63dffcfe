import { PayloadAction, createSlice } from '@reduxjs/toolkit';

import { ContentPopupMapKeys } from 'components/TrialInfoPopup/constants';

export type TrialInfoPopupState = {
    isTrialInfoPopupOpen: boolean;
    isRoleHasUltimateRate: boolean;
    isShowTargetPopup: boolean;
    targetPopupName: ContentPopupMapKeys | '';
    locationId: string;
};

const initialState: TrialInfoPopupState = {
    isTrialInfoPopupOpen: false,
    isRoleHasUltimateRate: false,
    isShowTargetPopup: false,
    targetPopupName: '',
    locationId: '',
};

export const trialInfoPopupSlice = createSlice({
    name: 'trialInfoPopup',
    initialState,
    reducers: {
        setTrialInfoPopupOpen: (
            state,
            action: PayloadAction<{
                isTrialInfoPopupOpen: boolean;
                isRoleHasUltimateRate: boolean;
                locationId?: string;
            }>,
        ) => {
            state.isTrialInfoPopupOpen = action.payload.isTrialInfoPopupOpen;
            state.isRoleHasUltimateRate = action.payload.isRoleHasUltimateRate;
            state.isShowTargetPopup = false;
            state.targetPopupName = '';
            state.locationId = action.payload.isTrialInfoPopupOpen
                ? action.payload.locationId || ''
                : '';
        },
        setTrialInfoTargetPopupOpen: (
            state,
            action: PayloadAction<{
                isTrialInfoPopupOpen: boolean;
                isRoleHasUltimateRate: boolean;
                isShowTargetPopup: boolean;
                locationId: string;
                targetPopupName: ContentPopupMapKeys;
            }>,
        ) => {
            state.isTrialInfoPopupOpen = action.payload.isTrialInfoPopupOpen;
            state.isRoleHasUltimateRate = action.payload.isRoleHasUltimateRate;
            state.isShowTargetPopup = action.payload.isShowTargetPopup;
            state.targetPopupName = action.payload.targetPopupName;
            state.locationId = action.payload.isTrialInfoPopupOpen
                ? action.payload.locationId
                : '';
        },
    },
});

export const {
    setTrialInfoPopupOpen,
    setTrialInfoTargetPopupOpen,
} = trialInfoPopupSlice.actions;

export default trialInfoPopupSlice.reducer;
