import { DocumentDirectory } from 'gql-types';
import {
    DOCUMENT_ACCESSES_FRAGMENT,
    DOCUMENT_ANTIVIRUS_CHECKS_FRAGMENT,
    DOCUMENT_FRAGMENT,
    DOCUMENT_REVIEW_FRAGMENT,
    DOCUMENT_REVIEW_REQUESTS_FRAGMENT,
    DOCUMENT_REVIEW_SETTING_FRAGMENT,
    DOCUMENT_TAGS_FRAGMENT,
} from 'services/documents/api';
import {
    formatDocuments,
    getCategoriesNumbersApiArray,
} from 'services/documents/utils';
import graph from 'services/graph';
import { t } from 'ttag';

import { IUser } from '../types/user';

const getParentDirectoryIdForRequest = (
    parentDirectoryId?: Nullable<string | number>,
) => {
    const parentDirectoryIdNumber = Number(parentDirectoryId);

    return !isNaN(parentDirectoryIdNumber) ? parentDirectoryIdNumber : null;
};

const getFormatDirectories = (directories: DocumentDirectory[] = []) =>
    directories.map((directory) => ({
        ...directory,
        category: t`Папка`,
        isDirectory: true,
    }));

// @ts-expect-error TS7006 [FIXME] Comment is autogenerated
function mappingDocumentsArgs(args) {
    const parentDirectoryId = getParentDirectoryIdForRequest(
        args.parentDirectoryId,
    );

    return {
        search: args.search,
        searchTitle: args.searchTitle,
        searchNumber: args.searchNumber,
        searchTag: args.searchTag,
        searchCompanyName: args.searchCompanyName,
        searchCompanyEdrpou: args.searchCompanyEdrpou,
        searchUserEmail: args.searchUserEmail,
        searchParameter: args.searchParameter,
        userEmails: args.userEmails,
        statusId: args.statusId,
        hasComments: args.hasComments,
        isWaitMySign: args.isWaitMySign,
        isOneSign: args.isOneSign,
        hasDateDelivered: args.hasDateDelivered,
        withoutTags: args.withoutTags,
        sortDate: args.sortDate,
        tag: args.tag,
        reviewFolder: args.reviewFolder,
        gte: args.dateFrom,
        lte: args.dateTo,
        ids: args.ids,
        conditions2: args.conditions2,
        amountGte: args.amountGte,
        amountLte: args.amountLte,

        id: args.id,
        limit: args.limit,
        offset: args.offset,
        seqnumOffset: args.seqnumOffset,
        order: args.order,
        orderField: args.orderField,
        direction: args.direction,
        categories: getCategoriesNumbersApiArray(
            args?.documentCategories || args.categories,
        ),
        parentDirectoryId,
    };
}

// @ts-expect-error TS7006 [FIXME] Comment is autogenerated
export const getArchiveList = async (args, currentUser: IUser) => {
    const { allArchiveItems } = await graph.query({
        query: /* GraphQL */ `
            query AllArchiveItems {
                allArchiveItems${graph.formatArguments(
                    mappingDocumentsArgs(args),
                )} {
                    documents {
                    ${DOCUMENT_FRAGMENT}
                    ${DOCUMENT_REVIEW_SETTING_FRAGMENT}
                    ${DOCUMENT_TAGS_FRAGMENT}
                    ${DOCUMENT_ANTIVIRUS_CHECKS_FRAGMENT}
                    ${DOCUMENT_ACCESSES_FRAGMENT}
                    comments {
                        id
                        isRejection
                        roleId
                        role {
                            company {
                                edrpou
                            }
                        }
                    }
                    reviews${graph.formatArguments({
                        add_is_last_condition: true,
                    })} {
                        ${DOCUMENT_REVIEW_FRAGMENT}
                    }
                    ${DOCUMENT_REVIEW_REQUESTS_FRAGMENT}
                    signatures {
                        roleId
                        keyOwnerEdrpou
                        stampOwnerEdrpou
                        isInternal
                        user {
                            email
                        }
                    }
                }
                    directories {
                        id
                        name
                        dateCreated
                        dateUpdated
                    }
                    count
                }
            }
        `,
    });

    const limit = Number(args.limit) || 0;
    const offset = Number(args.offset) || 0;

    return {
        hasNextPage: allArchiveItems.count > offset + limit,
        count: allArchiveItems.count,
        documents: formatDocuments(allArchiveItems.documents, currentUser),
        directories: getFormatDirectories(allArchiveItems.directories),
    };
};

export const getArchiveListBy = async ({
    args = {},
    // @ts-expect-error TS7031 [FIXME] Comment is autogenerated
    documentsFragment,
    // @ts-expect-error TS7031 [FIXME] Comment is autogenerated
    currentUser,
}) => {
    const { allArchiveItems } = await graph.query({
        query: /* GraphQL */ `
            query AllArchiveItems {
                allArchiveItems${graph.formatArguments(
                    mappingDocumentsArgs(args),
                )} {
                    documents {
                        ${documentsFragment}
                    }
                    directories {
                        id
                        name
                        dateCreated
                        dateUpdated
                    }
                    count
                }
            }
        `,
    });

    return {
        count: allArchiveItems.count,
        documents: formatDocuments(allArchiveItems.documents, currentUser),
        directories: getFormatDirectories(allArchiveItems.directories),
    };
};
