import graph from 'services/graph';
import io from 'services/io';
import { t } from 'ttag';

export const createDirectory = async ({
    name,
    parentId,
}: {
    name: string;
    parentId?: string;
}) =>
    await io.post(`/internal-api/directories`, {
        name,
        parent_id: parentId,
    });

export const deleteDirectories = async (directoriesIds: number[]) =>
    await io.post('/internal-api/delete-directories', {
        directory_ids: directoriesIds,
    });

export const changeDirectory = async ({
    id,
    name,
}: {
    id: string;
    name: string;
}) =>
    await io.patch(`/internal-api/directories/${id}`, {
        name,
    });

export const getDirectory = async (directoryId?: Nullable<number>) => {
    const { directory } = await graph.query({
        query: /* GraphQL */ `
            query GetDirectory($id: Int!) {
                directory(id: $id) {
                    id
                    name
                    path {
                        id
                        name
                    }
                }
            }
        `,
        variables: { id: directoryId },
    });

    return directory;
};

export const getAllDirectories = async ({
    parentDirectoryId,
    search,
    limit = 25,
    offset = 0,
}: {
    parentDirectoryId?: Nullable<number>;
    search?: string;
    limit?: number;
    offset?: number;
}) => {
    const { allDirectories } = await graph.query({
        query: /* GraphQL */ `
            query GetDirectories(
                $parentId: Int!
                $limit: Int!
                $offset: Int!
                $search: String
            ) {
                allDirectories(
                    parentId: $parentId
                    limit: $limit
                    search: $search
                    offset: $offset
                ) {
                    directories {
                        id
                        name
                    }
                    count
                }
            }
        `,
        variables: {
            parentId: parentDirectoryId || null,
            search,
            limit,
            offset,
        },
    });

    return allDirectories;
};

export const addToDirectory = async ({
    parentId = null,
    directoryIds = [],
    documentIds = [],
}: {
    parentId: Nullable<number>;
    directoryIds?: number[];
    documentIds?: string[];
}) =>
    await io.post('/internal-api/directories/add-to-directory', {
        parent_id: parentId,
        directory_ids: directoryIds || [],
        document_ids: documentIds || [],
    });

// @ts-expect-error TS7006 [FIXME] Comment is autogenerated
export const getFormattedDirectories = (directories) =>
    // @ts-expect-error TS7006 [FIXME] Comment is autogenerated
    directories.map((directory) => ({
        ...directory,
        category: t`Папка`,
        isDirectory: true,
    }));
