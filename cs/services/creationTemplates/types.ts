import { Template as Template<PERSON><PERSON> } from 'gql-types';

export type CollaboraWordExt = '.docx';
export type CollaboraWordExtOld = '.doc';
export type CollaboraExcelExt = '.xlsx';
export type CollaboraExcelExtOld = '.xls';

export interface CreationTemplate {
    id: TemplateApi['id'];
    title: Template<PERSON>pi['title'];
    category: string | null;
    dateCreated: Date;
    dateUpdated: Date;
    companyId: string | null;
    creatorRoleId: TemplateApi['creatorRoleId'];
    extension: CollaboraWordExt | CollaboraExcelExt;
    isFavorite: boolean;
    previewImgUrl: string | null;
    // based on TemplateApi['creatorRole']['user']
    creatorName: string;
    creatorEmail: string | null;
}

type DraftTemplateType = 'template' | 'standalone';

type DraftTemplateExt =
    | CollaboraWordExt
    | CollaboraWordExtOld
    | CollaboraExcelExt
    | CollaboraExcelExtOld;

export interface DraftTemplate {
    company_id: string;
    creator_role_id: string;
    date_created: string;
    date_updated: string;
    document_id: string | null;
    document_version_id: string | null;
    extension: DraftTemplateExt;
    id: string;
    template_id: string | null;
    type: DraftTemplateType;
}

export type CreateCreationTemplateExtension =
    | CollaboraWordExt
    | CollaboraExcelExt;

export interface CreateCreationTemplatePayload {
    title: string;
    category_id: number | null;
    extension?: CreateCreationTemplateExtension;
}

// class DocumentFromTemplateValidator(BaseModel)
export interface CreateDocumentFromTemplateWithSpecifiedFieldsPayload {
    title?: string;
    category_id?: number;
    date_document?: string;
    number?: string;
    amount?: number;
    expected_owner_signatures?: number;
    expected_recipient_signatures?: number;
    extra_fields?: Record<string, string>;
}

export type DraftListItemType = 'template' | 'version' | 'standalone';

export interface DraftListItemTemplate {
    id: string;
    title: string;
    category: string | null;
}

export interface DraftListItem {
    id: string;
    type: DraftListItemType;
    dateCreated: string;
    dateUpdated: string | null;
    dateScheduledDeletion: string | null;
    template: DraftListItemTemplate | null;
}

export interface DraftToCreatePayload {
    extension: CollaboraWordExt | CollaboraExcelExt;
}
