import { toServerDate } from 'lib/date';
import { redirect } from 'lib/navigation';
import { getArchiveListBy } from 'services/archive';
import { AccessSource } from 'services/documents/types';
import { getCategoriesNumbersApiArray } from 'services/documents/utils';

import { ACSK } from '../enums';

import {
    formatDocument,
    formatDocumentVersions,
    formatDocuments,
} from './utils';

import graph from '../graph';
import io from '../io';
import {
    getIsSharedDocumentView,
    getSignSessionId,
    prepareSignSessionOptions,
} from '../sign-session';
import { getAllDocumentRequiredFields, multiRejectDocuments } from './ts/api';
import { ES_SEARCH_DOCUMENT_LIMIT } from './ts/constants';

const DOCUMENT_DELETE_REQUEST_FRAGMENT = `
    deleteRequest {
        id
        documentId
        initiatorRoleId
        recipientsEmails
        receiverEdrpou
        initiatorEdrpou
        status
        message
        rejectMessage
        isR<PERSON>eiver
    }
`;
const DOCUMENT_RELATIVE_FRAGMENT = `
    dateCreated
    dateListing
    id
    isInput
    statusId
    displayStatusText
    title
    type
    displayCompanyEdrpou
    displayCompanyEmail
`;

// TODO: replace the "receivers" field with the "recipientId" field. In most cases, we already fetch all recipients with documents,
// so we don't need to fetch them again for each flow.
const DOCUMENT_FLOW_FRAGMENT = `
    flows {
        canSign
        edrpou
        displayCompanyName
        id
        isComplete
        dateSent
        order
        pendingSignaturesCount
        signaturesCount
        receivers
    }
`;
const DOCUMENT_RECIPIENT_FRAGMENT = `
    id
    emails
    edrpou
    isEmailsHidden
    dateSent
    dateReceived
    dateDelivered
`;

export const DOCUMENT_ANTIVIRUS_CHECKS_FRAGMENT = `
    antivirusChecks {
        id
        status
        provider
        dateCreated
        dateUpdated
    }
`;

const DRAFT_ANTIVIRUS_CHECK_FRAGMENT = `
    antivirusCheck {
        draftId
        status
        provider
        dateCreated
        dateUpdated
    }
`;

// Дивись тип: GroupMember
export const GROUP_MEMBER_FRAGMENT = `
    id
    role {
        id
        isAdmin
        user {
            id
            email
            authPhone
            firstName
            secondName
            lastName
        }
    }
`;

// зараз доступний тільки AccessSource.viewer
// відкритий доступ через ручне надання доступу (при завантаженні або редагуванні - співробітники -> доступ)
const DOCUMENT_ACCESSES_FRAGMENT = `
    accesses(source: ${AccessSource.viewer}) {
        id
        dateCreated
        roleId
        role {
            user {
                id
                email
                firstName
                secondName
                lastName
            }
        }
    }
    viewerGroups {
        id
        group {
            id
            name
            members {
                ${GROUP_MEMBER_FRAGMENT}
            }
        }
    }
`;

export const DOCUMENT_PARAMETER_FRAGMENT = `
    parameters {
        fieldId
        value
        isRequired
    }
`;

const DOCUMENT_VERSIONS_FRAGMENT = `
    versions {
        id
        name
        type
        isSent
        dateCreated
        contentHash
        contentLength
        reviewStatus
        extension
        role {
            id
            company {
                id
                name
                edrpou
            }
            user {
                email
            }
        }
        ${DOCUMENT_ANTIVIRUS_CHECKS_FRAGMENT}
    }
`;

export const DOCUMENT_DRAFTS_FRAGMENT = `
    drafts {
        id
        type
        dateCreated
        dateUpdated
        creatorRoleId
        creatorRole {
            companyId
            company {
                edrpou
                name
            }
            user {
                email
                authPhone
            }

        }
        documentId
        documentVersionId
        ${DRAFT_ANTIVIRUS_CHECK_FRAGMENT}
    }
 `;

export const GROUP_FRAGMENT = `
    id
    name
    members {
        ${GROUP_MEMBER_FRAGMENT}
    }
`;

export const DOCUMENT_SIGNERS_FRAGMENT = `
    roleId
    groupId
    groupSignerId
    assignerId
    dateSigned
    order
    role {
        canSignAndRejectDocument
        canSignAndRejectDocumentExternal
        canSignAndRejectDocumentInternal
        user {
            email
            authPhone
            firstName
            secondName
            lastName
        }
    }
    group {
        ${GROUP_FRAGMENT}
    }
    groupSignedBy {
        id
        userId
        user {
            id
            email
            authPhone
            firstName
            secondName
            lastName
        }
    }
`;

export const DOC_SCHEDULED_META_SUGGESTION = `
    scheduledMetaSuggestion {
        suggestion {
            id
            review
            category
            categoryDetails {
                id
                title
                companyId
            }
            edrpouRecipient
            recipientCompanyName
            dateDocument
            number
            amount
        }
        scheduledBy
        status
        dateUpdated
        dateCreated
    }
`;

/**
 * If change this fragment, please update the same fragment (need recheck) in the cs/components/filters/filtersActionCreators.js onExportDocumentsList
 */
const DOCUMENT_FRAGMENT = `
    id
    seqnum
    userId

    statusId
    displayStatusText
    title
    extension

    displayCompanyEdrpou
    displayCompanyEmail
    displayCompanyName
    type
    category
    categoryDetails {
        id
        title
        companyId
    }
    number
    amount
    edrpouOwner
    edrpouRecipient
    emailRecipient
    isRecipientEmailHidden

    recipients {
        ${DOCUMENT_RECIPIENT_FRAGMENT}
    }

    source
    isArchived
    isImported
    isInput

    canDelete
    isDeleteLocked

    firstSignBy
    isInternal
    isMultilateral

    isViewable

    accessLevel

    expectedSignatureFormat
    expectedOwnerSignatures
    expectedRecipientSignatures
    isOneSign
    isProtected

    reviewStatus

    dateCreated
    dateDelivered
    dateDocument
    dateListing
    dateUpdated
    dateFinished

    isInvalidSigned

    hasEUSignatures

    user {
        email
        authPhone
        firstName
        secondName
        lastName
    }

    companyOwner {
        name
    }

    companyRecipient {
        id
        name
    }

    contactRecipient {
        name
    }

    signers {
        ${DOCUMENT_SIGNERS_FRAGMENT}
    }

    children {
        creatorEdrpou
        document { ${DOCUMENT_RELATIVE_FRAGMENT} }
    }

    parent {
        creatorEdrpou
        document { ${DOCUMENT_RELATIVE_FRAGMENT} }
    }

    metadata{
        contentHash
    }

    revoke {
      id
      initiatorRoleId
      initiatorCompanyId
      reason
      status
      documentId
      signatureFormat
      signatures {
        dateCreated
        id
        isInternal
        keyAcsk
        keyCompanyFullName
        keyOwnerEdrpou
        keyOwnerFullName
        keyOwnerPosition
        keySerialNumber
        keyTimeMark
        revokeId
        roleId
        stampAcsk
        stampCompanyFullName
        stampOwnerEdrpou
        stampOwnerFullName
        stampOwnerPosition
        stampSerialNumber
        stampTimeMark
      }
      initiatorCompany {
        id
        name
        edrpou
       }
        initiatorRole {
            id
            email
            authPhone
            firstName
            secondName
            lastName
        }
    }

    ${DOCUMENT_DELETE_REQUEST_FRAGMENT}

    ${DOCUMENT_FLOW_FRAGMENT}

    ${DOCUMENT_PARAMETER_FRAGMENT}

    ${DOCUMENT_VERSIONS_FRAGMENT}

    ${DOCUMENT_DRAFTS_FRAGMENT}

    ${DOC_SCHEDULED_META_SUGGESTION}
`;
const DOCUMENT_COMMENTS_FRAGMENT = `
    comments {
        id
        accessCompanyId
        text
        isRejection
        dateCreated
        dateEdited
        roleId

        role {
            user {
                id
                email
                authPhone
                firstName
                secondName
                lastName
            }
            company {
                edrpou
            }
        }

        documentVersion {
            id
            name
        }
    }
`;
const DOCUMENT_REVIEW_FRAGMENT = `
    id
    roleId
    groupId
    type
    dateCreated
    documentId
    userEmail
    documentVersion {
        id
        name
    }

    role {
        position
        user {
            email
            authPhone
            firstName
            secondName
            lastName
        }
    }
    group {
        id
        name
        members {
            ${GROUP_MEMBER_FRAGMENT}
        }
    }
`;
const DOCUMENT_REVIEWS_FRAGMENT = `
    reviews {
        ${DOCUMENT_REVIEW_FRAGMENT}
    }
`;
const DOCUMENT_REVIEW_REQUEST_FRAGMENT = `
    id
    documentId
    fromRoleId
    toRoleId
    toGroupId
    status
    order
    dateCreated
    dateUpdated
    documentVersion {
        id
        name
    }

    toRole {
        user {
            email
            authPhone
            firstName
            secondName
            lastName
        }
    }
    toGroup {
        id
        name
        members {
            ${GROUP_MEMBER_FRAGMENT}
        }
    }
`;
const DOCUMENT_REVIEW_REQUESTS_FRAGMENT = `
    reviewRequests {
        ${DOCUMENT_REVIEW_REQUEST_FRAGMENT}
    }
`;
const DOCUMENT_REVIEW_SETTING_FRAGMENT = `
    reviewSetting {
        isRequired
        isParallel
    }
`;
const DOCUMENT_TAGS_FRAGMENT = `
    tags {
        id
        name
        canAssign
    }
`;
const DOCUMENT_SIGNATURES_FRAGMENT = `
    signatures {
        id
        roleId
        dateCreated
        keyAcsk
        keySerialNumber
        keyTimeMark
        keyCompanyFullName
        keyOwnerEdrpou
        keyOwnerFullName
        keyOwnerPosition
        keyIsLegal
        stampAcsk
        stampSerialNumber
        stampTimeMark
        stampCompanyFullName
        stampOwnerEdrpou
        stampOwnerFullName
        stampOwnerPosition
        stampIsLegal
        isInternal
        isValid
        userEmail
        user {
            email
            firstName
            secondName
            lastName
        }
    }
`;

const DOCUMENT_RECIPIENTS_FRAGMENT = `
    recipients {
        id,
        edrpou,
        emails,
        isEmailsHidden,
    }
`;

const DOCUMENT_MULTI_SIGN_FRAGMENT = `
    ${DOCUMENT_REVIEWS_FRAGMENT}
    ${DOCUMENT_FRAGMENT}
    ${DOCUMENT_FLOW_FRAGMENT}
    ${DOCUMENT_REVIEW_SETTING_FRAGMENT}
    ${DOCUMENT_SIGNATURES_FRAGMENT}
    ${DOCUMENT_VERSIONS_FRAGMENT}
`;

function formatSearchArg(option) {
    // ignore null, undefined and empty string
    if (option == null || option === '') return null;

    return Array.isArray(option) ? option : [option];
}

function getLocationSearchArgs(query) {
    return {
        search: query.q_search,
        searchTitle: formatSearchArg(query.q_title),
        searchNumber: formatSearchArg(query.q_number),
        searchTag: formatSearchArg(query.q_tag),
        searchCompanyName: formatSearchArg(query.q_company_name),
        searchCompanyEdrpou: formatSearchArg(query.q_company_edrpou),
        searchUserEmail: formatSearchArg(query.q_user_email),
        searchParameter: formatSearchArg(query.q_parameter),
    };
}

function mappingCountDocumentsArgs(args) {
    return {
        search: args.search,
        searchTitle: args.searchTitle,
        searchNumber: args.searchNumber,
        searchTag: args.searchTag,
        searchCompanyName: args.searchCompanyName,
        searchCompanyEdrpou: args.searchCompanyEdrpou,
        searchUserEmail: args.searchUserEmail,
        searchParameter: args.searchParameter,
        userEmails: args.userEmails,
        folderId: args.folderId,
        statusId: args.statusId,
        hasComments: args.hasComments,
        isWaitMySign: args.isWaitMySign,
        isOneSign: args.isOneSign,
        hasDateDelivered: args.hasDateDelivered,
        withoutTags: args.withoutTags,
        sortDate: args.sortDate,
        tag: args.tag,
        reviewFolder: args.reviewFolder,
        gte: args.dateFrom,
        lte: args.dateTo,
        ids: args.ids,
        conditions2: args.conditions2,
        categories: args.categories,
        amountGte: args.amountGte,
        amountLte: args.amountLte,
        isMyInvalidSigned: args.isMyInvalidSigned,
        isPartnerInvalidSigned: args.isPartnerInvalidSigned,
        invalidSignedRoles: args.invalidSignedRoles,
        accessLevel: args.accessLevel,
    };
}

function mappingDocumentsArgs(args) {
    return {
        ...mappingCountDocumentsArgs(args),
        id: args.id,
        limit: args.limit,
        offset: args.offset,
        seqnumOffset: args.seqnumOffset,
        order: args.order,
        orderField: args.orderField,
        direction: args.direction,
        categories: getCategoriesNumbersApiArray(
            args?.documentCategories || args.categories,
        ),
    };
}

// GRAPH
async function getDocument(id, currentUser, withError = false) {
    const data = await graph.get(
        `
        {
            document${graph.formatArguments({ id, withError })} {
                ${DOCUMENT_FRAGMENT}
                ${DOCUMENT_COMMENTS_FRAGMENT}
                ${DOCUMENT_SIGNATURES_FRAGMENT}
                ${DOCUMENT_TAGS_FRAGMENT}
                ${DOCUMENT_RECIPIENTS_FRAGMENT}
                ${DOCUMENT_ANTIVIRUS_CHECKS_FRAGMENT}
                ${DOCUMENT_ACCESSES_FRAGMENT}
                reviews${graph.formatArguments({
                    add_is_last_condition: true,
                })} {
                    ${DOCUMENT_REVIEW_FRAGMENT}
                }
                ${DOCUMENT_REVIEW_REQUESTS_FRAGMENT}
                ${DOCUMENT_REVIEW_SETTING_FRAGMENT}
            }
        }
    `,
        '',
        prepareSignSessionOptions(),
    );
    return data.document && formatDocument(data.document, currentUser);
}

const getCompaniesWhereAccessDocument = async (documentId) => {
    const data = await graph.query({
        query: /* GraphQL */ `
            query GetDocumentAvailableRoles($documentId: ID!) {
                documentAvailableRoles(documentId: $documentId) {
                    id
                    company {
                        id
                        edrpou
                        name
                    }
                }
            }
        `,
        variables: { documentId },
    });

    return data.documentAvailableRoles;
};

async function getDocumentVersions(id, currentUser) {
    const data = await graph.get(
        `
        {
            document${graph.formatArguments({ id })} {
                id
                signatures {
                    id
                }
                reviews${graph.formatArguments({
                    add_is_last_condition: true,
                })} {
                    ${DOCUMENT_REVIEW_FRAGMENT}
                }
                ${DOCUMENT_VERSIONS_FRAGMENT}
            }
        }
    `,
        '',
        prepareSignSessionOptions(),
    );
    return (
        data.document && {
            ...data.document,
            versions: formatDocumentVersions(data.document, currentUser),
        }
    );
}

async function getDocumentSignCount(id) {
    const data = await graph.get(
        `
        {
            document${graph.formatArguments({ id })} {
                signatures {
                    id
                }
            }
        }
    `,
        '',
        prepareSignSessionOptions(),
    );
    return data.document && data.document.signatures.length;
}

/**
 * @param {object} args
 * @param {string[]} args.ids
 * @param {number} [args.limit = 0]
 * @param {number} [args.offset = 0]
 * @param {IUser} currentUser
 * @return {Promise<{hasNextPage: boolean, documents: Document[], count: number}>}
 */
async function getDocuments(args, currentUser) {
    const query = `
        {
            allDocuments${graph.formatArguments(mappingDocumentsArgs(args))} {
                documents {
                    ${DOCUMENT_FRAGMENT}
                    ${DOCUMENT_REVIEW_SETTING_FRAGMENT}
                    ${DOCUMENT_TAGS_FRAGMENT}
                    ${DOCUMENT_ANTIVIRUS_CHECKS_FRAGMENT}
                    ${DOCUMENT_ACCESSES_FRAGMENT}
                    comments {
                        id
                        isRejection
                        roleId
                        role {
                            company {
                                edrpou
                            }
                        }
                    }
                    reviews${graph.formatArguments({
                        add_is_last_condition: true,
                    })} {
                        ${DOCUMENT_REVIEW_FRAGMENT}
                    }
                    ${DOCUMENT_REVIEW_REQUESTS_FRAGMENT}
                    signatures {
                        roleId
                        keyOwnerEdrpou
                        stampOwnerEdrpou
                        isInternal
                        userEmail
                        user {
                            email
                        }
                    }
                }
                count
            }
        }
    `;
    const {
        allDocuments: { documents, count },
    } = await graph.get(query);

    const limit = Number(args.limit) || 0;
    const offset = Number(args.offset) || 0;

    return {
        hasNextPage: count > offset + limit,
        count,
        documents: formatDocuments(documents, currentUser),
    };
}

async function getDocumentComments(id) {
    const data = await graph.get(`
        {
            document${graph.formatArguments({ id })} {
                ${DOCUMENT_COMMENTS_FRAGMENT}
            }
        }
    `);
    const sortedComments = Array.from(
        data.document.comments,
    ).sort((curr, prev) => curr.dateCreated.localeCompare(prev.dateCreated));
    return sortedComments || [];
}

async function getDocumentsBy(
    args = {},
    fragment,
    origin = '',
    requestOptions = {},
    currentUser,
) {
    const {
        allDocuments: { documents },
    } = await graph.get(
        `
        {
            allDocuments${graph.formatArguments(mappingDocumentsArgs(args))} {
                documents {
                    ${fragment}
                }
            }
        }
    `,
        origin,
        requestOptions,
    );

    return formatDocuments(documents, currentUser);
}

async function getDocumentsByPartially({
    args = {},
    ids = [],
    fragment,
    origin = '',
    requestOptions = {},
    isEsSearchEnabled = false,
    currentUser,
    isArchive = false,
    parentDirectoryId = null,
}) {
    // Get from oldest document, to ensure the immutability of the documents list
    // if new documents will be uploaded in same time
    const requiredArgs = { direction: 'asc' };
    const isUsingIds = ids.length > 0;
    const limit = isUsingIds ? 200 : 500;
    const overLimit = limit + 1;
    const documents = [];
    const directories = [];
    let downloadNumber = 0;
    let seqnumOffset = 0;

    // eslint-disable-next-line no-constant-condition
    while (true) {
        const offset = downloadNumber * limit;
        const isPenultOffset = ES_SEARCH_DOCUMENT_LIMIT - limit === offset;

        let getDocumentArgs = {};
        if (isUsingIds) {
            getDocumentArgs = {
                ids: ids.slice(offset, offset + overLimit),
                ...requiredArgs,
            };
        } else if (isEsSearchEnabled) {
            getDocumentArgs = {
                ...args,
                seqnumOffset,
                order: 'seqnum',
                limit: isPenultOffset ? limit : overLimit,
                ...requiredArgs,
            };
        } else {
            getDocumentArgs = {
                ...args,
                offset,
                limit: overLimit,
                ...requiredArgs,
            };
        }

        if (parentDirectoryId) {
            getDocumentArgs = {
                ...args,
                parentDirectoryId,
            };
        }

        if (isArchive) {
            const {
                directories: currentDirectories,
                documents: currentDocuments,
            } = await getArchiveListBy({
                args: getDocumentArgs,
                documentsFragment: fragment,
                currentUser,
            });
            const currentItems = [...currentDirectories, ...currentDocuments];

            if (currentItems.length > limit) {
                // There is more documents, continue to get them
                const overlimitItems = currentItems.pop();
                documents.push(...currentDocuments);
                directories.push(...currentDirectories);
                downloadNumber += 1;
                seqnumOffset = overlimitItems.seqnum;
            } else {
                // Reached last documents, stop the loop
                documents.push(...currentDocuments);
                directories.push(...currentDirectories);
                break;
            }
        }

        if (!isArchive) {
            const currentDocuments = await getDocumentsBy(
                getDocumentArgs,
                fragment,
                origin,
                requestOptions,
                currentUser,
            );

            if (currentDocuments.length > limit) {
                // There is more documents, continue to get them
                const overlimitDocument = currentDocuments.pop();
                documents.push(...currentDocuments);
                downloadNumber += 1;
                seqnumOffset = overlimitDocument.seqnum;
            } else {
                // Reached last documents, stop the loop
                documents.push(...currentDocuments);
                break;
            }
        }
    }

    // Ensure that there are no duplicates
    return [...new Set(directories), ...new Set(documents)];
}

async function getReviewHistory(id) {
    const {
        document: { reviews, reviewRequests },
    } = await graph.get(
        `
        {
            document${graph.formatArguments({ id })} {
                ${DOCUMENT_REVIEWS_FRAGMENT}
                reviewRequests${graph.formatArguments({
                    is_all_requests: true,
                })} {
                    ${DOCUMENT_REVIEW_REQUEST_FRAGMENT}

                    fromRole {
                        user {
                            email
                            firstName
                            secondName
                            lastName
                        }
                    }
                }
            }
        }
    `,
        '',
        prepareSignSessionOptions(),
    );
    return { reviews, reviewRequests };
}

// API METHODS
async function rejectDocument(docId, comment) {
    return await io.post(
        `/internal-api/documents/${docId}/reject`,
        { text: comment },
        false,
        prepareSignSessionOptions(),
    );
}

async function sendDocument(
    docId,
    emails = null,
    edrpou = null,
    origin = null,
    isEmailHidden = false,
    isWithoutData = false,
) {
    const isSharedDocumentViewMode = getIsSharedDocumentView(location.pathname);
    const signSessionId = getSignSessionId();

    origin = origin || location.origin || '';

    const data = isWithoutData
        ? {}
        : { emails, edrpou, is_email_hidden: isEmailHidden };

    if (isSharedDocumentViewMode) {
        return await io.post(
            `/shared-document-view/${docId}/send?ssid=${signSessionId}`,
            data,
            false,
            prepareSignSessionOptions(),
        );
    }

    return await io.post(
        `${origin}/internal-api/documents/${docId}/send`,
        data,
        false,
        prepareSignSessionOptions(),
    );
}

async function sharedDocumentViewSign({ docId, formData, getParameters }) {
    const signSessionId = getSignSessionId();

    const resp = await io.post(
        `/shared-document-view/${docId}?ssid=${signSessionId}`,
        formData,
        false,
        prepareSignSessionOptions(),
        getParameters,
    );

    const data = await resp.json();
    const next_url = data.next_url;

    setTimeout(() => {
        redirect(next_url);
    }, 2000);

    return resp;
}

async function signDocument(
    docId,
    {
        emailRecipient,
        edrpouRecipient,
        pkSign,
        pkSignAcsk,
        stampSign,
        stampSignAcsk,
        p7s,
        archive,
        keyCompanyName,
    },
    keys,
    signatureFormat,
    getParameters = {},
    isSignAnnulmentAct = false,
    annulmentActID = '',
) {
    const isSharedDocumentViewMode = getIsSharedDocumentView(location.pathname);

    let data = {
        archive,
        // todo: use `emails_recipient` that accept array of emails, instead of string of comma separated emails
        email_recipient: emailRecipient || null,
        edrpou_recipient: edrpouRecipient || null,
        key: pkSign,
        format: signatureFormat,
        company_name: keyCompanyName,
    };

    if (stampSign) {
        data = {
            ...data,
            stamp: stampSign,
        };
    }

    const formData = new FormData();
    const acsk =
        pkSignAcsk === ACSK.CESARIS || stampSignAcsk === ACSK.CESARIS
            ? ACSK.CESARIS
            : ACSK.DEFAULT;
    formData.append('data', JSON.stringify(data));
    formData.append('acsk', acsk);
    // if it's internal signature format, pass p7s field (useful info)
    if (p7s) {
        const blob = new Blob([p7s], { type: 'application/octet-stream' });
        formData.append('p7s', blob, 'p7s');
    }

    if (isSharedDocumentViewMode) {
        return await sharedDocumentViewSign({ docId, formData, getParameters });
    } else {
        return await io.post(
            isSignAnnulmentAct
                ? `/internal-api/documents/revoke/${annulmentActID}/sign`
                : `/internal-api/documents/${docId}/signatures`,
            formData,
            false,
            prepareSignSessionOptions(),
            getParameters,
        );
    }
}

/**
 * Convert office document (docx, xlsx, ...) to pdf. This method will create new version to the document.
 *
 * @param {string} docId
 * @returns {Promise<*>}
 */
async function convertOfficeDocumentToPdf(docId) {
    return await io.post(
        `/internal-api/documents/${docId}/office-to-pdf`,
        prepareSignSessionOptions(),
    );
}

async function changeDocumentRecipient(
    docId,
    emails,
    edrpou,
    isEmailsHidden = false,
) {
    // TODO: replace with general update document API `PATCH /internal-api/documents/${docId}`
    // because on the backend we use the hood we reuse update document logic for this endpoint
    return await io.post(`/internal-api/documents/${docId}/change-recipient`, {
        emails,
        edrpou,
        is_emails_hidden: isEmailsHidden,
    });
}

async function findRecipientEmail(docId, edrpous) {
    return await io.post(
        `/internal-api/documents/recipients/emails`,
        { edrpous, document_id: docId },
        true,
    );
}

async function commentDocument(docId, comment, isInternal = false) {
    return await io.post(
        `/internal-api/documents/${docId}/comments`,
        { text: comment, is_internal: isInternal },
        false,
        prepareSignSessionOptions(),
    );
}

async function commentVersionedDocument(
    docId,
    comment,
    isInternal = false,
    versionId,
) {
    return await io.post(
        `/internal-api/documents/${docId}/comments`,
        {
            text: comment,
            is_internal: isInternal,
            version: versionId || 'latest',
        },
        false,
        prepareSignSessionOptions(),
    );
}

async function deleteDocument(id) {
    return await io.request('DELETE', `/internal-api/documents/${id}`);
}

async function deleteDocuments() {
    return await io.request('DELETE', '/internal-api/documents');
}

async function getEmptySignatures() {
    return await io.getAsJson('/internal-api/signatures/empty');
}

async function updateEmptySignature(
    signId,
    pkSignInfo,
    stampSignInfo = undefined,
) {
    let data = {
        key_serial_number: pkSignInfo.serialNumber,
        key_owner_fullname: pkSignInfo.ownerFullName,
        key_owner_edrpou: pkSignInfo.edrpou,
        key_timemark: toServerDate(pkSignInfo.timemark),
        key_acsk: pkSignInfo.caServer,
    };
    if (stampSignInfo) {
        data = {
            ...data,
            stamp_serial_number: stampSignInfo.serialNumber,
            stamp_owner_fullname: stampSignInfo.ownerFullName,
            stamp_owner_edrpou: stampSignInfo.edrpou,
            stamp_timemark: toServerDate(stampSignInfo.timemark),
            stamp_acsk: stampSignInfo.caServer,
        };
    }
    await io.post(`/internal-api/signatures/empty/${signId}`, data);
}

async function downloadPdf(docId, { ext, versionId } = {}) {
    const isXml = ext === 'xml';
    const options = prepareSignSessionOptions();
    const request = isXml
        ? io.post(
              `/internal-api/documents/${docId}/xml-to-pdf`,
              { force: true },
              false,
              options,
          )
        : io.get(
              `/downloads/${docId}/print${
                  versionId ? `?version=${versionId}` : ''
              }`,
              options,
          );
    const response = await request;
    await io.download(response);
}

async function downloadSignSummary(docId) {
    const response = await io.get(
        `/internal-api/documents/${docId}/sign-summary`,
    );
    await io.download(response);
}

async function reviewDocument(data) {
    return await io.post(`/internal-api/reviews`, data);
}

async function reviewDocuments(data) {
    return await io.post(`/internal-api/reviews-batch`, data, true);
}

async function deleteDocumentReview(docId) {
    return await io.request('DELETE', `/internal-api/${docId}/reviews`);
}

function getReviewHistoryUrl(docId, format) {
    return `/downloads/documents/${docId}/reviews/history.${format}`;
}

async function downloadReviewsHistoryXLSX(docId) {
    const url = getReviewHistoryUrl(docId, 'xlsx');
    const response = await io.get(url);
    await io.download(response);
}

async function downloadMultiplyFiles(data) {
    return await io.post('/downloads/multi-archive', data, true);
}

async function downloadArchivedDocuments({ documentIds, directoryIds }) {
    return await io.post(
        '/downloads/archived-documents',
        {
            document_ids: documentIds,
            directory_ids: directoryIds,
        },
        true,
    );
}

async function createDocumentTags(data) {
    return await io.request('POST', '/internal-api/documents/tags', data);
}

async function connectDocumentsAndTags(data) {
    return await io.request(
        'POST',
        '/internal-api/documents/tags/connections',
        data,
    );
}

async function disconnectDocumentsAndTags(data) {
    return await io.request(
        'DELETE',
        '/internal-api/documents/tags/connections',
        data,
    );
}

async function createDeleteRequest(docIds, message) {
    return await io.request('POST', '/internal-api/documents/delete-request', {
        document_ids: docIds,
        message,
    });
}

async function rejectDeleteRequest(requestIds, rejectMessage) {
    return await io.request(
        'POST',
        '/internal-api/documents/reject-delete-request',
        {
            delete_request_ids: requestIds,
            reject_message: rejectMessage,
        },
    );
}

async function cancelDeleteRequestVote(requestIds) {
    return await io.request(
        'POST',
        '/internal-api/documents/cancel-delete-vote',
        {
            delete_request_ids: requestIds,
        },
    );
}

async function acceptDeleteRequest(requestIds) {
    return await io.request(
        'POST',
        '/internal-api/documents/accept-delete-request',
        {
            delete_request_ids: requestIds,
        },
    );
}

/**
 * @param {DocumentListDocumentItem[]} docs
 * @param {AddRecipientsRequestPayload} receivers
 * @returns {Promise<*|{ok}|Response>}
 */
async function addFlow(docs, receivers) {
    return await io.request(
        'POST',
        '/internal-api/flows/',
        { docs, receivers },
        false,
        prepareSignSessionOptions(),
    );
}

async function cancelDeleteRequest(documentsIds) {
    return await io.request('DELETE', '/internal-api/cancel-delete-requests', {
        documents_ids: documentsIds,
    });
}

async function shareDocument(documentId, rolesIds, comment) {
    return await io.request(
        'PATCH',
        `/internal-api/documents/${documentId}/accesses`,
        {
            roles_ids: rolesIds,
            comment: comment || undefined,
        },
    );
}

async function deleteLink(parentId, childId) {
    return await io.request(
        'DELETE',
        `/internal-api/documents/${parentId}/child/${childId}`,
    );
}

async function addLink(parentId, childId) {
    return await io.request(
        'POST',
        `/internal-api/documents/${parentId}/child/${childId}`,
    );
}

async function addLinkGroup(parentId, childrenIds) {
    return await io.request(
        'POST',
        `/internal-api/documents/${parentId}/children`,
        {
            children_ids: childrenIds,
        },
    );
}

async function addRefDoc(parentId, childId, type) {
    return await io.request(
        'POST',
        `/internal-api/documents/ttn/${parentId}/child/${childId}`,
        {
            type,
        },
    );
}

async function deleteRefDoc(parentId, childId) {
    return await io.request(
        'DELETE',
        `/internal-api/documents/ttn/${parentId}/child/${childId}`,
    );
}

async function uploadNewDocumentVersion(docId, file) {
    return await io.postFile(`/internal-api/document/${docId}/version`, file);
}

async function deleteDocumentVersion(docId, versionId) {
    return await io.request(
        'DELETE',
        `/internal-api/document/${docId}/version/${versionId}`,
    );
}

const getAllDocumentsCount = async () => {
    return (
        await graph.query({
            query: /* GraphQL */ `
                {
                    currentRole {
                        company {
                            usedDocumentCount
                        }
                    }
                }
            `,
        })
    ).currentRole.company.usedDocumentCount;
};

export {
    addFlow,
    addLink,
    addLinkGroup,
    addRefDoc,
    cancelDeleteRequest,
    changeDocumentRecipient,
    commentDocument,
    commentVersionedDocument,
    connectDocumentsAndTags,
    convertOfficeDocumentToPdf,
    createDocumentTags,
    deleteDocument,
    deleteDocumentReview,
    deleteDocuments,
    deleteLink,
    deleteRefDoc,
    disconnectDocumentsAndTags,
    downloadPdf,
    downloadReviewsHistoryXLSX,
    findRecipientEmail,
    getDocument,
    getDocumentVersions,
    getDocumentComments,
    getDocumentSignCount,
    getDocuments,
    getDocumentsBy,
    getDocumentsByPartially,
    getEmptySignatures,
    getLocationSearchArgs,
    getReviewHistory,
    getReviewHistoryUrl,
    getAllDocumentRequiredFields,
    shareDocument,
    rejectDocument,
    multiRejectDocuments,
    reviewDocument,
    reviewDocuments,
    sendDocument,
    signDocument,
    updateEmptySignature,
    downloadMultiplyFiles,
    downloadArchivedDocuments,
    createDeleteRequest,
    rejectDeleteRequest,
    cancelDeleteRequestVote,
    acceptDeleteRequest,
    downloadSignSummary,
    DOCUMENT_REVIEW_FRAGMENT,
    DOCUMENT_FRAGMENT,
    DOCUMENT_FLOW_FRAGMENT,
    DOCUMENT_REVIEWS_FRAGMENT,
    DOCUMENT_REVIEW_SETTING_FRAGMENT,
    DOCUMENT_SIGNATURES_FRAGMENT,
    DOCUMENT_VERSIONS_FRAGMENT,
    DOCUMENT_TAGS_FRAGMENT,
    DOCUMENT_ACCESSES_FRAGMENT,
    DOCUMENT_REVIEW_REQUESTS_FRAGMENT,
    DOCUMENT_MULTI_SIGN_FRAGMENT,
    uploadNewDocumentVersion,
    deleteDocumentVersion,
    getCompaniesWhereAccessDocument,
    getAllDocumentsCount,
};
