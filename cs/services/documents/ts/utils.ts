import {
    NeedFields,
    WithoutRequiredFieldsDoc,
} from 'components/DocumentsRequiredFieldsError/types';
import { formatDateTime } from 'components/document/utils';
import { DocumentListDocumentItem } from 'components/documentList/types';
import { msgid, ngettext } from 'ttag';

import { Nullable } from '../../../types/general';
import { ICompany, IUser } from '../../../types/user';
import {
    DocumentCategory,
    DocumentReviewType,
    DocumentStatus,
} from '../../enums';
import { SignDocValidationResultType } from './enums';
import {
    AllDocumentRequiredFields,
    Document,
    DocumentVersion,
    Draft,
    SignDocValidationResult,
    Signer,
    TitleDoc,
} from './types';

import {
    canCurrentCompanySignDocument,
    canSignByOrder,
    canSignIfDocumentIsOneSignType,
    canUndoReject,
    getRemainSideSignaturesCount,
    hasNeededRolesSignatures,
    isReadyToSignAfterReview,
} from '../utils';

import { getAllDocumentRequiredFields } from '../api';
import { DOCUMENT_VERSION_STATUS_INFO } from './constants';

// import { getAllDocumentRequiredFields } from '../api';

export const isDocFinished = (doc: DocumentListDocumentItem): boolean =>
    doc.statusId === DocumentStatus.FINISHED ||
    doc.statusId === DocumentStatus.REJECT;

export const isDocSigned = (doc: DocumentListDocumentItem): boolean => {
    return doc.statusId === DocumentStatus.SIGNED;
};

export const isDocApproved = (doc: DocumentListDocumentItem): boolean => {
    return doc.statusId === DocumentStatus.APPROVED;
};

export const isDocSignedAndSent = (doc: DocumentListDocumentItem): boolean => {
    return doc.statusId === DocumentStatus.SIGNED_AND_SENT;
};

const isUserDocSigner = (
    doc: DocumentListDocumentItem,
    roleId: string,
): boolean => {
    if (!doc.signers || !doc.signers.length) {
        return true;
    }
    return doc.signers
        .map((signer: Signer) => {
            if (signer.groupId) {
                return signer.group.members.map((member) => member.role.id);
            } else return signer.roleId;
        })
        .flat()
        .includes(roleId);
};

const isDocRecipientsMissing = (doc: DocumentListDocumentItem): boolean => {
    if (doc.isInternal || doc.isInput) {
        return false;
    }
    if (doc.isMultilateral) {
        return (doc.flows || []).length === 0;
    }
    return !(
        doc.edrpouRecipient &&
        (doc.emailRecipient || doc.isRecipientEmailHidden)
    );
};

export const canSignDoc = (
    doc: DocumentListDocumentItem,
    roleId: string,
    edrpou: string,
): SignDocValidationResultType => {
    if (isDocRecipientsMissing(doc)) {
        return SignDocValidationResultType.MISSING_RECIPIENT;
    }
    if (!isReadyToSignAfterReview(doc)) {
        return SignDocValidationResultType.WAIT_REQUIRED_REVIEW;
    }
    if (!canSignIfDocumentIsOneSignType(doc, edrpou)) {
        return SignDocValidationResultType.ONE_SIGN_DOCUMENT_ONLY_FOR_CONTR_AGENT;
    }
    if (!canCurrentCompanySignDocument(doc, roleId, edrpou)) {
        return SignDocValidationResultType.ON_RECIPIENT_SIDE;
    }
    if (!isUserDocSigner(doc, roleId)) {
        return SignDocValidationResultType.NOT_DOCUMENT_SIGNER;
    }
    if (!canSignByOrder(doc, roleId, edrpou)) {
        return SignDocValidationResultType.WAIT_SIGN_ORDER;
    }
    return SignDocValidationResultType.OK;
};

export const validateSignDocs = (
    docs: DocumentListDocumentItem[],
    roleId: string,
    edrpou: string,
): SignDocValidationResult[] => {
    return docs.map((doc) => ({
        doc,
        validation: canSignDoc(doc, roleId, edrpou),
    })) as SignDocValidationResult[];
};

export const canSignDocs = (
    docs: DocumentListDocumentItem[],
    roleId: string,
    edrpou: string,
): boolean => {
    return docs.some(
        (doc) =>
            canSignDoc(doc, roleId, edrpou) === SignDocValidationResultType.OK,
    );
};

export const buildDocTitle = (doc: TitleDoc): string => {
    const base = doc.type || doc.title;
    if (doc.number) {
        return `${base} ${doc.number}`;
    } else {
        return base;
    }
};

export const getDocumentWordByCount = (count: number): string => {
    return ngettext(msgid`документ`, `документи`, `документів`, count);
};

export const getDirectoryWordByCount = (count: number): string => {
    return ngettext(msgid`папка`, `папки`, `папок`, count);
};

export const getReviewersWithType = (
    doc: Pick<DocumentListDocumentItem, 'reviews'>,
) => doc.reviews?.filter((review) => review.type) || [];

export const getReviewsEmailsSet = (doc: DocumentListDocumentItem) => {
    const reviewersWithType = getReviewersWithType(doc);

    return new Set(
        reviewersWithType?.length
            ? reviewersWithType.map((review) => review.role.user.email)
            : [],
    );
};

export const getReviewsGroupsId = (doc: DocumentListDocumentItem) => {
    const reviewersWithType = getReviewersWithType(doc);
    return reviewersWithType.length
        ? reviewersWithType.map((review) => review.type && review.groupId)
        : [];
};

export const getActiveReviews = (doc: DocumentListDocumentItem) => {
    const reviewsEmailsSet = getReviewsEmailsSet(doc);
    const reviewsGroupsId = getReviewsGroupsId(doc);

    return (doc.reviewRequests || []).filter((request) => {
        if (request.toRoleId) {
            return !reviewsEmailsSet.has(request.toRole.user.email);
        }
        if (request.toGroupId) {
            return !reviewsGroupsId.includes(request.toGroupId);
        }
    });
};

export const getPendingReviewCount = (doc: DocumentListDocumentItem) => {
    return getActiveReviews(doc).length;
};

export const getIsReviewParallel = (
    doc: Pick<DocumentListDocumentItem, 'reviewSetting'>,
): boolean => (doc.reviewSetting ? doc.reviewSetting.isParallel : true);

export const getApprovedReviewCount = (doc: DocumentListDocumentItem) =>
    getReviewersWithType(doc).filter(
        ({ type }) => type === DocumentReviewType.APPROVE,
    ).length;

export const getRejectedReviewCount = (doc: DocumentListDocumentItem) =>
    getReviewersWithType(doc).filter(
        ({ type }) => type === DocumentReviewType.REJECT,
    ).length;

export const getReviewByRoleId = (
    doc: Pick<DocumentListDocumentItem, 'reviews'>,
    roleId: string,
) => getReviewersWithType(doc).find((review) => review.roleId === roleId);

export const isRoleIdReviewApproved = (
    doc: Pick<DocumentListDocumentItem, 'reviews'>,
    roleId: string,
) =>
    doc.reviews.some(
        (review) =>
            review.roleId === roleId &&
            review.type === DocumentReviewType.APPROVE,
    );

export const isRoleIdReviewRejected = (
    doc: Pick<DocumentListDocumentItem, 'reviews'>,
    roleId: string,
) =>
    doc.reviews.some(
        (review) =>
            review.roleId === roleId &&
            review.type === DocumentReviewType.REJECT,
    );

export const getIsActualDocumentVersionPage = (
    documentActualVersion?: DocumentVersion,
    locationVersionId?: string,
) =>
    locationVersionId ? locationVersionId === documentActualVersion?.id : true;

export const getIsActualDraftDocumentPage = (
    documentActualDraft?: Draft,
    locationDraftId?: string,
) => (locationDraftId ? locationDraftId === documentActualDraft?.id : false);

export const getIsRoleIdInReviewGroups = (
    doc: DocumentListDocumentItem,
    roleId: string,
) => {
    const reviewRequests = getActiveReviews(doc);

    return reviewRequests.some(
        (request) =>
            request.toGroupId &&
            request.toGroup?.members.some(
                (member) => member.role.id === roleId,
            ),
    );
};

export const getReviewsByVersion = (
    doc: DocumentListDocumentItem | Document,
    versionId: string,
) => doc.reviews.filter((review) => review.documentVersion?.id === versionId);

export const getIsVersionedDocument = (doc?: Pick<Document, 'versions'>) =>
    doc && doc.versions.length > 0;

export const getIsSignedDocument = (doc: DocumentListDocumentItem | Document) =>
    doc.signatures.length > 0;

// after sign or reject, the document goes to the normal flow
export const getIsVersionedDocumentFlow = (
    doc?: DocumentListDocumentItem | Document,
) =>
    Boolean(
        doc &&
            getIsVersionedDocument(doc) &&
            !getIsSignedDocument(doc) &&
            doc.statusId !== DocumentStatus.REJECT,
    );

// if needed add here another fields by version
export const getDocumentByVersion = (
    doc: DocumentListDocumentItem,
    versionId: string,
) => ({
    ...doc,
    reviews: getReviewsByVersion(doc, versionId),
});

const getIsDocLatestVersion = (
    doc: DocumentListDocumentItem,
    version?: DocumentVersion,
) => version && getLatestDocumentVersionAnyType(doc)?.id === version.id;

/**
 * When user upload and send "new_upload" or "editor_created" document version
 * then we flip recipient and owner roles.
 *
 * For example, if recipient upload new document version, then he becomes owner
 * and previous owner becomes recipient.
 */
const isDocumentVersionChangeSide = (version: DocumentVersion) =>
    version.type === 'new_upload' || version.type == 'editor_created';

// It can be both "new_upload" and "convert_format" types
export const getLatestDocumentVersionAnyType = (doc: Document) =>
    doc.versions[0];

export const getIsCurrentCompanyShouldStartSigningVersionedDocument = (
    doc: Document,
) => {
    if (doc.isInternal) {
        return true;
    }

    const foundVersion = doc.versions.find((version) =>
        isDocumentVersionChangeSide(version),
    );
    if (!foundVersion) {
        return false;
    }

    return foundVersion.isIncoming;
};

export const documentVersionsHasDraft = (
    doc: Document,
    isVersionedDocument: boolean,
) => {
    if (!isVersionedDocument) {
        return;
    }

    const documentVersionIds = doc.versions.map((version) => version.id);

    return doc.drafts.find((draft) =>
        documentVersionIds.includes(draft.documentVersionId),
    );
};

export const getIsCanSignDocumentVersion = (
    doc: Document,
    roleId: string,
    currentCompanyEdrpou: string,
    versionId?: string,
) => {
    const latestVersion = getLatestDocumentVersionAnyType(doc);
    if (!latestVersion) {
        return false;
    }

    // check that user selected the latest version for signing
    if (versionId && latestVersion.id !== versionId) {
        return false;
    }

    if (documentVersionsHasDraft(doc, true)) {
        return false;
    }

    return (
        getIsCurrentCompanyShouldStartSigningVersionedDocument(doc) &&
        canSignByOrder(doc, roleId, currentCompanyEdrpou) &&
        canUndoReject(doc, roleId)
    );
};

export const getIsCanSendDocumentVersion = (
    doc: DocumentListDocumentItem,
    versionId?: string,
) => {
    const version = versionId
        ? doc.versions.find((item) => item.id === versionId)
        : doc.versions[0];

    const isActualVersion = getIsDocLatestVersion(doc, version);
    const isIncomingVersion = version?.isIncoming;
    const isSent = version?.isSent;

    return isActualVersion && !isIncomingVersion && !isSent;
};

export const getDocumentVersionStatusInfo = (version: DocumentVersion) => {
    let statusInfo = null;
    const isCurrentCompanyOwner = !version.isIncoming;

    if (!version.isIncoming && !version.isSent) {
        statusInfo = DOCUMENT_VERSION_STATUS_INFO.DOWNLOADED;
    }

    if (version.isSent) {
        statusInfo = DOCUMENT_VERSION_STATUS_INFO.SENT;
    }
    return (
        statusInfo && {
            title:
                statusInfo[
                    isCurrentCompanyOwner ? 'ownerTitle' : 'recipientTitle'
                ],
            color: statusInfo.color,
        }
    );
};

export const getIsIncomingVersion = (
    version: DocumentVersion,
    currentUser: IUser,
) => {
    switch (version.type) {
        case 'converted_format':
            // випадок якщо компанія конвертує версію документу - вона базується на версії власника - тому цю версію слід вважати вхідною
            return (
                version.role.company.id === currentUser.currentRole.company.id
            );
        // якщо користувач завантажив нову версію документу або створив через редактор, це вихідна версія
        case 'editor_created':
        case 'new_upload':
        default:
            return (
                version.role.company.id !== currentUser.currentRole.company.id
            );
    }
};

export const getDocumentVersionHash = (
    doc: Pick<DocumentListDocumentItem, 'versions'>,
    versionId?: string,
) => {
    const isVersionedDoc = getIsVersionedDocument(doc);

    if (!isVersionedDoc) {
        return undefined;
    }

    const version = versionId
        ? doc.versions.find((versionsItem) => versionsItem.id === versionId)
        : doc.versions[0];

    return version?.contentHash;
};

export const getDocumentVersionByLocation = (
    locationVersionId: Nullable<string | undefined>,
    documentVersions: DocumentVersion[],
) =>
    locationVersionId
        ? documentVersions.find(
              (version) => version.id === locationVersionId,
          ) ?? null
        : documentVersions[0];

export const getDocumentDraftByLocation = (
    locationDraftId: Nullable<string | undefined>,
    documentDrafts: Draft[],
) => {
    return locationDraftId
        ? documentDrafts.find((draft) => draft.id === locationDraftId)
        : null;
};

export const getCurrentCompanyDocumentFlow = (
    doc: DocumentListDocumentItem,
    currentCompanyEdrpou: string,
) => doc.flows.find((flow) => flow.edrpou === currentCompanyEdrpou);

export const getDateSentToRecipient = (
    doc: DocumentListDocumentItem,
    companyEdrpou: string,
) => {
    const recipients = (doc.recipients || []).filter(
        (recipient) => recipient.edrpou !== companyEdrpou && recipient.dateSent,
    );

    if (!recipients.length) {
        return null;
    }
    // для двостороннього документу повертаємо просто дату
    if (!doc.isMultilateral) {
        return formatDateTime(recipients[0].dateSent);
    }
    // for multiple recipients return comma separated list with date and edrpou
    return recipients
        .map(
            (recipient) =>
                `${recipient.edrpou} ${formatDateTime(recipient.dateSent)}`,
        )
        .join(', ');
};

export const getDateReceivedByRecipient = (
    doc: DocumentListDocumentItem,
    companyEdrpou: string,
) => {
    const recipients = (doc.recipients || []).filter(
        (recipient) =>
            recipient.edrpou !== companyEdrpou && recipient.dateReceived,
    );

    if (!recipients.length) {
        return null;
    }
    // для двостороннього документу повертаємо просто дату
    if (!doc.isMultilateral) {
        return formatDateTime(recipients[0].dateReceived);
    }
    // for multiple recipients return comma separated list with date and edrpou
    return recipients
        .map(
            (recipient) =>
                `${recipient.edrpou} ${formatDateTime(
                    recipient.dateReceived,
                )} `,
        )
        .join(', ');
};

export const getDateSeenByRecipient = (
    doc: DocumentListDocumentItem,
    companyEdrpou: string,
) => {
    const recipients = (doc.recipients || []).filter(
        (recipient) =>
            recipient.edrpou !== companyEdrpou && recipient.dateDelivered,
    );

    if (!recipients.length) {
        return null;
    }
    // для двостороннього документу повертаємо просто дату
    if (!doc.isMultilateral) {
        return formatDateTime(recipients[0].dateDelivered);
    }
    // for multiple recipients return comma separated list with date and edrpou
    return recipients
        .map(
            (recipient) =>
                `${recipient.edrpou} ${formatDateTime(
                    recipient.dateDelivered,
                )} `,
        )
        .join(', ');
};

export const getSeenInputDocumentDate = (
    doc: DocumentListDocumentItem,
    companyEdrpou: string,
) => {
    if (!doc.isInput) {
        return null;
    }

    const recipient = (doc.recipients || []).find(
        (item) => item.edrpou === companyEdrpou && item.dateDelivered,
    );

    return recipient?.dateDelivered
        ? formatDateTime(recipient.dateDelivered)
        : null;
};

export const getIsDocHasAllNeededSignatures = (
    doc: DocumentListDocumentItem,
    currentCompanyEdrpou: string,
) =>
    getRemainSideSignaturesCount(doc) === 0 &&
    hasNeededRolesSignatures(doc, currentCompanyEdrpou);

export const getIsCurrentCompanyCanAddSignersForBilateralDoc = (
    doc: DocumentListDocumentItem,
    currentCompany: ICompany,
) => {
    const isFirstSignByRecipient = doc.firstSignBy === 'recipient';
    const isCurrentCompanyRecipient =
        doc.edrpouRecipient === currentCompany.edrpou;

    if (
        [DocumentStatus.FINISHED, DocumentStatus.DELETED].includes(doc.statusId)
    ) {
        return false;
    }

    if (doc.isOneSign) {
        return isFirstSignByRecipient
            ? isCurrentCompanyRecipient
            : !isCurrentCompanyRecipient;
    }

    if (!isFirstSignByRecipient && !isCurrentCompanyRecipient) {
        return doc.statusId <= DocumentStatus.SIGNED;
    }

    if (isFirstSignByRecipient && isCurrentCompanyRecipient) {
        return doc.statusId <= DocumentStatus.SIGNED_AND_SENT;
    }

    return true;
};

export const getCheckedDocumentRequiredFields = (
    doc: DocumentListDocumentItem,
    requiredCompaniesFields: AllDocumentRequiredFields[],
    options: {
        shouldFilterByCompanyRecipient?: boolean;
        shouldFilterByMultilateralRecipient?: boolean;
        additionalDocCategories?: DocumentCategory[];
    } = {},
): WithoutRequiredFieldsDoc | null => {
    const getFilteredCompanyRequiredFields = () =>
        requiredCompaniesFields.filter(
            (item) => item.companyId === doc.companyRecipient?.id,
        );

    const getFilteredByMultilateralRecipientRequiredFields = () =>
        requiredCompaniesFields.filter((item) =>
            doc.recipients.find(
                (recipient) =>
                    recipient.edrpou === item.company.edrpou &&
                    recipient.edrpou !== doc.edrpouOwner,
            ),
        );

    let companyRequiredFields = requiredCompaniesFields;

    if (options.shouldFilterByCompanyRecipient)
        companyRequiredFields = getFilteredCompanyRequiredFields();

    if (options.shouldFilterByMultilateralRecipient) {
        companyRequiredFields = getFilteredByMultilateralRecipientRequiredFields();
    }

    // Normalize category (null -> DocumentCategory.OTHER)
    const category = doc.category?.toString() ?? DocumentCategory.OTHER;

    // Check only against fields with same category or "ANY" category
    const fields = companyRequiredFields.filter(
        (field) =>
            field.documentCategory === category ||
            field.documentCategory === DocumentCategory.ANY ||
            options.additionalDocCategories?.includes(field.documentCategory),
    );

    // Iterate over all required fields settings and check against document
    //
    // WARNING: if you want to change logic here, please be sure to update
    // the same logic on backend side [1]. Otherwise, you can get different
    // results on frontend and backend.
    //
    // 1. app.documents_required_fields.validators._get_required_fields)
    const needFields = new Set<NeedFields>();
    for (const field of fields) {
        if (field.isDateRequired && !doc.dateDocument) {
            needFields.add(NeedFields.IS_DATE_REQUIRED);
        }

        if (field.isNumberRequired && !doc.number) {
            needFields.add(NeedFields.IS_NUMBER_REQUIRED);
        }

        // Exlicitly check for null for amount, because 0 is completely valid value
        if (field.isAmountRequired && doc.amount === null) {
            needFields.add(NeedFields.IS_AMOUNT_REQUIRED);
        }

        // Only field with type "ANY" can require category and that category
        // must be something other than "DocumentCategory.OTHER" (null in model/database)
        if (
            field.isTypeRequired &&
            field.documentCategory === DocumentCategory.ANY &&
            category === DocumentCategory.OTHER
        ) {
            needFields.add(NeedFields.IS_TYPE_REQUIRED);
        }
    }

    return needFields.size > 0
        ? {
              needFields: Array.from(needFields),
              doc,
              requiredCompaniesFields,
          }
        : null;
};

export const getCheckedDocumentsRequiredFields = (
    docs: DocumentListDocumentItem[],
    requiredCompaniesFields: AllDocumentRequiredFields[],
    options: {
        shouldFilterByCompanyRecipient?: boolean;
        shouldFilterByMultilateralRecipient?: boolean;
    } = {},
) =>
    docs
        .map((doc) =>
            getCheckedDocumentRequiredFields(
                doc,
                requiredCompaniesFields,
                options,
            ),
        )
        .filter((doc): doc is WithoutRequiredFieldsDoc => doc !== null);

export const getDocumentsWithoutRequiredFieldsByEdrpou = async (
    docs: DocumentListDocumentItem[],
    edrpous: string[],
) => {
    const requiredCompaniesFields = await getAllDocumentRequiredFields({
        edrpous,
    });

    return getCheckedDocumentsRequiredFields(docs, requiredCompaniesFields);
};

export const isDocumentWithAllRequiredFields = (
    doc: DocumentListDocumentItem,
): boolean => {
    if (!doc.category || String(doc.category) === DocumentCategory.ANY) {
        return false;
    }

    return !(
        !doc.title ||
        !doc.dateDocument ||
        !doc.number ||
        doc.amount === null
    );
};

const makeArrayWithoutDuplicates = (array: string[]) =>
    Array.from(new Set(array));

export const fetchDocumentsCheckRecipientRequiredFields = async (
    docs: DocumentListDocumentItem[],
    currentCompany: ICompany, // needed for remove current company from "getAllDocumentRequiredFields" request
): Promise<WithoutRequiredFieldsDoc[]> => {
    // збираємо ід контрагентів для двосторонніх документів (є поля повязані з recipient)
    const recipientIdList = docs
        .map((doc) => doc.companyRecipient?.id)
        .filter((companyId): companyId is string => !!companyId)
        .filter((companyId) => companyId !== currentCompany.id)
        .filter(Boolean);

    // збираємо всі ЄДРПОУ контрагентів для багатосторонніх документів
    const recipientEdrpouList = docs
        .map(({ flows }) => (flows || []).map((flow) => flow.edrpou))
        .flat()
        .filter((edrpou) => edrpou !== currentCompany.edrpou)
        .filter(Boolean);

    // розділив логіку для двосторонніх та багатосторонніх документів
    const [
        requiredCompaniesFields,
        requiredCompaniesFieldsForMultilateralDocs,
    ] = await Promise.all([
        recipientIdList.length
            ? getAllDocumentRequiredFields({
                  companies_ids: makeArrayWithoutDuplicates(recipientIdList),
              })
            : Promise.resolve([]),
        recipientEdrpouList.length
            ? getAllDocumentRequiredFields({
                  edrpous: makeArrayWithoutDuplicates(recipientEdrpouList),
              })
            : Promise.resolve([]),
    ]);

    return [
        ...getCheckedDocumentsRequiredFields(docs, requiredCompaniesFields, {
            // для двосторонніх документів буде враховуватися doc.companyRecipient?.id та requiredCompaniesFields.[number].companyId
            shouldFilterByCompanyRecipient: true,
        }),
        ...getCheckedDocumentsRequiredFields(
            docs,
            requiredCompaniesFieldsForMultilateralDocs,
            {
                // для багатосторонніх документів буде враховуватися doc.recipients
                shouldFilterByMultilateralRecipient: true,
            },
        ),
    ];
};

export const isOwnerCompanySignedDocument = (doc: Document) => {
    const { edrpouOwner, signatures } = doc;
    return signatures.some(
        (signature) =>
            signature.keyOwnerEdrpou === edrpouOwner ||
            signature.stampOwnerEdrpou === edrpouOwner,
    );
};

export const isRecipientSignedDocument = (doc: Document) => {
    const { edrpouOwner, signatures } = doc;
    return signatures.some(
        (signature) =>
            signature.keyOwnerEdrpou !== edrpouOwner ||
            signature.stampOwnerEdrpou !== edrpouOwner,
    );
};

export const isDocumentDeleteLocked = (doc: Document): boolean => {
    return doc.isDeleteLocked;
};

// якщо користувач заповнив хоча б одне з полів вручну, то документ уважається перевіреним після/під час сканування
export const getIsScannedDocumentVerifiedByUser = (doc: Document): boolean => {
    if (!doc) {
        return false;
    }

    return !!(
        doc.amount ||
        doc.number ||
        doc.dateDocument ||
        doc.category ||
        doc.edrpouRecipient
    );
};
