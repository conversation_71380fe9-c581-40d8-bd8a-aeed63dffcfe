/* global after, before, describe, it */
import { expect } from 'chai';

import { getConfig } from '../../../lib/test';

describe('services/graph', () => {
    // @ts-expect-error TS7034 [FIXME] Comment is autogenerated
    let graph;

    before(() => {
        // @ts-expect-error TS2339 [FIXME] Comment is autogenerated
        global.config = getConfig();
        graph = require('../../graph'); // eslint-disable-line global-require
    });

    after(() => {
        graph = null;
        // @ts-expect-error TS2339 [FIXME] Comment is autogenerated
        global.config = undefined;
    });

    describe('addSlashes', () => {
        it('adds slash for double quote', () => {
            // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
            expect(graph.addSlashes('Some "text"')).to.equal('Some \\"text\\"');
        });

        it('does not add slash for single quote', () => {
            // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
            expect(graph.addSlashes("Some 'text'")).to.equal("Some 'text'");
        });
    });

    describe('formatArgument', () => {
        it('boolean', () => {
            // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
            expect(graph.formatArgument('key', true)).to.equal('true');
            // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
            expect(graph.formatArgument('key', false)).to.equal('false');
        });

        it('number', () => {
            // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
            expect(graph.formatArgument('key', 0)).to.equal('0');
            // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
            expect(graph.formatArgument('key', 1.5)).to.equal('1.5');
        });

        it('string', () => {
            // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
            expect(graph.formatArgument('key', 'value')).to.equal('"value"');
        });

        it('string, but still processed as number', () => {
            // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
            expect(graph.formatArgument('limit', '0')).to.equal('0');
        });
    });

    describe('formatArguments', () => {
        it('empty', () => {
            // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
            expect(graph.formatArguments({})).to.equal('');
        });

        it('one argument', () => {
            // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
            expect(graph.formatArguments({ key: 'value' })).to.equal(
                '(key: "value")',
            );
        });

        it('multiple arguments', () => {
            expect(
                // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
                graph.formatArguments({
                    key: 'value',
                    limit: '5',
                }),
            ).to.equal('(key: "value", limit: 5)');
        });

        it('has undefined', () => {
            expect(
                // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
                graph.formatArguments({
                    key: 'value',
                    limit: '5',
                    folderId: undefined,
                    id: undefined,
                }),
            ).to.equal('(key: "value", limit: 5)');
        });
    });
});
