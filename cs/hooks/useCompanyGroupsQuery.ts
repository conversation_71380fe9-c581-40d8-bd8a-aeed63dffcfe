import { useSelector } from 'react-redux';

import { useQuery } from '@tanstack/react-query';

import { SEARCH_COMPANY_GROUPS } from 'lib/queriesConstants';
import { getCurrentUserRoleId } from 'selectors/app.selectors';
import { GetEmployeeGroupsResponse, getEmployeeGroups } from 'services/groups';

interface UseCompanyGroupsQuery {
    search?: string;
}

export const useCompanyGroupsQuery = ({
    search = '',
}: UseCompanyGroupsQuery) => {
    const currentRoleId = useSelector(getCurrentUserRoleId);

    return useQuery({
        queryFn: () => getEmployeeGroups({ name: search }),
        queryKey: [SEARCH_COMPANY_GROUPS, currentRoleId, search],
        staleTime: 60 * 1000,
        retryOnMount: false,
        placeholderData: {
            groups: [],
            count: 0,
        } as GetEmployeeGroupsResponse,
    });
};
