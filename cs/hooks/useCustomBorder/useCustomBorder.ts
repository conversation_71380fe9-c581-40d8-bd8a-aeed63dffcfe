import cn from 'classnames';

import css from './styles.css';

interface UseCustomBorderCSSVars {
    color?: string;
    activeColor?: string;
    width?: string;
    spanWidth?: string;
    animationDuration?: string;
}
interface UseCustomBorderOptions extends UseCustomBorderCSSVars {
    pseudoElement?: 'before' | 'after';
}

const CSS_VARS: Record<keyof UseCustomBorderCSSVars, string> = {
    color: '--custom-border-color',
    activeColor: '--custom-border-active-color',
    width: '--custom-border-width',
    spanWidth: '--custom-border-span-width',
    animationDuration: '--custom-border-animation-duration',
} as const;

const convertOptionsToCssVars = (options: UseCustomBorderOptions) => {
    return {
        [CSS_VARS.color]: options.color || 'var(--default-border)',
        [CSS_VARS.width]: options.width || '1px',
        [CSS_VARS.spanWidth]: options.spanWidth || '10px',
        [CSS_VARS.activeColor]: options.activeColor || 'var(--default-border)',
        [CSS_VARS.animationDuration]: options.animationDuration || '1s',
    };
};

const getClassName = (pseudoElement?: 'before' | 'after') => {
    if (pseudoElement === 'before') {
        return css.borderBefore;
    }
    if (pseudoElement === 'after') {
        return css.borderAfter;
    }
    return css.border;
};
/**
 * @deprecated
 * use SvgBorder component instead
 */
export const useCustomBorder = (options: UseCustomBorderOptions = {}) => {
    return {
        className: cn(css.vars, getClassName(options.pseudoElement)),
        activeClassName: css.active,
        getStyles: () => convertOptionsToCssVars(options),
    };
};
