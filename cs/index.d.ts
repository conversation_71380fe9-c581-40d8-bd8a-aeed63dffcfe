/// @graphql-typed-document-node/core

declare module '*.svg' {
    const content: any;
    export default content;
}

declare module '*.png' {
    const content: string;
    export default content;
}

declare module '*.jpg' {
    const content: string;
    export default content;
}

declare module '*.jpeg' {
    const content: string;
    export default content;
}

declare module '*.gif' {
    const content: string;
    export default content;
}

declare module '*.pdf' {
    const content: string;
    export default content;
}

declare module '*.css' {
    interface IClassNames {
        [className: string]: string;
    }
    const classNames: IClassNames;
    export default classNames;
}

declare module '*.po' {
    import type { LocaleData } from 'ttag';
    const localeData: LocaleData;
    export = localeData;
}

declare type Nullable<T> = T | null;

declare type ISODate = string;

declare let STATIC_HOST: string;

declare let TAG: string;

declare let LEVEL: string;

declare let config: Record<string, string>;
