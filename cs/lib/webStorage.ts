import React from 'react';

import * as CryptoJS from 'crypto-js';
import Cookies from 'js-cookie';

import { Nullable } from '../types/general';

import { logErrorByLevel } from './helpers';

import uuid from '../services/uuid';
import { COOKIE_KEY_CRYPTO } from './constants';

// Local storage
export function getLocalStorageItem(name: string) {
    let item: Nullable<string> = null;

    try {
        item = window.localStorage.getItem(name);
    } catch (error) {
        logErrorByLevel('Error during reading from localStorage', {
            error_message: error.message,
            storage_key: name,
        });
        return item;
    }

    if (item) {
        try {
            return JSON.parse(item);
        } catch (err) {
            return item;
        }
    }
    return item;
}

export function setLocalStorageItem(name: string, item: any) {
    try {
        if (item !== null && item !== undefined) {
            window.localStorage.setItem(name, JSON.stringify(item));
        }
    } catch (error) {
        logErrorByLevel('Error during setting to localStorage', {
            error_message: error.message,
            storage_key: name,
        });
    }
}

export function removeLocalStorageItem(name: string) {
    try {
        window.localStorage.removeItem(name);
    } catch (error) {
        logErrorByLevel('Error during removing item from localStorage', {
            error_message: error.message,
            storage_key: name,
        });
    }
}

/**
 * Like useState, but the state is persisted in localStorage
 *
 * @param storageKey - the key to store the value under in localStorage
 * @param defaultValue - the default value to use if there is no value in localStorage
 * @returns [state, setState, removeState] - the state, a function to set the state, and a function to remove the state from localStorage
 */
export function useLocalStoredState<T>(
    storageKey: string,
    defaultValue: T,
): [T, (newValue: T) => void] {
    const [state, setState] = React.useState<T>(() => {
        return getLocalStorageItem(storageKey) ?? defaultValue;
    });

    const _set = (newValue: T) => {
        setLocalStorageItem(storageKey, newValue);
        setState(newValue);
    };

    return [state, _set];
}

// Session storage
export function getSessionStorageItem(name: string) {
    let item: Nullable<string> = null;

    try {
        item = window.sessionStorage.getItem(name);
    } catch (error) {
        logErrorByLevel('Error during reading from sessionStorage', {
            error_message: error.message,
            storage_key: name,
        });
        return null;
    }

    if (item) {
        try {
            return JSON.parse(item);
        } catch (err) {
            return item;
        }
    }
    return item;
}

export function setSessionStorageItem(name: string, item: any) {
    try {
        if (item !== null && item !== undefined) {
            window.sessionStorage.setItem(name, JSON.stringify(item));
        }
    } catch (error) {
        logErrorByLevel('Error during setting item to sessionStorage', {
            error_message: error.message,
            storage_key: name,
        });
    }
}

// stored keys encryption
export function encryptStoredKeys(item: any): string {
    try {
        const secretKey = uuid();
        Cookies.set(
            COOKIE_KEY_CRYPTO,
            secretKey,
            { expires: 30 }, // 30 days
        );
        const cipher = CryptoJS.AES.encrypt(JSON.stringify(item), secretKey);
        return cipher.toString();
    } catch (err) {
        logErrorByLevel('Unhandled error on encrypt stored keys', {
            error_message: err.message,
        });
        throw err;
    }
}

export function decryptStoredKeys(item: any): any {
    try {
        const secretKey = Cookies.get(COOKIE_KEY_CRYPTO);
        if (!item || !secretKey) {
            return {};
        }
        const bytes = CryptoJS.AES.decrypt(item, secretKey);
        const cipher = bytes.toString(CryptoJS.enc.Utf8);
        return JSON.parse(cipher);
    } catch (err) {
        logErrorByLevel('Unhandled error on decrypt stored keys', {
            error_message: err.message,
            item,
        });
        return {};
    }
}
