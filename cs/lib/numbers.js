export const formatPrice = (value, fixed = 2) => {
    const numberValue = Number(value);

    return typeof numberValue === 'number' && !isNaN(numberValue)
        ? numberValue.toFixed(fixed).toString()
        : '';
};

export const sanitizeAndFormatPrice = (value, fixed = 2) => {
    const sanitizedInput = value.replace(',', '.');
    const numberValue = Number(sanitizedInput);

    return typeof numberValue === 'number' &&
        !isNaN(numberValue) &&
        numberValue > 0
        ? numberValue.toFixed(fixed).toString()
        : '';
};

export const safePercent = (first, second, fixed = 2) => {
    let result = 0;
    if (
        typeof first === 'number' &&
        typeof second === 'number' &&
        second !== 0
    ) {
        result = ((first / second) * 100).toFixed(fixed);
    }
    return result;
};

export const sum = (arr, key) => {
    const getter = (item) => {
        return key ? item[key] : item;
    };
    return arr.reduce((prev, item) => prev + getter(item), 0);
};

/**
 * @example: 10099  -> 100.99
 * @param {string|number} amount
 * @returns {number}
 */
export const amountPayloadValueToLocalValue = (amount) => {
    return parseFloat((Number(amount) / 100).toFixed(2));
};
/**
 * @example: 1000.99 -> 100099
 * @param {string | number} amount
 * @returns {number}
 */
export const amountValueToPayloadValue = (amount) => {
    return Number((parseFloat(amount) * 100).toFixed());
};

/**
 * @example: -.00 -> 0.00 | -000090.00 => -90.00 | 0000.00 -> 0.00
 * @param {string} amount
 * @returns {string}
 */
export function getCorrectAmount(amount) {
    return parseFloat(amount) ? parseFloat(amount).toFixed(2) : '0.00';
}

export { formatPriceWithSpaces } from './ts/numbers';

export default {
    formatPrice,
    safePercent,
    sum,
    amountPayloadValueToLocalValue,
    amountValueToPayloadValue,
    getCorrectAmount,
};
