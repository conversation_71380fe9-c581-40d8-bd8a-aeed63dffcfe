// @ts-expect-error TS2307 [FIXME] Comment is autogenerated
import { State } from '../components/auth/authFeatureFlags/types';

interface AuthStore {
    authFeatureFlags: State;
}

const getSlice = (authState: AuthStore): State => authState.authFeatureFlags;

export const getAuthFeatureFlagsRequestState = (authState: AuthStore) =>
    getSlice(authState).requestState;

export const getAuthFeatureFlagsErrorMessage = (authState: AuthStore) =>
    getSlice(authState).errorMessage;

export const getAuthFeatureFlags = (authState: AuthStore) =>
    getSlice(authState).flags;
