import { AnyAction } from 'redux';

import actions from './shareDocumentPopupActions';

import { State } from './shareDocumentPopupTypes';

const initState: State = {
    isActive: false,
    documentId: null,

    showCoworkerSuggestion: false,
    selectedCoworkers: [],
    coworkerInputValue: '',
    suggestedCoworkers: [],
    coworkers: [],

    comment: '',

    isSubmitting: false,
    submitError: '',
};

const shareDocumentPopupReducer = (
    state: State = initState,
    action: AnyAction,
): State => {
    switch (action.type) {
        case actions.SHARE_DOCUMENT_POPUP__SHOW:
            return {
                ...state,
                isActive: true,
                documentId: action.documentId,
            };
        case actions.SHARE_DOCUMENT_POPUP__CLOSE:
            return {
                ...state,
                isActive: false,
                documentId: null,
            };
        case actions.SHARE_DOCUMENT_POPUP__CLEAR_STATE:
            return { ...initState };
        case actions.SHARE_DOCUMENT_POPUP__OPEN_AUTOSUGGEST:
            return {
                ...state,
                showCoworkerSuggestion: true,
            };
        case actions.SHARE_DOCUMENT_POPUP__CLOSE_AUTOSUGGEST:
            return {
                ...state,
                showCoworkerSuggestion: false,
            };
        case actions.SHARE_DOCUMENT_POPUP__SET_COWORKERS:
            return {
                ...state,
                coworkers: action.coworkers,
            };
        case actions.SHARE_DOCUMENT_POPUP__SET_COWORKERS_SUGGESTIONS:
            return {
                ...state,
                suggestedCoworkers: action.suggestedCoworkers,
            };
        case actions.SHARE_DOCUMENT_POPUP__SET_COWORKER_INPUT:
            return {
                ...state,
                coworkerInputValue: action.coworkerInputValue,
            };
        case actions.SHARE_DOCUMENT_POPUP__SET_SELECTED_COWORKER:
            return {
                ...state,
                selectedCoworkers: action.selectedCoworkers,
            };
        case actions.SHARE_DOCUMENT_POPUP__SET_COMMENT_TEXT:
            return {
                ...state,
                comment: action.comment,
            };
        case actions.SHARE_DOCUMENT_POPUP__START_SUBMIT:
            return {
                ...state,
                submitError: '',
                isSubmitting: true,
            };
        case actions.SHARE_DOCUMENT_POPUP__FINISH_SUBMIT:
            return {
                ...state,
                isSubmitting: false,
            };
        case actions.SHARE_DOCUMENT_POPUP__SET_SUBMIT_ERROR:
            return {
                ...state,
                submitError: action.submitError,
            };
        default:
            return state;
    }
};

export default shareDocumentPopupReducer;
