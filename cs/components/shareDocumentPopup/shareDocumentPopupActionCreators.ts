import { Thunk } from '../../types';
import { CurrentCompanyCoworkerRole, IRole } from '../../types/user';
import { NotificationPayload } from './types';

import notificationCenterActionCreators from '../notificationCenter/notificationCenterActionCreators';
import actions from '../shareDocumentPopup/shareDocumentPopupActions';

import { shareDocument } from '../../services/documents/api';
import { getCurrentCompanyRoles } from '../../services/user';

function updateCoworkerSuggestion(roles: CurrentCompanyCoworkerRole[]): Thunk {
    return async (dispatch, getState) => {
        const {
            shareDocumentPopup: { selectedCoworkers },
            app: {
                currentUser: { currentRole },
            },
        } = getState();
        const alreadyInListIds = selectedCoworkers.map(
            (user: IRole) => user.id,
        );
        const suggestedCoworkers = roles.filter(
            (role: IRole) =>
                role.id !== currentRole.id &&
                !alreadyInListIds.includes(role.id),
        );
        dispatch({
            type: actions.SHARE_DOCUMENT_POPUP__SET_COWORKERS_SUGGESTIONS,
            suggestedCoworkers,
        });
    };
}

function loadCurrentCompanyRoles(search?: string): Thunk {
    return async (dispatch) => {
        try {
            const { currentCompanyRoles } = await getCurrentCompanyRoles({
                search,
            });
            dispatch(updateCoworkerSuggestion(currentCompanyRoles));
            dispatch({
                type: actions.SHARE_DOCUMENT_POPUP__SET_COWORKERS,
                coworkers: currentCompanyRoles,
            });
        } catch (e) {
            // catch error
        }
    };
}

function onShow(documentId: string): Thunk {
    return async (dispatch) => {
        dispatch({
            type: actions.SHARE_DOCUMENT_POPUP__SHOW,
            documentId,
        });
    };
}

function onClose(): Thunk {
    return (dispatch) => {
        dispatch({ type: actions.SHARE_DOCUMENT_POPUP__CLOSE });
    };
}

function onAutosuggestClose(): Thunk {
    return (dispatch) => {
        dispatch({ type: actions.SHARE_DOCUMENT_POPUP__CLOSE_AUTOSUGGEST });
    };
}

function onCoworkerInputChange(value: string): Thunk {
    return async (dispatch) => {
        dispatch({
            type: actions.SHARE_DOCUMENT_POPUP__SET_COWORKER_INPUT,
            coworkerInputValue: value,
        });
    };
}

function onSuggestedCoworkerClick(coworker: IRole): Thunk {
    return (dispatch, getState) => {
        const {
            shareDocumentPopup: { selectedCoworkers, coworkers },
        } = getState();
        const newSelectedCoworkers = [...selectedCoworkers, coworker];
        dispatch({
            type: actions.SHARE_DOCUMENT_POPUP__SET_SELECTED_COWORKER,
            selectedCoworkers: newSelectedCoworkers,
        });
        dispatch(updateCoworkerSuggestion(coworkers));
        dispatch(onAutosuggestClose());
        dispatch(onCoworkerInputChange(''));
    };
}

function onRemoveSelectedCoworker(role: IRole): Thunk {
    return (dispatch, getState) => {
        const {
            shareDocumentPopup: { selectedCoworkers, coworkers },
        } = getState();
        const newSelectedCoworkers = selectedCoworkers.filter(
            (coworker) => coworker.id !== role.id,
        );
        dispatch({
            type: actions.SHARE_DOCUMENT_POPUP__SET_SELECTED_COWORKER,
            selectedCoworkers: newSelectedCoworkers,
        });
        dispatch(updateCoworkerSuggestion(coworkers));
    };
}

function onAutosuggestOpen(search: string): Thunk {
    return (dispatch) => {
        dispatch(loadCurrentCompanyRoles(search));
        dispatch({ type: actions.SHARE_DOCUMENT_POPUP__OPEN_AUTOSUGGEST });
    };
}

function onCommentChange(comment: string): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.SHARE_DOCUMENT_POPUP__SET_COMMENT_TEXT,
            comment,
        });
    };
}

function onSaveChanges(): Thunk {
    return async (dispatch, getState) => {
        const {
            shareDocumentPopup: { selectedCoworkers, comment, documentId },
        } = getState();
        const rolesIds = selectedCoworkers.map((coworker) => coworker.id);
        dispatch({ type: actions.SHARE_DOCUMENT_POPUP__START_SUBMIT });

        try {
            await shareDocument(documentId, rolesIds, comment);
        } catch (error) {
            dispatch({
                type: actions.SHARE_DOCUMENT_POPUP__SET_SUBMIT_ERROR,
                submitError: error.toString(),
            });
            return;
        } finally {
            dispatch({ type: actions.SHARE_DOCUMENT_POPUP__FINISH_SUBMIT });
        }
        dispatch({ type: actions.SHARE_DOCUMENT_POPUP__CLEAR_STATE });
        dispatch(onClose());
        const notificationPayload: NotificationPayload = {
            type: 'text',
            textType: 'success',
            title: 'Пересилання документа',
            text: 'Документ успішно надіслано',
            showCloseButton: true,
            autoClose: 5000,
        };
        dispatch(
            // @ts-expect-error TS2769 [FIXME] Comment is autogenerated
            notificationCenterActionCreators.addNotification(
                notificationPayload,
            ),
        );
    };
}

export default {
    onShow,
    onClose,

    onSuggestedCoworkerClick,
    onAutosuggestClose,
    onRemoveSelectedCoworker,
    onCoworkerInputChange,
    onAutosuggestOpen,
    onCommentChange,
    onSaveChanges,
};
