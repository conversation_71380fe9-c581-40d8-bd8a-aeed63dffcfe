import React from 'react';
import { connect } from 'react-redux';

import { formatUserIdentifier } from 'lib/ts/helpers';
import { t } from 'ttag';

import { StoreState } from '../../types/store';
import { IRole } from '../../types/user';

import actionCreators from '../shareDocumentPopup/shareDocumentPopupActionCreators';

import { formatFullName } from '../../lib/helpers';

import Button from '../ui/button/button';
import Message from '../ui/message/message';
import Popup from '../ui/popup/popup';
import { ITag } from '../ui/tags/tagsTypes';
import TextShorten from '../ui/textShorten/textShorten';
import Textarea from '../ui/textarea/textarea';

import AutosuggestionTags from '../autosuggestionTags/autosuggestionTags';
import { DispatchToProps, State } from './shareDocumentPopupTypes';

import css from './shareDocumentPopup.css';

function mapStateToProps(state: StoreState) {
    return {
        ...state.shareDocumentPopup,
    };
}

const ShareDocumentPopup = (props: State & DispatchToProps) => {
    const renderEmailSuggestion = (suggestion: IRole) => {
        const contactFullName = formatFullName({
            firstName: suggestion.user.firstName,
            secondName: suggestion.user.secondName,
            lastName: suggestion.user.lastName,
        });
        return (
            <div className={css.table}>
                <div className={css.cell}>
                    <TextShorten>
                        {formatUserIdentifier(suggestion.user)}
                    </TextShorten>
                </div>
                <div className={css.cell}>
                    <TextShorten>
                        <b>{contactFullName}</b>
                    </TextShorten>
                </div>
            </div>
        );
    };

    const handleCommentChange = (
        evt: React.ChangeEvent<HTMLTextAreaElement>,
    ) => {
        props.onCommentChange(evt.target.value);
    };

    const prepareChosenItem = (role: IRole): ITag => {
        const userFullName = formatFullName(role.user);
        return {
            id: role.id,
            name: userFullName || formatUserIdentifier(role.user),
        };
    };

    const isSubmitButtonDisabled =
        props.isSubmitting || props.selectedCoworkers.length === 0;

    return (
        <Popup fullContent active={props.isActive} onClose={props.onClose}>
            <div className={css.root}>
                <h3 className={css.title}>{t`Пересилання документа`}</h3>
                <div className={css.label}>
                    {t`Оберіть співробітників вашої компанії, яким хочете надіслати документ`}
                </div>
                <div>
                    <AutosuggestionTags
                        shouldSearchOnEmptyValue
                        value={props.coworkerInputValue}
                        placeholder={t`Вкажіть email або ім’я співробітника`}
                        showSuggestions={props.showCoworkerSuggestion}
                        suggestionsData={props.suggestedCoworkers}
                        chosenItems={props.selectedCoworkers}
                        prepareChosenItem={prepareChosenItem}
                        onSuggestionClick={props.onSuggestedCoworkerClick}
                        onCloseSuggestions={props.onAutosuggestClose}
                        onRemoveItem={props.onRemoveSelectedCoworker}
                        onDataChange={props.onCoworkerInputChange}
                        onAutosuggest={props.onAutosuggestOpen}
                        renderSuggestion={renderEmailSuggestion}
                    />
                </div>
                <div className={css.label}>{t`Залиште коментар`}</div>
                <Textarea
                    withCounter
                    key="message"
                    value={props.comment}
                    onChange={handleCommentChange}
                    name="message"
                    maxLength={500}
                    placeholder={t`Напишіть свій коментар`}
                />
                <div className={css.button}>
                    <Button
                        theme="cta"
                        isLoading={props.isSubmitting}
                        disabled={isSubmitButtonDisabled}
                        onClick={props.onSaveChanges}
                    >
                        {t`Переслати документ`}
                    </Button>
                </div>
                {props.submitError && (
                    <div className={css.message}>
                        <Message sizeSmall type="error">
                            {props.submitError}
                        </Message>
                    </div>
                )}
            </div>
        </Popup>
    );
};

export default connect(mapStateToProps, actionCreators)(ShareDocumentPopup);
