.root {
    display: flex;
    flex-direction: column;
    padding: 0;
}

.flag {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    display: flex;
    height: 20px;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.flag::before,
.flag::after {
    position: absolute;
    top: 0;
    display: block;
    width: 50%;
    height: 100%;
    content: '';
    transition: background-color 0.3s;
}

.flag::before {
    left: 0;
    background-color: #0075bf;
    border-top-left-radius: 5px;
}

.flag::after {
    right: 0;
    background-color: #ffdc00;
    border-top-right-radius: 5px;
}

.flag.euFlag::before,
.flag.euFlag::after {
    background-color: #0042c6;
}

.flagStarIcon {
    position: relative;
    z-index: 1;
    width: 10px;
    height: 10px;
    color: #fc0;
    transform: scale(0);
    transition: transform 0.3s;
}

.flag.euFlag .flagStarIcon {
    transform: scale(1);
}

.logoBox {
    margin: -30px 0 40px;
}

.logoWrapper {
    position: relative;
    width: 80px;
    height: 80px;
}

.logoWrapper > * {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.message {
    color: #626c84;
    font-size: 14px;
}

.switchLabel:hover {
    cursor: pointer;
}

.strong {
    color: var(--content-color);
    font-weight: 600;
}

.title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 18px;
}

.subtitle {
    margin-bottom: 30px;
}

.content {
    display: flex;
    width: 250px;
    min-height: 250px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.expiredMessage {
    margin-bottom: 15px;
}

.errorMessage {
    margin-bottom: 15px;
    color: var(--red-color);
}

.errorPseudoLink {
    margin-bottom: 15px;
}

.checkbox {
    display: flex;
    align-items: center;
}

.betaLabel {
    padding: 2px 6px;
    margin-left: 16px;
    background: #ffe551;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 500;
}

@media screen and (max-width: 768px) {
    .title {
        margin-top: 40px;
        font-size: 24px;
    }

    .logoBox {
        margin: 60px 0 0;
    }

    .flag {
        top: 52px;
    }

    .flag::before {
        border-top-left-radius: 0;
    }

    .flag::after {
        border-top-right-radius: 0;
    }

    .subtitle {
        margin-bottom: 0;
    }

    .content {
        width: 100%;
        min-height: unset;
        margin-top: 70px;
        margin-bottom: 50px;
    }
}
