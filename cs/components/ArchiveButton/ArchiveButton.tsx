import React, { <PERSON> } from 'react';

import { BlackTooltip } from '@vchasno/ui-kit';

import { useArchiveItems } from 'components/ArchiveButton/useArchiveItems';
import { DocumentListDocumentItem } from 'components/documentList/types';
import { openInNewTab } from 'lib/navigation';
import { sendToGTMV4 } from 'services/analytics/gtm';
import { t } from 'ttag';
import IconButton from 'ui/iconButton/iconButton';

import FolderPlusSvg from './images/folderPlus.svg';

import css from './ArchiveButton.css';

interface ArchiveButtonProps {
    docs?: DocumentListDocumentItem[];
    className?: string;
}

const ArchiveButton: FC<ArchiveButtonProps> = ({ className, docs }) => {
    const {
        isDisabledArchiveButton,
        getTitleArchiveButton,
        onArchiveItems,
        isAccessArchiveItems,
    } = useArchiveItems(docs);

    const onClickArchiveButton = () => {
        sendToGTMV4({
            event: 'ec_docpage_icon_move_to_archive_click',
        });
        onArchiveItems();
    };

    return (
        <div className={className}>
            <BlackTooltip
                title={
                    <div>
                        <div>{getTitleArchiveButton()}</div>
                        {!isAccessArchiveItems && (
                            <div
                                className={css.link}
                                onClick={() => {
                                    openInNewTab('/app/archive-preview');
                                }}
                            >{t`Дізнатися більше`}</div>
                        )}
                    </div>
                }
            >
                <span>
                    <IconButton
                        svg={FolderPlusSvg}
                        onClick={onClickArchiveButton}
                        disabled={isDisabledArchiveButton}
                    />
                </span>
            </BlackTooltip>
        </div>
    );
};

export default ArchiveButton;
