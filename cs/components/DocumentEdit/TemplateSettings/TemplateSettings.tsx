import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import {
    BlackTooltip,
    FlexBox,
    SelectComponents,
    SelectProps,
} from '@vchasno/ui-kit';

import TemplateSettingsSelect, {
    TemplateOption,
    TemplateSettingsSelectProps,
} from 'components/DocumentsUploader/TemplateSettings/TemplateSettingsSelect';
import { useTemplateResetEffect } from 'components/DocumentsUploader/TemplateSettings/useTemplateResetEffect';
import { useUISectionsContext } from 'components/DocumentsUploader/useUISectionsContext';
import Icon from 'ui/icon';

import { EditDocumentForm } from '../types';

import { getOptionDisableMessage, mergeTagLists } from './utils';

import { useDocumentEditContext } from '../DocumentEditContext';

import InfoSvg from '../../../icons/info.svg';

import css from './TemplateSettings.css';

const TemplateSettings: React.FC = () => {
    const [snapshot, setSnapshot] = useState<EditDocumentForm | null>(null);
    const methods = useFormContext<EditDocumentForm>();
    const [, { expandSections }] = useUISectionsContext();

    const templateDataRef = useTemplateResetEffect(() => {
        setSnapshot(null);
    });

    const { doc, user } = useDocumentEditContext();

    const components: SelectProps['components'] = {
        Option: (optionProps) => {
            const disableReason = getOptionDisableMessage(
                doc,
                user,
                methods.getValues(),
                (optionProps.data as TemplateOption).source,
            );

            return (
                // @ts-ignore
                <SelectComponents.Option {...optionProps}>
                    <FlexBox justify="space-between">
                        {optionProps.children}
                        {disableReason && (
                            <BlackTooltip
                                disableInteractive
                                title={disableReason}
                            >
                                <span className={css.icon}>
                                    <Icon glyph={InfoSvg} />
                                </span>
                            </BlackTooltip>
                        )}
                    </FlexBox>
                </SelectComponents.Option>
            );
        },
    };

    const expandSectionsEffect = (
        dataFromTemplate: Parameters<TemplateSettingsSelectProps['onApply']>[0],
    ) => {
        const expandedSections: Parameters<typeof expandSections>[0] = [];

        if (
            dataFromTemplate.employeesSigners.length > 0 ||
            dataFromTemplate.employeesViewers.length > 0 ||
            dataFromTemplate.employeesReviewers.length > 0
        ) {
            expandedSections.push('employees');
        }

        if (dataFromTemplate.selectedTags.length > 0) {
            expandedSections.push('tags');
        }

        if (dataFromTemplate.documentParameters.length > 0) {
            expandedSections.push('additionalParameters');
        }

        if (expandedSections.length) {
            expandSections(expandedSections);
        }
    };

    return (
        <TemplateSettingsSelect
            components={components}
            isOptionDisabled={(option: TemplateOption) =>
                Boolean(
                    getOptionDisableMessage(
                        doc,
                        user,
                        methods.getValues(),
                        option.source,
                    ),
                )
            }
            onApply={(dataFromTemplate) => {
                templateDataRef.current = dataFromTemplate;

                const formValues = methods.getValues();

                if (formValues.selectedTags.length > 0) {
                    // при накладанні сценаріїв ярлики не заміщувати, а додавати до наявних,
                    // щоб надалі правильно відстежувати стан зміни після накладення шаблоні
                    // імітуємо ніби в самому шаблоні уже вказані ярлики
                    dataFromTemplate.selectedTags = mergeTagLists(
                        formValues.selectedTags,
                        dataFromTemplate.selectedTags,
                    );
                }
                methods.setValue('templateId', dataFromTemplate.templateId);

                if (!snapshot) {
                    setSnapshot(formValues);
                }

                const mergedData = {
                    ...formValues,
                    ...dataFromTemplate,
                };

                // Оскільки в сценарії відсутні додаткові параметри - він не має ніяк впливати на додаткові параметри документа
                if (
                    dataFromTemplate.documentParameters.length === 0 &&
                    formValues.documentParameters.length > 0
                ) {
                    mergedData.documentParameters =
                        formValues.documentParameters;
                }

                methods.reset(mergedData, {
                    // важливо зберігати defaultValues - щоб використовувати для порівняння перед формуванням payload
                    keepDefaultValues: true,
                });

                expandSectionsEffect(dataFromTemplate);
            }}
            onReset={() => {
                if (snapshot) {
                    methods.reset(snapshot, {
                        keepDefaultValues: true,
                    });
                    setSnapshot(null);
                }
            }}
        />
    );
};

export default TemplateSettings;
