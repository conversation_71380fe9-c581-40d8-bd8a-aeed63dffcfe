import { useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';

import { yupResolver } from '@hookform/resolvers/yup';

import { documentEditSchema } from 'components/DocumentEdit/documentEditSchema';
import { queryClient } from 'lib/queries';
import { CACHE_TIME_MS, GET_DOCUMENT_FIELDS } from 'lib/queriesConstants';
import { getIsActiveProMaximalOrTrialRateExists } from 'selectors/app.selectors';
import { getDocumentFields } from 'services/documentFields/api';
import { Document } from 'services/documents/ts/types';
import logger from 'services/logger';

import { IUser } from '../../types/user';
import { EditDocumentForm } from './types';

import { docParametersToFormParameters, documentToFormFields } from './mappers';

const resolver = yupResolver(documentEditSchema, {
    recursive: true,
});

export const useEditDocumentForm = (doc: Document, user: IUser) => {
    const methods = useForm<EditDocumentForm>({
        shouldUnregister: false,
        resolver,
        reValidateMode: 'onChange',
        defaultValues: {
            ...useMemo(() => documentToFormFields(doc), [doc]),
            companyEdrpou: user.currentCompany.edrpou,
            isPayedRate: useSelector(getIsActiveProMaximalOrTrialRateExists),
            // цим полем, користувач підтверджує, що ШІ розпізнав вірно дані і ми
            // з таблиці розпізнаваних даних ШІ, переносимо дані на документ. І записуватимемо, що дані верифіковано юзером
            isScheduledMetaSuggestionVerify: false,
        },
    });

    useEffect(() => {
        if (doc.parameters?.length) {
            queryClient
                .fetchQuery(
                    [GET_DOCUMENT_FIELDS, methods.getValues('companyEdrpou')],
                    getDocumentFields,
                    {
                        staleTime: CACHE_TIME_MS,
                    },
                )
                .then((documentFields) => {
                    methods.reset({
                        ...methods.getValues(),
                        documentParameters: docParametersToFormParameters(
                            doc.parameters ?? [],
                            documentFields,
                        ),
                    });
                })
                .catch((error) => {
                    logger.error('Failed to fetch document fields', error);
                });
        }
    }, [doc.parameters]);

    return methods;
};
