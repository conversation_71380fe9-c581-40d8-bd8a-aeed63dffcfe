import { CommentForm } from 'components/DocumentsUploader/CommentSection/types';
import {
    AIDocumentRecognition,
    Actor,
    DocumentsUploadForm,
    FilesFields,
    UploadMetaFields,
} from 'components/DocumentsUploader/types';
import { UpdateDocumentPayload } from 'services/documents/ts/api';

interface EmployeesMeta {
    employeesWhichSigned: Actor[];
    employeesWhichReviewed: Actor[];
}

interface MetaSuggestionReviewFields {
    isScheduledMetaSuggestionVerify: boolean;
}

export type EditDocumentForm = Omit<
    DocumentsUploadForm,
    keyof (FilesFields & CommentForm)
> &
    EmployeesMeta &
    AIDocumentRecognition &
    MetaSuggestionReviewFields;

export type RootFieldChangedMap = Partial<
    Record<
        keyof Omit<
            EditDocumentForm,
            'templateId' | 'companyEdrpou' | 'isPayedRate'
        >,
        boolean
    >
>;

export type EditDocumentFormWithoutMeta = Omit<
    EditDocumentForm,
    keyof UploadMetaFields
>;

export type ApiErrorDetails = Record<
    keyof UpdateDocumentPayload,
    Record<string, string>
>;

export interface ApiErrorResponseBody {
    status: number;
    code: 'invalid_request' | string;
    reason: string;
    details: ApiErrorDetails;
}

export const isApiErrorResponseBody = (
    error: unknown,
): error is ApiErrorResponseBody => {
    return (
        typeof error === 'object' &&
        error !== null &&
        'reason' in error &&
        'code' in error &&
        'details' in error &&
        typeof error.details === 'object'
    );
};
