import { expect } from 'chai';
import { Actor } from 'components/DocumentsUploader/types';

import {
    documentToCounterpartiesFields,
    documentToEmployeeReviewersFields,
    documentToEmployeesSignersFields,
    documentToInfoFields,
    editFormToCounterpartiesPayload,
    editFormToEmployeeSignersPayload,
    editFormToTagSettingsPayload,
    editFormToVersionSettingsPayload,
} from '../../mappers';

type Doc = Parameters<typeof documentToCounterpartiesFields>[0];
const makeDoc = (data: Partial<Doc>): Doc =>
    ({
        ...data,
    } as Doc);

describe('cs/components/DocumentEdit/mappers.ts', () => {
    describe('documentToInfoFields', () => {
        const assert = (
            doc: Doc,
            expected: ReturnType<typeof documentToInfoFields>,
        ) => {
            expect(documentToInfoFields(doc)).to.deep.equal(expected);
        };
        it('should map filled values correct', () => {
            const dateDocument = new Date();
            const doc = makeDoc({
                title: 'test',
                amount: 100,
                number: 'number',
                category: 1,
                dateDocument: dateDocument.toISOString(),
            });
            assert(doc, {
                title: 'test',
                amount: 100,
                number: 'number',
                category: '1',
                date: dateDocument,
            });
        });

        it('should map empty values correct', () => {
            assert(makeDoc({}), {
                title: '',
                number: '',
                amount: null,
                category: null,
                date: null,
            });
        });
    });

    describe('documentToEmployeesSignersFields', () => {
        const assert = (
            doc: Partial<Doc>,
            expected: ReturnType<typeof documentToEmployeesSignersFields>,
        ) => {
            expect(
                documentToEmployeesSignersFields(makeDoc(doc)),
            ).to.deep.equal(expected);
        };

        it('should map employeesSigners corrects', () => {
            const reviewRole = {
                roleId: '5a5cdc1b-6207-48db-abdc-a9a4e1b4c07d',
                role: {
                    user: {
                        email: '<EMAIL>',
                    },
                },
            } as Doc['signers'][number];
            assert(
                {
                    signers: [reviewRole],
                    signatures: [],
                },
                {
                    employeesSigners: [
                        {
                            type: 'role',
                            id: '5a5cdc1b-6207-48db-abdc-a9a4e1b4c07d',
                            source: {
                                ...reviewRole.role,
                                id: reviewRole.roleId,
                            } as any,
                        },
                    ],
                    employeesSignIsOrdered: false,
                    employeesWhichSigned: [],
                },
            );
        });

        it('should map employeesSignIsOrdered corrects', () => {
            const reviewRole = {
                roleId: '5a5cdc1b-6207-48db-abdc-a9a4e1b4c07d',
                role: {
                    id: '1',
                    user: {
                        email: '<EMAIL>',
                    },
                },
                order: 1,
            } as Doc['signers'][number];
            assert(
                {
                    signers: [reviewRole],
                    signatures: [],
                },
                {
                    employeesSigners: [
                        {
                            type: 'role',
                            id: '5a5cdc1b-6207-48db-abdc-a9a4e1b4c07d',
                            source: {
                                ...reviewRole.role,
                                id: reviewRole.roleId,
                            } as any,
                        },
                    ],
                    employeesSignIsOrdered: true,
                    employeesWhichSigned: [],
                },
            );
        });
    });

    describe('documentToEmployeeReviewersFields', () => {
        const assert = (
            doc: Partial<Doc>,
            expected: ReturnType<typeof documentToEmployeeReviewersFields>,
        ) => {
            expect(
                documentToEmployeeReviewersFields(makeDoc(doc)),
            ).to.deep.equal(expected);
        };

        it('should return employeesReviewersIsOrdered and employeesIsSignAfterReview correct', () => {
            assert(
                {
                    reviews: [],
                    reviewRequests: [],
                    reviewSetting: {
                        isParallel: false,
                        isRequired: true,
                    },
                },
                {
                    employeesReviewersIsOrdered: true,
                    employeesReviewers: [],
                    employeesIsSignAfterReview: true,
                    employeesWhichReviewed: [],
                },
            );
        });

        it('should return employeesReviewers correct', () => {
            const reviewRole = {
                id: '4b33c4e6-862e-40a4-807d-3b270caef6ae',
                toRoleId: '5a5cdc1b-6207-48db-abdc-a9a4e1b4c07d',
                toRole: {
                    user: {
                        email: '<EMAIL>',
                    },
                },
            } as Doc['reviewRequests'][number];

            assert(
                {
                    reviewRequests: [reviewRole],
                },
                {
                    employeesReviewersIsOrdered: false,
                    employeesIsSignAfterReview: false,
                    employeesReviewers: [
                        {
                            type: 'role',
                            id: '5a5cdc1b-6207-48db-abdc-a9a4e1b4c07d',
                            source: {
                                ...reviewRole.toRole,
                                id: reviewRole.toRoleId,
                            } as any,
                        },
                    ],
                    employeesWhichReviewed: [],
                },
            );
        });
    });

    describe('documentToCounterpartiesFields', () => {
        const assert = (
            doc: Partial<Doc>,
            expected: ReturnType<typeof documentToCounterpartiesFields>,
        ) => {
            expect(documentToCounterpartiesFields(makeDoc(doc))).to.deep.equal(
                expected,
            );
        };

        const prepareFlow = (options: {
            edrpou: string;
            order?: number;
            email: string;
            signaturesCount?: number;
            is_emails_hidden?: boolean;
            name: string;
            // @ts-expect-error TS2741 [FIXME] Comment is autogenerated
        }): Doc['flows'][number] => ({
            canSign: true,
            edrpou: options.edrpou,
            displayCompanyName: options.name,
            id: 'd47302ab-30ba-466e-80e6-369d18a032bd',
            isComplete: false,
            order: options.order ?? null,
            pendingSignaturesCount: 1,
            signaturesCount: options.signaturesCount || 1,
            receivers: {
                edrpou: options.edrpou,
                emails: [options.email],
                is_emails_hidden: options.is_emails_hidden || false,
            },
        });

        it('should return empty values in doc is internal', () => {
            assert(
                { isInternal: true },
                {
                    isParallel: false,
                    counterparties: [],
                },
            );
        });

        it('should return data for parallel multilateral doc', () => {
            assert(
                {
                    isInternal: false,
                    isMultilateral: true,
                    flows: [
                        prepareFlow({
                            name: 'ПП "Тестер"',
                            edrpou: '77777777',
                            email: '<EMAIL>',
                        }),
                        prepareFlow({
                            name: 'ПП "Тестер 2"',
                            edrpou: '88888888',
                            email: '<EMAIL>',
                        }),
                        prepareFlow({
                            name: 'ПП "Тестер"',
                            edrpou: '77777777',
                            email: '<EMAIL>',
                        }),
                    ],
                },
                {
                    isParallel: true,
                    counterparties: [
                        {
                            edrpou: '77777777',
                            name: 'ПП "Тестер"',
                            email: '<EMAIL>',
                            shouldSign: true,
                            isEmailHidden: false,
                        },
                        {
                            edrpou: '88888888',
                            name: 'ПП "Тестер 2"',
                            email: '<EMAIL>',
                            shouldSign: true,
                            isEmailHidden: false,
                        },
                        {
                            edrpou: '77777777',
                            name: 'ПП "Тестер"',
                            email: '<EMAIL>',
                            shouldSign: true,
                            isEmailHidden: false,
                        },
                    ],
                },
            );
        });

        it('should return data for parallel multilateral doc', () => {
            assert(
                {
                    isInternal: false,
                    isMultilateral: true,
                    flows: [
                        prepareFlow({
                            name: 'ПП "Тестер"',
                            edrpou: '77777777',
                            email: '<EMAIL>',
                            order: 1,
                        }),
                        prepareFlow({
                            name: 'ПП "Тестер 2"',
                            edrpou: '88888888',
                            email: '<EMAIL>',
                            order: 2,
                        }),
                        prepareFlow({
                            name: 'ПП "Тестер"',
                            edrpou: '77777777',
                            email: '<EMAIL>',
                            order: 3,
                        }),
                    ],
                },
                {
                    isParallel: false,
                    counterparties: [
                        {
                            edrpou: '77777777',
                            name: 'ПП "Тестер"',
                            email: '<EMAIL>',
                            shouldSign: true,
                            isEmailHidden: false,
                        },
                        {
                            edrpou: '88888888',
                            name: 'ПП "Тестер 2"',
                            email: '<EMAIL>',
                            shouldSign: true,
                            isEmailHidden: false,
                        },
                        {
                            edrpou: '77777777',
                            name: 'ПП "Тестер"',
                            email: '<EMAIL>',
                            shouldSign: true,
                            isEmailHidden: false,
                        },
                    ],
                },
            );
        });

        it('should return data when recipient first sign document', () => {
            assert(
                {
                    firstSignBy: 'recipient',
                    edrpouRecipient: '55555555',
                    companyNameRecipient: 'ПП "Тестер"',
                    emailRecipient: '<EMAIL>',
                    expectedRecipientSignatures: 1,
                    isRecipientEmailHidden: false,
                    edrpouOwner: '77777777',
                    companyNameOwner: 'ПП "Тестер"',
                    emailOwner: '<EMAIL>',
                    expectedOwnerSignatures: 1,
                },
                {
                    isParallel: false,
                    counterparties: [
                        {
                            edrpou: '55555555',
                            name: 'ПП "Тестер"',
                            email: '<EMAIL>',
                            shouldSign: true,
                            isEmailHidden: false,
                        },
                        {
                            edrpou: '77777777',
                            name: 'ПП "Тестер"',
                            email: '<EMAIL>',
                            shouldSign: true,
                            isEmailHidden: false,
                        },
                    ],
                },
            );
        });

        it('should return data when owner first sign document', () => {
            assert(
                {
                    firstSignBy: 'owner',
                    edrpouRecipient: '55555555',
                    companyNameRecipient: 'ПП "Тестер"',
                    emailRecipient: 'emailRecipient',
                    expectedRecipientSignatures: 1,
                    isRecipientEmailHidden: false,
                    edrpouOwner: '77777777',
                    companyNameOwner: 'ПП "Тестер"',
                    emailOwner: 'emailOwner',
                    expectedOwnerSignatures: 1,
                },
                {
                    isParallel: false,
                    counterparties: [
                        {
                            edrpou: '77777777',
                            name: 'ПП "Тестер"',
                            email: 'emailOwner',
                            shouldSign: true,
                            isEmailHidden: false,
                        },
                        {
                            edrpou: '55555555',
                            name: 'ПП "Тестер"',
                            email: 'emailRecipient',
                            shouldSign: true,
                            isEmailHidden: false,
                        },
                    ],
                },
            );
        });

        it('should return empty data when recipient data not exists', () => {
            assert(
                {
                    firstSignBy: 'owner',
                    edrpouRecipient: null,
                    companyNameRecipient: null,
                    emailRecipient: null,
                    expectedRecipientSignatures: 1,
                    isRecipientEmailHidden: null,
                    edrpouOwner: '77777777',
                    companyNameOwner: 'ПП "Тестер"',
                    emailOwner: 'emailOwner',
                    expectedOwnerSignatures: 1,
                },
                {
                    isParallel: false,
                    counterparties: [],
                },
            );
        });
    });

    describe('editFormToEmployeeSignersPayload', () => {
        it('should return empty employee payload correct', () => {
            expect(
                editFormToEmployeeSignersPayload({
                    employeesSignIsOrdered: true,
                    employeesSigners: [],
                    employeesWhichSigned: [],
                }),
            ).to.deep.equal({
                signers_settings: {
                    parallel_signing: false,
                    entities: [],
                },
            });
        });

        it('should return mapped payload correct', () => {
            expect(
                editFormToEmployeeSignersPayload({
                    employeesSignIsOrdered: true,
                    employeesSigners: [
                        {
                            type: 'role',
                            id: '5a5cdc1b-6207-48db-abdc-a9a4e1b4c07d',
                        } as Actor,
                    ],
                    employeesWhichSigned: [],
                }),
            ).to.deep.equal({
                signers_settings: {
                    parallel_signing: false,
                    entities: [
                        {
                            id: '5a5cdc1b-6207-48db-abdc-a9a4e1b4c07d',
                            type: 'role',
                        },
                    ],
                },
            });

            expect(
                editFormToEmployeeSignersPayload({
                    employeesSignIsOrdered: true,
                    employeesSigners: [
                        {
                            type: 'role',
                            id: '5a5cdc1b-6207-48db-abdc-a9a4e1b4c07d',
                        } as Actor,
                    ],
                    employeesWhichSigned: [
                        {
                            type: 'role',
                            id: '4a5cdc1b-6207-48db-abdc-a9a4e1b4c07d',
                        } as Actor,
                    ],
                }),
            ).to.deep.equal({
                signers_settings: {
                    parallel_signing: false,
                    entities: [
                        {
                            type: 'role',
                            id: '4a5cdc1b-6207-48db-abdc-a9a4e1b4c07d',
                        },
                        {
                            id: '5a5cdc1b-6207-48db-abdc-a9a4e1b4c07d',
                            type: 'role',
                        },
                    ],
                },
            });
        });
    });

    describe('editFormToCounterpartiesPayload', () => {
        it('should return recipients for non internal payload', () => {
            expect(
                editFormToCounterpartiesPayload({
                    isInternal: false,
                    isParallel: false,
                    counterparties: [
                        {
                            edrpou: '77777777',
                            name: 'ПП "Тестер"',
                            email: 'emailOwner',
                            shouldSign: true,
                            isEmailHidden: false,
                        },
                        {
                            edrpou: '55555555',
                            name: 'ПП "Тестер"',
                            email: '',
                            shouldSign: false,
                            isEmailHidden: true,
                        },
                    ],
                }),
            ).to.deep.equal({
                recipients_settings: {
                    is_internal: false,
                    is_ordered: true,
                    recipients: [
                        {
                            edrpou: '77777777',
                            is_email_hidden: false,
                            role: 'signer',
                            emails: ['emailOwner'],
                        },
                        {
                            edrpou: '55555555',
                            is_email_hidden: true,
                            role: 'viewer',
                            emails: null,
                        },
                    ],
                },
            });
        });

        it('should return empty recipients array if isInternal', () => {
            expect(
                editFormToCounterpartiesPayload({
                    isInternal: true,
                    isParallel: false,
                    counterparties: [
                        {
                            edrpou: '77777777',
                            name: 'ПП "Тестер"',
                            email: 'emailOwner',
                            shouldSign: true,
                            isEmailHidden: false,
                        },
                        {
                            edrpou: '55555555',
                            name: 'ПП "Тестер"',
                            email: '',
                            shouldSign: false,
                            isEmailHidden: true,
                        },
                    ],
                }),
            ).to.deep.equal({
                recipients_settings: {
                    is_internal: true,
                },
            });
        });
    });

    describe('editFormToVersionSettingsPayload', () => {
        it('should return version settings', () => {
            expect(
                editFormToVersionSettingsPayload({
                    isVersioned: true,
                }),
            ).to.deep.equal({
                version_settings: {
                    is_versioned: true,
                },
            });
        });
    });

    describe('editFormToTagSettingsPayload', () => {
        it('should return replace tags payload correct', () => {
            expect(
                editFormToTagSettingsPayload({
                    selectedTags: [
                        { id: '1', name: 'tag1' },
                        { id: '', isNew: true, name: 'new' },
                    ],
                }),
            ).to.deep.equal({
                tags_settings: {
                    replace: {
                        tags_ids: ['1'],
                        new_tags: ['new'],
                    },
                },
            });
        });
    });
});
