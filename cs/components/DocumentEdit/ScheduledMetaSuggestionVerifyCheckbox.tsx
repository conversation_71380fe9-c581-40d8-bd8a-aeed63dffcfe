import React, { useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import { Checkbox } from '@vchasno/ui-kit';

import { useDocumentEditContext } from 'components/DocumentEdit/DocumentEditContext';
import {
    documentMetaSuggestionToInfoFields,
    normalizeDate,
} from 'components/DocumentEdit/mappers';
import { EditDocumentForm } from 'components/DocumentEdit/types';
import { getIsScanDocVerifiedByUser } from 'components/ScheduledMetaSuggestionIcon/utils';
import isEqual from 'lodash/isEqual';
import { t } from 'ttag';

const ScheduledMetaSuggestionVerifyCheckbox: React.FC = () => {
    const methods = useFormContext<EditDocumentForm>();
    const { doc } = useDocumentEditContext();

    const metaSuggestionAmount = methods.watch('metaSuggestionAmount');
    const metaSuggestionDate = methods.watch('metaSuggestionDate');
    const metaSuggestionNumber = methods.watch('metaSuggestionNumber');
    const metaSuggestionCategory = methods.watch('metaSuggestionCategory');
    const metaSuggestionCounterparties = methods.watch(
        'metaSuggestionCounterparties',
    );

    const docMetaSuggestionEdrpouRecipient =
        doc?.scheduledMetaSuggestion?.suggestion?.edrpouRecipient;
    const isMetaSuggestionEdrpouRecipientChanged = docMetaSuggestionEdrpouRecipient
        ? !metaSuggestionCounterparties?.some(
              (metaSuggestionCounterparty) =>
                  metaSuggestionCounterparty.edrpou ===
                  docMetaSuggestionEdrpouRecipient,
          )
        : false;

    const docMetaSuggestion = documentMetaSuggestionToInfoFields(doc);

    const isAmountChanged =
        metaSuggestionAmount?.toString() !==
        docMetaSuggestion.metaSuggestionAmount?.toString();
    const isDateChanged = !isEqual(
        normalizeDate(metaSuggestionDate),
        normalizeDate(docMetaSuggestion.metaSuggestionDate),
    );
    const isNumberChanged =
        metaSuggestionNumber?.toString() !==
        docMetaSuggestion.metaSuggestionNumber?.toString();
    const isCategoryChanged =
        metaSuggestionCategory?.toString() !==
        docMetaSuggestion.metaSuggestionCategory?.toString();

    const isMetaSuggestionFieldChanged =
        isAmountChanged ||
        isDateChanged ||
        isNumberChanged ||
        isCategoryChanged ||
        isMetaSuggestionEdrpouRecipientChanged;

    useEffect(() => {
        if (isMetaSuggestionFieldChanged) {
            methods.setValue('isScheduledMetaSuggestionVerify', false);
        }
    }, [isMetaSuggestionFieldChanged]);

    // для не відсканованих документів, або тих, які не пройшли ще обробку не показуємо чекбокс,
    // або коли нерозпізнано жодного параметру
    if (
        !doc?.scheduledMetaSuggestion?.scheduledBy ||
        doc?.scheduledMetaSuggestion.status !== 'finished' ||
        (!metaSuggestionAmount &&
            !metaSuggestionDate &&
            !metaSuggestionNumber &&
            !metaSuggestionCategory)
    ) {
        return null;
    }

    // якщо у відсканованого документа є хоч один заповнений параметр юзером, то не показуємо чекбокс
    if (getIsScanDocVerifiedByUser(doc)) {
        return null;
    }

    return (
        <Controller
            control={methods.control}
            name="isScheduledMetaSuggestionVerify"
            render={({ field }) => (
                <Checkbox
                    checked={field.value as boolean}
                    onChange={field.onChange}
                    disabled={isMetaSuggestionFieldChanged}
                    label={t`Дані розпізнано вірно`}
                />
            )}
        />
    );
};

export default ScheduledMetaSuggestionVerifyCheckbox;
