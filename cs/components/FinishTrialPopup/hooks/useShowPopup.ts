import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { differenceInDays } from 'date-fns';
import { getLocalStorageItem } from 'lib/webStorage';
import {
    getActiveTrialEmployeesProRate,
    getCurrentCompanyEdrpou,
} from 'selectors/app.selectors';

import { ShowedStateItemProps } from '../types';

import { setDates, setShowedStateToStorage } from '../utils';

import { isShowedEndTrialPopupName } from '../constants';

export const useShowPopup = () => {
    const edrpouCurrentCompany = useSelector(getCurrentCompanyEdrpou);
    const activeTrialProRate = useSelector(getActiveTrialEmployeesProRate);

    const [isOpen, setIsOpen] = useState(false);

    const { today, endDateTrial } = setDates(activeTrialProRate?.endDate || '');

    const daysLeft = differenceInDays(endDateTrial, today);

    useEffect(() => {
        if (!edrpouCurrentCompany || !activeTrialProRate) {
            return;
        }

        const isShowed = (getLocalStorageItem(isShowedEndTrialPopupName)?.[
            edrpouCurrentCompany
        ] as ShowedStateItemProps) || {
            isShowedFiveDays: false,
            isShowedTwoDays: false,
        };

        if (daysLeft <= 2 && !isShowed.isShowedTwoDays) {
            isShowed.isShowedTwoDays = true;
            isShowed.isShowedFiveDays = true;
            setIsOpen(true);
        } else if (daysLeft <= 5 && !isShowed.isShowedFiveDays) {
            isShowed.isShowedFiveDays = true;
            setIsOpen(true);
        }

        setShowedStateToStorage(edrpouCurrentCompany, isShowed);
    }, [edrpouCurrentCompany, activeTrialProRate]);

    return {
        isOpen: isOpen,
        daysLeft: daysLeft,
        setIsOpen: setIsOpen,
    };
};
