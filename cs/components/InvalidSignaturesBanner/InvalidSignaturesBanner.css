.container {
    overflow: hidden;
    max-height: 78px;
    border: 1.5px solid var(--orange-border);
    margin-bottom: 30px;
    background: var(--white-bg);
    border-radius: var(--border-radius);
    transition: max-height ease-in 0.3s;
}

.containerShowed {
    max-height: 1000px;
}

.title {
    position: relative;
    display: flex;
    align-items: center;
    padding: 10px;
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
}

.title p {
    display: inline-block;
    max-width: 75%;
}

.titleIcon {
    width: 56px;
    flex: 0 0 56px;
    margin-right: 10px;
}

.hideButton {
    position: absolute;
    top: 30px;
    right: 30px;
    width: 20px;
    cursor: pointer;
    transform: rotate(0);
}

.containerShowed .hideButton {
    transform: rotate(180deg);
}

.main {
    display: flex;
    padding: 0 20px 30px;
}

.mainContent {
    margin-top: 32px;
}

.textIcon {
    position: relative;
    top: 4px;
    display: inline-block;
    width: 20px;
}

.mainImage {
    display: none;
    width: 263px;
    flex: 0 0 263px;
}

.mainButtons {
    display: flex;
    margin-top: 40px;
}

.mainButtons button {
    min-height: 50px;
}

.mainButtons button + button {
    margin-left: 10px;
}

@media all and (min-width: 580px) {
    .title {
        padding: 16px 40px 16px 30px;
        font-size: 22px;
        line-height: 24px;
    }

    .titleIcon {
        margin-right: 10px;
    }

    .mainButtons button {
        min-height: 40px;
    }
}

@media all and (min-width: 1100px) {
    .mainImage {
        display: block;
        align-self: flex-start;
    }
}

@media all and (min-width: 1200px) {
    .main {
        display: flex;
        padding: 0 80px 60px 40px;
    }

    .mainContent {
        margin-right: 80px;
    }

    .mainImage {
        display: block;
        align-self: flex-start;
    }
}

@media all and (min-width: 1220px) {
    .title {
        font-size: 24px;
        line-height: 28px;
    }
}
