import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { t } from 'ttag';

import { getFiltersSignFolder } from '../../../../selectors/filter.selectors';
import filtersActionCreators from '../../../filters/filtersActionCreators';

import eventTracking from '../../../../services/analytics/eventTracking';
import FilterResetItem from '../FilterResetItem';

const SignStatusFilterResetBadge: React.FC<
    React.PropsWithChildren<unknown>
> = () => {
    const dispatch = useDispatch();
    const signFolder = useSelector(getFiltersSignFolder);

    if (signFolder.length === 0 || signFolder.find((item) => item === 'all')) {
        return null;
    }

    const handleClick = () => {
        eventTracking.sendToGTM({
            category: 'New_filter',
            action: 'Clear_filter',
            label: 'signing_status',
        });

        dispatch(filtersActionCreators.onRemoveAllSignFolderFilter());
    };

    return (
        <FilterResetItem onClick={handleClick}>
            {t`Статус підписання`}
        </FilterResetItem>
    );
};

export default SignStatusFilterResetBadge;
