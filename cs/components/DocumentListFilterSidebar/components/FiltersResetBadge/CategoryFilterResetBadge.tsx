import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import filtersActionCreators from 'components/filters/filtersActionCreators';
import {
    getFiltersDocumentCategories,
    isFilterSelectValue,
} from 'selectors/filter.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';

import FilterResetItem from '../FilterResetItem';

const CategoryFilterResetBadge: React.FC<
    React.PropsWithChildren<unknown>
> = () => {
    const dispatch = useDispatch();
    const documentCategories = useSelector(getFiltersDocumentCategories);

    if (!isFilterSelectValue(documentCategories)) {
        return null;
    }

    const handleClick = () => {
        eventTracking.sendToGTM({
            category: 'New_filter',
            action: 'Clear_filter',
            label: 'documents_type',
        });
        dispatch(filtersActionCreators.onResetCategoryFilter());
    };

    return (
        <FilterResetItem onClick={handleClick}>
            {t`Тип документів`}
        </FilterResetItem>
    );
};

export default CategoryFilterResetBadge;
