import React, { FC, MouseEvent, useState } from 'react';
import { connect } from 'react-redux';

import cn from 'classnames';
import { t } from 'ttag';

import { StoreState } from '../../types/store';

import actionCreators from './proRateInfoPopupActionCreators';

import Bullets from '../ui/bullets/bullets';
import Icon from '../ui/icon/icon';
import Popup from '../ui/popup/popup';

import { PRO_FUNCTIONS, PRO_FUNCTIONS_LINKS } from '../../lib/constants';
import ProFunctionalityInfo, {
    ProFuncId,
} from '../proFunctionalityInfo/proFunctionalityInfo';
import ProRatePriceAndLinks from '../proRatePriceAndLinks/proRatePriceAndLinks';
import { isUserUsedTrial } from '../proRateTrialPopup/helper';
import { DispatchToProps, State } from './proRateInfoPopupTypes';

import SvgArrowLeft from './images/arrowLeft.svg';
import SvgArrowRight from './images/arrowRight.svg';

import css from './proRateInfoPopup.css';

type Props = State & DispatchToProps;

function mapStateToProps(state: StoreState) {
    return {
        ...state.proRateInfoPopup,
        currentUser: state.app.currentUser,
    };
}

const ProRateInfoPopup: FC<React.PropsWithChildren<Props>> = (props: Props) => {
    const [currIndex, changeIndex] = useState<number>(0);

    const proRate = isUserUsedTrial(props.currentUser);

    const canIncrease = currIndex + 1 < PRO_FUNCTIONS.length;
    const canDecrease = currIndex > 0;

    const handleDecrease = (evt: MouseEvent): void => {
        evt.preventDefault();
        if (canDecrease) changeIndex((i) => i - 1);
    };
    const handleIncrease = (evt: MouseEvent): void => {
        evt.preventDefault();
        if (canIncrease) changeIndex((i) => i + 1);
    };

    const leftArrowClasses = cn(
        css.iconArrow,
        canDecrease ? css.iconArrowActive : css.iconArrowDisabled,
    );
    const rightArrowClasses = cn(
        css.iconArrow,
        canIncrease ? css.iconArrowActive : css.iconArrowDisabled,
    );

    return (
        <Popup fullContent active={props.isActive} onClose={props.onClose}>
            <div className={css.root}>
                <h3 className={css.title}>
                    {!proRate
                        ? t`Підключайте на тестовий період всі функції сервісу та опрацьовуйте документи вдвічі швидше.`
                        : t`Оберіть тариф з розширеними функціями!`}
                </h3>
                <div className={css.contentWrapper}>
                    <div className={css.centerBlock}>
                        <div className={css.content}>
                            <ProFunctionalityInfo
                                funcId={PRO_FUNCTIONS[currIndex] as ProFuncId}
                            >
                                {PRO_FUNCTIONS_LINKS[currIndex] && (
                                    <p className={css.link}>
                                        <a
                                            href={
                                                PRO_FUNCTIONS_LINKS[currIndex]
                                            }
                                            rel="noopener noreferrer"
                                            target="_blank"
                                        >
                                            {t`Переглянути відео про функцію`}
                                        </a>
                                    </p>
                                )}
                                <div>
                                    <ProRatePriceAndLinks
                                        trackingLabel={props.locationId}
                                    />
                                </div>
                            </ProFunctionalityInfo>
                        </div>
                    </div>
                </div>
                <div className={css.carousel}>
                    <div className={css.iconArrowWrap} onClick={handleDecrease}>
                        <div className={leftArrowClasses}>
                            <Icon glyph={SvgArrowLeft} />
                        </div>
                    </div>
                    <Bullets
                        // @ts-expect-error TS2322 [FIXME] Comment is autogenerated
                        size="big"
                        theme="#9aaabf"
                        activeIndex={currIndex}
                        length={PRO_FUNCTIONS.length}
                    />
                    <div className={css.iconArrowWrap} onClick={handleIncrease}>
                        <div className={rightArrowClasses}>
                            <Icon glyph={SvgArrowRight} />
                        </div>
                    </div>
                </div>
            </div>
        </Popup>
    );
};

export default connect(mapStateToProps, actionCreators)(ProRateInfoPopup);
