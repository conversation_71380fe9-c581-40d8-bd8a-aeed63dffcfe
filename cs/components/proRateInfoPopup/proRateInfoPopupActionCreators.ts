import { Thunk } from '../../types';

import { getCurrentCompanyPermissionMap } from '../../selectors/app.selectors';
import actions from './proRateInfoPopupActions';

import eventTracking from '../../services/analytics/eventTracking';
import { PermissionCategory } from './proRateInfoPopupTypes';

function onShow(): Thunk {
    return (dispatch) => {
        dispatch({ type: actions.PRO_RATE_INFO_POPUP__SHOW });
    };
}

function onClose(): Thunk {
    return (dispatch) => {
        dispatch({ type: actions.PRO_RATE_INFO_POPUP__CLOSE });
    };
}

function showProRatePopupToCompany(
    locationId: string,
    permission: PermissionCategory,
): Thunk {
    return (dispatch, getState): boolean => {
        dispatch({
            type: actions.PRO_RATE_INFO_POPUP__SET_TRACKING_LOCATION,
            locationId,
        });

        const showPopup = !getCurrentCompanyPermissionMap(getState())[
            permission
        ];

        if (showPopup) {
            dispatch(onShow());

            if (locationId) {
                eventTracking.sendEvent(
                    'pro-rate',
                    'show-info-about-pro',
                    locationId,
                );
            }
        }

        return showPopup;
    };
}

export default { onShow, onClose, showProRatePopupToCompany };
