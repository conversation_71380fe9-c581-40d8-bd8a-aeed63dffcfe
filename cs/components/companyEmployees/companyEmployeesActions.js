const actions = {
    COMPANY_EMPLOYEES__GET_TAGS_FILTERS: 'COMPANY_EMPLOYEES__GET_TAGS_FILTERS',
    COMPANY_EMPLOYEES__UPDATE_ROLES_FILTERS:
        'COMPANY_EMPLOYEES__UPDATE_ROLES_FILTERS',
    COMPANY_EMPLOYEES__GET_EMPLOYEES_WITH_INVALID_SIGNATURES:
        'COMPANY_EMPLOYEES__GET_EMPLOYEES_WITH_INVALID_SIGNATURES',
    COMPANY_EMPLOYEES__CREATE_INVALID_SIGNATURES_FILTER:
        'COMPANY_EMPLOYEES__CREATE_INVALID_SIGNATURES_FILTER',
    COMPANY_EMPLOYEES__ADD_INVALID_SIGNATURES_FILTER:
        'COMPANY_EMPLOYEES__ADD_INVALID_SIGNATURES_FILTER',
    COMPANY_EMPLOYEES__REMOVE_INVALID_SIGNATURES_FILTER:
        'COMPANY_EMPLOYEES__REMOVE_INVALID_SIGNATURES_FILTER',
    COMPANY_EMPLOYEES__RESET_INVALID_SIGNATURES_FILTER:
        'COMPANY_EMPLOYEES__RESET_INVALID_SIGNATURES_FILTER',
    COMPANY_EMPLOYEES__SET_EMPLOYEES_STATUS_FILTER:
        'COMPANY_EMPLOYEES__SET_EMPLOYEES_STATUS_FILTER',
    COMPANY_EMPLOYEES__GET_EMPLOYEES: 'COMPANY_EMPLOYEES__GET_EMPLOYEES',
    COMPANY_EMPLOYEES__UPDATE_SEARCH_QUERY:
        'COMPANY_EMPLOYEES__UPDATE_SEARCH_QUERY',
    COMPANY_EMPLOYEES__RESET_FILTERS: 'COMPANY_EMPLOYEES__RESET_FILTERS',
    COMPANY_EMPLOYEES__SET_EMPLOYEES: 'COMPANY_EMPLOYEES__SET_EMPLOYEES',
    COMPANY_EMPLOYEES__SET_IS_SHOW_TERMINATE_EMPLOYEES_POPUP:
        'COMPANY_EMPLOYEES__SET_IS_SHOW_TERMINATE_EMPLOYEES_POPUP',
    COMPANY_EMPLOYEES__SET_LIST_PAGE: 'COMPANY_EMPLOYEES__SET_LIST_PAGE',
    COMPANY_EMPLOYEES__SET_LIST_LIMIT: 'COMPANY_EMPLOYEES__SET_LIST_LIMIT',
};

export default actions;
