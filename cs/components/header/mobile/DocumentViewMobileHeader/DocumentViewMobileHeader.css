.root {
    position: fixed;
    z-index: 100;
    top: 0;
    right: 0;
    left: 0;
    height: var(--mobile-header-height);
    background-color: var(--white-bg);
    color: var(--content-color);
    transform: translateY(-100%);
    transition: transform 0.3s;
}

.show {
    transform: translateY(0);
}

.iconBtn {
    display: flex;
    width: var(--mobile-header-height);
    height: var(--mobile-header-height);
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    color: var(--content-color);
}

.icon {
    width: 50%;
    height: 50%;
}

.title {
    max-width: calc(100% - 100px);
    flex-grow: 1;
    font-size: 16px;
    font-weight: 500;
    line-height: 16px;
    text-align: center;
}
