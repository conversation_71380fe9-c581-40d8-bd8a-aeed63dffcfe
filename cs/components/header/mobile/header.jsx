import React from 'react';
import { Link } from 'react-router-dom';

import PropTypes from 'prop-types';
import { User } from 'records/user';
import { ApplicationMode } from 'services/enums';

import ThemeToggle from '../../../contexts/theme/ThemeToggle';
import AllProjectsWidget from '../../AllProjectsWidget/AllProjectsWidget';
import AuthButtons from '../../AuthButtons';
import ContactsPopover from '../../ContactsPopover/ContactsPopover';
import SideMenuContainer from '../../sideMenu/sideMenuContainer';
import UserInviteButton from '../../userInviteButton/userInviteButton';
import MultiBrandLogo from '../components/MultiBrandLogo';
import CreateDocument from './CreateDocument';

// styles
import css from './header.css';

const Uploader = React.lazy(() =>
    import(/* webpackChunkName: "Uploader" */ '../../uploader/uploader'),
);

const Header = (props) => {
    const { authButtons, button, currentUser, location } = props;
    const isMasterAdmin =
        currentUser &&
        currentUser.currentRole &&
        currentUser.currentRole.isMasterAdmin;

    if (props.applicationMode === ApplicationMode.SIGN_SESSION) {
        return (
            <div className={css.root}>
                <div className={css.wrapper}>
                    <div className={css.logoHolder}>
                        <div className={css.logo}>
                            <MultiBrandLogo />
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (props.applicationMode === ApplicationMode.SHARED_DOCUMENT_VIEW) {
        return (
            <div className={css.root}>
                <div className={css.wrapper}>
                    <div className={css.menuLogoContainer}>
                        <div className={css.tool}>
                            <AllProjectsWidget alignPopover="left" />
                        </div>
                        <div className={css.logoHolder}>
                            <div className={css.logo}>
                                <MultiBrandLogo />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    let buttons = button;
    if (authButtons && !currentUser) {
        buttons = <AuthButtons />;
    }

    return (
        <>
            <div className={css.placeholder} />
            <div className={css.root}>
                <div className={css.wrapper}>
                    <div className={css.menuLogoContainer}>
                        <div className={css.menu}>
                            <SideMenuContainer
                                location={location}
                                buttons={buttons}
                                currentUser={props.currentUser}
                                onAddCompany={props.onAddCompany}
                            />
                        </div>
                        <div className={css.logoHolder}>
                            <Link className={css.logo} to="/app">
                                <MultiBrandLogo />
                            </Link>
                        </div>
                    </div>
                    <div className={css.tools}>
                        <div className={css.tool}>
                            <ThemeToggle className={css.themeToggle} />
                        </div>
                        <div className={css.tool}>
                            <React.Suspense fallback={null}>
                                <Uploader isIconButtonType />
                            </React.Suspense>
                        </div>
                        <CreateDocument />
                        <div className={css.tool}>
                            <ContactsPopover />
                        </div>

                        {currentUser && (
                            <div className={css.tool}>
                                <UserInviteButton
                                    iconTitle={
                                        isMasterAdmin
                                            ? 'Запросити контрагента'
                                            : 'Запросити співробітника'
                                    }
                                    isTooltipVisible={props.isTooltipVisible}
                                    isTooltipActive={props.isTooltipActive}
                                    popoverPosition="right"
                                    popoverPointerPosition="right"
                                    onCloseTooltip={props.onCloseTooltip}
                                    onOpenPopup={props.onOpenInvitePopup}
                                    onClosePopup={props.onClosePopup}
                                />
                            </div>
                        )}
                        <div className={css.tool}>
                            <AllProjectsWidget alignPopover="right" />
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

Header.propTypes = {
    applicationMode: PropTypes.string,
    currentUser: PropTypes.instanceOf(User),
    authButtons: PropTypes.bool,
    isInviteAllowed: PropTypes.bool,
    isTooltipActive: PropTypes.bool,
    isTooltipVisible: PropTypes.bool,
    isNewCompanyPopupShown: PropTypes.bool,
    isUploaderButtonDisabled: PropTypes.bool,
    button: PropTypes.node,
    location: PropTypes.object,
    onOpenInvitePopup: PropTypes.func,
    onClosePopup: PropTypes.func,
    onCloseTooltip: PropTypes.func,
    onAddCompany: PropTypes.func,
    onShowUploaderPopup: PropTypes.func,
};

export default Header;
