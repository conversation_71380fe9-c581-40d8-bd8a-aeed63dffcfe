import React from 'react';

import { isClientRendering } from 'lib/helpers';
import { getLocationQuery } from 'lib/url';
import { UserPropType } from 'records/user';
import { t } from 'ttag';

import Button from '../ui/button/button';

import io from '../../services/io';
import Footer from '../Footer';
import Header from '../header/Header';

import css from './canceledSubscription.css';

const CanceledSubscription = ({ currentUser }) => {
    const query = isClientRendering()
        ? getLocationQuery(document.location)
        : {};
    const contentData = currentUser
        ? {
              title: t`Ви більше не будете отримувати листи з нагадуваннями про неопрацьовані запити у Вчасно.`,
              text: t`Якщо ви вже тут, переглянете їх?`,
              url: io.buildUrl('/app', query),
              buttonText: t`Перейти до документів`,
          }
        : {
              title: t`Ви більше не будете отримувати листи з нагадуваннями про запрошення у Вчасно.`,
              text: t`Але документи від ваших партнерів все ще чекають на опрацювання. Якщо ви вже тут, переглянете їх?`,
              url: io.buildUrl('/auth/registration', query),
              buttonText: t`Зареєструватись`,
          };
    return (
        <div className={css.root}>
            <Header authButtons currentUser={currentUser} typeStatic />
            <div className={css.container}>
                <div className={css.wrapper}>
                    <div>
                        <img
                            src={`${config.STATIC_HOST}/images/tetyanka1.png`}
                            alt=""
                        />
                    </div>
                    <div className={css.title}>{contentData.title}</div>
                    <div className={css.text}>{contentData.text}</div>
                    <a href={contentData.url}>
                        <Button size="small" theme="blue" typeContour>
                            {contentData.buttonText}
                        </Button>
                    </a>
                </div>
            </div>
            <Footer />
        </div>
    );
};

CanceledSubscription.propTypes = {
    currentUser: UserPropType,
};

export default CanceledSubscription;
