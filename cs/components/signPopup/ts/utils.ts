import io from 'services/io';
import loggerService from 'services/logger';

/**
 * Тип джерела ключа для сертифікат
 * qualified_key - апаратний або хмарний ключ
 * file_key - файловий ключ
 * usb_key - USB/апаратний ключ
 * cloud_key - хмарний ключ
 */
export type KeySourceType =
    | 'file_key'
    | 'qualified_key'
    | 'cloud_key'
    | 'usb_key';

interface MainCertificateInfo {
    certEndDate: string;
    isStamp: boolean;
    state: string;
    locality: string;
}

/**
 * Мінімальна інформація про сертифікат та ключ для оновлення
 */
interface MainKeyInfo {
    serialNumber: string;
    caServer: string;
}

export interface CertificateUpdatePayload {
    type: KeySourceType;
    date_end: string;
    is_stamp: boolean;
    serial_number: string;
    acsk: string;
    state: string;
    locality: string;
}

export async function updateCertificateInfo(
    certificateInfo: MainCertificateInfo,
    keyInfo: MainKeyInfo,
    type: KeySourceType,
) {
    const payload: CertificateUpdatePayload = {
        type,
        date_end: certificateInfo.certEndDate,
        is_stamp: certificateInfo.isStamp,
        serial_number: keyInfo.serialNumber,
        acsk: keyInfo.caServer,
        state: certificateInfo.state,
        locality: certificateInfo.locality,
    };
    try {
        await io.post('/internal-api/signatures/certificates', payload);
    } catch {
        loggerService.error('Error updating certificate info');
    }
}
