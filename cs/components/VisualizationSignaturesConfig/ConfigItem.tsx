import React from 'react';

import Icon from 'ui/icon';
import { Glyph } from 'ui/icon/icon';

import SelectedSvg from './images/selected.svg';
import UnselectedSvg from './images/unselected.svg';

import css from './visualizationSignaturesConfig.css';

interface ConfigItemProps {
    item: {
        value: string;
        label: string;
        icon: Glyph;
        activeIcon: Glyph;
    };
    onChange: (configValue: string) => void;
    isChecked: boolean;
}

const ConfigItem: React.FC<ConfigItemProps> = ({
    item,
    onChange,
    isChecked,
}) => (
    <div className={css.configItemWrapper}>
        <input
            hidden
            className={css.input}
            type="radio"
            name="visualizationConfig"
            value={item.value}
            id={`visualizationConfig_${item.value}`}
            checked={isChecked}
            onChange={() => onChange(item.value)}
        />
        <label
            className={css.inputLabel}
            htmlFor={`visualizationConfig_${item.value}`}
        >
            <div className={css.configItem}>
                <div className={css.configItemCircle}>
                    <Icon glyph={isChecked ? SelectedSvg : UnselectedSvg} />
                </div>
                <p className={css.configItemText}>{item.label}</p>
                <div className={css.configItemImage}>
                    <Icon glyph={isChecked ? item.activeIcon : item.icon} />
                </div>
            </div>
        </label>
    </div>
);

export default ConfigItem;
