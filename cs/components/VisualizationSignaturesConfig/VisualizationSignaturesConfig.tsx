import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import ConfigItem from 'components/VisualizationSignaturesConfig/ConfigItem';
import { visualizationConfig } from 'components/VisualizationSignaturesConfig/constants';
import companyCardActionCreators from 'components/companyCard/companyCardActionCreators';
import { getCurrentCompanyConfig } from 'selectors/app.selectors';

import css from './visualizationSignaturesConfig.css';

const VisualizationSignaturesConfig: React.FC = () => {
    const dispatch = useDispatch();
    const companyConfig = useSelector(getCurrentCompanyConfig);
    const renderSignatureAtPageConfig = companyConfig.render_signature_at_page;

    const onChange = (value: string) => {
        dispatch(
            companyCardActionCreators.onChangeAdditionalConfig(
                {
                    render_signature_at_page: value,
                },
                {
                    render_signature_at_page: value,
                },
            ),
        );
    };

    return (
        <div className={css.container}>
            {visualizationConfig.map((item) => (
                <ConfigItem
                    item={item}
                    key={item.value}
                    isChecked={renderSignatureAtPageConfig === item.value}
                    onChange={onChange}
                />
            ))}
        </div>
    );
};

export default VisualizationSignaturesConfig;
