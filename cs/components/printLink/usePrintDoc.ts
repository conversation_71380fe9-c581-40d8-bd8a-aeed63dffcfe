import { useSelector } from 'react-redux';

import { getCurrentUser } from 'selectors/app.selectors';
import { getDocumentLocationVersionId } from 'selectors/router.selectors';
import { sendToGTMV4 } from 'services/analytics/gtm';
import { Document } from 'services/documents/ts/types';
import { getIsVersionedDocument } from 'services/documents/ts/utils';
import {
    getDocumentViewerConfigByCompany,
    getDocumentViewerUrl,
} from 'services/documents/utils';
import { DocumentRevokeStatus } from 'services/enums';

export const usePrintDoc = ({ doc }: { doc: Document }) => {
    const locationVersionId = useSelector(getDocumentLocationVersionId);
    const currentUser = useSelector(getCurrentUser);

    const isVersionedDoc = getIsVersionedDocument(doc);
    const isRevokeDoc = doc.revoke?.status === DocumentRevokeStatus.COMPLETED;

    const handlePrintClick = () => {
        sendToGTMV4({
            event: 'ec_docpage_icon_print_click',
        });
    };
    const config = {
        print: true,
        ...getDocumentViewerConfigByCompany(currentUser, {
            ...(isRevokeDoc && { renderAnnulmentMarkDocument: true }),
        }),
    };
    const versionConfig = {
        isVersionedDoc,
        locationVersionId,
    };

    const printDocumentViewerUrl = getDocumentViewerUrl(
        doc.id,
        doc.ext,
        config,
        '',
        versionConfig,
    );

    return {
        handlePrintClick,
        printDocumentViewerUrl,
    };
};
