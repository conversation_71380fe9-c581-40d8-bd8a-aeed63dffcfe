import React from 'react';
import { Provider } from 'react-redux';

import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

import { ConnectedRouter } from 'connected-react-router';
import ThemeContextProvider from 'contexts/theme';
import { isClientRendering } from 'lib/helpers';
import { queryClient } from 'lib/queries';
import applyPolyfills from 'services/polyfill';
import PostHogProvider from 'services/posthog';
import { setDefaultLang } from 'ttag';

import RootComponent from './RouterComponents';
import store, { browserHistory } from './store';

if (isClientRendering()) {
    setDefaultLang('uk');
    applyPolyfills();
}

const appWrapper = () => (
    <Provider store={store}>
        <ConnectedRouter history={browserHistory}>
            <ThemeContextProvider>
                <QueryClientProvider client={queryClient}>
                    <PostHogProvider>
                        <RootComponent />
                    </PostHogProvider>
                    <ReactQueryDevtools initialIsOpen={false} />
                </QueryClientProvider>
            </ThemeContextProvider>
        </ConnectedRouter>
    </Provider>
);

export default appWrapper;
