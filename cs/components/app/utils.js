import Cookies from 'js-cookie';
import { PRO_TRIALS_SET } from 'services/billing/constants';

import { AccountRate } from '../../services/enums';

import { isBrowserSupported } from '../../lib/helpers';

import {
    COOKIE_KEY_2FA_REMINDED,
    WS_KEY_COMPLETE_REGISTRATION,
    WS_KEY_NOT_SHOW_OLD_BROWSER_ALERT,
} from '../../lib/constants';
import {
    getLocalStorageItem,
    getSessionStorageItem,
    setLocalStorageItem,
} from '../../lib/webStorage';
import eventTracking from '../../services/analytics/eventTracking';
import auth from '../../services/auth';
import loggerService from '../../services/logger';

export function checkOldBrowser() {
    const isUnsupportedBrowser = !isBrowserSupported();
    const showAlertConditionBySessionStorage = !getSessionStorageItem(
        WS_KEY_NOT_SHOW_OLD_BROWSER_ALERT,
    );
    const showAlertConditionByCookie = !Cookies.get(
        WS_KEY_NOT_SHOW_OLD_BROWSER_ALERT,
    );
    return (
        isUnsupportedBrowser &&
        showAlertConditionBySessionStorage &&
        showAlertConditionByCookie
    );
}

export function check2FARemind(user) {
    if (!(user.emailConfirmed && user.registrationCompleted)) {
        return false;
    }
    return (
        !user.is2FAEnabledInProfile &&
        !user.is2FAEnabledByRule &&
        !Cookies.get(COOKIE_KEY_2FA_REMINDED)
    );
}

export async function initColbert() {
    if (config.COLBERT_TOKEN && window.colbert) {
        const loggingBlacklist = [
            // used for testers
            'NO_QUESTIONNAIRES',
            // used to inform about user's very first website visit
            'NEVER_VISITED',
        ];
        try {
            const { ts, id, sha1 } = await auth.colbertIdentify();
            // [HOTFIX]: Colbert have some problem working with SPA, so need to
            // explicitly set api host to prevent "UNDEFINED_API_HOST" error
            if (config.COLBERT_API_HOST)
                window.colbert('_API_HOST', config.COLBERT_API_HOST);
            window.colbert('create', config.COLBERT_TOKEN, {
                ts,
                id,
                sha1,
                onFailure: (err) => {
                    if (loggingBlacklist.indexOf(err.reason) === -1) {
                        loggerService.error('Error on colbert.create', err);
                    }
                },
            });
        } catch (err) {
            loggerService.error('Error on initializing Colbert service', {
                err: err.message,
            });
        }
    }
}

export function sendFBPCompleteRegistrationEvent(source, email) {
    const isSend = getLocalStorageItem(WS_KEY_COMPLETE_REGISTRATION);
    if (!isSend) {
        eventTracking.sendEventFBP('CompleteRegistration', {
            value: source,
            currency: email,
        });
        setLocalStorageItem(WS_KEY_COMPLETE_REGISTRATION, true);
    }
}

const isRatesHasActiveTrial = (rates) => {
    return rates.some((rate) => {
        return PRO_TRIALS_SET.has(rate);
    });
};

const isRoleHasUltimateRate = (rates) => {
    return rates.some((rate) => {
        return [
            AccountRate.ULTIMATE,
            AccountRate.ULTIMATE_2022_12,
            AccountRate.PRO,
        ].includes(rate);
    });
};

// for archive page and archive page with folder, exclude path /app/archive-preview
export const getIsArchiveNextPathname = (nextPathname) =>
    /^\/app\/archive(\/\d+)?$/.test(nextPathname);

export default {
    check2FARemind,
    checkOldBrowser,
    initColbert,
    sendFBPCompleteRegistrationEvent,
    isRatesHasActiveTrial,
    isRoleHasUltimateRate,
    getIsArchiveNextPathname,
};
