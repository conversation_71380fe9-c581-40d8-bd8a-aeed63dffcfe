import { useSelector } from 'react-redux';

import { useDocumentPageUX } from 'hooks/useDocumentPageUX';
import { getLocationQuery } from 'lib/url';
import { getApplicationMode } from 'selectors/app.selectors';
import { ApplicationMode } from 'services/enums';

export const useShowAppHeader = () => {
    const { hideAppHeader } = useDocumentPageUX();
    const applicationMode = useSelector(getApplicationMode);
    const { is_edi: isEdi } = getLocationQuery(location);

    return (
        !hideAppHeader &&
        applicationMode !== ApplicationMode.SIGN_SESSION &&
        isEdi !== '1'
    );
};
