import React from 'react';

import PropTypes from 'prop-types';
import { User } from 'records/user';
import { t } from 'ttag';

import Footer from '../Footer';
import AgreementStatements from '../agreementStatements/agreementStatements';
import Header from '../header/Header';
import statements from './statements';

// styles
import css from './privacy.css';

const Privacy = ({ currentUser }) => {
    const title = t`Політика конфіденційності`;

    return (
        <div className={css.root}>
            <Header authButtons currentUser={currentUser} typeStatic />

            <div className={css.container}>
                <div className={css.wrapper}>
                    <AgreementStatements data={statements} title={title} />
                </div>
            </div>
            <Footer />
        </div>
    );
};

Privacy.propTypes = {
    currentUser: PropTypes.instanceOf(User),
};

export default Privacy;
