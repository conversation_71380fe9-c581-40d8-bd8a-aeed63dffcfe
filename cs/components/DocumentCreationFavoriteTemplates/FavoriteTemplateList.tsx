import React from 'react';

import { FlexBox } from '@vchasno/ui-kit';

import EmptySearchBox from 'components/DocumentCreationCompanyTemplates/EmptySearchBox';
import PreviewItem from 'components/DocumentCreationCompanyTemplates/PreviewItem';
import { useSearchFetch } from 'components/DocumentCreationCompanyTemplates/SearchFetchProvider';
import SearchResultHeader from 'components/DocumentCreationCompanyTemplates/SearchResultHeader';
import { usePreviewTemplate } from 'components/DocumentCreationCompanyTemplates/TemplateList/usePreviewButtons';
import { useTemplateMakeUnFavoriteMutation } from 'components/DocumentCreationCompanyTemplates/TemplateList/useTemplateMakeUnFavoriteMutation';
import TemplateListGridLayout from 'components/DocumentCreationCompanyTemplates/TemplateListGridLayout';
import { CreationTemplate } from 'services/creationTemplates';

const FavoriteTemplateList: React.FC = () => {
    const templateMakeUnFavoriteMutation = useTemplateMakeUnFavoriteMutation();
    const previewTemplateButtonConfig = usePreviewTemplate();

    const { data, isFetching, count, isSearch, error } = useSearchFetch();

    const makeHandleFavoriteClick = (item: CreationTemplate) => async () => {
        templateMakeUnFavoriteMutation.mutate([item.id]);
    };

    if (error) {
        return <EmptySearchBox isError />;
    }

    if (!isFetching && isSearch && count === 0) {
        return <EmptySearchBox />;
    }

    return (
        <FlexBox direction="column">
            {isSearch && count > 0 && <SearchResultHeader />}
            <TemplateListGridLayout scale={1.25}>
                {data.map((item) => (
                    <PreviewItem
                        key={item.id}
                        title={item.title}
                        imgSrc={item.previewImgUrl}
                        buttons={[previewTemplateButtonConfig(item)]}
                        isFavorite={item.isFavorite}
                        isCompany={Boolean(item.companyId)}
                        createdDate={item.dateCreated}
                        updateDate={item.dateUpdated}
                        creatorEmail={item.creatorEmail}
                        creatorName={item.creatorName}
                        isPublic={!item.companyId}
                        onFavoriteClick={makeHandleFavoriteClick(item)}
                    />
                ))}
            </TemplateListGridLayout>
        </FlexBox>
    );
};

export default FavoriteTemplateList;
