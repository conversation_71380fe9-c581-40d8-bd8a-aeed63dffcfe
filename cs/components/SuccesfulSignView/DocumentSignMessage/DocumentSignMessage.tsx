import React from 'react';

import { t } from 'ttag';

export interface SignHintSuccessAlertProps {
    className?: string;
    isPlural?: boolean;
}

const DocumentSignMessage: React.FC<
    React.PropsWithChildren<SignHintSuccessAlertProps>
> = ({ className, isPlural = false }) => {
    return (
        <p className={className}>
            {isPlural
                ? t`Вони мають таку саму юридичну силу, як і паперові.
                            З цими документами ви можете проходити
                            податкову перевірку або використовувати їх в суді.
                            Папір більше не потрібний!`
                : t`Він має таку саму юридичну силу, як і паперовий.
                            З цим документом ви можете проходити
                            податкову перевірку або використовувати його в суді.
                            Папір більше не потрібний!`}
            <br />
            {t`Надсилайте документи в електронному вигляді!`}
        </p>
    );
};

export default DocumentSignMessage;
