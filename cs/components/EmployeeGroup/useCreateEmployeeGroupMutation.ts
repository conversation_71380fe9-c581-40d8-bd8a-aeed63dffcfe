import { useMutation } from '@tanstack/react-query';

import { queryClient } from 'lib/queries';
import { GET_EMPLOYEE_GROUP_LIST_QUERY } from 'lib/queriesConstants';
import { createEmployeeGroup } from 'services/groups';

export const useCreateEmployeeGroupMutation = () => {
    return useMutation({
        mutationFn: (params: Parameters<typeof createEmployeeGroup>) =>
            createEmployeeGroup(...params),
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [GET_EMPLOYEE_GROUP_LIST_QUERY],
            });
        },
    });
};
