import { useMutation } from '@tanstack/react-query';

import { queryClient } from 'lib/queries';
import {
    GET_EMPLOYEE_GROUP_LIST_QUERY,
    GET_EMPLOYEE_GROUP_QUERY,
} from 'lib/queriesConstants';
import { updateEmployeeGroup } from 'services/groups';

export const useUpdateEmployeeGroupMutation = () => {
    return useMutation({
        mutationFn: (params: Parameters<typeof updateEmployeeGroup>) =>
            updateEmployeeGroup(...params),
        onSuccess: (_data, variables) => {
            const employeeGroupId = variables[0].employeeGroupId;

            queryClient.invalidateQueries({
                queryKey: [GET_EMPLOYEE_GROUP_LIST_QUERY],
            });
            queryClient.invalidateQueries({
                queryKey: [GET_EMPLOYEE_GROUP_QUERY, employeeGroupId],
                refetchType: 'none',
            });
        },
    });
};
