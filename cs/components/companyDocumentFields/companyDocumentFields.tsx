import React, { FC, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
    Alert,
    Button,
    FlexBox,
    Pagination,
    Paragraph,
    Text,
    snackbarToast,
} from '@vchasno/ui-kit';

import { useShowProRatePopupHandlerWrapper } from 'hooks/useShowProRatePopupHandlerWrapper';
import { isEmptyObject, sortByOrder } from 'lib/helpers';
import { openInNewTab } from 'lib/navigation';
import { bindActionCreators } from 'redux';
import { getCurrentCompanyPermissionMap } from 'selectors/app.selectors';
import {
    createNewField,
    deleteField,
    swapFieldsPosition,
    updateField,
} from 'services/documentFields/api';
import { DOCUMENT_FIELDS_HINT } from 'services/documentFields/constants';
import { DocumentFieldType } from 'services/documentFields/enums';
import {
    DocumentField,
    DocumentFieldKeys,
} from 'services/documentFields/types';
import { validateSaveNewFields } from 'services/documentFields/utils';
import { mapStateToCurrentUser } from 'store/utils';
import { jt, t } from 'ttag';
import { confirmPopupConfigProps } from 'ui/confirmPopup/confirmPopup';

import { openPopup as openDocPopup } from './companyDocumentFieldsActionCreators';

import eventTracking from '../../services/analytics/eventTracking';
import uuid from '../../services/uuid';
import { PermissionCategory } from '../proRateInfoPopup/proRateInfoPopupTypes';
import AdditionalFieldSettingsAccessPopup from '../tagsAccessPopup/AdditionalFieldSettingsAccessPopup/AdditionalFieldSettingsAccessPopup';
import DocumentFieldRow from './documentFieldRow/documentFieldRow';
import EmptyState from './emptyState/emptyState';
import FieldForm from './fieldForm/fieldForm';

import css from './companyDocumentFields.css';

interface Props {
    documentFields: DocumentField[];
    onConfirmPopupShow: (popupProps: confirmPopupConfigProps) => void;
    onFetchCompanyDocumentFields: () => void;
}

type FieldState = 'new' | 'edit' | undefined;

const initField = (): EditableField => ({
    id: uuid(),
    name: '',
    type: DocumentFieldType.text,
    isRequired: false,
    enumOptions: null,
    state: 'new',
    roles: [],
    canEdit: false,
});

export interface EditableField extends DocumentField {
    errors?: string[];
    state?: FieldState;
}

const SHOW_LIMIT = 10;

const CompanyDocumentFields: FC<React.PropsWithChildren<Props>> = ({
    documentFields,
    onConfirmPopupShow,
    onFetchCompanyDocumentFields,
}) => {
    // Handle new fields
    const showProRatePopupHandlerWrapper = useShowProRatePopupHandlerWrapper();
    const isAccess = useSelector(getCurrentCompanyPermissionMap)[
        PermissionCategory.ADDITIONAL_FIELDS
    ];
    const [fields, setFields] = useState<EditableField[]>([]);
    const [newField, setNewFiled] = useState<EditableField | null>(null);
    const [isSavingProcess, setIsSavingProcess] = useState<boolean>(false);
    const [page, setPage] = useState(1);
    const [errorMessage, setErrorMessage] = useState<string>('');

    useEffect(() => {
        setFields([...documentFields].sort(sortByOrder));
    }, [documentFields]);

    const { currentUser } = useSelector(mapStateToCurrentUser);
    const dispatch = useDispatch();

    const { openPopup } = useMemo(
        () => bindActionCreators({ openPopup: openDocPopup }, dispatch),
        [],
    );

    const onDelete = async (fieldId: string) => {
        const documentField = fields.find((item) => item.id === fieldId);
        if (!documentField) {
            throw Error('Document field does not exist');
        }

        const deleteCallback = async (id: string) => {
            try {
                await deleteField(id);
                setPage(1);
            } catch (err) {
                // Show delete error and exit function
                const updatedFields = fields.map((item) => {
                    if (item.id === id) {
                        item.errors = [
                            // @ts-expect-error TS2352 [FIXME] Comment is autogenerated
                            jt`Виникла помилка при видаленні параметра. ${err.message}` as string,
                        ];
                    }
                    return item;
                });
                setFields(updatedFields);
                return;
            }

            eventTracking.sendEvent('additional-fields', 'delete');

            // Update existed fields
            onFetchCompanyDocumentFields();
        };

        onConfirmPopupShow({
            title: t`Ви впевнені, що хочете видалити параметр "${documentField.name}"?`,
            text: t`Дані по параметру будуть видалені з усіх документів.`,
            buttonText: t`Видалити`,
            buttonTheme: 'danger',
            onConfirm: () => deleteCallback(fieldId),
        });
    };

    const onChangePosition = async (index: number, indexTo: number) => {
        await swapFieldsPosition(fields[index].id, fields[indexTo].id);

        // Update existed fields
        onFetchCompanyDocumentFields();
    };

    // @ts-expect-error TS2554 [FIXME] Comment is autogenerated
    const onAddNewFields = showProRatePopupHandlerWrapper(
        () => {
            setErrorMessage('');
            // show hidden fields
            setNewFiled(initField());
        },
        'document-settings/extra-fields/create',
        PermissionCategory.ADDITIONAL_FIELDS,
    );

    const onCancel = () => {
        setFields(documentFields);
    };

    // @ts-expect-error TS2554 [FIXME] Comment is autogenerated
    const onEdit = showProRatePopupHandlerWrapper(
        (fieldId: string) => {
            setFields((items) =>
                items.map((item) =>
                    item.id === fieldId
                        ? { ...item, state: 'edit' as FieldState }
                        : item,
                ),
            );
        },
        'document-settings/extra-fields/edit',
        PermissionCategory.ADDITIONAL_FIELDS,
    );

    const onFieldChange = (
        fieldId: string,
        key: DocumentFieldKeys,
        value: unknown,
    ) => {
        setFields((items) =>
            items.map((item) => {
                if (item.id === fieldId) {
                    return {
                        ...item,
                        [key]: value,
                    };
                }
                return item;
            }),
        );
    };

    const getEditedFields = () => {
        return fields.filter((field) => {
            if (!field.state) {
                // Fields without state is not edited/created
                return false;
            }
            if (field.state === 'edit') {
                // Update fields only if name or enumOptions was changed
                const oldField = documentFields.find((f) => f.id === field.id);
                return (
                    oldField &&
                    (oldField.name !== field.name ||
                        oldField.enumOptions !== field.enumOptions)
                );
            }
            return true;
        });
    };

    const onSave = async () => {
        const editedFields = getEditedFields();
        const errorsMap = validateSaveNewFields(editedFields);

        //clear previous errorMessageState
        setErrorMessage('');

        // If new fields are not valid, show errors and exit function
        if (!isEmptyObject(errorsMap)) {
            setFields((items) =>
                items.map((item) => ({
                    ...item,
                    errors: errorsMap[item.id],
                })),
            );
            return;
        }

        // Nothing to update, just cancel
        if (editedFields.length === 0) {
            onCancel();
            return;
        }

        // Send new document fields to backend
        const successFieldIds: string[] = [];
        let errorFieldName = '';
        let exceptionMessage = '';

        setIsSavingProcess(true);

        for (const item of editedFields) {
            try {
                await updateField(item);
                eventTracking.sendEvent('additional-fields', 'edit');
                successFieldIds.push(item.id);
            } catch (err) {
                errorFieldName = item.name;
                exceptionMessage = err.message;
                // Stop at first error
                break;
            }
        }

        setIsSavingProcess(false);

        if (successFieldIds.length > 0) {
            onFetchCompanyDocumentFields();
        }

        if (exceptionMessage) {
            snackbarToast.error(
                `Не вдалося зберегти параметр "${errorFieldName}". ${exceptionMessage}`,
            );
        }
    };

    const onSaveNewField = async () => {
        if (!newField) {
            return;
        }

        const errorsMap = validateSaveNewFields([newField]);

        //clear previous errorMessageState
        setErrorMessage('');

        // If new fields are not valid, show errors and exit function
        if (!isEmptyObject(errorsMap)) {
            setFields((items) =>
                items.map((item) => ({
                    ...item,
                    errors: errorsMap[item.id],
                })),
            );
            return;
        }
        setIsSavingProcess(true);

        try {
            await createNewField(newField);
            snackbarToast.success(
                t`Параметр "${newField.name}" успішно створено`,
            );
            eventTracking.sendEvent('additional-fields', 'create');
            onFetchCompanyDocumentFields();
            setNewFiled(null);
        } catch (err) {
            snackbarToast.error(t`Не вдалося зберегти параметр`);
        }

        setIsSavingProcess(false);
    };

    const createNewParameter = (
        <>
            {newField && (
                <FlexBox key="new" direction="column" gap={16}>
                    <FieldForm
                        field={newField}
                        onFieldChange={(_fieldId, key, value) => {
                            setNewFiled({
                                ...newField,
                                [key]: value,
                            });
                        }}
                        isTypeEditable
                    />
                    <FlexBox>
                        <Button
                            theme="secondary"
                            onClick={() => {
                                setNewFiled(null);
                                setErrorMessage('');
                            }}
                        >
                            {t`Відмінити`}
                        </Button>
                        <Button
                            loading={isSavingProcess}
                            onClick={onSaveNewField}
                        >
                            {t`Додати новий параметр`}
                        </Button>
                    </FlexBox>
                </FlexBox>
            )}
        </>
    );

    const modifyMode = fields.some((field) => !!field.state);

    // Handle empty state
    if (fields.length === 0 || !isAccess) {
        return (
            <FlexBox direction="column" gap={16}>
                <EmptyState
                    onAddNewFields={() => {
                        onAddNewFields();

                        if (!isAccess) {
                            eventTracking.sendToGTMV4({
                                event:
                                    'ec_additional_fields_features_settings_trial',
                            });
                        }
                    }}
                />
                {createNewParameter}
            </FlexBox>
        );
    }

    const renderEditButtons = () => (
        <FlexBox justify="flex-end">
            <Button loading={isSavingProcess} onClick={onSave}>
                {t`Зберегти`}
            </Button>
            <Button theme="secondary" onClick={onCancel}>{t`Відмінити`}</Button>
        </FlexBox>
    );

    const renderField = (field: EditableField, index: number) => {
        if (field.state === 'edit') {
            return (
                <>
                    <div className={css.editForm}>
                        <FieldForm
                            field={field}
                            onFieldChange={onFieldChange}
                        />
                    </div>
                    <div className={css.block}>{renderEditButtons()}</div>
                </>
            );
        } else {
            // @ts-expect-error TS2554 [FIXME] Comment is autogenerated
            const updatePosition = showProRatePopupHandlerWrapper(
                (value: number) => {
                    return onChangePosition(index, index + value);
                },
                '',
                PermissionCategory.ADDITIONAL_FIELDS,
            );
            return (
                <>
                    <DocumentFieldRow
                        field={field}
                        onDelete={onDelete}
                        onEdit={onEdit}
                        onChangePosition={updatePosition}
                        showModifyButtons={!modifyMode}
                        isFirst={index === 0}
                        isLast={index === documentFields.length - 1}
                    />
                    {currentUser.currentRole.isAdmin && (
                        <span className={css.popupLinkBlock}>
                            <Text
                                type="link"
                                // @ts-expect-error TS2554 [FIXME] Comment is autogenerated
                                onClick={showProRatePopupHandlerWrapper(
                                    () => {
                                        openPopup(field);
                                    },
                                    'document-settings/extra-fields/rights-settings',
                                    PermissionCategory.ADDITIONAL_FIELDS,
                                )}
                            >
                                {t`Налаштувати права заповнення у документах`}
                            </Text>
                        </span>
                    )}
                </>
            );
        }
    };

    const fieldsForDisplay = fields.slice(
        (page - 1) * SHOW_LIMIT,
        page * SHOW_LIMIT,
    );

    return (
        <>
            <FlexBox direction="column" gap={16}>
                {!newField && (
                    <FlexBox className={css.header} key="header">
                        <FlexBox direction="column">
                            <Paragraph> {DOCUMENT_FIELDS_HINT}</Paragraph>
                            <Text
                                type="link"
                                onClick={() =>
                                    openInNewTab(
                                        'https://www.youtube.com/watch?v=jNgGAiwOEbQ',
                                    )
                                }
                            >{t`У відео-інструкції розповідаємо, як користуватися цією можливістю.`}</Text>
                        </FlexBox>
                        <Button
                            style={{ flexShrink: 0, alignSelf: 'flex-start' }}
                            theme="secondary"
                            onClick={onAddNewFields}
                        >{t`Додати ще один параметр`}</Button>
                    </FlexBox>
                )}

                {createNewParameter}
                {errorMessage && (
                    <Alert wide type="error">
                        {errorMessage}
                    </Alert>
                )}
                <FlexBox direction="column" tagName="ul">
                    {fieldsForDisplay.map((field, index) => (
                        <li key={field.id} className={css.separateItem}>
                            {renderField(field, index)}
                        </li>
                    ))}
                </FlexBox>
                <FlexBox justify="flex-end">
                    <Pagination
                        total={Math.ceil(fields.length / SHOW_LIMIT)}
                        initPage={1}
                        current={page}
                        onChange={setPage}
                        scrollOnChange={false}
                        hideOnSinglePage
                    />
                </FlexBox>
            </FlexBox>

            <AdditionalFieldSettingsAccessPopup />
        </>
    );
};

export default CompanyDocumentFields;
