.table {
    display: grid;
    width: 100%;
    align-items: center;
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 140px 350px 1fr;
}

.label {
    grid-column: 1;
}

.cell {
    grid-column: 2;
}

.innerLabel {
    align-self: start;
    padding-top: 10px;
    grid-column: 1;
}

.cellSpan {
    display: grid;
    align-items: center;
    grid-column: 2 / span 3;
    grid-column-gap: 20px;
    grid-row-gap: 10px;
    grid-template-columns: 350px 140px;
}

.innerCell {
    grid-column: 1;
}

.addButtonContainer {
    grid-column: 1;
}

.cancelButtonContainer {
    grid-column: 2;
}

.addButton {
    position: relative;
}

.cancelIcon {
    position: relative;
    width: 10px;
    height: 10px;
    cursor: pointer;
    user-select: none;
}

.parametersList {
    display: grid;
    grid-column: 1 / 3;
    grid-column-gap: 20px;
    grid-template-columns: 140px 350px 1fr;
}

@media all and (max-width: 768px) {
    .table {
        grid-row-gap: 10px;
        grid-template-columns: 350px 140px;
    }

    .addButtonContainer {
        grid-column: 1;
    }

    .cell {
        grid-column: 1;
    }

    .cellSpan {
        grid-column: 1 / 3;
    }

    .parametersList {
        grid-row-gap: 10px;
        grid-template-columns: 350px 140px;
    }
}

@media all and (max-width: 490px) {
    .table {
        grid-template-columns: 220px 110px;
    }

    .cellSpan {
        grid-template-columns: 220px 110px;
    }

    .parametersList {
        grid-template-columns: 220px 110px;
    }
}
