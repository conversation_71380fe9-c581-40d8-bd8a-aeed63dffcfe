import React from 'react';

import { motion } from 'framer-motion';

import css from './TypedText.css';

type TypedTextProps = {
    text: string | string[];
    delay?: number;
};

export const TypedText: React.FC<TypedTextProps> = ({ text, delay = 0 }) => {
    const lines = Array.isArray(text) ? text : [text];

    const chars = lines.flatMap((line) => {
        return line.split('').concat('\n');
    });

    return (
        <span className={css.text}>
            {chars.map((char, i) => {
                if (char === '\n') {
                    return <br key={i} />;
                }

                return (
                    <motion.span
                        variants={{
                            visible: {
                                opacity: 1,
                                transition: {
                                    duration: 0.05,
                                    delay: delay + i * 0.05,
                                },
                            },
                            hidden: { opacity: 0 },
                        }}
                        initial="hidden"
                        animate="visible"
                        exit="hidden"
                        className={css.char}
                        key={i}
                    >
                        {char}
                    </motion.span>
                );
            })}
        </span>
    );
};
