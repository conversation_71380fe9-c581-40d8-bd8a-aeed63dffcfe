import React, { <PERSON> } from 'react';

import Icon from 'components/ui/icon';
import { RateStatus } from 'services/enums';

import { RATE_STATUS_NAMES_MAP } from '../constants';

import SvgCheck from '../images/check.svg';

import css from './ratesAdminStatus.css';

type RatesAdminStatusProps = {
    status: RateStatus;
};

const RatesAdminStatus: FC<RatesAdminStatusProps> = ({ status }) => {
    const statusName = RATE_STATUS_NAMES_MAP[status];

    if (status === RateStatus.ACTIVE) {
        return (
            <div className={css.root}>
                <div className={css.icon}>
                    <Icon glyph={SvgCheck} />
                </div>
                <span className={css.statusActive}>{statusName}</span>
            </div>
        );
    }

    return <span>{statusName}</span>;
};

export default RatesAdminStatus;
