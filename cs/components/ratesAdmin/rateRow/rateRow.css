.plan {
    display: flex;
    box-sizing: border-box;
    align-items: center;
}

.planCell {
    display: flex;
    flex: 1;
    text-align: center;
}

.plansHeader {
    display: flex;
    padding: 10px;
    font-weight: bold;
}

.plansHeader .planCell:last-child {
    visibility: hidden;
}

.planCell:first-child {
    flex: 1.5;
}

.editableRatesWrapper {
    display: flex;
    flex: 1;
    justify-content: flex-end;
}
