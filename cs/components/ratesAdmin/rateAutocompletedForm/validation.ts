import { yupResolver } from '@hookform/resolvers/yup';

import { t } from 'ttag';
import * as yup from 'yup';

import { RateAutocompletedFormFields } from './types';

const rateAutocompleteFormValidationSchema: yup.ObjectSchema<RateAutocompletedFormFields> = yup
    .object()
    .shape({
        number: yup.string().required(),
        amount: yup.string().required(),
        startDate: yup
            .date()
            .nullable()
            .default(null)
            .required(t`Початкова дата обов'язкова`),
        endDate: yup
            .date()
            .nullable()
            .default(null)
            .required(t`Кінцева дата обов'язкова`),
    });

export const rateAutocompleteFormResolver = yupResolver(
    rateAutocompleteFormValidationSchema,
);
