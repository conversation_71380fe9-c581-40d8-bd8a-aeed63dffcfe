import React from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';

import { Button, FlexBox, snackbarToast } from '@vchasno/ui-kit';

import PropTypes from 'prop-types';
import { t } from 'ttag';

// styles
import css from './tokenField.css';

const TokenField = (props) => {
    return (
        <FlexBox direction="column" justify="flex-start" align="flex-start">
            {props.token && (
                <FlexBox>
                    <div className={css.input}>{props.token}</div>
                    <CopyToClipboard
                        text={props.token}
                        onCopy={() =>
                            snackbarToast.success(t`Токен скопійовано!`)
                        }
                    >
                        <Button
                            theme="secondary"
                            size="sm"
                        >{t`Копіювати токен`}</Button>
                    </CopyToClipboard>
                </FlexBox>
            )}

            <Button theme="danger" onClick={props.onCancelClick}>
                {t`Скинути всі токени цього співробітника`}
            </Button>
        </FlexBox>
    );
};

TokenField.propTypes = {
    token: PropTypes.string,
    onCancelClick: PropTypes.func.isRequired,
};

export default TokenField;
