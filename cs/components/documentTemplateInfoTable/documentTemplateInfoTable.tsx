import React, { FC } from 'react';

import ConditionsList from 'components/documentTemplates/ConditionsList';
import { useTemplateConditionsList } from 'hooks/useTemplateConditionsList';
import { formatUserIdentifier } from 'lib/ts/helpers';
import { t } from 'ttag';

import { Template, TemplateParticipant } from '../documentTemplates/types';

import DocumentFieldsList from '../documentTemplates/documentFieldsList';
import RolesList from '../documentTemplates/rolesList';
import TagsList from '../documentTemplates/tagsList';

import css from './documentTemplateInfoTable.css';

interface Props {
    data: Template;
}

const DocumentTemplateInfoTable: FC<React.PropsWithChildren<Props>> = ({
    data,
}: Props) => {
    const { conditionsList } = useTemplateConditionsList(data);
    const isOrderedSignProcess = Boolean(data?.signersSettings?.is_ordered);
    const isOrderedReviewProcess = Boolean(data?.reviewSettings?.is_ordered);

    return (
        <table className={css.root}>
            <tbody>
                <tr>
                    <td>
                        <b>{t`Сценарій`}</b>
                    </td>
                    <td>
                        <div className={css.templateName}>{data.name}</div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>{t`Відповідальна особа`}</b>
                    </td>
                    <td>
                        {data.assignedTo && (
                            <p>
                                {data.assignedTo.user.firstName}{' '}
                                {data.assignedTo.user.secondName}{' '}
                                <span className={css.email}>
                                    {formatUserIdentifier(data.assignedTo.user)}
                                </span>
                            </p>
                        )}
                        {!data.assignedTo && '-'}
                    </td>
                </tr>
                <ConditionsList
                    list={conditionsList}
                    variant="table-horizontal"
                />
                <tr>
                    <td>
                        <b>{t`Погодження`}</b>
                        <div className={css.email}>{`(${
                            isOrderedReviewProcess
                                ? t`Почергове`
                                : t`Паралельне`
                        })`}</div>
                    </td>
                    <td>
                        <RolesList
                            ordered={isOrderedReviewProcess}
                            items={data.reviewers}
                        />
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>{t`Підписання`}</b>
                        <div className={css.email}>{`(${
                            isOrderedSignProcess ? t`Почергове` : t`Паралельне`
                        })`}</div>
                    </td>
                    <td>
                        <RolesList
                            ordered={isOrderedSignProcess}
                            items={data.signers}
                        />
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>{t`Доступ до документів`}</b>
                    </td>
                    <td>
                        <RolesList
                            items={
                                [
                                    ...data.viewerRoles.map((role) => ({
                                        role,
                                        ...role,
                                        type: 'role',
                                        group: {},
                                    })),
                                    ...data.viewerGroups.map((group) => ({
                                        group,
                                        ...group,
                                        type: 'group',
                                        role: {},
                                    })),
                                ] as TemplateParticipant[]
                            }
                        />
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>{t`Додаткові параметри`}</b>
                    </td>
                    <td>
                        <DocumentFieldsList settings={data.fieldsSettings} />
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>{t`Ярлики`}</b>
                    </td>
                    <td>
                        <TagsList tagsSettings={data.tagsSettings} />
                    </td>
                </tr>
            </tbody>
        </table>
    );
};

export default DocumentTemplateInfoTable;
