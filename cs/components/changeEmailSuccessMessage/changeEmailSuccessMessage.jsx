import React from 'react';
import MediaQuery from 'react-responsive';

import PropTypes from 'prop-types';
import { jt, t } from 'ttag';

import Icon from '../ui/icon/icon';
import Message from '../ui/message/message';
import PseudoLink from '../ui/pseudolink/pseudolink';

import { MEDIA_WIDTH } from '../../lib/constants';
import auth from '../../services/auth';

// icons
import SvgCheck from './images/check.svg';

// styles
import css from './changeEmailSuccessMessage.css';

const ChangeEmailSuccessMessage = ({ email }) => {
    const logoutButton = <PseudoLink onClick={auth.logout}>Вийдіть</PseudoLink>;
    const br = <br key="br" />;
    return (
        <div className={css.root}>
            <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                <div className={css.image}>
                    <img
                        src={`${config.STATIC_HOST}/images/tetyanka9.png`}
                        alt=""
                    />
                </div>
            </MediaQuery>
            <div className={css.content}>
                <div className={css.header}>
                    <Message type="success">
                        <div className={css.icon}>
                            <Icon glyph={SvgCheck} />
                        </div>
                        {jt`Ви успішно надіслали собі запрошення ${br}
                        на новий email`}
                    </Message>
                </div>
                <ul className={css.list}>
                    <li className={css.step}>
                        {jt`${logoutButton} з
                        сервісу ${config.BRAND_NAME}`}
                    </li>
                    <li className={css.step}>
                        {t`Отримайте лист за адресою`} <b>{email}</b>
                    </li>
                    <li
                        className={css.step}
                    >{t`Натисніть  у листі на кнопку “Зареєструватись”`}</li>
                    <li className={css.step}>
                        {t`На сторінці реєстрації вкажіть пароль та прийміть умови користування`}
                    </li>
                </ul>
            </div>
        </div>
    );
};

ChangeEmailSuccessMessage.propTypes = {
    email: PropTypes.string,
};

export default ChangeEmailSuccessMessage;
