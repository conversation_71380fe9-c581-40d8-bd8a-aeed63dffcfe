import React from 'react';
import { useSelector } from 'react-redux';

import { t } from 'ttag';

import {
    getCurrentCompanyEdrpou,
    getCurrentCompanyName,
    getCurrentUserEmail,
} from '../../../selectors/app.selectors';

import css from './CompanyInformation.css';

const CompanyInformation: React.FC = () => {
    const edrpou = useSelector(getCurrentCompanyEdrpou);
    const companyName = useSelector(getCurrentCompanyName);
    const currentUserEmail = useSelector(getCurrentUserEmail);

    return (
        <div className={css.container}>
            <h5 className={css.title}>{t`Компанія`}</h5>
            <table className={css.table}>
                <tbody>
                    <tr>
                        <td>{t`ЄДРПОУ/ІПН`}</td>
                        <td>{edrpou}</td>
                    </tr>
                    <tr>
                        <td>Email</td>
                        <td>{currentUserEmail}</td>
                    </tr>
                    <tr>
                        <td>{t`Назва`}</td>
                        <td>{companyName}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
};

export default CompanyInformation;
