import React, { FC, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { FlexBox } from '@vchasno/ui-kit';

import actions from 'components/Checkout/checkoutActions';
import { formatPrice } from 'lib/numbers';
import {
    getActiveEmployeesCountSelector,
    getCurrentCompanyMaxEmployees,
} from 'selectors/app.selectors';
import { getCheckoutChosenRateInformation } from 'selectors/checkout.selectors';
import { t } from 'ttag';

import { CounterInput } from './CounterInput';
import { EmployeesLimitsAlert } from './EmployeesLimitsAlert';

import css from './AddEmployeesForm.css';

export const AddEmployeesForm: FC = () => {
    const dispatch = useDispatch();

    const chosenRateInformation = useSelector(getCheckoutChosenRateInformation);
    const currentEmployeesCount = useSelector(getActiveEmployeesCountSelector);
    const maxEmployeesCount = useSelector(getCurrentCompanyMaxEmployees) || 0;
    const [employeesCount, setEmployeesCount] = useState(10);
    // @ts-expect-error TS18047 [FIXME] Comment is autogenerated
    const pricePerUnit = chosenRateInformation.price_per_unit_total;

    useEffect(() => {
        dispatch({
            type: actions.CHECKOUT__SET_AMOUNT,
            amount: 0,
            amountEmployee: employeesCount,
            price: formatPrice(pricePerUnit * employeesCount),
        });
    }, [employeesCount]);

    useEffect(() => {
        return () => {
            dispatch({
                type: actions.CHECKOUT__SET_AMOUNT,
                amount: 0,
                amountEmployee: 0,
                price: formatPrice(0),
            });
        };
    }, []);

    return (
        <FlexBox direction="column" gap={16}>
            <EmployeesLimitsAlert
                quantityEmployees={currentEmployeesCount}
                limitEmployees={maxEmployeesCount}
            />
            <FlexBox
                wrap="wrap"
                align="center"
                // @ts-expect-error TS2322 [FIXME] Comment is autogenerated
                gap="16px 38px"
                className={css.serviceWrapper}
            >
                <div className={css.serviceTitleWrapper}>
                    <div className={css.serviceTitle}>{t`Співробітники`}</div>
                    <div className={css.serviceUnitPrice}>
                        {formatPrice(pricePerUnit)} {t`грн/співробітник`}
                    </div>
                </div>
                <div className={css.input}>
                    <CounterInput
                        value={employeesCount}
                        setValue={setEmployeesCount}
                    />
                </div>
            </FlexBox>
        </FlexBox>
    );
};
