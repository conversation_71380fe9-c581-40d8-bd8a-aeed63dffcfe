import React, { FC } from 'react';

import cn from 'classnames';
import FlexBox from 'components/FlexBox';

import css from './EmployeesLimitsAlert.css';

interface EmployeesLimitsAlertProps {
    quantityEmployees: number;
    limitEmployees: number;
}

export const EmployeesLimitsAlert: FC<EmployeesLimitsAlertProps> = ({
    quantityEmployees,
    limitEmployees,
}) => {
    const isReachedLimit = quantityEmployees >= limitEmployees;
    const viewCountEmployees = `${quantityEmployees}/${limitEmployees}`;

    return (
        <FlexBox
            align="center"
            gap={12}
            className={cn(
                css.employeesCountAlert,
                isReachedLimit ? css.them2 : css.them1,
            )}
        >
            <div className={css.smileWrapper}>
                {isReachedLimit ? '🧐' : '🤩'}
            </div>
            <div className={css.text}>
                {isReachedLimit
                    ? `Досягнено ліміту співробітників ${viewCountEmployees} за вашим тарифом`
                    : `В компанію запрошено ${viewCountEmployees} співробітників згідно вашого тарифу`}
            </div>
        </FlexBox>
    );
};
