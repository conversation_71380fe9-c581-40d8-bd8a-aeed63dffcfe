import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';

import { Text } from '@vchasno/ui-kit';

import UltimateRatePayment from 'components/Checkout/Payment/UltimateRatePayment';
import { PAYMENT_FLOW } from 'components/Checkout/constants';
import { getLocationQuery, stringifyLocationQuery } from 'lib/url';
import {
    getCheckoutRates,
    getRecommendedWebRate,
    getWebRatesToExtend,
} from 'selectors/checkout.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { UNLIMITED_RATES_SET } from 'services/billing/constants';
import { AccountRate } from 'services/enums';
import { EventCapture } from 'services/posthog';
import { t } from 'ttag';

import { getRates, setChosenRate } from './checkoutActionCreators';

import Card from '../ui/card/card';

import Order from './Order/Order';
import { Payment } from './Payment/Payment';

import css from './Checkout.css';

const Checkout: React.FC = () => {
    const dispatch = useDispatch();
    const [
        selectedPaymentFlow,
        setSelectedPaymentFlow,
    ] = useState<PAYMENT_FLOW>();
    const location = useLocation();
    const history = useHistory();
    const rates = useSelector(getCheckoutRates);
    const webRatesToExtend = useSelector(getWebRatesToExtend);
    const recommendedWebRate = useSelector(getRecommendedWebRate);
    const query = getLocationQuery(location) as { rate: AccountRate };
    const isUnlimitedRate = UNLIMITED_RATES_SET.has(query.rate);

    useEffect(() => {
        if (recommendedWebRate && rates) {
            const webRate = webRatesToExtend[0];

            // because for unlimited rates user must contact with manager
            if (!query.rate) {
                query.rate =
                    UNLIMITED_RATES_SET.has(recommendedWebRate) && webRate.rate
                        ? webRate.rate
                        : recommendedWebRate;
                history.push({ search: stringifyLocationQuery(query) });
            }
        }
    }, [recommendedWebRate, rates, query.rate]);

    useEffect(() => {
        if (rates) {
            dispatch(setChosenRate(query.rate));
        }
    }, [query.rate, rates]);

    useEffect(() => {
        dispatch(getRates());
    }, []);

    return (
        <div className={css.container}>
            <EventCapture event="checkout_page" />
            <div className={css.content}>
                <div className={css.paymentInfo}>
                    <Order />
                </div>
                <div className={css.payment}>
                    {!isUnlimitedRate && (
                        <Card wideContent>
                            <div className={css.paymentTitle}>{t`Разом`}</div>
                            <Payment
                                onPaymentMethodClick={setSelectedPaymentFlow}
                            />
                        </Card>
                    )}
                    {isUnlimitedRate && (
                        <UltimateRatePayment
                            onClick={() => {
                                eventTracking.sendToGTMV4({
                                    event: 'ec_order_ultimate_checkout',
                                });
                            }}
                        />
                    )}
                    {selectedPaymentFlow === PAYMENT_FLOW.BILL_GENERATION && (
                        <Text className={css.remark}>
                            <sup>*</sup>
                            {t`Після отримання оплати тариф активується впродовж 30 хв.`}
                        </Text>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Checkout;
