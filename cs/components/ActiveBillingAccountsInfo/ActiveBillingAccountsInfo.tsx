import React, { FC } from 'react';
import { connect } from 'react-redux';

import { t } from 'ttag';

import { AccountRate } from '../../services/enums';
import { BillingAcount, CompanyRate } from '../../types/billing';
import { StoreState } from '../../types/store';
import { ICompany } from '../../types/user';

import { partition } from '../../lib/utils';
import { isRateActive } from '../../services/billing/utils';

import Button from '../ui/button/button';
import Icon from '../ui/icon/icon';

import {
    BILL_GENERATION_PAGE_PATTERN,
    RATES_PAGE_LINK,
} from '../../lib/routing/constants';
import eventTracking from '../../services/analytics/eventTracking';
import {
    countBalance,
    getBillingAccountsWithoutFree,
    getCombinedActiveRates,
    getRatesWithoutSomeRate,
    isShowFreeRate,
    needRatesBeCombined,
} from '../../services/billing';
import {
    FREE_RATES_SET,
    INTEGRATION_RATES_SET,
    UNLIMITED_RATES_SET,
} from '../../services/billing/constants';
import ActiveBillingAccountCard, {
    ActiveBillingAccountCardProps,
} from './ActiveBillingAccountCard/ActiveBillingAccountCard';
import ActiveBillingAccountCardElement from './ActiveBillingAccountCardElement/ActiveBillingAccountCardElement';

import Bonus from './images/bonus.svg';
import Left from './images/left.svg';
import Main from './images/main.svg';
import Sent from './images/sent.svg';

import css from './ActiveBillingAccountsInfo.css';

const mapAccountsRatesToRender = (
    billingAccounts: BillingAcount[],
    rates: CompanyRate[],
    activeRates: AccountRate[],
): ActiveBillingAccountCardProps[] => {
    const needCombinedRates = needRatesBeCombined(activeRates);
    const showFreeRate = isShowFreeRate(
        activeRates.reduce((acc, rate) => [...acc, rate], []),
    );

    const updatedBillingAccounts: BillingAcount[] = showFreeRate
        ? billingAccounts
        : getBillingAccountsWithoutFree(billingAccounts);
    const updatedRates: CompanyRate[] = showFreeRate
        ? rates
        : getRatesWithoutSomeRate(rates, FREE_RATES_SET);
    const updatedActiveRates: AccountRate[] = showFreeRate
        ? activeRates
        : getCombinedActiveRates(activeRates);

    const activeRatesById = updatedRates.reduce<
        Partial<Record<AccountRate, CompanyRate>>
    >((res, curr) => {
        res[curr.rate] = curr;
        return res;
    }, {});

    const activeAccounts = updatedBillingAccounts.filter(isRateActive);

    const coalescedAccountsById = activeAccounts.reduce<
        Partial<Record<AccountRate, BillingAcount>>
    >((res, curr) => {
        const existingProperty = res[curr.rate];

        if (existingProperty) {
            existingProperty.units += curr.units;
            existingProperty.unitsLeft += curr.unitsLeft;

            res[curr.rate] = existingProperty;
        } else {
            res[curr.rate] = { ...curr };
        }

        return res;
    }, {});

    const balance = countBalance(billingAccounts);

    const [
        integrationActiveRates,
        restActiveRates,
        // @ts-expect-error TS2345 [FIXME] Comment is autogenerated
    ] = partition(updatedActiveRates, (rate) =>
        // @ts-expect-error TS2345 [FIXME] Comment is autogenerated
        INTEGRATION_RATES_SET.has(rate),
    );

    const res = restActiveRates.map((rate) => {
        // @ts-expect-error TS2345 [FIXME] Comment is autogenerated
        if (UNLIMITED_RATES_SET.has(rate)) {
            return {
                accountRate: rate,
                // @ts-expect-error TS2538 [FIXME] Comment is autogenerated
                startDate: activeRatesById[rate]?.startDate,
                // @ts-expect-error TS2538 [FIXME] Comment is autogenerated
                endDate: activeRatesById[rate]?.endDate,
                firstCard: (
                    <ActiveBillingAccountCardElement
                        iconElement={<Icon glyph={Left} />}
                        label={t`Залишилось документів`}
                        numberElement="∞"
                    />
                ),
                link: (
                    <a href={RATES_PAGE_LINK}>
                        <Button
                            theme="cta"
                            onClick={() =>
                                eventTracking.sendEvent(
                                    'rates',
                                    'go-to-rates-from-settings',
                                )
                            }
                        >
                            {t`Обрати тарифний план`}
                        </Button>
                    </a>
                ),
            };
        }

        return {
            accountRate: rate,
            // @ts-expect-error TS2538 [FIXME] Comment is autogenerated
            startDate: activeRatesById[rate]?.startDate ?? undefined,
            // @ts-expect-error TS2538 [FIXME] Comment is autogenerated
            endDate: activeRatesById[rate]?.endDate ?? undefined,
            firstCard: (
                <ActiveBillingAccountCardElement
                    iconElement={<Icon glyph={Sent} />}
                    label={t`Надіслано документів`}
                    numberElement={
                        needCombinedRates
                            ? balance.bonusesSent
                            : // @ts-expect-error TS2538 [FIXME] Comment is autogenerated
                              (coalescedAccountsById[rate]?.units ?? 0) -
                              // @ts-expect-error TS2538 [FIXME] Comment is autogenerated
                              (coalescedAccountsById[rate]?.unitsLeft ?? 0)
                    }
                />
            ),
            secondCard: (
                <ActiveBillingAccountCardElement
                    iconElement={<Icon glyph={Left} />}
                    label={t`Залишилось документів`}
                    numberElement={
                        needCombinedRates
                            ? balance.bonusesLeft
                            : // @ts-expect-error TS2538 [FIXME] Comment is autogenerated
                              coalescedAccountsById[rate]?.unitsLeft ?? 0
                    }
                />
            ),
            link: (
                <a href={RATES_PAGE_LINK}>
                    <Button
                        theme="cta"
                        onClick={() =>
                            eventTracking.sendEvent(
                                'rates',
                                'go-to-rates-from-settings',
                            )
                        }
                    >
                        {t`Обрати тарифний план`}
                    </Button>
                </a>
            ),
        };
    });

    // pick frost non-null rate, then fast-forward
    // TODO: add short-circuiting
    const possibleIntegrationAccount = [...INTEGRATION_RATES_SET].reduce<
        BillingAcount | undefined
    >(
        (coalescedResult, curr) =>
            coalescedResult || coalescedAccountsById[curr],
        undefined,
    );

    if (integrationActiveRates.length > 0) {
        const rate = integrationActiveRates[0];
        res.push({
            accountRate: rate,
            // @ts-expect-error TS2538 [FIXME] Comment is autogenerated
            startDate: activeRatesById[rate]?.startDate ?? undefined,
            // @ts-expect-error TS2538 [FIXME] Comment is autogenerated
            endDate: activeRatesById[rate]?.endDate ?? undefined,
            firstCard: (
                <ActiveBillingAccountCardElement
                    iconElement={<Icon glyph={Bonus} />}
                    label={
                        <span
                            className={css.blackLabel}
                        >{t`Бонусний рахунок`}</span>
                    }
                    subLabel={t`Для надсилання контрагентам `}
                    numberElement={balance.bonusesLeftIntegrationRate}
                />
            ),
            secondCard: (
                <ActiveBillingAccountCardElement
                    iconElement={<Icon glyph={Main} />}
                    label={
                        <span
                            className={css.blackLabel}
                        >{t`Основний рахунок`}</span>
                    }
                    subLabel={t`Для надсилання контрагентам `}
                    numberElement={balance.documentsLeftIntegrationRate}
                />
            ),
            link: (
                <div className={css.linksBlock}>
                    <a href={RATES_PAGE_LINK}>
                        <Button
                            theme="blue"
                            typeContour
                            onClick={() =>
                                eventTracking.sendEvent(
                                    'rates',
                                    'go-to-rates-from-settings',
                                )
                            }
                        >
                            {t`Обрати тарифний план`}
                        </Button>
                    </a>
                    <a href={BILL_GENERATION_PAGE_PATTERN}>
                        <Button theme="cta">{t`Поповнити баланс`}</Button>
                    </a>
                </div>
            ),
        });
    } else if (possibleIntegrationAccount) {
        const rate = coalescedAccountsById[AccountRate.INTEGRATION]
            ? AccountRate.INTEGRATION
            : AccountRate.INTEGRATION_TRIAL;

        const billingAccount = possibleIntegrationAccount;

        res.push({
            // @ts-expect-error TS2322 [FIXME] Comment is autogenerated
            accountRate: rate,
            startDate: billingAccount.dateActivated ?? undefined,
            endDate: billingAccount.dateExpired ?? undefined,
            firstCard: (
                <ActiveBillingAccountCardElement
                    iconElement={<Icon glyph={Bonus} />}
                    label={
                        <span
                            className={css.blackLabel}
                        >{t`Бонусний рахунок`}</span>
                    }
                    subLabel={t`Для надсилання контрагентам `}
                    numberElement={balance.bonusesLeftIntegrationRate}
                />
            ),
            secondCard: (
                <ActiveBillingAccountCardElement
                    iconElement={<Icon glyph={Main} />}
                    label={
                        <span
                            className={css.blackLabel}
                        >{t`Основний рахунок`}</span>
                    }
                    subLabel={t`Для надсилання контрагентам `}
                    numberElement={balance.documentsLeftIntegrationRate}
                />
            ),
            link: (
                <div className={css.linksBlock}>
                    <a href={RATES_PAGE_LINK}>
                        <Button theme="blue" typeContour>
                            {t`Обрати тарифний план`}
                        </Button>
                    </a>
                    <a href={BILL_GENERATION_PAGE_PATTERN}>
                        <Button theme="cta">{t`Поповнити баланс`}</Button>
                    </a>
                </div>
            ),
        });
    }

    // @ts-expect-error TS2322 [FIXME] Comment is autogenerated
    return res;
};

const mapStateToProps = (state: StoreState) => ({
    company: state.app.currentUser.currentRole.company,
});

interface ActiveBillingAccountsInfoProps {
    company: ICompany;
}

const ActiveBillingAccountsInfo: FC<
    React.PropsWithChildren<ActiveBillingAccountsInfoProps>
> = ({ company }) => {
    const { billingAccounts, rates, activeRates } = company;
    const toRender = mapAccountsRatesToRender(
        billingAccounts.toJS(),
        rates.toJS(),
        // @ts-expect-error TS2345 [FIXME] Comment is autogenerated
        activeRates,
    );

    return (
        <>
            {toRender.map((props) => (
                <ActiveBillingAccountCard {...props} key={props.accountRate} />
            ))}
        </>
    );
};

export default connect(mapStateToProps)(ActiveBillingAccountsInfo);
