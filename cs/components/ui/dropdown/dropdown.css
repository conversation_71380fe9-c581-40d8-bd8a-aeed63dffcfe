.dropdown {
    position: relative;
    display: inline-block;
    width: 100%;
    vertical-align: middle;
    white-space: normal;
}

:global(.vchasno-autumn-theme) .dropdown {
    --orange-border: var(--autumn-3-color);
    --hover-bg: var(--autumn-4-color);
}

:global(.vchasno-spring-theme) .dropdown {
    --orange-border: var(--spring-3-color);
    --hover-bg: var(--spring-4-color);
}

.dropdown:hover {
    cursor: pointer;
}

.container {
    position: relative;
}

.label {
    color: var(--slate-grey-color);
    font-size: 14px;
    line-height: 25px;
}

.inputWrapper {
    position: relative;
}

.input {
    position: relative;
    display: block;
    width: 100%;
    box-sizing: border-box;
    padding: 10px 35px 10px 10px;
    border: 1px solid var(--default-border);
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
    color: var(--content-color);
    cursor: pointer;
    font-size: 13px;
    line-height: 18px;
    pointer-events: none;
    transition: all 100ms ease;
    user-select: none;
}

.input:hover {
    border-color: #f2f6f7;
    box-shadow: inset 0 0 0 1px #f2f6f7;
}

.arrow {
    position: absolute;
    top: 0;
    right: 8px;
    bottom: 0;
    display: flex;
    width: 24px;
    height: 24px;
    align-items: center;
    justify-content: center;
    margin: auto;
    transform: rotate(0);
    transition: transform 0.3s;
}

.arrowIcon {
    width: 8px;
    height: 8px;
}

.dropdownOpened .arrow {
    transform: rotate(180deg);
}

.input:disabled + .arrow {
    display: none;
}

.dropped {
    z-index: 99;
    width: 100%;
    box-sizing: border-box;
    padding: 10px 0;
    border: 1px solid var(--orange-border);
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
}

:global(.vchasno-autumn-theme) .dropped,
:global(.vchasno-autumn-theme) .input {
    background-color: var(--white-color);
}

.dropped[data-popper-placement='top'] {
    border-top: 1px solid var(--orange-border);
    border-bottom: none;
}

.dropdown :not(.lightMode) .dropped {
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.dropdown :not(.lightMode) .dropped[data-popper-placement='top'] {
    border-top: 1px solid var(--orange-border);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.list {
    position: relative;
    max-height: 240px;
    overflow-x: hidden;
    overflow-y: auto;
}

.item {
    padding: 10px 10px;
    border-radius: 0;
    color: var(--content-color);
    font-size: 13px;
    line-height: 16px;
}

.item:hover {
    background-color: var(--hover-bg);
    cursor: pointer;
}

.item.itemDisabled {
    color: var(--dark-grey-color);
}

.item.itemDisabled:hover {
    background: none;
    cursor: default;
}

.validation {
    margin-top: 5px;
    color: var(--red-color);
}

.dropdownOpened .input {
    border: 1px solid var(--orange-border);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.dropdownOpened .input:disabled {
    border: 1px solid var(--default-border);
    border-radius: var(--border-radius);
    color: var(--pigeon-color);
}

.dropdownOpened .topPlacements .input {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.lightMode .input {
    border: 0;
}

.lightMode .dropdownOpened .input {
    border: 0;
}

.dropdownSizeBig .input {
    position: relative;
    display: block;
    width: 100%;
    padding: 15px 35px 15px 10px;
}

.dropdownSizeLarge .input {
    position: relative;
    display: block;
    width: 100%;
    padding: 15px 35px 15px 15px;
}

.dropdownError .input {
    border-color: var(--error-border);
}

.dropdownDisabled .input {
    background-color: var(--grey-bg);
    color: var(--dark-pigeon-color);
}
