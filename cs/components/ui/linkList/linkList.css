.root + .root {
    margin-top: 10px;
}

.item {
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
}

.item + .item {
    margin-top: 10px;
}

.table {
    display: table;
    width: 100%;
    color: inherit;
    table-layout: fixed;
}

.table:hover {
    color: inherit;
    text-decoration: none;
}

.cell,
.cellSmall,
.cellRight,
.icon,
.cellRightSmall {
    display: table-cell;
    overflow: hidden;
    height: 50px;
    padding: 0 18px;
    text-overflow: ellipsis;
    vertical-align: middle;
    white-space: nowrap;
}

.cellSmall,
.cellRightSmall {
    padding: 0 10px;
    font-size: 13px;
}

.cellRight,
.cellRightSmall {
    color: var(--dark-pigeon-color);
    text-align: right;
}

.icon {
    width: 30px;
    height: 30px;
    padding: 0 18px;
    text-align: left;
    vertical-align: middle;
}

.mainItem {
    width: 150px;
    font-weight: bold;
}

.link {
    display: block;
    cursor: pointer;
}

.link:hover {
    box-shadow: inset 0 0 0 1px var(--primary-cta-color);
    text-decoration: none;
}

.disabled {
    color: var(--dark-pigeon-color);
}
