import React, { FC } from 'react';

import { Button, FlexBox } from '@vchasno/ui-kit';

import cn from 'classnames';

import css from './countButton.css';

type ButtonColorTheme = 'blue' | 'red';

interface CountButtonProps {
    onClick: () => void;
    isActive: boolean;
    colorTheme?: ButtonColorTheme;
    title?: string;
    count?: number;
    titleClassName?: string;
    className?: string;
    isSquare?: boolean;
}
const CountButton: FC<CountButtonProps> = ({
    onClick,
    colorTheme = 'blue',
    isActive,
    title,
    count,
    titleClassName,
    className,
    isSquare = false,
}) => {
    return (
        <Button
            className={cn(className, css.button, css[colorTheme], {
                [css.square]: isSquare,
            })}
            onClick={onClick}
            data-selected={isActive}
            theme="secondary"
        >
            <FlexBox align="center" gap={10}>
                {title && (
                    <span className={cn(titleClassName, css.title)}>
                        {title}
                    </span>
                )}
                {typeof count === 'number' && (
                    <div className={css.counter}>{count}</div>
                )}
            </FlexBox>
        </Button>
    );
};

export default CountButton;
