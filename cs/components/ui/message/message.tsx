import React, { FC, PropsWithChildren, ReactNode } from 'react';

import cn from 'classnames';

import Icon from '../icon/icon';

import SvgAlert from './images/alert.svg';

import css from './message.css';

interface MessageProps {
    className?: string;
    type?: 'success' | 'info' | 'hint' | 'error' | 'alert' | 'warning';
    sizeSmall?: boolean;
    children: ReactNode;
}

const Message: FC<PropsWithChildren<MessageProps>> = ({
    className,
    type,
    sizeSmall,
    children,
}) => {
    const showAlertIcon = type && ['alert', 'warning'].includes(type);

    return (
        <div
            className={cn(css.root, className, {
                [css.typeSuccess]: type === 'success',
                [css.typeInfo]: type === 'info',
                [css.typeHint]: type === 'hint',
                [css.typeError]: type === 'error',
                [css.typeAlert]: type === 'alert',
                [css.typeWarning]: type === 'warning',
                [css.sizeSmall]: sizeSmall,
            })}
            data-qa="qa_message"
        >
            {showAlertIcon && (
                <div className={css.icon}>
                    <Icon glyph={SvgAlert} />
                </div>
            )}
            {children}
        </div>
    );
};

export default React.memo<MessageProps>(Message);
