.root {
    font-size: 18px;
}

.typeSuccess {
    color: var(--green-color);
}

.typeInfo {
    color: var(--primary-cta-color);
}

.typeHint {
    color: var(--dark-pigeon-color);
}

.typeError {
    color: var(--red-color);
}

.typeAlert,
.typeWarning {
    position: relative;
    padding-left: 20px;
    color: var(--red-color);
    font-weight: bold;
}

.typeWarning {
    color: var(--primary-cta-color);
}

.sizeSmall {
    font-size: 13px;
}

.icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 13px;
    height: 13px;
}
