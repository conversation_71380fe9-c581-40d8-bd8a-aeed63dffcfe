import React from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import DragBox from 'ui/DragBox';

import SelectedItem, { SelectedItemProps } from '../SelectedItem';

import css from './SelectedDraggableList.css';

export interface SelectedDraggableListProps
    extends Pick<
        SelectedItemProps,
        | 'onRemoveItem'
        | 'onRearrangeItem'
        | 'disabledActions'
        | 'isOrdered'
        | 'minIndex'
        | 'startIndex'
    > {
    items: Array<SelectedItemProps['item']>;
}

const SelectedDraggableList: React.FC<SelectedDraggableListProps> = ({
    items,
    onRemoveItem,
    disabledActions,
    minIndex,
    isOrdered,
    onRearrangeItem,
    startIndex,
}) => {
    const renderItem = (item: SelectedItemProps['item'], index: number) => {
        if (!disabledActions) {
            return (
                <DragBox
                    key={item.id}
                    index={index}
                    id={item.id}
                    moveCard={onRearrangeItem!}
                    className={css.draggableItem}
                >
                    <SelectedItem
                        isOrdered={isOrdered}
                        item={item}
                        prevItem={items[index - 1]}
                        nextItem={items[index + 1]}
                        index={index}
                        startIndex={startIndex}
                        itemsLength={items.length}
                        onRemoveItem={
                            !disabledActions ? onRemoveItem : undefined
                        }
                        onRearrangeItem={onRearrangeItem}
                        minIndex={minIndex}
                    />
                </DragBox>
            );
        }

        return (
            <SelectedItem
                key={item.id}
                disabledActions={disabledActions}
                isOrdered={isOrdered}
                item={item}
                prevItem={items[index - 1]}
                nextItem={items[index + 1]}
                index={index}
                itemsLength={items.length}
                onRemoveItem={!disabledActions ? onRemoveItem : undefined}
                onRearrangeItem={onRearrangeItem}
                minIndex={minIndex}
            />
        );
    };
    return <ul className={css.list}>{items.map(renderItem)}</ul>;
};

export default (props: SelectedDraggableListProps) => (
    <DndProvider backend={HTML5Backend}>
        <SelectedDraggableList {...props} />
    </DndProvider>
);
