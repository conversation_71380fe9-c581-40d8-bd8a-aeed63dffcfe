import React from 'react';

import cn from 'classnames';
import PropTypes from 'prop-types';

import CloseButton from '../closeButton/closeButton';

// styles
import css from './notification.css';

class Notification extends React.Component {
    static propTypes = {
        active: PropTypes.bool,
        showCloseButton: PropTypes.bool,
        withinNotificationCenter: PropTypes.bool,
        title: PropTypes.string,
        borderColor: PropTypes.string,
        children: PropTypes.node,
        onClose: PropTypes.func,
    };

    handleClose = () => {
        this.props.onClose();
    };

    render() {
        const totalClasses = cn(
            this.props.withinNotificationCenter
                ? css.rootForNotificationCenter
                : css.root,
            {
                [css.active]: this.props.active,
                [css.rootThemeGreen]: this.props.borderColor === 'green',
                [css.rootThemeYellow]: this.props.borderColor === 'yellow',
            },
        );

        return (
            <div className={totalClasses}>
                {this.props.showCloseButton && (
                    <CloseButton size="small" onClose={this.handleClose} />
                )}
                <div>
                    {this.props.title ? (
                        <h3 className={css.title}>{this.props.title}</h3>
                    ) : null}
                    {this.props.children}
                </div>
            </div>
        );
    }
}

export default Notification;
