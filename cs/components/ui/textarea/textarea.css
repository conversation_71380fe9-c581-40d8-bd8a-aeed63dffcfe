.rootError .textarea {
    border-color: var(--error-border);
}

.title {
    margin-bottom: 5px;
    font-weight: bold;
}

.textarea {
    display: inline-block;
    width: 100%;
    height: 120px;
    box-sizing: border-box;
    padding: 10px 15px;
    border: 1px solid var(--default-border);
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
    box-shadow: none;
    color: var(--content-color);
    font-size: 13px;
    line-height: 18px;
    resize: none;
    vertical-align: middle;
}

.textarea:disabled {
    background-color: var(--grey-bg);
    color: var(--dark-pigeon-color);
}

.textarea:disabled:hover {
    cursor: default;
}

.textarea:focus {
    border-color: var(--primary-cta-color);
}

.textareaButtonHeight {
    height: 40px;
}

.hint {
    display: block;
    margin: 5px 0;
    color: var(--pigeon-color);
}

.error {
    display: block;
    margin: 5px 0;
    color: var(--red-color);
}

.counter {
    text-align: right;
}
