import React from 'react';
import ReactDOM from 'react-dom';

import cn from 'classnames';

import { usePortalContainerRef } from '../../../hooks/usePortalContainerRef';
import { useTabletOrMobileDevice } from '../../../hooks/useTabletOrMobileDevide';

import css from './FixedFooter.css';

export interface FixedFooterProps {
    className?: string;
    show: boolean;
    zIndex?: number;
}

const FixedFooter: React.FC<React.PropsWithChildren<FixedFooterProps>> = ({
    className,
    show,
    children,
    zIndex = 1,
}) => {
    const containerRef = usePortalContainerRef();
    const isMobileOrTablet = useTabletOrMobileDevice();

    if (!containerRef.current || !isMobileOrTablet) {
        return null;
    }

    return ReactDOM.createPortal(
        <div
            className={cn(css.root, className, { [css.show]: show })}
            style={{ zIndex }}
        >
            {children}
        </div>,
        containerRef.current,
    );
};

export default FixedFooter;
