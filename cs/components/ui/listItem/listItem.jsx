import React from 'react';

import cn from 'classnames';
import PropTypes from 'prop-types';

import ClearButton from '../clearButton/clearButton';

// styles
import css from './listItem.css';

const ListItem = (props) => {
    const totalClasses = cn(css.root, {
        [css.rootSmall]: props.sizeSmall,
        [css.rootWhite]: props.color === 'white',
    });

    return (
        <div className={totalClasses}>
            <div className={css.label}>{props.label}</div>
            {typeof props.onDelete === 'function' && (
                <div className={css.clearIcon}>
                    <ClearButton onClear={() => props.onDelete(props.label)} />
                </div>
            )}
        </div>
    );
};

ListItem.propTypes = {
    sizeSmall: PropTypes.bool,
    color: PropTypes.string,
    label: PropTypes.string.isRequired,
    onDelete: PropTypes.func,
};

export default ListItem;
