.root {
    display: inline-flex;
    align-content: center;
    cursor: pointer;
    line-height: 20px;
}

.switchBg {
    position: relative;
    z-index: 0;
    display: inline-flex;
    width: 36px;
    height: 20px;
    box-sizing: border-box;
    flex-shrink: 0;
    background: var(--pigeon-color);
    border-radius: 100px;
    box-shadow: inset 0 2px 0 rgba(57, 96, 131, 0.2);
    transition: background-color 0.3s;
}

.active .switchBg {
    background: var(--blue-bg);
    box-shadow: inset 0 2px 0 rgba(57, 96, 131, 0.2);
}

:global(.vchasno-autumn-theme) .switchBg {
    box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.1) inset;
}

:global(.vchasno-autumn-theme) .active .switchBg {
    background: var(--autumn-cta-color);
}

.disabled {
    cursor: default;
    opacity: 0.7;
}

.circle {
    position: absolute;
    top: 0;
    left: 0;
    width: 16px;
    height: 16px;
    background-color: var(--white-bg);
    border-radius: 50%;
    transform: translate(2px, 2px);
    transition: transform 0.3s;
}

.switchBg > input:checked + .circle {
    transform: translate(18px, 2px);
}

.label {
    margin-left: 12px;
    color: var(--content-color);
    font-size: 14px;
    line-height: 20px;
}
