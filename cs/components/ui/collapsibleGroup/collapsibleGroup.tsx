import React, { useState } from 'react';

import cn from 'classnames';

import Hint, { HintProps } from '../hint/hint';
import Icon from '../icon/icon';

import ArrowSvg from '../../../icons/arrow-down.svg';

import css from './collapsibleGroup.css';

type IconPosition = 'left' | 'right';

interface CollapsibleGroupProps {
    title: string | React.ReactNode;
    onHeaderClick?: (isCollapsed: boolean) => void;
    isOpened?: boolean;
    isTypeHeaderBig?: boolean;
    hint?: string | React.ReactNode;
    preview?: React.ReactNode;
    titleLabel?: React.ReactNode;
    headBlock?: React.ReactNode;
    hintPosition?: HintProps['position'];
    iconPosition?: IconPosition;
    containerClassName?: string;
    titleElement?: React.ReactNode;
    interactiveTitleIcon?: React.ReactNode;
}

const CollapsibleGroup: React.FC<
    React.PropsWithChildren<CollapsibleGroupProps>
> = ({
    title,
    titleLabel,
    headBlock,
    hint,
    hintPosition,
    isTypeHeaderBig,
    children,
    preview,
    isOpened = false,
    onHeaderClick,
    iconPosition = 'left',
    containerClassName,
    titleElement,
    interactiveTitleIcon,
}) => {
    const [isCollapsed, setIsCollapsed] = useState(!isOpened);

    const handleHeaderClick = () => {
        if (onHeaderClick) {
            onHeaderClick(!isCollapsed);
        }
    };

    const handleToggleCollapsedState = () => {
        setIsCollapsed((_isCollapsed) => !_isCollapsed);
        handleHeaderClick();
    };

    return (
        <div
            className={cn(css.root, containerClassName, {
                [css.rootCollapsed]: isCollapsed,
            })}
            data-qa={
                typeof title === 'string' ? `qa_block_${title}` : undefined
            }
        >
            <div className={css.titleBlock}>
                <div className={css.inlineBlock}>
                    <button
                        className={cn(css.button, {
                            [css.buttonBig]: isTypeHeaderBig,
                            [css.buttonIconLeft]: iconPosition === 'left',
                            [css.buttonIconRight]: iconPosition === 'right',
                        })}
                        onClick={handleToggleCollapsedState}
                        data-qa={
                            typeof title === 'string'
                                ? `qa_open_btn_${title}`
                                : undefined
                        }
                    >
                        {title}
                        <Icon glyph={ArrowSvg} className={css.icon} />
                    </button>
                </div>
                {hint && (
                    <div className={css.inlineBlock}>
                        <Hint black position={hintPosition || 'left'}>
                            <div className={css.hint}>{hint}</div>
                        </Hint>
                    </div>
                )}
                {titleLabel && (
                    <div className={css.titleLabel}>{titleLabel}</div>
                )}
                {titleElement && titleElement}

                {interactiveTitleIcon && (
                    <div className={css.interactiveTitleIcon}>
                        {interactiveTitleIcon}
                    </div>
                )}
            </div>
            {isCollapsed && preview}
            {headBlock && <div className={css.head}>{headBlock}</div>}
            <div className={css.content}>{children}</div>
        </div>
    );
};

export default CollapsibleGroup;
