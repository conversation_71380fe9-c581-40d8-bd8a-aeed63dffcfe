.root {
    display: flex;
    min-height: 100vh;
    flex-direction: column;
}

.main {
    display: flex;
    max-width: 480px;
    box-sizing: border-box;
    flex: 1;
    flex-direction: column;
    align-items: start;
    justify-content: center;
    padding: 64px 16px;
    margin: 0 auto;
    gap: 35px;
    text-align: left;
}

.back {            
    display: flex;
    width: 48px;
    height: 48px;
    align-items: center;
    justify-content: center;
    background: var(--white-color);
    border-radius: 50%;
}

.back svg {
    width: 18px;
    height: 18px;
    color: var(--slate-white-color);
}

@media (max-width: 768px) {
    .main { padding: 32px 16px; }
}
