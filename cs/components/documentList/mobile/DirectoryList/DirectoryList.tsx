import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { ColDef, ModuleRegistry } from '@ag-grid-community/core';
import { AgGridReact, AgGridReactProps } from '@ag-grid-community/react';

import cn from 'classnames';
import {
    defineGridColumnPropsBySettingItem,
    getContainerStyles,
    getRowClass,
    getRowId,
    getSortedColumnList,
    handleAgGridCellClicked,
} from 'components/documentList/GridTable/utils';
import actionCreators from 'components/documentList/documentListActionCreators';
import { useGridTableContext } from 'components/documentList/gridTableContext';
import mobDirectoryListColumnProps from 'components/documentList/mobile/DirectoryList/mobDirectoryListColumnConfigProps';
import {
    getDocumentListActiveTableSettings,
    getSelectedDocuments,
} from 'components/documentList/selectors';
import {
    CommonDocumentColumnKey,
    DocumentListTableSettingItem,
} from 'components/documentList/types';
import { useHandleAgGridRowClicked } from 'components/documentList/useHandleAgGridRowClicked';
import 'lib/ag-grid';
import { getAppFlags } from 'selectors/app.selectors';
import { Document } from 'services/documents/ts/types';

import { frameworkComponents } from '../../GridTable/frameworkComponents';
import { rowSelection } from '../../GridTable/staticGridProps';

ModuleRegistry.registerModules([ClientSideRowModelModule]);

export interface GridTableProps {
    className?: string;
}

const columns: DocumentListTableSettingItem['name'][] = ['title', 'date'];

const MIN_ITEMS_FOR_PERFORM_MODE = 26;

const DirectoryList: React.FC<React.PropsWithChildren<GridTableProps>> = () => {
    const flags = useSelector(getAppFlags);
    const isDocumentViewLockActive = flags.DOCUMENT_VIEW_LOCK_ON;
    const selectedDocuments = useSelector(getSelectedDocuments);

    const dispatch = useDispatch();

    const { directories, gridApi, setGridApi } = useGridTableContext();
    const handleAgGridRowClicked = useHandleAgGridRowClicked();

    const defaultColDef: ColDef<Document> = {
        suppressMovable: false,
    };

    // визначаємо колонку з чекбоксами
    const selectionColumnDef = useMemo(
        () =>
            ({
                resizable: false,
                lockPinned: true,
                pinned: 'left',
                // width: DEFAULT_PINNED_LEFT_COLUMN_WIDTH,
                minWidth: 0,
            } as AgGridReactProps['selectionColumnDef']),
        [],
    );

    const activeTableSettings = useSelector(getDocumentListActiveTableSettings);
    const isPerformMode = directories.length > MIN_ITEMS_FOR_PERFORM_MODE;

    const sortedColumnList = useMemo(
        () =>
            getSortedColumnList({
                activeTableSettings,
                savedGridColumnsState: [],
            }),
        [activeTableSettings],
    );

    const columnDefs = useMemo(() => {
        const filteredSortedColumnList = sortedColumnList.filter((item) =>
            columns.includes(item.name),
        );

        return [
            ...filteredSortedColumnList.map((item) => ({
                colId: item.name,
                ...defineGridColumnPropsBySettingItem(item),
                ...mobDirectoryListColumnProps[
                    item.name as CommonDocumentColumnKey
                ],
                flex: item.name === 'title' && 1,
                width: undefined,
            })),
        ] as ColDef<Document>[];
    }, [sortedColumnList]);

    const handleGridReady: AgGridReactProps['onGridReady'] = (event) => {
        setGridApi(event.api);

        event.api.forEachLeafNode((node) => {
            node.setSelected(node.data.selected, false);
        });
    };

    const onSelectionChanged: AgGridReactProps['onSelectionChanged'] = () => {
        const selectedRows = gridApi?.getSelectedRows();
        // @ts-expect-error TS7006 [FIXME] Comment is autogenerated
        const selectedDocumentIds = selectedDocuments.map((item) => item.id);

        const selectedDirectoryIds = selectedRows
            ?.filter((row) => row.isDirectory)
            .map((item) => item.id);

        dispatch(
            actionCreators.batchSelection(
                selectedDocumentIds,
                selectedDirectoryIds,
            ),
        );
    };

    return (
        <div
            className={cn('ag-theme-alpine', {
                'perform-mode': isPerformMode,
            })}
            style={getContainerStyles(directories.length)}
        >
            <AgGridReact
                suppressScrollOnNewData
                selectionColumnDef={selectionColumnDef}
                scrollbarWidth={isPerformMode ? undefined : 0}
                getRowClass={isDocumentViewLockActive ? getRowClass : undefined}
                getRowId={getRowId}
                onSelectionChanged={onSelectionChanged}
                onRowClicked={handleAgGridRowClicked}
                onGridReady={handleGridReady}
                rowHeight={40}
                headerHeight={-1}
                onCellClicked={handleAgGridCellClicked}
                components={frameworkComponents}
                domLayout={isPerformMode ? 'normal' : 'autoHeight'}
                defaultColDef={defaultColDef}
                popupParent={document.body}
                rowData={directories}
                rowSelection={rowSelection}
                columnDefs={columnDefs}
            />
        </div>
    );
};

export default DirectoryList;
