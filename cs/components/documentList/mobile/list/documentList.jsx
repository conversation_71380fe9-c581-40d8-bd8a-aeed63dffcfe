import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import MediaQuery from 'react-responsive';

import { Checkbox, FlexBox, Pagination, Paragraph } from '@vchasno/ui-kit';

import DirectoryBreadcrumbs from 'components/documentList/DirectoryBreadcrumbs';
import constants from 'components/documentList/constants';
import documentListActionCreators from 'components/documentList/documentListActionCreators';
import { useGridTableContext } from 'components/documentList/gridTableContext';
import DirectoriesToolbar from 'components/documentList/mobile/DirectoriesToolbar';
import DirectoryList from 'components/documentList/mobile/DirectoryList';
import { getDocumentListDocuments } from 'components/documentList/selectors';
import FiltersPopupContainer from 'components/filtersPopup/filtersPopupContainer';
import { useHistoryPagination } from 'hooks/useHistoryPagination';
import { MEDIA_WIDTH } from 'lib/constants';
import PropTypes from 'prop-types';
import {
    getDocumentListDirectories,
    getSelectedDirectories,
    getSelectedDocuments,
} from 'selectors/documentList.selectors';
import { getIsArchivePage } from 'selectors/router.selectors';
import { t } from 'ttag';

import PanelBottomFixed from '../../../ui/panelBottomFixed/panelBottomFixed';

import CommentPopup from '../../../commentPopup/commentPopup';
import PageTitle from '../../../pageTitle/pageTitle';
import SearchBarQuery from '../../../searchBarQuery/searchBarQuery';
import DocumentItem from '../item/documentListItem';
import Toolbar from '../toolbar/documentListToolbar';

// styles
import css from './documentList.css';

const DocumentList = (props) => {
    const dispatch = useDispatch();
    const historyPagination = useHistoryPagination();
    const selectedDocuments = useSelector(getSelectedDocuments);
    const selectedDirectories = useSelector(getSelectedDirectories);
    const documents = useSelector(getDocumentListDocuments);
    const directories = useSelector(getDocumentListDirectories);
    const selectedCount = selectedDocuments.length + selectedDirectories.length;
    const openedCommentsPopupDocument = props.documents.find(
        (document) => props.activeDocumentCommentsId === document.id,
    );
    const isSelectedFew = selectedCount > 0 && !props.isSelectedAll;
    const isSelectedAllChecked = props.isSelectedAll || isSelectedFew;
    const { gridApi } = useGridTableContext();

    const isArchivePage = useSelector(getIsArchivePage);

    const onSelectedChange = () => {
        if (isSelectedAllChecked && !isSelectedFew) {
            dispatch(documentListActionCreators.onDeselectAllDocuments());
            gridApi?.deselectAll();
        } else {
            dispatch(documentListActionCreators.onSelectAllDocuments());
            gridApi?.selectAll();
        }
    };
    const isSignWithKepFlowActive = props.isSignWithKepFlowActive;

    return (
        <div>
            <PageTitle />
            {isArchivePage && (
                <MediaQuery maxWidth={MEDIA_WIDTH.normal}>
                    <FiltersPopupContainer sortButtons={constants.SORT_DATA} />
                </MediaQuery>
            )}
            <div className={css.search}>
                <SearchBarQuery placeholder={t`Пошук документів`} />
            </div>
            <div className={css.breadcrumbs}>
                <DirectoryBreadcrumbs />
            </div>
            {(documents.length > 0 || directories.length > 0) && (
                <FlexBox justify="space-between" className={css.checkboxBlock}>
                    <Checkbox
                        onChange={onSelectedChange}
                        checked={isSelectedAllChecked}
                        partial={isSelectedFew}
                        label={t`Обрати всі`}
                    />
                    {selectedCount > 0 && (
                        <Paragraph>
                            {t`Обрано`}: {selectedCount}
                        </Paragraph>
                    )}
                </FlexBox>
            )}
            {directories.length > 0 && <DirectoryList />}
            {documents.length === 0 && directories.length === 0 && (
                <Paragraph
                    textAlign="center"
                    className={css.emptyDocumentListText}
                >{t`Документи відсутні`}</Paragraph>
            )}
            <ul className={css.list}>
                {props.documents.map((doc) => {
                    const isUnsuccessfullySigned = props.unsuccessfullySignedDocuments
                        ? doc.id in props.unsuccessfullySignedDocuments
                        : false;
                    return (
                        <li key={doc.id} className={css.item}>
                            <DocumentItem
                                isUnsuccessfullySigned={isUnsuccessfullySigned}
                                doc={doc}
                                onSelect={props.onSelectDocument}
                                onDeselect={props.onDeselectDocument}
                                onShowComments={props.onShowComments}
                            />
                        </li>
                    );
                })}
            </ul>
            {selectedDocuments.length > 0 &&
                selectedDirectories.length === 0 &&
                !isSignWithKepFlowActive && (
                    <PanelBottomFixed>
                        <Toolbar
                            isRejectPopupOpened={props.isRejectPopupOpened}
                            isSendPopupOpened={props.isSendPopupOpened}
                            isSignSummaryLoading={props.isSignSummaryLoading}
                            documentActions={props.documentActions}
                            sentDocCount={props.sentDocCount}
                            errorMessage={props.errorMessage}
                            sentResults={props.sentResults}
                            sendPopupStatus={props.sendPopupStatus}
                            selectedDocuments={selectedDocuments}
                            onDownloadSignSummary={props.onDownloadSignSummary}
                            onSignDocuments={props.onSignDocuments}
                            onSendDocuments={props.onSendDocuments}
                            onReject={props.onRejectDocuments}
                            onOpenPopup={props.onOpenPopup}
                            onClosePopup={props.onClosePopup}
                        />
                    </PanelBottomFixed>
                )}
            {selectedDirectories.length > 0 && (
                <PanelBottomFixed>
                    <DirectoriesToolbar />
                </PanelBottomFixed>
            )}
            <div className={css.footer}>
                <div className={css.pagination}>
                    <Pagination
                        gapStep={props.documentsCount > 100 ? 10 : 5}
                        scrollOnChange
                        total={Math.ceil(
                            props.documentsCount / props.documentsPerPage,
                        )}
                        current={historyPagination.currentPage}
                        hideOnSinglePage
                        onChange={(page) => {
                            historyPagination.setPage(page);
                            props.onPageCounterClick(page);
                        }}
                    />
                </div>
            </div>
            {props.isCommentsListPopupOpened && (
                <CommentPopup
                    comments={props.commentsList}
                    commentAdded
                    active={props.isCommentsListPopupOpened}
                    docs={
                        openedCommentsPopupDocument
                            ? [openedCommentsPopupDocument]
                            : selectedDocuments
                    }
                    analyticsLocation={'document_list'}
                    userEmail={props.currentUser.email}
                    onClose={() => props.onClosePopup('commentsList')}
                    onCommentDocuments={props.onCommentDocuments}
                />
            )}
        </div>
    );
};

DocumentList.propTypes = {
    isAddCommentsPopupOpened: PropTypes.bool,
    isSendPopupOpened: PropTypes.bool,
    isSelectedAll: PropTypes.bool,
    isRejectPopupOpened: PropTypes.bool,
    isHeaderFixed: PropTypes.bool,
    isDeletePopupOpened: PropTypes.bool,
    isCommentsListPopupOpened: PropTypes.bool,

    currentUser: PropTypes.object,
    documentActions: PropTypes.object.isRequired,
    unsuccessfullySignedDocuments: PropTypes.object,

    documents: PropTypes.array.isRequired,
    commentsList: PropTypes.array.isRequired,
    sentResults: PropTypes.array.isRequired,

    documentsPerPage: PropTypes.number,
    documentsCount: PropTypes.number,
    currentPage: PropTypes.number.isRequired,
    sentDocCount: PropTypes.number,

    hasNextPage: PropTypes.bool,
    useSimplePagination: PropTypes.bool,

    dateSortType: PropTypes.string,
    activeDocumentCommentsId: PropTypes.string,
    sendPopupStatus: PropTypes.string,
    errorMessage: PropTypes.string,

    onSelectAllDocuments: PropTypes.func.isRequired,
    onCommentDocuments: PropTypes.func.isRequired,
    onDeselectAllDocuments: PropTypes.func.isRequired,
    onSelectDocument: PropTypes.func.isRequired,
    onDeselectDocument: PropTypes.func.isRequired,
    onSignDocuments: PropTypes.func.isRequired,
    onSendDocuments: PropTypes.func.isRequired,
    onRejectDocuments: PropTypes.func.isRequired,
    onDeleteDocuments: PropTypes.func.isRequired,
    onPageCounterClick: PropTypes.func.isRequired,
    onRecipientChange: PropTypes.func.isRequired,
    onOpenPopup: PropTypes.func.isRequired,
    onClosePopup: PropTypes.func.isRequired,
    onShowComments: PropTypes.func.isRequired,
    onOpenCreateDeleteRequestPopup: PropTypes.func.isRequired,
    onOpenResolveDeleteRequestPopup: PropTypes.func.isRequired,
};

export default DocumentList;
