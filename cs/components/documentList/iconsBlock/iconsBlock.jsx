import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { BlackTooltip } from '@vchasno/ui-kit';

import cn from 'classnames';
import { DATETIME_FORMAT } from 'components/txtXmlViewer/signatures/constants';
import { formatDate } from 'lib/date';
import { getAppFlags } from 'selectors/app.selectors';
import { getCurrentCompany } from 'selectors/app.selectors';
import { getIsArchivePage } from 'selectors/router.selectors';
import eventTracking from 'services/analytics/eventTracking';
import {
    getIsDocumentHasRejectedDeleteRequest,
    hasDocumentEmptyRequiredParameters,
} from 'services/documents/utils';
import { t } from 'ttag';

import { getDocumentListPinnedIconsSettings } from '../selectors';

import { makeSuppressRowClickEffectHandler } from '../GridTable/utils';

import Icon from '../../ui/icon/icon';

import CreateDeleteRequest from '../../createDeleteRequestPopup/createDeleteRequest';
import HeadBlock from '../headIconBlock/headIconBlock';
import LinkedDocumentsIcon from '../linkedDocumentsIcon/linkedDocumentsIcon';
import AntivirusStatus from './AntivirusStatus/AntivirusStatus';

import SvgAlarm from './images/alarm.svg';
import SvgAlert from './images/alert.svg';
import SvgEye from './images/eye.svg';
import SvgFileDisabled from './images/file-disabled.svg';
import SvgMessage from './images/message.svg';

import css from './iconsBlock.css';

const IconsBlock = (props) => {
    const enableSplitSignPermission = useSelector(getAppFlags)
        .ENABLE_SEPARATE_DOC_SIGN_PERMISSIONS;
    const {
        doc,
        onOpenCreateDeleteRequestPopup,
        onOpenResolveDeleteRequestPopup,
        onShowComments,
        isUnsuccessfullySigned,
        errorMessage,
        reasonForSignDisabled,
    } = props;

    const {
        documentLinkedDocuments,
        documentCommentsAndDeleteRequest,
        documentAntivirusStatus,
        documentReviewStatus,
        documentRequiredParameters,
    } = useSelector(getDocumentListPinnedIconsSettings);

    const isArchivePage = useSelector(getIsArchivePage);
    const currentCompany = useSelector(getCurrentCompany);

    const onOpenDeleteRequestPopup = makeSuppressRowClickEffectHandler(
        (evt) => {
            evt.preventDefault();
            onOpenCreateDeleteRequestPopup([doc]);
        },
    );

    const onOpenResolveDeleteRequestPopupClick = makeSuppressRowClickEffectHandler(
        (evt) => {
            evt.preventDefault();
            onOpenResolveDeleteRequestPopup([doc]);
        },
    );

    const onShowCommentsList = makeSuppressRowClickEffectHandler((evt) => {
        evt.preventDefault();
        eventTracking.sendToGTM({
            event: 'click_comments',
        });
        onShowComments(doc.id);
    });

    const isEmptyRequiredParameters = hasDocumentEmptyRequiredParameters(doc);

    const isShowDocumentHasBeenSeenIcon = useMemo(() => {
        if (doc.isInput) {
            return Boolean(
                doc.recipients.find(
                    (recipient) => recipient.edrpou === currentCompany.edrpou,
                )?.dateDelivered,
            );
        }

        return Boolean(doc.dateDelivered);
    }, [doc, currentCompany]);

    const seenTooltipText = useMemo(() => {
        // Якщо це вхідний документ, то показуємо дату перегляду компанії (не показуємо по інших учасниках)
        if (doc.isInput) {
            const companyRecipient = doc.recipients.find(
                (recipient) => recipient.edrpou === currentCompany.edrpou,
            );

            if (!companyRecipient?.dateDelivered) {
                return t`Документ не переглянуто`;
            }

            return (
                <div>
                    {t`Документ переглянуто`}
                    <br />
                    {formatDate(
                        companyRecipient.dateDelivered,
                        DATETIME_FORMAT,
                    )}
                </div>
            );
        }

        // Далі це контекст компанії власника документа

        const counterpartyRecipients = doc.recipients.filter(
            (recipient) => recipient.edrpou !== currentCompany.edrpou,
        );

        // Якщо це двохсторонній документ, то показуємо в контексті одного контрагента
        if (counterpartyRecipients.length === 1) {
            const dateSeen = counterpartyRecipients[0].dateDelivered;

            if (!dateSeen) {
                return t`Контрагент не переглянув документ`;
            }

            return (
                <div>
                    {t`Контрагент переглянув документ`}
                    <br />
                    {doc.edrpouRecipient}
                    {' - '}
                    {formatDate(dateSeen, DATETIME_FORMAT)}
                </div>
            );
        }

        // Далі це контекст багатостороннього документу, треба в розрізі кожного підсвітити дату та час перегляду або не перегляду

        const datesSeenList = counterpartyRecipients.map((recipient) => {
            return `${recipient.edrpou} - ${
                recipient.dateDelivered
                    ? formatDate(recipient.dateDelivered, DATETIME_FORMAT)
                    : t`не переглянув`
            }`;
        });

        return (
            <div>
                {t`Перегляд контрагентами: `}
                {datesSeenList.map((seenLabel) => (
                    <div key={seenLabel}>{seenLabel}</div>
                ))}
            </div>
        );
    }, [doc, currentCompany]);

    return (
        <div className={css.iconContainer}>
            {documentLinkedDocuments?.isActive && (
                <span className={css.icon}>
                    <LinkedDocumentsIcon doc={doc} />
                </span>
            )}
            {documentCommentsAndDeleteRequest?.isActive && (
                <span className={css.icon}>
                    {doc.deleteRequest &&
                        doc.deleteRequest.status !== 'accepted' &&
                        !getIsDocumentHasRejectedDeleteRequest(doc) && (
                            <CreateDeleteRequest
                                requestIconType
                                docs={[doc]}
                                onCreateClick={onOpenDeleteRequestPopup}
                                onResolveRequestClick={
                                    onOpenResolveDeleteRequestPopupClick
                                }
                            />
                        )}
                    {(!doc.deleteRequest ||
                        getIsDocumentHasRejectedDeleteRequest(doc)) && (
                        <div
                            className={cn(css.commentIcon, {
                                [css.commentIconDisabled]:
                                    doc.comments.length === 0,
                            })}
                            onClick={onShowCommentsList}
                        >
                            {doc.comments.length > 0 && (
                                <span className={css.counterBadge}>
                                    {doc.comments.length}
                                </span>
                            )}
                            <Icon glyph={SvgMessage} />
                        </div>
                    )}
                </span>
            )}
            {documentAntivirusStatus?.isActive && (
                <span
                    className={css.icon}
                    onClick={() => {
                        eventTracking.sendToGTM({
                            event: 'click_antivirus',
                        });
                    }}
                >
                    <AntivirusStatus
                        status={doc.antivirusChecks?.[0]?.status}
                    />
                </span>
            )}
            {documentReviewStatus?.isActive && (
                <span className={css.icon}>
                    {isUnsuccessfullySigned && !reasonForSignDisabled && (
                        <span title={errorMessage}>
                            <Icon glyph={SvgAlert} />
                        </span>
                    )}
                    {!isUnsuccessfullySigned && isShowDocumentHasBeenSeenIcon && (
                        <BlackTooltip
                            placement="right"
                            disableInteractive
                            title={<>{seenTooltipText}</>}
                        >
                            <span
                                onClick={() => {
                                    eventTracking.sendToGTM({
                                        event: 'click_eye',
                                    });
                                }}
                            >
                                <Icon glyph={SvgEye} />
                            </span>
                        </BlackTooltip>
                    )}
                    {!isUnsuccessfullySigned && !isShowDocumentHasBeenSeenIcon && (
                        <div
                            onClick={() => {
                                eventTracking.sendToGTM({
                                    event: 'click_eye',
                                });
                            }}
                            style={{ opacity: 0.1, marginTop: 1 }}
                        >
                            <Icon glyph={SvgEye} />
                        </div>
                    )}
                </span>
            )}
            {documentRequiredParameters?.isActive && !isArchivePage && (
                <span
                    className={css.icon}
                    onClick={() => {
                        eventTracking.sendToGTM({
                            event: 'click_warning',
                        });
                    }}
                >
                    {isEmptyRequiredParameters && (
                        <BlackTooltip
                            disableInteractive
                            placement="right"
                            title={t`Заповніть додаткові параметри`}
                        >
                            <span>
                                <Icon glyph={SvgAlarm} />
                            </span>
                        </BlackTooltip>
                    )}
                    {!isEmptyRequiredParameters && (
                        <div style={{ opacity: 0.05, marginTop: 1 }}>
                            <Icon glyph={SvgAlarm} />
                        </div>
                    )}
                </span>
            )}
            {reasonForSignDisabled && enableSplitSignPermission && (
                <BlackTooltip
                    placement="right"
                    disableInteractive
                    title={reasonForSignDisabled}
                >
                    <span className={css.icon}>
                        <Icon glyph={SvgFileDisabled} />
                    </span>
                </BlackTooltip>
            )}
            <HeadBlock doc={doc} />
        </div>
    );
};

export default IconsBlock;
