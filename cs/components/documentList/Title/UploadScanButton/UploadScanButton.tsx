import React, { useMemo } from 'react';
import { useDropzone } from 'react-dropzone';
import { useSelector } from 'react-redux';

import { BlackTooltip, Button } from '@vchasno/ui-kit';

import { useOnboardingUploadScanDocsButtonContext } from 'components/OnboardingUploadScanDocsButton/context';
import { UPLOAD_SCAN_DOCS_BUTTON_ID } from 'components/documentList/Title/UploadScanButton/constants';
import { useUploadScanFilesMutation } from 'components/documentList/Title/UploadScanButton/useUploadScanFilesMutation';
import { WS_KEY_ES_HAS_ACTUAL_UPLOAD } from 'lib/constants';
import { MBtoBytes } from 'lib/ts/numbers';
import { getLocalStorageItem } from 'lib/webStorage';
import { getCurrentCompanyUploadConfig } from 'selectors/app.selectors';
import { getDocumentListDirectoryId } from 'selectors/router.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { mapStatetoHasPermission } from 'store/utils';
import { t } from 'ttag';
import Icon from 'ui/icon';

import {
    MAX_FILES_COUNT,
    MAX_FILE_SIZE_MB,
} from '../../../../constants/upload';

import UploadSvg from './images/upload.svg';

import css from '../Title.css';

const UploadScanButton: React.FC = () => {
    const uploadScanFilesMutation = useUploadScanFilesMutation();
    const { hasPermission } = useSelector(mapStatetoHasPermission);
    const isUserCanArchiveDocuments = hasPermission('canArchiveDocuments');
    const currentCompanyUploadConfig = useSelector(
        getCurrentCompanyUploadConfig,
    );
    const documentListDirectoryId = useSelector(getDocumentListDirectoryId);
    const {
        isRun: isOnboardingRunning,
        handleHideOnboarding,
    } = useOnboardingUploadScanDocsButtonContext();

    const { getInputProps, open } = useDropzone({
        multiple: true,
        maxSize: MBtoBytes(
            currentCompanyUploadConfig?.max_file_size || MAX_FILE_SIZE_MB,
        ),
        onDrop: (files) => {
            onChangeInput(files);
        },
        maxFiles:
            currentCompanyUploadConfig?.max_total_count || MAX_FILES_COUNT,
        accept: {
            'application/pdf': [],
            'image/*': [],
        },
    });

    const isUserHasActualUploadLocalStorage = useMemo(
        () => getLocalStorageItem(WS_KEY_ES_HAS_ACTUAL_UPLOAD),
        [],
    );

    const isDisabledButton =
        isUserHasActualUploadLocalStorage || !isUserCanArchiveDocuments;

    const handleButtonClick = () => {
        eventTracking.sendToGTMV4({
            event: 'ec_scans_import_upload_button_click',
        });

        if (isOnboardingRunning) {
            handleHideOnboarding();
        }

        open();
    };

    const onChangeInput = (files: File[]) => {
        // const files = event.target.files;
        if (!files || files.length === 0) {
            return;
        }

        const filesForUpload = Array.from(files);

        uploadScanFilesMutation.mutateAsync([
            filesForUpload,
            documentListDirectoryId,
        ]);
    };

    const getTooltipTitle = () => {
        if (isUserHasActualUploadLocalStorage) {
            return t`Дочекайтеся завершення минулого завантаження`;
        }

        if (!isUserCanArchiveDocuments) {
            return t`У вас немає прав на архівування документів`;
        }
        return '';
    };

    return (
        <>
            <BlackTooltip title={getTooltipTitle()}>
                <Button
                    id={UPLOAD_SCAN_DOCS_BUTTON_ID}
                    disabled={isDisabledButton}
                    theme="secondary"
                    onClick={handleButtonClick}
                    className={css.button}
                >
                    {t`Завантажити скан`}
                    <span style={{ marginLeft: 4, width: 16, height: 16 }}>
                        <Icon glyph={UploadSvg} />
                    </span>
                </Button>
            </BlackTooltip>
            <input {...getInputProps()} />
        </>
    );
};

export default UploadScanButton;
