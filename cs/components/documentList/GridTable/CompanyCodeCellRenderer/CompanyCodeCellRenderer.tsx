import React from 'react';
import { useSelector } from 'react-redux';

import { getIsScanDocVerifiedByUser } from 'components/ScheduledMetaSuggestionIcon/utils';

import { DocumentCellRendererParams } from '../types';

import { getDocumentListDocumentsMap } from '../../selectors';

import AnchorWrapper from '../AnchorWrapper';
import { CopyActionIconContainer } from '../CopyActionIcon';

const CompanyCodeCellRenderer = (params: DocumentCellRendererParams) => {
    const doc = useSelector(getDocumentListDocumentsMap)[params.data.id];

    if (!doc) {
        return null;
    }

    const docMetaSuggestionDisplayEdrpou = !getIsScanDocVerifiedByUser(doc)
        ? doc.scheduledMetaSuggestion?.suggestion?.edrpouRecipient
        : null;
    const docDisplayEdrpou =
        doc.displayEdrpou || docMetaSuggestionDisplayEdrpou || '';

    if (!docDisplayEdrpou) {
        return <AnchorWrapper docId={doc.id} />;
    }

    return (
        <CopyActionIconContainer
            displayValue={docDisplayEdrpou}
            dataQa="qa_doc_srn"
        />
    );
};

export default CompanyCodeCellRenderer;
