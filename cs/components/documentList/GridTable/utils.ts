import { CSSProperties, SyntheticEvent } from 'react';

import {
    BodyScrollEndEvent,
    ColDef,
    ColumnState,
    GridApi,
    IRowDragItem,
    RowClassParams,
} from '@ag-grid-community/core';
import { AgGridReactProps } from '@ag-grid-community/react';

import cn from 'classnames';
import { normalizeAmount } from 'components/DocumentEdit/mappers';
import {
    WS_GRID_COLUMN_STATE_ARCHIVE_LS_KEY,
    WS_GRID_COLUMN_STATE_INBOX_LS_KEY,
    WS_GRID_COLUMN_STATE_INTERNAL_LS_KEY,
    WS_GRID_COLUMN_STATE_LS_KEY,
    WS_GRID_COLUMN_STATE_OUTGOING_LS_KEY,
} from 'lib/constants';
import { getLocalStorageItem, setLocalStorageItem } from 'lib/webStorage';
import { DocumentSettings } from 'services/documents/ts/api';
import { Document } from 'services/documents/ts/types';
import {
    getDirectoryWordByCount,
    getDocumentWordByCount,
} from 'services/documents/ts/utils';
import { DocumentFolder } from 'services/enums';
import { IRole } from 'types/user';

import { Nullable } from '../../../types/general';
import {
    DocumentListDocumentItem,
    DocumentListTableSettingItem,
} from '../types';
import { ExtendedRowEvent, SuppressRowClick } from './types';

import HeaderCellComponent from './HeaderCellComponent';

type GridType = 'main' | 'internal' | 'inbox' | 'outgoing' | 'archive';

const mapGridTypeToKey: Record<GridType, string> = {
    main: WS_GRID_COLUMN_STATE_LS_KEY,
    internal: WS_GRID_COLUMN_STATE_INTERNAL_LS_KEY,
    inbox: WS_GRID_COLUMN_STATE_INBOX_LS_KEY,
    outgoing: WS_GRID_COLUMN_STATE_OUTGOING_LS_KEY,
    archive: WS_GRID_COLUMN_STATE_ARCHIVE_LS_KEY,
} as const;

const folderToGridTypeMap: Record<string, GridType> = {
    [DocumentFolder.INTERNAL]: 'internal',
    [DocumentFolder.INBOX]: 'inbox',
    [DocumentFolder.OUTGOING]: 'outgoing',
};

export const getGridTypeByFolder = (folder: string | null): GridType => {
    if (!folder) {
        return 'main';
    }

    return folderToGridTypeMap[folder] || 'main';
};

export const getGridColumnStateFromLocalStorage = (type: GridType = 'main') =>
    (getLocalStorageItem(mapGridTypeToKey[type]) || []) as ColumnState[];

export const saveGridColumnStateToLocalStorage = (
    data: ColumnState[],
    type: GridType = 'main',
) => {
    setLocalStorageItem(mapGridTypeToKey[type], data);
};

export const makeSuppressRowClickEffectHandler = (
    handler?: (_event: SyntheticEvent) => void,
) => (event: SyntheticEvent) => {
    // @ts-ignore
    (event.nativeEvent as SuppressRowClick['event']).suppressRowClick = true;

    if (typeof handler === 'function') {
        handler(event);
    }
};

export const isRowClickWasSuppressed = (
    event: ExtendedRowEvent['event'],
): boolean => {
    return Boolean(event.suppressRowClick);
};

export const isCheckboxCellClicked = (gridApi: Nullable<GridApi>) => {
    return Boolean(gridApi?.getFocusedCell()?.column.getColId() === 'check');
};

const getCategory = (doc: DocumentListDocumentItem) => {
    if (doc.category !== null) {
        return String(doc.category);
    }

    if (
        doc.scheduledMetaSuggestion?.suggestion?.category !== null &&
        doc.scheduledMetaSuggestion?.suggestion?.category !== undefined
    ) {
        return String(doc.scheduledMetaSuggestion.suggestion.category);
    }

    return '0';
};

export const prepareUpdatePayload = (
    doc: DocumentListDocumentItem,
    payload: Partial<DocumentSettings>,
) => ({
    update_sign_process: false,
    document_settings: {
        date_document:
            doc.dateDocument ||
            doc.scheduledMetaSuggestion?.suggestion?.dateDocument,
        number:
            doc.number || doc.scheduledMetaSuggestion?.suggestion?.number || '',
        title: doc.title,
        category: getCategory(doc),
        amount:
            normalizeAmount(doc.amount) ||
            normalizeAmount(doc?.scheduledMetaSuggestion?.suggestion?.amount),
        ...payload,
    },
});

export const normalizeAmountActionPayload = (
    amount: string,
): Nullable<string> =>
    amount === '' ? null : String(parseFloat(parseFloat(amount).toFixed(2)));

const LEFT_OVERFLOW_CLASS = 'ag-grid-body-scroll-left-overflow';
const RIGHT_OVERFLOW_CLASS = 'ag-grid-body-scroll-right-overflow';
// після того як проскролюємо в крайній правий бік, відстань до правого краю може бути більше ніж ширина вьюпорта
const PIXEL_OFFSET = 3;

export const onBodyScrollEnd = (event: BodyScrollEndEvent) => {
    if (event.direction === 'horizontal') {
        const scrollWidth =
            document.querySelector('.ag-header-viewport')?.scrollWidth || 0;
        const { right, left } = event.api.getHorizontalPixelRange();

        // визначаємо чи треба додавати тінь зліва
        if (left > 0) {
            document.body.classList.add(LEFT_OVERFLOW_CLASS);
        } else {
            document.body.classList.remove(LEFT_OVERFLOW_CLASS);
        }

        // визначаємо чи треба додавати тінь справа
        if (right + PIXEL_OFFSET >= scrollWidth) {
            document.body.classList.remove(RIGHT_OVERFLOW_CLASS);
        } else {
            document.body.classList.add(RIGHT_OVERFLOW_CLASS);
        }
    }
};

export const getInvisibleColIds = (api: GridApi) => {
    const displayedColumnsCenter = api.getDisplayedCenterColumns();
    // визначаємо ширину viewport в якому відбувається скролл
    const scrollWidth =
        document.querySelector('.ag-header-viewport')?.clientWidth || 0;

    const { left } = api.getHorizontalPixelRange();

    const colIdList = displayedColumnsCenter.map((item: any) => item.colId);

    const actualWidthList = displayedColumnsCenter.map(
        (item: any) => item.actualWidth,
    );

    const startScrollPoint = actualWidthList.map((_: number, index: number) =>
        actualWidthList
            .slice(0, index)
            .reduce((acc: number, item: number) => acc + item, 0),
    );

    const finishScrollPoint = startScrollPoint.map(
        (start: number, index: number) => start + actualWidthList[index],
    );

    // визначаємо колонки які повністю видимі, якщо частково видимі позначаємо false
    const visibility = startScrollPoint.map((start: number, index: number) => {
        return (
            start >= left && left + scrollWidth >= finishScrollPoint[index] - 1
        );
    });

    return {
        firstColId: colIdList[visibility.indexOf(true) - 1],
        lastColId: colIdList[visibility.lastIndexOf(true) + 1],
    } as const;
};

export const getRowId: AgGridReactProps['getRowId'] = (data) =>
    data.data.id.toString(); // toString need for directories ids

export const defineGridColumnPropsBySettingItem = (
    item: DocumentListTableSettingItem,
): ColDef<DocumentListDocumentItem> => {
    const props: ColDef<DocumentListDocumentItem> = {
        colId: item.name,
        headerName: item.text,
        /**
         * @type {CustomFieldRendererParams}
         */
        cellRendererParams: { item },
        headerComponentParams: { item },
        /**
         * In order to allow custom handling all keyboard event
         */
        suppressKeyboardEvent: () => true,
        headerComponent: HeaderCellComponent,
    };

    // custom company field config
    if (item.field) {
        /**
         * @type {CustomFieldRenderer}
         */
        props.cellRenderer = 'customFieldRenderer';
    }

    if (item?.field?.canEdit) {
        props.cellStyle = { overflow: 'visible' };
    }

    return props;
};

export const getRowClass = (params: RowClassParams) =>
    !params.data.isDirectory
        ? cn({
              'ag-row-document-view-locked': !(params.data as Document)
                  .isViewable,
              'ag-row-document-invalid-signature': (params.data as Document)
                  .isInvalidSigned,
          })
        : undefined;

export const getContainerStyles = (docCount: number) => {
    return {
        // width: '100%',
        // встановлюємо динамічно мінімальну висоту таблиці враховуючи кількіст документів
        // якщо 0 або більше 25 то встановлюємо мінімальну висоту 1 рядка + 15px - це висота скроллбару
        '--ag-grid-min-height':
            docCount > 0 && docCount <= 25
                ? `calc(36px * ${docCount} + 16px)`
                : 'calc(36px + 16px)',
    } as CSSProperties;
};

export const getLastSentVersionDocument = (document: Document) => {
    return document.versions?.find((version) => version.isSent);
};

export const isDocumentHasMultipleVersions = (document: Document) =>
    document.versions?.length > 1;

export const getLastDocumentDraft = (document: Document) =>
    document.drafts?.[0];

export const rowDragTextCallback = function (params: IRowDragItem) {
    if (params.rowNodes?.length === 1) {
        return params.rowNodes[0].data.name || params.rowNodes[0].data.title; // для випадку, якщо це папка або документ
    }

    const documentsCount = params.rowNodes?.filter(
        (node) => !node.data.isDirectory,
    ).length;
    const directoriesCount = params.rowNodes?.filter(
        (node) => node.data.isDirectory,
    ).length;
    return `${
        !!documentsCount
            ? `${documentsCount} ${getDocumentWordByCount(documentsCount)}`
            : ''
    } ${!!directoriesCount && !!documentsCount ? ', ' : ''} ${
        !!directoriesCount
            ? `${directoriesCount} ${getDirectoryWordByCount(directoriesCount)}`
            : ''
    }`;
};

export const getSortedColumnList = ({
    savedGridColumnsState,
    activeTableSettings,
}: {
    savedGridColumnsState: ColumnState[];
    activeTableSettings: DocumentListTableSettingItem[];
}) => {
    if (!Array.isArray(savedGridColumnsState)) {
        return activeTableSettings;
    }

    const savedGridColumnsStateKeyList = savedGridColumnsState
        .filter((column) => column.colId)
        .map((column) => column.colId as string);

    const columnSettingsKeyList = activeTableSettings.map((item) => item.name);

    return [
        ...savedGridColumnsStateKeyList.filter((key) =>
            columnSettingsKeyList.includes(key),
        ),
        ...columnSettingsKeyList.filter(
            (key) => !savedGridColumnsStateKeyList.includes(key),
        ),
    ].map((key) =>
        activeTableSettings.find((item) => item.name === key),
    ) as DocumentListTableSettingItem[];
};

export const handleAgGridCellClicked: AgGridReactProps['onCellClicked'] = (
    event,
) => {
    // збільшуємо область натискання на чекбокс на всю область ячейки
    if (
        event.type === 'cellClicked' &&
        event.colDef.colId === 'ag-Grid-ControlsColumn'
    ) {
        // @ts-ignore
        event.event.suppressRowClick = true;
        return event.node.setSelected(!event.node.isSelected());
    }
};

export const isDocumentSignersHasCurrentUserRole = (
    currentUserRole: IRole,
    document: Document,
) => {
    return document.signers?.some((signer) => {
        if (signer.roleId) {
            return signer.roleId === currentUserRole.id;
        }

        if (signer.group?.members) {
            return signer.group.members.some(
                (member) => member.role?.id === currentUserRole.id,
            );
        }
        return false;
    });
};
