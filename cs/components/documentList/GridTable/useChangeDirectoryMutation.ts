import { useMutation } from '@tanstack/react-query';
import { snackbarToast } from '@vchasno/ui-kit';

import { changeDirectory } from 'services/directories';
import { t } from 'ttag';

export const useChangeDirectoryMutation = () =>
    useMutation({
        mutationFn: (params: Parameters<typeof changeDirectory>) =>
            changeDirectory(...params),
        onSuccess: () => {
            snackbarToast.success(t`Папку успішно оновлено!`);
        },
        onError: (error) => {
            const errorMessage =
                // @ts-expect-error TS18046 [FIXME] Comment is autogenerated
                error.message || t`Виникла помилка при оновленні папки`;
            snackbarToast.error(errorMessage);
        },
    });
