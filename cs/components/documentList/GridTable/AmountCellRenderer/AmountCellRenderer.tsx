import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { normalizeAmount } from 'components/DocumentEdit/mappers';
import {
    getIsArchiveScanDoc,
    getIsScanDocVerifiedByUser,
} from 'components/ScheduledMetaSuggestionIcon/utils';
import { DocumentListDocumentItem } from 'components/documentList/types';
import {
    setDocMetaSuggestionsReviewStatus,
    updateDocument,
} from 'services/documents/ts/api';

import { DocumentCellRendererParams } from '../types';

import documentListActionCreators from '../../documentListActionCreators';
import { getDocumentListDocumentsMap } from '../../selectors';

import { normalizeAmountActionPayload, prepareUpdatePayload } from '../utils';

import EditFieldRenderer from '../EditFieldRenderer';

const getDocAmount = (doc: DocumentListDocumentItem) => {
    const docAmount = doc.amount;
    const metaSuggestionAmount =
        doc.scheduledMetaSuggestion?.suggestion?.amount;

    if (
        docAmount &&
        (typeof docAmount === 'number' || typeof docAmount === 'string')
    ) {
        return docAmount;
    }

    if (
        getIsArchiveScanDoc(doc) &&
        !getIsScanDocVerifiedByUser(doc) &&
        (typeof metaSuggestionAmount === 'number' ||
            typeof metaSuggestionAmount === 'string')
    ) {
        return metaSuggestionAmount;
    }
    return '';
};

const AmountCellRenderer = (params: DocumentCellRendererParams) => {
    const dispatch = useDispatch();
    const doc = useSelector(getDocumentListDocumentsMap)[params.data.id];

    if (!doc) {
        return null;
    }

    const docAmount = String(getDocAmount(doc));

    const handleChange = async (amount: string) => {
        await updateDocument(
            params.data.id,
            prepareUpdatePayload(doc, {
                amount: normalizeAmount(amount),
            }),
        );

        // коли це документ з архіву після розпізнавання даних через ШІ
        if (getIsArchiveScanDoc(doc) && !getIsScanDocVerifiedByUser(doc)) {
            await setDocMetaSuggestionsReviewStatus(
                doc.scheduledMetaSuggestion!.suggestion!.id,
                'invalid',
            );
            dispatch(documentListActionCreators.onLoadDocuments());
        } else {
            dispatch(
                documentListActionCreators.updateSingleDocumentListItem({
                    ...doc,
                    amount: normalizeAmountActionPayload(amount),
                }),
            );
        }
    };

    return (
        <EditFieldRenderer
            doc={doc}
            dataQa="qa_doc_amount"
            displayValue={docAmount}
            editValue={docAmount}
            onCommit={handleChange}
            inputProps={{
                type: 'number',
            }}
        />
    );
};

export default AmountCellRenderer;
