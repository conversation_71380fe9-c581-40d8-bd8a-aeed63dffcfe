import { useReducer } from 'react';

const SET_VALUE = 'SET_VALUE';
const SET_EDIT_MODE = 'SET_EDIT_MODE';
const SET_COMMIT_STATE = 'START_COMMIT';
const COMMIT_SUCCESS = 'COMMIT_SUCCESS';
const COMMIT_FAILURE = 'COMMIT_FAILURE';
const RESET_RESULT = 'RESET_RESULT';

type SetValue = {
    type: typeof SET_VALUE;
    payload: string;
};

type SetEditMode = {
    type: typeof SET_EDIT_MODE;
    payload: boolean;
};

type SetCommitState = {
    type: typeof SET_COMMIT_STATE;
};
type ResetResult = {
    type: typeof RESET_RESULT;
};
type SetCommitSuccess = {
    type: typeof COMMIT_SUCCESS;
    payload: string;
};
type SetCommitFailure = {
    type: typeof COMMIT_FAILURE;
    payload: string;
};

type Actions =
    | SetValue
    | SetEditMode
    | SetCommitState
    | SetCommitSuccess
    | SetCommitFailure
    | ResetResult;

interface State {
    editMode: boolean;
    value: string;
    isPending: boolean;
    isSuccess: boolean;
    isFailure: boolean;
}

const reducer = (state: State, action: Actions): State => {
    switch (action.type) {
        case SET_VALUE:
            return {
                ...state,
                value: action.payload,
            };
        case SET_EDIT_MODE:
            return {
                ...state,
                editMode: action.payload,
            };
        case SET_COMMIT_STATE:
            return {
                ...state,
                isPending: true,
                isSuccess: false,
                isFailure: false,
            };
        case COMMIT_SUCCESS:
            return {
                ...state,
                isPending: false,
                editMode: false,
                value: action.payload,
                isSuccess: true,
            };
        case COMMIT_FAILURE:
            return {
                ...state,
                isPending: false,
                editMode: false,
                value: action.payload,
                isFailure: true,
            };
        case RESET_RESULT:
            return {
                ...state,
                isFailure: false,
                isSuccess: false,
            };
        default:
            return state;
    }
};

export const useEditFieldReducer = (value = '') => {
    const [state, dispatch] = useReducer(reducer, {
        isPending: false,
        editMode: false,
        isFailure: false,
        isSuccess: false,
        value,
    });

    const dispatchAction = {
        setValue: (payload: string) =>
            dispatch({
                type: SET_VALUE,
                payload,
            }),
        setEditMode: (payload: boolean) =>
            dispatch({
                type: SET_EDIT_MODE,
                payload,
            }),
        startCommit: () =>
            dispatch({
                type: SET_COMMIT_STATE,
            }),
        commitSuccess: (payload: string) =>
            dispatch({
                type: COMMIT_SUCCESS,
                payload,
            }),
        commitFailure: (payload: string) =>
            dispatch({
                type: COMMIT_FAILURE,
                payload,
            }),
        resetResult: () => {
            dispatch({
                type: RESET_RESULT,
            });
        },
    };

    return { state, dispatchAction };
};
