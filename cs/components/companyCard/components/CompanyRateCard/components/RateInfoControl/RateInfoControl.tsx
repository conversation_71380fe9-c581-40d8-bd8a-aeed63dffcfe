import React from 'react';

import { BlackTooltip, FlexBox } from '@vchasno/ui-kit';

import css from './RateInfoControl.css';

interface RateControlProps {
    onClick: () => void;
    hint?: string;
}

const RateInfoControl: React.FC<RateControlProps> = ({ onClick, hint }) => {
    return (
        <BlackTooltip title={hint}>
            <FlexBox
                justify="center"
                align="center"
                className={css.control}
                onClick={onClick}
            >
                <span className={css.icon}>+</span>
            </FlexBox>
        </BlackTooltip>
    );
};

export default RateInfoControl;
