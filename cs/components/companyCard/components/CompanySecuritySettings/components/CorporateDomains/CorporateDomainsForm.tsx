import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';

import {
    Alert,
    Button,
    FlexBox,
    TextInput,
    snackbarToast,
} from '@vchasno/ui-kit';

import appActionCreators from 'components/app/appActionCreators';
import { getCurrentCompanyEmailDomains } from 'selectors/app.selectors';
import { updateCompanySecurityPermissions } from 'services/ts/user';
import { mapStatetoHasPermission } from 'store/utils';
import { jt, t } from 'ttag';

import { DomainFields } from './types';

import { corporateDomainsFormResolver } from './validation';

import css from './CorporateDomains.css';

const CorporateDomainsForm = () => {
    const dispatch = useDispatch();
    const currentCompanyEmailDomains = useSelector(
        getCurrentCompanyEmailDomains,
    );
    const { hasPermission } = useSelector(mapStatetoHasPermission);
    const {
        control,
        handleSubmit,
        formState,
        setError,
    } = useForm<DomainFields>({
        resolver: corporateDomainsFormResolver,
        defaultValues: {
            domains: '',
        },
    });

    const onSubmit = async ({ domains }: DomainFields) => {
        const newDomainsArray = domains
            .split(',')
            .map((domain) => domain.trim());

        const isDomainAlreadyExist =
            currentCompanyEmailDomains &&
            newDomainsArray.some((newDomain) =>
                currentCompanyEmailDomains.includes(newDomain),
            );

        if (isDomainAlreadyExist) {
            setError('domains', {
                type: 'custom',
                message: t`Домен вже було додано`,
            });
            return;
        }

        const updatedEmailDomains = (currentCompanyEmailDomains || []).concat(
            newDomainsArray,
        );

        try {
            await updateCompanySecurityPermissions({
                email_domains: updatedEmailDomains,
            });

            dispatch(
                appActionCreators.updateCompanyState({
                    payload: {
                        emailDomains: updatedEmailDomains,
                    },
                }),
            );
        } catch (error) {
            const errorMessage =
                error.code === 'invalid_request'
                    ? t`Неправильний формат домену`
                    : error.message;
            snackbarToast.error(errorMessage);
        }
    };

    if (
        Array.isArray(currentCompanyEmailDomains) &&
        currentCompanyEmailDomains?.length >= 5
    ) {
        const boldAlertString = (
            <b>{t`Ви досягли ліміту збережених доменів`}.</b>
        );

        return (
            <Alert type="info" icon="info">
                {jt`${boldAlertString} Видаліть збережені, щоб додати нові`}
            </Alert>
        );
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <FlexBox align="start" className={css.formContainer}>
                <Controller
                    name="domains"
                    control={control}
                    render={({ field, fieldState }) => (
                        <TextInput
                            disabled={!hasPermission('canEditSecurity')}
                            className={css.input}
                            value={field.value}
                            onChange={field.onChange}
                            placeholder={t`Вкажіть корпоративний домен`}
                            error={fieldState.error?.message}
                        />
                    )}
                />
                <Button
                    type="submit"
                    size="lg"
                    className={css.button}
                    disabled={
                        !formState.isDirty || !hasPermission('canEditSecurity')
                    }
                >
                    {t`Зберегти`}
                </Button>
            </FlexBox>
        </form>
    );
};

export default CorporateDomainsForm;
