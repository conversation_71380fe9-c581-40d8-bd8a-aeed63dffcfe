import React from 'react';

import { FlexBox, Paragraph, Text } from '@vchasno/ui-kit';

import { t } from 'ttag';

import css from './AllowedIps.css';

const AllowedIpsHint = () => (
    <FlexBox direction="column" gap={16} className={css.hint}>
        <Paragraph className={css.hintMainText}>
            {t`Ви можете дозволити своїм співробітникам заходити у “Вчасно” тільки з певних IP адрес. Для цього вкажіть IP адреси, розділивши їх комами`}
        </Paragraph>
        <ul className={css.hintList}>
            <li>
                <Text type="secondary">{t`IP адреса може бути постійною або змінною.`}</Text>
            </li>
            <li>
                <Text type="secondary">{t`Якщо IP адреса постійна - просто вкажіть її у полі`}</Text>
            </li>
            <li>
                <Text type="secondary">{t`Якщо IP адреса змінна – вкажіть ту частину адреси, що буде повторюватись у форматі XXX*`}</Text>
            </li>
        </ul>
        <Paragraph>
            <Text type="secondary">
                {t`Наприклад, якщо ви вкажете 111* - ви дозволите доступ тільки з тих IP адрес, що починаються на 111`}
            </Text>
        </Paragraph>
    </FlexBox>
);

export default AllowedIpsHint;
