import React, { FC } from 'react';

import { t } from 'ttag';

import { ParticipantInfo } from './types';

import Alert from '../../../../../ui/Alert/Alert';
import Button from '../../../../../ui/button/button';
import Icon from '../../../../../ui/icon/icon';
import Popup from '../../../../../ui/popup/popup';

import FlexBox from '../../../../../FlexBox/FlexBox';
import SignReviewWarningList from './DeletePopupWarningList/SignReviewWarningList';
import TemplateWarningList from './DeletePopupWarningList/TemplateWarningList';

import BasketButtonDelete from '../../components/EmployeeGroupCard/images/delete.svg';
import DeleteIcon from './images/group_delete.svg';

import css from './EmployeeGroupDeletePopup.css';

interface Props {
    isOpen: boolean;
    groupName: string;
    participantInfo: ParticipantInfo;
    onClose: VoidFunction;
    onDeleteEmployeeGroup: VoidFunction;
}

const EmployeeGroupDeletePopup: FC<Props> = ({
    isOpen,
    groupName,
    participantInfo,
    onClose,
    onDeleteEmployeeGroup,
}) => {
    const {
        documentSigners,
        documentReviewers,
        documentAutomationTemplates,
    } = participantInfo;

    const signReviewList = [documentSigners, documentReviewers].flat();
    return (
        <Popup active={isOpen} className={css.container} onClose={onClose}>
            <FlexBox direction="column">
                <Icon className={css.mainImage} glyph={DeleteIcon} />
                <h2 className={css.header}>
                    {`Ви впевнені, що хочете видалити команду «${groupName}»?`}
                </h2>
                {!!signReviewList.length && (
                    <>
                        <div className={css.warningTitle}>
                            {t`Ця команда задіяна в погодженні/підписанні:`}
                        </div>
                        <SignReviewWarningList
                            signReviewList={signReviewList}
                        />
                    </>
                )}
                {!!documentAutomationTemplates.length && (
                    <>
                        <div className={css.warningTitle}>
                            {t`Ця команда задіяна в сценаріях:`}
                        </div>
                        <TemplateWarningList
                            documentTemplates={documentAutomationTemplates}
                        />
                    </>
                )}
                {(!!signReviewList.length ||
                    !!documentAutomationTemplates.length) && (
                    <Alert theme="warning" hideIcon>
                        <p>
                            {t`Після видалення команди необхідно прибрати її з`}
                            {!!signReviewList.length && (
                                <span> {t`погодження/підписання`} </span>
                            )}
                            {!!signReviewList.length &&
                                !!documentAutomationTemplates.length && (
                                    <span> {t`та`} </span>
                                )}
                            {!!documentAutomationTemplates.length && (
                                <span> {t`умов сценаріїв`} </span>
                            )}
                        </p>
                    </Alert>
                )}
                <FlexBox
                    className={css.buttonContainer}
                    align="center"
                    justify="space-between"
                >
                    <Button
                        className={css.button}
                        typeContour
                        theme="blue"
                        onClick={onClose}
                    >
                        {t`Не видаляти`}
                    </Button>
                    <Button
                        className={css.button}
                        typeContour
                        theme="red"
                        onClick={onDeleteEmployeeGroup}
                    >
                        <Icon
                            className={css.iconBasket}
                            glyph={BasketButtonDelete}
                        />
                        <span>{t`Видалити`}</span>
                    </Button>
                </FlexBox>
            </FlexBox>
        </Popup>
    );
};

export default EmployeeGroupDeletePopup;
