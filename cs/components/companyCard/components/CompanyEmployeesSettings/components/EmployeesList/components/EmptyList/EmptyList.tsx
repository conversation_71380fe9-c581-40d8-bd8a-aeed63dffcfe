import React, { FC } from 'react';
import { useDispatch } from 'react-redux';

import { t } from 'ttag';

import actions from '../../../../../../../companyEmployees/companyEmployeesActionCreators';

import Icon from '../../../../../../../ui/icon/icon';
import PseudoLink from '../../../../../../../ui/pseudolink/pseudolink';

import FlexBox from '../../../../../../../FlexBox/FlexBox';

import NotFoundSvg from './images/not-found.svg';

import css from './EmptyList.css';

interface EmptyListProps {
    companyId?: string;
}

const EmptyList: FC<EmptyListProps> = ({ companyId }) => {
    const dispatch = useDispatch();

    const resetFiltersHandler = () => {
        dispatch(actions.resetFilters());
        dispatch(actions.onGetEmployees(companyId));
    };

    return (
        <FlexBox
            className={css.container}
            direction="column"
            align="center"
            justify="center"
            gap={10}
        >
            <Icon className={css.icon} glyph={NotFoundSvg} />
            <span
                className={css.text}
            >{t`За цим запитом нічого не знайдено`}</span>
            <PseudoLink
                className={css.resetFiltersBtn}
                type="underlined"
                onClick={resetFiltersHandler}
            >{t`Скинути налаштування пошуку`}</PseudoLink>
        </FlexBox>
    );
};

export default EmptyList;
