import React, { FC } from 'react';

import cn from 'classnames';

import { Nullable } from '../../../../../../../../types/general';

import { getNewOrderType } from '../../../../../../../../lib/helpers';

import Icon from '../../../../../../../ui/icon/icon';

import FlexBox from '../../../../../../../FlexBox/FlexBox';

import ArrowSvg from './images/arrow.svg';
import CloseSvg from './images/close.svg';

import css from './SortedTableHeaderItem.css';

export type OrderType = 'asc' | 'desc';

interface SortedTableHeaderItemProps {
    title: string;
    orderType: Nullable<OrderType>;
    onSort: (newOrderType: OrderType) => void;
    onReset: () => void;
}

const SortedTableHeaderItem: FC<SortedTableHeaderItemProps> = ({
    title,
    orderType,
    onSort,
    onReset,
}) => {
    const onSortHandler = () => {
        const newOrderType = getNewOrderType(orderType);
        onSort(newOrderType);
    };

    return (
        <FlexBox gap={2} align="center">
            <span title={title} className={css.title}>
                {title}
            </span>
            <div
                className={cn(css.iconWrapper, {
                    [css.activeIcon]: !!orderType,
                    [css.reverseIcon]: orderType === 'asc',
                })}
                onClick={onSortHandler}
            >
                <Icon glyph={ArrowSvg} />
            </div>
            {orderType && (
                <div className={css.iconWrapper} onClick={onReset}>
                    <Icon glyph={CloseSvg} />
                </div>
            )}
        </FlexBox>
    );
};

export default SortedTableHeaderItem;
