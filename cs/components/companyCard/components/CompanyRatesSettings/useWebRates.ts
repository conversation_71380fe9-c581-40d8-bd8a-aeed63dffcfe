import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { getFormattedWebRatesForSettings } from 'selectors/companyCard.selectors';
import { ALL_MAXIMAL_RATES } from 'services/billing';
import { getCurrentCompanyUsedDocumentCount } from 'services/user';

export const useWebRates = () => {
    const formattedWebRatesForSettings = useSelector(
        getFormattedWebRatesForSettings,
    );
    const [webRatesForView, setWebRatesForView] = useState<
        ReturnType<typeof composeFormattedWebRatesWithUsedDocumentCount>
    >([]);

    function composeFormattedWebRatesWithUsedDocumentCount(
        rateList: typeof formattedWebRatesForSettings,
        count: number,
    ) {
        return rateList.map((webRate) => {
            const rateType = webRate.type;
            const documentsCountForViewLimit =
                webRate.documentsCountForViewLimit;

            const isMaxDocumentsCountForView = documentsCountForViewLimit
                ? (count * 100) / documentsCountForViewLimit >= 90
                : false;
            const infinity = '∞';

            return {
                ...webRate,
                isMaxDocumentsCountForView:
                    isMaxDocumentsCountForView &&
                    !ALL_MAXIMAL_RATES.includes(rateType),
                lockDocumentsCountForView:
                    documentsCountForViewLimit &&
                    !ALL_MAXIMAL_RATES.includes(rateType)
                        ? (documentsCountForViewLimit - (count || 0)) * -1
                        : null,
                documentsCountForView:
                    documentsCountForViewLimit &&
                    !ALL_MAXIMAL_RATES.includes(rateType)
                        ? `${count}/${documentsCountForViewLimit}`
                        : infinity,
            } as const;
        });
    }

    useEffect(() => {
        getCurrentCompanyUsedDocumentCount().then((count) => {
            setWebRatesForView(
                composeFormattedWebRatesWithUsedDocumentCount(
                    formattedWebRatesForSettings,
                    count ?? 0,
                ),
            );
        });
    }, [formattedWebRatesForSettings]);

    return {
        webRatesForView,
    } as const;
};
