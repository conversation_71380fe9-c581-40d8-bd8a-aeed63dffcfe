.root {
    width: 700px;
}

.title {
    margin-bottom: 15px;
    font-weight: bold;
}

.checkbox {
    display: flex;
    margin-top: 25px;
    margin-bottom: 25px;
}

.documentSetting {
    padding: 0 0 225px;
    border-bottom: 1px solid var(--default-border);
    margin-bottom: 20px;
}

.submit {
    position: fixed;
    z-index: 1000;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px 0;
    border-top: 1px solid var(--pigeon-color);
    background-color: var(--white-bg);
}

.uploadSetting {
    display: table;
    padding: 0;
    margin: auto;
}

.radiobuttons {
    position: relative;
    z-index: 1;
    margin-bottom: 15px;
}

.hint {
    width: 200px;
}

.message {
    margin-top: 5px;
}

.buttonBlock {
    margin-top: 10px;
}

.fields {
    display: flex;
}

.field + .field {
    margin-left: 30px;
}

.label {
    display: block;
    margin-bottom: 5px;
}

.group {
    padding-top: 20px;
    border-top: 1px solid var(--default-border);
    margin-top: 20px;
}

.info {
    margin-top: 20px;
    color: var(--dark-pigeon-color);
}

.date {
    max-width: 240px;
}

.fieldLabel {
    display: block;
    margin-top: 20px;
    margin-bottom: 5px;
    font-weight: bold;
}
