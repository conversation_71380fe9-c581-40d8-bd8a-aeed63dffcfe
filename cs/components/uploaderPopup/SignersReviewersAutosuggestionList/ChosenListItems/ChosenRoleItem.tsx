import React from 'react';

import NoActiveRoleBadge from 'components/NoActiveRoleBadge';
import { formatFullName } from 'lib/helpers';
import { formatUserIdentifier } from 'lib/ts/helpers';

import { SignerReviewerRole } from '../../../../types/user';

import TextShorten from '../../../ui/textShorten/textShorten';

import css from '../SignersReviewersAutosuggestionList.css';

interface Props {
    role: Pick<SignerReviewerRole, 'id' | 'user'>;
    fetchIsActive?: boolean;
}

const ChosenRoleItem: React.FC<Props> = ({ role, fetchIsActive }) => {
    return (
        <div className={css.table}>
            <div className={css.cell}>
                <TextShorten className={css.fullName}>
                    {fetchIsActive && (
                        <NoActiveRoleBadge roleId={role.id}>
                            {' '}
                        </NoActiveRoleBadge>
                    )}
                    {formatFullName(role.user)}
                </TextShorten>
            </div>
            <div className={css.cell}>
                <TextShorten>{formatUserIdentifier(role.user)}</TextShorten>
            </div>
        </div>
    );
};

export default ChosenRoleItem;
