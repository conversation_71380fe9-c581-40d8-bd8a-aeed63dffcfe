import React, { useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';

import { Button, FlexBox, Paragraph, Title } from '@vchasno/ui-kit';
import { Alert } from '@vchasno/ui-kit';

import cn from 'classnames';
import OutlinedInput from 'components/ui/input/OutlinedInput/OutlinedInput';
import Popup from 'components/ui/popup/popup';
import PseudoLink from 'components/ui/pseudolink/pseudolink';
import { EMAIL_MAX_LENGTH } from 'lib/constants';
import { getCurrentUserEmail } from 'selectors/app.selectors';
import { getIsEmailChangeConfirmPopupActive } from 'selectors/profileForm.selectors';
import { c, t } from 'ttag';

import { useProfileFormEmailChangeConfirm } from './useProfileFormEmailChangeConfirm';

import emailPng from './images/mail.png';

import css from './ProfileFormEmailChangeConfirmPopup.css';

interface ProfileFormEmailChangeConfirmPopupProps {
    hasOldEmail: boolean;
    email?: string;
    onCloseEdit: () => void;
}

const ProfileFormEmailChangeConfirmPopup: React.FC<ProfileFormEmailChangeConfirmPopupProps> = ({
    hasOldEmail,
    email = '',
    onCloseEdit,
}) => {
    const isOpen = useSelector(getIsEmailChangeConfirmPopupActive);
    const [step, setStep] = useState(0);

    const currentEmail = useSelector(getCurrentUserEmail);

    const {
        controlEmailPassword,
        formStateEmailPassword,
        onStartEmailChange,
        onClose,
        onResendEmail,
        setValueEmailPassword,
        onSubmitPassword,
        handleSubmitEmailPassword,
    } = useProfileFormEmailChangeConfirm(email, setStep);

    // set to email field on step 2 value from props
    useEffect(() => {
        setValueEmailPassword('email', email);
    }, [email]);

    const handleClose = () => {
        onClose();
        onCloseEdit();
    };

    const currentEmailBold = <b>{`${currentEmail}`}</b>;

    return (
        <Popup active={isOpen} onClose={onClose} className={css.root}>
            {step === 0 && (
                <FlexBox direction="column" gap={15}>
                    <Title
                        level={2}
                        className={css.title}
                    >{t`Зміна електронної адреси`}</Title>
                    <Alert type="warning">
                        {t`Процес оновлення email може зайняти деякий час.`}
                    </Alert>
                    <div>
                        <Paragraph>
                            {t`Будь ласка, зверніть увагу, що ваш email буде змінений у
                    сервісах:`}
                        </Paragraph>
                        <ul className={css.list}>
                            <li>{t`«Вчасно.КЕП»`}</li>
                            <li>{t`«Вчасно.Каса»`}</li>
                            <li>{t`«Вчасно.EDI»`}</li>
                            <li>{t`«Вчасно.ТТН»`}</li>
                        </ul>
                    </div>
                    {hasOldEmail && (
                        <div>
                            <Paragraph>
                                {t`Ваш старий email (до оновлення) буде відображатися на
                    завершених документах та в історії дій з документами.`}
                            </Paragraph>
                            <Paragraph>
                                {t`Повідомте контрагентів про зміну пошти, щоб ви могли
                    отримувати документи на новий email.`}
                            </Paragraph>
                        </div>
                    )}
                    <Button
                        className={css.marginTopLarge}
                        size="lg"
                        onClick={() => onStartEmailChange({ email })}
                    >{t`Продовжити`}</Button>
                </FlexBox>
            )}
            {step === 1 && (
                <form
                    className={css.form}
                    onSubmit={handleSubmitEmailPassword(onSubmitPassword)}
                >
                    <FlexBox direction="column" gap={25}>
                        <img
                            className={css.icon}
                            src={emailPng}
                            alt="Email picture"
                        />

                        <FlexBox direction="column" gap={15}>
                            <Title
                                level={2}
                                className={cn(css.title, css.textCentered)}
                            >
                                {t`Зміна електронної пошти`}
                            </Title>
                            <p className={css.subTitle}>
                                {t`Для зміни електронної пошти підтвердіть свій пароль`}
                            </p>
                        </FlexBox>
                        <FlexBox
                            className={css.formWrapper}
                            direction="column"
                            gap={15}
                        >
                            <Controller
                                control={controlEmailPassword}
                                name="email"
                                render={({ field, fieldState }) => (
                                    <OutlinedInput
                                        label={t`Новий email`}
                                        type="text"
                                        name="email"
                                        value={field.value}
                                        onChange={(event) => {
                                            field.onChange(
                                                event.target.value.trim(),
                                            );
                                        }}
                                        maxLength={EMAIL_MAX_LENGTH + 1}
                                        error={fieldState.error?.message}
                                    />
                                )}
                            />
                            <Controller
                                control={controlEmailPassword}
                                name="password"
                                render={({ field, fieldState }) => (
                                    <OutlinedInput
                                        label={t`Поточний пароль`}
                                        type="password"
                                        required
                                        autoFocus
                                        value={field.value}
                                        onChange={(event) => {
                                            field.onChange(
                                                event.target.value.trim(),
                                            );
                                        }}
                                        error={fieldState.error?.message}
                                    />
                                )}
                            />
                        </FlexBox>
                        <FlexBox
                            className={css.buttonWrapper}
                            justify="space-between"
                            align="center"
                            gap={20}
                        >
                            <Button
                                theme="secondary"
                                size="lg"
                                wide
                                onClick={onClose}
                            >{t`Скасувати`}</Button>
                            <Button
                                type="submit"
                                size="lg"
                                wide
                                disabled={!formStateEmailPassword.isValid}
                                loading={formStateEmailPassword.isSubmitting}
                            >{t`Підтвердити`}</Button>
                        </FlexBox>
                    </FlexBox>
                </form>
            )}
            {step === 2 && (
                <FlexBox direction="column" gap={50}>
                    <FlexBox direction="column" gap={15}>
                        <Title
                            level={2}
                            className={css.confirmEmailTitle}
                        >{t`Підтвердіть ваш email`}</Title>
                        <p className={css.subTitle}>
                            {c('send email')
                                .jt`На ваш email ${currentEmailBold} було надіслано посилання для підтвердження вашої електронної адреси. Перейдіть по ньому, щоб підтвердити новий email.`}
                        </p>
                    </FlexBox>
                    <FlexBox direction="column" gap={15}>
                        <FlexBox gap={5}>
                            {t`Не отримали листа?`}
                            <PseudoLink
                                type="underlined"
                                onClick={onResendEmail}
                            >{t`Надіслати повторно`}</PseudoLink>
                        </FlexBox>

                        <FlexBox direction="column" gap={0}>
                            {t`Не маєте доступу до цього email?`}
                            <PseudoLink
                                type="underlined"
                                onClick={onClose}
                            >{t`Вкажіть нову електронну адресу`}</PseudoLink>
                        </FlexBox>
                    </FlexBox>
                    <Button
                        size="lg"
                        wide
                        onClick={handleClose}
                    >{t`OK`}</Button>
                </FlexBox>
            )}
        </Popup>
    );
};

export default ProfileFormEmailChangeConfirmPopup;
