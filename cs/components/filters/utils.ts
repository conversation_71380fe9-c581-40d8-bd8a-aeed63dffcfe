import { FORM_ERROR } from 'final-form';
import { LocationDescriptorObject } from 'history';
import { QUERY_DATE_FORMAT } from 'lib/constants';
import { addDate, formatDate } from 'lib/date';
import { formatFullName } from 'lib/helpers';
import { getLocationQuery, stringifyLocationQuery } from 'lib/url';
import { Moment } from 'moment';
import {
    DocumentFolder,
    DocumentStatus,
    DocumentsFilteredByDeliveredState,
} from 'services/enums';
import { t } from 'ttag';

import { Nullable } from '../../types/general';
import { Comment } from '../../types/user';

import { Fields } from '../exportDataPopup/exportDataPopup';
import { REVIEW_FOLDERS, SIGN_FOLDERS_QUERY_MAP } from './constants';

const STATUS_ID_QUERY_KEY = 'status_id';
const CONDITIONS_2_QUERY_KEY = 'conditions2';

export function filterByDate({
    dateFrom,
    dateTo,
    push,
    pathname = '/app/documents',
}: {
    dateFrom: Nullable<Moment>;
    dateTo: Nullable<Moment>;
    push: (location: LocationDescriptorObject) => void;
    pathname?: string;
}) {
    // TODO: Browser API location usage is discouraged, consider obtainig location of react-router
    const query = getLocationQuery(document.location);

    if (dateFrom) {
        query.date_from = formatDate(dateFrom, QUERY_DATE_FORMAT) as string;
    } else {
        delete query.date_from;
    }

    if (dateTo) {
        query.date_to = formatDate(dateTo, QUERY_DATE_FORMAT) as string;
    } else {
        delete query.date_to;
    }

    // показувати результати з початку
    delete query.page;

    push({ pathname, search: stringifyLocationQuery(query) });
}

interface Amounts {
    amountGte: string;
    amountLte: string;
}

export function filterByDocsAmount(
    { amountGte, amountLte }: Amounts,
    push: (location: LocationDescriptorObject) => void,
    pathname = '/app/documents',
) {
    const query = getLocationQuery(document.location);
    if (amountGte) {
        query.amount_gte = amountGte;
        delete query.page;
    } else {
        delete query.amount_gte;
    }
    if (amountLte) {
        query.amount_lte = amountLte;
        delete query.page;
    } else {
        delete query.amount_lte;
    }
    push({ pathname, search: stringifyLocationQuery(query) });
}

export function filterNotDelivered(
    filteredNotDelivered = false,
    push: (location: LocationDescriptorObject) => void,
    pathname = '/app/documents',
) {
    let query = { ...getLocationQuery(document.location) };
    if (filteredNotDelivered) {
        delete query.has_date_delivered;
    } else {
        query = {
            ...getLocationQuery(document.location),
            // @ts-expect-error TS2322 [FIXME] Comment is autogenerated
            has_date_delivered:
                DocumentsFilteredByDeliveredState.IS_NOT_DELIVERED,
        };
        if (query.page) delete query.page;
    }

    push({ pathname, search: stringifyLocationQuery(query) });
}

export function filterWithoutTags(
    filteredWithoutTags = false,
    push: (location: LocationDescriptorObject) => void,
    pathname = '/app/documents',
) {
    // TODO: Browser API location usage is discouraged, consider obtainig location of react-router
    let query = getLocationQuery(document.location);
    if (filteredWithoutTags) {
        delete query.without_tags;
    } else {
        query = {
            ...getLocationQuery(document.location),
            without_tags: 'true',
        };
        if (query.page) delete query.page;
        if (query.tag) delete query.tag;
        if (query.tags) delete query.tags; // ???
    }

    push({ pathname, search: stringifyLocationQuery(query) });
}

function doesFolderMatchQuery(
    folderQuery: typeof SIGN_FOLDERS_QUERY_MAP[keyof typeof SIGN_FOLDERS_QUERY_MAP],
    query: Record<string, unknown>,
): boolean {
    if (folderQuery.status_id !== undefined && folderQuery.status_id !== null) {
        if (
            query[STATUS_ID_QUERY_KEY] === undefined ||
            query[STATUS_ID_QUERY_KEY] === null ||
            String(query[STATUS_ID_QUERY_KEY]) !== String(folderQuery.status_id)
        ) {
            return false;
        }
    }

    if (
        Array.isArray(folderQuery.conditions2) &&
        folderQuery.conditions2.length > 0
    ) {
        const queryConditions2 = Array.isArray(query[CONDITIONS_2_QUERY_KEY])
            ? (query[CONDITIONS_2_QUERY_KEY] as unknown[]).map(String)
            : query[CONDITIONS_2_QUERY_KEY] !== undefined &&
              query[CONDITIONS_2_QUERY_KEY] !== null
            ? [String(query[CONDITIONS_2_QUERY_KEY])]
            : [];

        const allConditionsMatch = folderQuery.conditions2.every(
            (requiredCondition) =>
                queryConditions2.includes(String(requiredCondition)),
        );

        if (!allConditionsMatch) {
            return false;
        }
    }

    return true;
}

// !!! Тут змінили return type з string на string[] - потрібно або розділити цю функцію на дві, або дивитися по всьому додатку і перевіряти у всіх місцях чи метчиться тепер масив, а не рядок
export function getCurrentSignFolders(query: Record<string, unknown>) {
    const matchingFolders: string[] = [];

    const queryHasSpecificSignFolderFilters =
        query[STATUS_ID_QUERY_KEY] !== undefined ||
        (query[CONDITIONS_2_QUERY_KEY] !== undefined &&
            query[CONDITIONS_2_QUERY_KEY] !== null &&
            (Array.isArray(query[CONDITIONS_2_QUERY_KEY])
                ? (query[CONDITIONS_2_QUERY_KEY] as unknown[]).length > 0
                : String(query[CONDITIONS_2_QUERY_KEY]) !== ''));

    if (!queryHasSpecificSignFolderFilters) {
        if (Object.keys(SIGN_FOLDERS_QUERY_MAP).includes('all')) {
            matchingFolders.push('all');
        }
    } else {
        for (const folderName of (Object.keys(SIGN_FOLDERS_QUERY_MAP) as Array<
            keyof typeof SIGN_FOLDERS_QUERY_MAP
        >).filter((key) => key !== 'all')) {
            const folderQuery = SIGN_FOLDERS_QUERY_MAP[folderName];

            if (doesFolderMatchQuery(folderQuery, query)) {
                matchingFolders.push(folderName);
            }
        }
    }
    return matchingFolders;
}

export function filterBySignFolder(
    push: (location: LocationDescriptorObject) => void,
    location: LocationDescriptorObject,
    folder:
        | (keyof typeof SIGN_FOLDERS_QUERY_MAP)[]
        | keyof typeof SIGN_FOLDERS_QUERY_MAP,
    pathname = '/app/documents',
) {
    const currentQuery = getLocationQuery(location);

    // Видаляємо існуючі параметри status_id та conditions2, оскільки будемо формувати їх заново
    delete currentQuery[STATUS_ID_QUERY_KEY];
    delete currentQuery[CONDITIONS_2_QUERY_KEY];

    const foundStatusIds: DocumentStatus[] = [];
    const foundConditions2: string[] = [];

    const folderKeysToProcess = Array.isArray(folder)
        ? (folder.filter(
              (key) => key !== undefined && key !== null,
          ) as (keyof typeof SIGN_FOLDERS_QUERY_MAP)[])
        : folder !== undefined && folder !== null
        ? [folder]
        : [];

    folderKeysToProcess.forEach((key) => {
        const folderConfig = SIGN_FOLDERS_QUERY_MAP[key];
        if (folderConfig) {
            // Перевіряємо, чи ключ існує в мапі та чи містить status_id
            if (folderConfig.status_id !== undefined) {
                foundStatusIds.push(folderConfig.status_id);
            }
            // Перевіряємо, чи ключ існує в мапі та чи містить conditions2 (і він є масивом)
            if (Array.isArray(folderConfig.conditions2)) {
                foundConditions2.push(...folderConfig.conditions2); // Об'єднуємо масиви conditions2
            }
        } else {
            console.warn(`Сталася помилка, статус "${key}" не знайдено`);
        }
    });

    const uniqueConditions2 = [...new Set(foundConditions2)];

    delete currentQuery.page; // Видаляємо параметр сторінки при зміні фільтрів

    const updatedQuery: any = {
        ...currentQuery,
    };

    if (foundStatusIds.length > 0) {
        updatedQuery[STATUS_ID_QUERY_KEY] = foundStatusIds;
    }

    if (uniqueConditions2.length > 0) {
        updatedQuery[CONDITIONS_2_QUERY_KEY] = uniqueConditions2;
    }

    push({
        pathname,
        search: stringifyLocationQuery(updatedQuery),
    });
}

export function filterByDocumentCategory(
    push: (location: LocationDescriptorObject) => void,
    location: LocationDescriptorObject,
    categories?: string | string[],
    pathname = '/app/documents',
) {
    const query = getLocationQuery(location);

    if (categories) {
        query.categories = categories as string;
    }

    delete query.page;

    push({
        pathname,
        search: stringifyLocationQuery(query),
    });
}

export function filterByReviewFolder(
    push: (location: LocationDescriptorObject) => void,
    folder?: string,
    pathname = '/app/documents',
) {
    const searchParams = new URLSearchParams(location.search);

    if (folder && folder !== REVIEW_FOLDERS[0].value) {
        searchParams.set('folder_id', DocumentFolder.NOT_INTERNAL);
        searchParams.append('folder_id', DocumentFolder.INTERNAL);
        searchParams.set('review_folder', folder);

        if (searchParams.has('page')) {
            searchParams.delete('page');
        }
    } else {
        const isWaiteMySignFilterActive =
            searchParams.get('wait_my_sign') === 'true';

        searchParams.delete('review_folder');

        // паралельно може бути включений Очікують на мій підпис, який також має працювати з подвійним набором папок (внутрішні та зовнішні)
        // folder_id=6008&folder_id=6007
        if (!isWaiteMySignFilterActive) {
            searchParams.set('folder_id', DocumentFolder.NOT_INTERNAL);
        }
    }

    push({ pathname, search: searchParams.toString() });
}

export function formatCommentsForExport(comments: Array<Comment>) {
    if (comments.length === 0) return null;
    return comments
        .slice(Math.max(comments.length - 5, 0))
        .map(
            (comment) =>
                `${formatFullName(comment.role.user)}: ${comment.text}`,
        )
        .join(', ');
}

export const validateDocumentsExport = (fields: Fields) => {
    if (!fields.fromDate || !fields.toDate || !fields.format) {
        return {
            fromDate: t`Будь ласка, заповніть всі поля`,
        };
    } else if (new Date(fields.fromDate) > new Date(fields.toDate)) {
        return {
            [FORM_ERROR]: t`Початок періоду не може бути пізнишим ніж кінець`,
        };
        // @ts-expect-error TS2531 [FIXME] Comment is autogenerated
    } else if (addDate(fields.fromDate, 1, 'year') < new Date(fields.toDate)) {
        return {
            [FORM_ERROR]: t`Максимальний період вивантаження складає 1 рік, вкажіть менший період.`,
        };
    }

    return {};
};
