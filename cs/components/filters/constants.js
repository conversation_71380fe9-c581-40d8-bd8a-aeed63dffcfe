import { t } from 'ttag';

import { DocumentFolder } from '../../services/enums';
import { ReviewStatusQueryParam } from './types';

export const REVIEW_FOLDERS = [
    {
        value: 'all',
        label: t`Всі`,
    },
    {
        value: ReviewStatusQueryParam.pending,
        label: t`На погодженні`,
    },
    {
        value: ReviewStatusQueryParam.approved,
        label: t`Погоджені всіма`,
    },
    {
        value: ReviewStatusQueryParam.rejected,
        label: t`Відхилені`,
    },
    {
        value: ReviewStatusQueryParam.fromMe,
        label: t`Ініційовані мною`,
    },
    {
        value: ReviewStatusQueryParam.withMyReview,
        label: t`Погоджені мною`,
    },
    {
        value: ReviewStatusQueryParam.waitMyReview,
        label: t`Очікують на моє погодження`,
    },
];

export const SIGN_FOLDERS = [
    {
        value: 'all',
        label: t`Всі`,
    },
    {
        value: 'downloaded',
        label: t`Потребують вказання контрагента`,
    },
    {
        value: 'ready',
        label: t`Очікують підпису моєї компанії`,
    },
    {
        value: 'ready_to_send',
        label: t`Готові для надсилання`,
    },
    {
        value: 'waited',
        label: t`Очікують підпису контрагента`,
    },
    {
        value: 'finished',
        label: t`Підписані всіма`,
    },
    {
        value: 'canceled',
        label: t`Відхилені`,
    },
];

export const SIGN_FOLDERS_ARCHIVE = [
    {
        value: 'all',
        label: t`Всі`,
    },
    {
        value: 'downloaded',
        label: t`Потребують вказання контрагента`,
    },
    {
        value: 'ready',
        label: t`Очікують підпису моєї компанії`,
    },
    {
        value: 'ready_to_send',
        label: t`Готові для надсилання`,
    },
    {
        value: 'waited',
        label: t`Очікують підпису контрагента`,
    },
    {
        value: 'finished',
        label: t`Підписані всіма`,
    },
    {
        value: 'canceled',
        label: t`Відхилені`,
    },
    {
        value: 'revoked',
        label: t`Анульовані`,
    },
    {
        value: 'histored',
        label: t`Імпортовані`,
    },
];

export const SIGN_FOLDERS_QUERY_MAP = {
    all: {
        status_id: undefined,
        conditions2: undefined,
    },
    downloaded: {
        status_id: undefined,
        conditions2: ['60007000'], // all uploaded documents, 6000 - special status allowed to be with sign status filter
    },
    ready: {
        status_id: undefined,
        conditions2: [
            '60017002', // incoming SENT
            '60017003120', // incoming SIGNED + is3p + hasNotAllSignatures
            '60017004', // incoming SIGNED_AND_SENT
            '60017007220', // incoming APPROVED + hasNotAllSignatures
            '*********', // outgoing READY + isNot3p
            '60027003020', // outgoing SIGNED + isNot3p + hasNotAllSignatures
            '60027007120', // outgoing APPROVED + is3p + hasNotAllSignatures
            '60027007010', // outgoing APPROVED + isNot3p + isOneSign + hasNotAllSignatures
            '60077001', // READY + INTERNAL
            '60077003', // SIGNED + INTERNAL
            '600070102221', // FLOW_PROCESS + current company's flow order
            '600070002221', // FLOW_PROCESS (Just uploaded and should be signed by owner) + recipient's flow order
        ],
    },
    ready_to_send: {
        status_id: undefined,
        conditions2: [
            '60017007221', // incoming APPROVED + hasAllSignatures
            '60017003121', // incoming SIGNED + is3p + hasAllSignatures
            '*********', // outgoing READY + is3p
            '60027003021', // outgoing SIGNED + isNot3p + hasAllSignatures
            '60027007101', // outgoing APPROVED + is3p + isMultiSign + hasAllSignatures
            '60027007011', // outgoing APPROVED + isNot3p + isOneSign + hasAllSignatures
        ],
    },
    waited: {
        status_id: undefined,
        conditions2: [
            '60027002', // outgoing SENT
            '*********', // outgoing SIGNED + is3p
            '60027004', // outgoing SIGNED_AND_SENT
            '60027007020', // outgoing APPROVED + isNot3p + isMultiSign
            '6002700701', // outgoing APPROVED + is3p + isOneSign
            '600070102220', // FLOW_PROCESS + recipient's flow order
        ],
    },
    finished: {
        status_id: undefined,
        conditions2: ['60007008'],
    },
    canceled: {
        status_id: undefined,
        conditions2: ['60007006'],
    },
    revoked: {
        status_id: undefined,
        conditions2: ['60007011'],
    },
    histored: {
        status_id: undefined,
        conditions2: ['60007020'],
    },
};

// folders that can be used both with sign status filter
export const SIGN_FILTER_COMPATIBLE_FOLDERS = [
    DocumentFolder.INBOX,
    DocumentFolder.OUTGOING,
    DocumentFolder.INTERNAL,
    DocumentFolder.NOT_INTERNAL,
];
