.root {
    font-size: 90%;
}

.title {
    margin-bottom: 1cm;
    text-align: center;
}

.table {
    width: 100%;
    margin-top: 20px;
    border-collapse: collapse;
    font-weight: bold;
    table-layout: fixed;
}

.cell {
    padding: 0 0.1cm;
    border: 0.05cm solid var(--content-color);
}

.requisites {
    display: table;
    width: 100%;
    margin-top: 1.5cm;
    table-layout: fixed;
}

.requisites + .requisites {
    margin-top: 1.5cm;
}

.column {
    position: relative;
    display: table-cell;
    padding-right: 1cm;
}

.sign {
    display: table-cell;
    padding-right: 1cm;
    vertical-align: bottom;
    white-space: nowrap;
}
