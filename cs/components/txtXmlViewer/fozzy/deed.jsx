import React from 'react';

import PropTypes from 'prop-types';

import SignLine from '../ui/signLine/signLine';

import { formatDate } from '../../../lib/date';
import { XmlDocument } from '../../../records/document';
import { XmlData } from '../../../records/xml';
import { DATE_FORMAT } from '../constants';
import { SERVICE_TYPE_SUPPORT } from './constants';

import css from './deed.css';

class Deed extends React.Component {
    static propTypes = {
        printMode: PropTypes.bool,
        doc: PropTypes.instanceOf(XmlDocument).isRequired,
        data: PropTypes.instanceOf(XmlData).isRequired,
    };

    static defaultProps = {
        printMode: false,
    };

    componentDidMount() {
        // Need to re-render component with refs objects
        this.forceUpdate();
        window.addEventListener('resize', this.handleResize);
    }

    componentWillUnmount() {
        window.removeEventListener('resize', this.handleResize);
    }

    handleResize = () => {
        this.forceUpdate();
    };

    render() {
        const { data } = this.props;
        const {
            extra,
            date,
            number,
            owner,
            partner,
            services,
            agreementName,
            agreementDate,
        } = data;
        return (
            <div
                className={css.root}
                ref={(el) => {
                    this.container = el;
                }}
            >
                <div className={css.title}>
                    <div>АКТ № {number}</div>
                    <div>
                        приймання-передачі наданих послуг ІТ <br />
                        за договором № {agreementName} від{' '}
                        {formatDate(agreementDate, DATE_FORMAT)}р.
                    </div>
                </div>
                <div className={css.table}>
                    <div className={css.cell}>місто Київ</div>
                    <div className={css.cell}>
                        <div className={css.textRight}>
                            {formatDate(date, DATE_FORMAT)} р.
                        </div>
                    </div>
                </div>
                <div className={css.content}>
                    <p>
                        Товариство з обмеженою відповідальністю {partner.name}{' '}
                        (надалі іменується - Замовник) в особі{' '}
                        {extra.partnerRepresentativeFull}, який діє на підставі{' '}
                        {extra.partnerReason}, з однієї сторони, та
                    </p>
                    <p>
                        <b>Фізична особа-підприємець {owner.fullName}</b>{' '}
                        (надалі іменується - Виконавець), з іншої сторони, разом
                        надалі - Сторони, а кожен окремо - Сторона, склали цей
                        акт приймання-передачі наданих послуг ІТ(далі - Акт) до
                        договору про надання послуг{' '}
                        <b>
                            від {formatDate(agreementDate, DATE_FORMAT)}р. №{' '}
                            {agreementName}
                        </b>{' '}
                        про те, що:
                    </p>
                    <div className={css.item}>
                        1. Виконавець надав, а замовник прийняв Послуги ІТ з:
                        <ul>
                            {services.items.map((item) => (
                                <li
                                    className={css.paragraph}
                                    key={item.slice(7)}
                                >
                                    {item}
                                </li>
                            ))}
                            {data.extra.serviceType ===
                                SERVICE_TYPE_SUPPORT && (
                                <li className={css.paragraph}>
                                    Кількість відпрацьованого часу -{' '}
                                    {data.extra.serviceEstimate}{' '}
                                    {data.extra.serviceEstimateUnits}
                                </li>
                            )}
                        </ul>
                    </div>
                    <div className={css.item}>
                        2. Загальна вартість Послуг ІТ без ПДВ складає:{' '}
                        {services.totalSum} грн.({services.totalSumStr})
                    </div>
                    <div className={css.item}>
                        3. Замовник не має жодних претензій до якості наданих
                        Виконавцем Послуг ІТ. Після завершення розрахунків в
                        повному обсязі Виконавець не матиме жодних претензій до
                        Замовника.
                    </div>
                </div>
                <div className={css.table}>
                    <div className={css.cell}>
                        <b>Від Замовника</b>
                        <br />
                        <b>{partner.representativePosition}</b>
                        <div className={css.nowrap}>
                            <div className={css.line}>
                                <SignLine full />
                            </div>
                            <b>{partner.representative}</b>
                        </div>
                    </div>
                    <div className={css.cell}>
                        <b>Від Виконавця</b>
                        <br />
                        <br />
                        <div className={css.nowrap}>
                            <div className={css.line}>
                                <SignLine full />
                            </div>
                            <b>{owner.representative}</b>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

export default (props) => <Deed {...props} />;
