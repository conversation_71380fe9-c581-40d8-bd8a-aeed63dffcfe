import React, { Component } from 'react';

import cn from 'classnames';
import { XMLDocumentViewOrientation } from 'components/txtXmlViewer/types';
import { formatError, isUrl2PdfViewer } from 'lib/helpers';
import memoizeOne from 'memoize-one';
import PropTypes from 'prop-types';
import {
    XmlDocument,
    apiTxtDocument,
    apiXmlDocument,
    updateTxtDocument,
    updateXmlDocument,
} from 'records/document';
import { ViewerDataStatuses } from 'services/enums';
import { getOriginalUrl } from 'services/viewer';
import { t } from 'ttag';
import Icon from 'ui/icon';

import { isExpandable, isXmlDocument } from './utils';

import Warning from './ui/warning/warning';

import io from '../../services/io';
import loggerService from '../../services/logger';
import DummyViewer from '../dummyViewer/dummyViewer';
import FooterRenderer from '../footerRenderer/footerRenderer';
import Loading from '../loading/loading';
import { XML_TO_JSON_MAX_LIMIT, XML_TO_JSON_URL_REGEXP } from './constants';
import { prepareRendererArgs, validateXmlDocument } from './renderer';
import SignaturesTable from './signatures/signaturesTable';

import annulmentMark from './images/annulment_mark.svg';

import css from './txtXmlViewer.css';

export const TxtXmlViewerContext = React.createContext({
    isExpandable: false,
    isExpanding: false,
    onExpandFactory: () => undefined,
});

const deriveContext = memoizeOne((data, status, handleExpandFactory) => ({
    isExpandable: data ? isExpandable(data) : false,
    isExpanding: status === ViewerDataStatuses.contentLoading,
    onExpandFactory: handleExpandFactory,
}));

const edrpousHideWarning = ['35039974'];

/**
 * Component for viewing TXT/XML documents.
 *
 * The configuration for parsing is defined in the XML_DOCUMENT_MAPPING constant.
 *
 * Basic flow:
 *  1. Load JSON data about the document from the "url" prop (see `loadData` method).
 *  2. Load XML/TXT content from the "content_url" field of the JSON data (see `loadContent` method).
 *  3. Parse the XML/TXT content (see `prepareRendererArgs` method).
 *  4. Render the parsed content.
 */
class TxtXmlViewer extends Component {
    static defaultProps = {
        isExpandAll: false,
        isPrintMode: false,
        isSaveToFileMode: false,
        renderSignaturesTable: false,
        renderSignatureInInterface: true,
        renderSignatureOnPrintDocument: true,
        renderReviewInInterface: true,
        renderReviewOnPrintDocument: true,
    };
    static propTypes = {
        isExpandAll: PropTypes.bool,
        isPrintMode: PropTypes.bool,
        renderAnnulmentMarkDocument: PropTypes.bool,
        isSaveToFileMode: PropTypes.bool,
        renderSignaturesTable: PropTypes.bool,
        renderSignatureInInterface: PropTypes.bool,
        renderSignatureOnPrintDocument: PropTypes.bool,
        renderReviewInInterface: PropTypes.bool,
        renderReviewOnPrintDocument: PropTypes.bool,
        url: PropTypes.string,
    };

    state = {
        contentUrl: null,
        data: null,
        status: ViewerDataStatuses.none,
    };

    componentDidMount() {
        if (this.props.url) {
            this.loadData();
        }
    }

    handleExpandFactory = (lastStr) => {
        return async (evt) => {
            evt.preventDefault();
            const last = parseInt(lastStr, 10);
            if (!isNaN(last)) {
                await this.loadContent(this.state.contentUrl, {
                    first: last + 1,
                });
            }
        };
    };

    handlePrint = (data) => {
        const [, , , , renderWithPages] = prepareRendererArgs(data);
        if (this.props.isPrintMode && !renderWithPages) {
            const images = document.querySelectorAll('img');
            const expectedImageCount = images.length;
            let loadedImageCount = 0;

            if (!expectedImageCount) {
                window.print();
                return;
            }

            function imageLoaded() {
                loadedImageCount++;
                if (loadedImageCount === expectedImageCount) {
                    window.print();
                }
            }

            images.forEach((img) => {
                img.addEventListener('load', imageLoaded);
                img.addEventListener('error', imageLoaded);
            });
        }
    };

    async loadContent(url, extraParams) {
        this.setState({
            status: ViewerDataStatuses.contentLoading,
        });

        try {
            const params = await this.prepareParams(url, extraParams);

            let original_url;

            // In case of local url2pdf call to viewer
            if (isUrl2PdfViewer() && url.includes('localhost')) {
                original_url = io.buildDockerhostUrl(url, params);
            } else {
                original_url = io.buildUrl(url, params);
            }

            const response = await io.get(original_url);
            let { data } = this.state;

            const contentObj = {
                encoding: response.headers.get('X-Evo-Content-Encoding'),
                contentType: response.headers.get('Content-Type'),
            };

            // Optimisation: use native fetch.json instead manual json parsing from buffer in updateXmlDocument
            if (
                data instanceof XmlDocument &&
                contentObj.contentType.slice(0, 16) === 'application/json'
            ) {
                contentObj.contentJson = await response.json();
            } else {
                contentObj.buffer = await response.arrayBuffer();
            }

            if (data instanceof XmlDocument) {
                data = validateXmlDocument(
                    await updateXmlDocument(data, contentObj),
                );
            } else {
                data = await updateTxtDocument(data, contentObj);
            }

            this.setState(
                {
                    contentUrl: url,
                    data,
                    status: ViewerDataStatuses.contentLoaded,
                },
                () => this.handlePrint(data),
            );
        } catch (err) {
            loggerService.error('Unable to load or process content data', {
                err: formatError(err),
            });
            this.setState({
                status: ViewerDataStatuses.contentError,
            });
        }
    }

    async loadData() {
        this.setState({
            status: ViewerDataStatuses.dataLoading,
        });

        try {
            const data = await io.getAsJson(this.props.url);
            this.setState(
                {
                    data: isXmlDocument(data)
                        ? apiXmlDocument(data)
                        : apiTxtDocument(data),
                    status: ViewerDataStatuses.dataLoaded,
                },
                async () => {
                    await this.loadContent(data.content_url);
                },
            );
        } catch (err) {
            loggerService.error('Unable to load viewer data', {
                err: formatError(err),
            });
            this.setState({
                data: null,
                status: ViewerDataStatuses.dataError,
            });
        }
    }

    async prepareParams(url, extraParams) {
        const params = extraParams || {};
        /*
         * For some documents, there is also an additional optimization that allows downloading only part of the
         * document in JSON format. This is primarily used for NovaPoshta documents, which can be very large to
         * process in the browser. Jira task: DOC-1493
         */
        if (this.props.isExpandAll && XML_TO_JSON_URL_REGEXP.test(url)) {
            const data = await io.getAsJson(
                url.replace('/xml-to-json', '/xml-to-json/rows'),
            );

            params.limit = data.rows || XML_TO_JSON_MAX_LIMIT;
            if (!data.rows) {
                loggerService.log('XML to JSON response have zero limit', {
                    rows: data.rows,
                    url,
                });
            }
            if (params.limit > XML_TO_JSON_MAX_LIMIT) {
                loggerService.log(
                    'Attempt to download XML to JSON file exceeded max limit rows',
                    { rows: data.rows, url },
                );
                params.limit = XML_TO_JSON_MAX_LIMIT;
            }
        }
        return params;
    }

    /**
     * Property to set landscape orientation
     * Just add static property to React class or function component
     * Use VisualizationXmlComponent for typing React function components - Component.orientation === 'landscape'
     * @type {XMLDocumentViewOrientation}
     */
    viewOrientation = XMLDocumentViewOrientation.portrait;

    render() {
        const {
            isPrintMode,
            renderAnnulmentMarkDocument,
            isSaveToFileMode,
            renderSignaturesTable,
            renderSignatureInInterface,
            renderSignatureOnPrintDocument,
            renderReviewInInterface,
            renderReviewOnPrintDocument,
            url,
        } = this.props;
        const { data, status } = this.state;

        const isShowWarning =
            isSaveToFileMode && !edrpousHideWarning.includes(data?.edrpou);

        const originalUrl = url ? getOriginalUrl(url) : url;
        const renderSignatures = isPrintMode
            ? renderSignatureOnPrintDocument
            : renderSignatureInInterface;
        const renderReviews = isPrintMode
            ? renderReviewOnPrintDocument
            : renderReviewInInterface;

        let content;
        let renderContentWithPages;

        if (status === ViewerDataStatuses.contentError) {
            content = (
                <DummyViewer
                    frame
                    title={t`Неможливо завантажити зміст документу`}
                    url={originalUrl}
                />
            );
        } else if (status === ViewerDataStatuses.dataError) {
            content = <DummyViewer frame url={originalUrl} />;
        } else if (
            status === ViewerDataStatuses.contentLoaded ||
            (data && data.content)
        ) {
            const [
                component,
                parsedData,
                renderBuiltinSignatures,
                renderBuiltinReviews,
                renderWithPages,
            ] = prepareRendererArgs(data);
            renderContentWithPages = renderWithPages;

            if (component.orientation) {
                this.viewOrientation = component.orientation;
            }

            const useBuiltinRender =
                renderBuiltinSignatures || renderBuiltinReviews;

            const isPrintAfterLoadScript =
                renderContentWithPages && isPrintMode;

            content = (
                <div
                    className={cn(
                        {
                            [css.content]: !renderContentWithPages,
                            [css.withPages]: renderContentWithPages,
                        },
                        'ph-no-capture',
                    )}
                >
                    {renderAnnulmentMarkDocument && (
                        <Icon
                            glyph={annulmentMark}
                            className={css.annulmentMark}
                        />
                    )}
                    {component({
                        ...this.props,
                        data: parsedData,
                        doc: data,
                        renderSignatures: renderBuiltinSignatures,
                        renderReviews: renderBuiltinReviews,
                        onLoadedPageScript:
                            isPrintAfterLoadScript && window.print,
                    })}
                    {!useBuiltinRender && (
                        <FooterRenderer
                            renderSignatures={renderSignatures}
                            renderReviews={renderReviews}
                            doc={data}
                        />
                    )}
                    {(isPrintMode ||
                        isSaveToFileMode ||
                        renderSignaturesTable) && (
                        <SignaturesTable doc={data} />
                    )}
                </div>
            );
        }

        if (!content) {
            return (
                <div
                    className={cn({
                        [css.root]: !isPrintMode,
                        [css.rootPrint]: isPrintMode,
                    })}
                >
                    <div className={css.loading}>
                        <Loading />
                    </div>
                </div>
            );
        }

        return (
            <TxtXmlViewerContext.Provider
                value={deriveContext(
                    this.state.data,
                    this.state.status,
                    this.handleExpandFactory,
                )}
            >
                <div
                    className={cn({
                        [css.root]: !isPrintMode && !renderContentWithPages,
                        [css.rootPrint]: isPrintMode && !renderContentWithPages,
                        [css.rootWithPages]: renderContentWithPages,
                        [css.landscape]:
                            this.viewOrientation ===
                            XMLDocumentViewOrientation.landscape,
                    })}
                >
                    {isShowWarning && <Warning url={url} />}
                    {content}
                </div>
            </TxtXmlViewerContext.Provider>
        );
    }
}

export default TxtXmlViewer;
