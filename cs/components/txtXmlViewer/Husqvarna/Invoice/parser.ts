import moment from 'moment';

import { HusqvarnaDeliveryNoteData, Line } from './types';

import { parseTableData, textContent } from '../../utils';

import { parseXml as domParseXml } from '../../../../services/xml';
import * as constants from './constants';

export const parseXmlLine = (itemXml: XMLDocument): Line => ({
    number: textContent(itemXml, constants.LINE_NUMBER),
    productCode: textContent(itemXml, constants.PRODUCT_CODE),
    productDescription: textContent(itemXml, constants.PRODUCT_DESCRIPTION),
    units: textContent(itemXml, constants.UNITS),
    qty: textContent(itemXml, constants.QTY),
    price: textContent(itemXml, constants.PRICE),
    lineTotal: textContent(itemXml, constants.LINE_TOTAL),
    discount: textContent(itemXml, constants.DISCOUNT),
});

export const parseXmlLines = (xml: XMLDocument): Line[] => {
    const linesXmls = xml.querySelector(constants.LINES)
        ? parseTableData(xml, constants.LINES, constants.LINE)
        : [];

    return linesXmls.map(parseXmlLine);
};

export const parseXml = (content: string): HusqvarnaDeliveryNoteData => {
    const xml = domParseXml(content);

    const orderDate = textContent(xml, constants.ORDER_DATE).trim();

    return {
        customerName: textContent(xml, constants.CUSTOMER_NAME),
        customerPhone: textContent(xml, constants.CUSTOMER_PHONE),
        customerAddress: textContent(xml, constants.CUSTOMER_ADDRESS),
        orderDate: orderDate && moment(orderDate),
        orderNumber: textContent(xml, constants.ORDER_NUMBER),
        warehouse: textContent(xml, constants.WAREHOUSE),
        lines: parseXmlLines(xml),
        vat: textContent(xml, constants.VAT),
        totalWithoutVat: textContent(xml, constants.TOTAL_WITHOUT_VAT),
        totalVat: textContent(xml, constants.TOTAL_VAT),
        totalWithVat: textContent(xml, constants.TOTAL_WITH_VAT),
    };
};

export default (data: { content: string }) => parseXml(data.content);
