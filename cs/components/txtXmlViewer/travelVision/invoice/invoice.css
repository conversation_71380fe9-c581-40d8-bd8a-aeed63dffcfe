.documentTitle {
    padding-bottom: 0.05cm;
    border-bottom: 0.075cm solid var(--content-color);
    margin-bottom: 0.4cm;
    font-size: 110%;
    font-weight: bold;
    text-indent: 0.05cm;
}

.requisitesTable {
    width: 100%;
    font-size: 85%;
}

.requisitesTable td {
    padding-top: 0.1cm;
    padding-bottom: 0.1cm;
    vertical-align: top;
}

.requisiteLabel {
    width: 15%;
}

.agreementRow td {
    padding-top: 0.6cm;
}

.details {
    padding-bottom: 0.2cm;
    border-bottom: 0.075cm solid var(--content-color);
    margin-bottom: 0.2cm;
    font-size: 90%;
}

.footerTable {
    width: 100%;
    font-size: 85%;
}

.footerTable tbody th {
    width: 70%;
    font-weight: bold;
    text-align: right;
}

.footerTable tbody td {
    width: 30%;
    padding: 0 0.2cm;
}

.alignBottom {
    vertical-align: bottom;
}

.textCenter {
    text-align: center;
}
