import React from 'react';

import cn from 'classnames';
import { getPKCodeTitle } from 'lib/helpers';
import PropTypes from 'prop-types';
import { Signature } from 'records/document';

import css from './signatureImage.css';

const SignatureImage = ({ data }) => {
    if (!data) {
        return null;
    }

    // For stamp need to display company name instead of owner name
    const title = data.isSignature ? data.fullName : data.companyFullName;

    return (
        <div
            className={cn({
                [css.rootSignature]: data.isSignature,
                [css.rootStamp]: !data.isSignature,
            })}
        >
            <div className={css.data}>
                {title}
                <br />
                {getPKCodeTitle(data)}
                <br />
                {data.edrpou}
            </div>
        </div>
    );
};

SignatureImage.propTypes = {
    data: PropTypes.instanceOf(Signature),
};

export default SignatureImage;
