export const DATE_FORMAT_FULL = 'DD MMMM YYYY';

export const DATE_QUERY = 'Invoice-Header > InvoiceDate';
export const DATE_DELIVERY_QUERY =
    'Invoice-Reference > ReceivingAdvice > DeliveryDate';
export const BUYER_ORDER_QUERY = 'Invoice-Reference > Order > BuyerOrderNumber';
export const BUYER_ORDER_DATE_QUERY =
    'Invoice-Reference > Order > BuyerOrderDate';

export const BUYER_ORDER_OVERWRITE_QUERY =
    'Invoice-Reference > Order > BuyerOrderOverwrite';

export const MANAGER_ORDER_QUERY = 'Invoice-Reference > Order > ManagerOrder';
export const PAYMENT_TERN_ORDER_QUERY =
    'Invoice-Reference > Order > PaymentTernOrder';

export const NOTE_NUMBER_QUERY = 'Invoice-Header > InvoiceNumber';
export const EXTRA_ADD_DOC_QUERY = 'Invoice-Header > AddDoc';
export const EXTRA_ADD_DOC_SIGNER_QUERY = 'Invoice-Header > AddDocSigner';
export const NOTE_AGREEMENT_NUMBER_QUERY = 'Invoice-Header > ContractNumber';
export const NOTE_AGREEMENT_DATE_QUERY = 'Invoice-Header > ContractDate';
export const PAYMENT_TYPE = 'Invoice-Header > InvoicePaymentMeans';

export const PLACE_QUERY = 'Invoice-Header > Place';

export const PAYMENT_TERM = 'Invoice-Header > PAYMENT_TERM';
export const DELIVERY_ADDRESS = 'DeliveryAddress';

export const BUYER_ADDRESS_STREET_QUERY =
    'Invoice-Parties > Buyer > StreetAndNumber';
export const BUYER_ADDRESS_CITY_QUERY = 'Invoice-Parties > Buyer > CityName';
export const BUYER_ADDRESS_POSTAL_CODE_QUERY =
    'Invoice-Parties > Buyer > PostalCode';
export const BUYER_NAME_QUERY = 'Invoice-Parties > Buyer > Name';
export const BUYER_EDRPOU_QUERY =
    'Invoice-Parties > Buyer > UtilizationRegisterNumber';
export const BUYER_TAX_ID_QUERY = 'Invoice-Parties > Buyer > TaxID';
export const BUYER_PHONE = 'Invoice-Parties > Buyer > PhoneNumber';
export const SELLER_ACTUAL_ADDRESS_STREET_QUERY =
    'Invoice-Parties > SellerHeadquarters > StreetAndNumber';
export const SELLER_ACTUAL_PHONE =
    'Invoice-Parties > SellerHeadquarters > PhoneNumber';
export const SELLER_ACTUAL_ADDRESS_CITY_QUERY =
    'Invoice-Parties > SellerHeadquarters > CityName';
export const SELLER_ACTUAL_ADDRESS_POSTAL_CODE_QUERY =
    'Invoice-Parties > SellerHeadquarters > PostalCode';
export const SELLER_LEGAL_ADDRESS_STREET_QUERY =
    'Invoice-Parties > Seller > StreetAndNumber';
export const SELLER_LEGAL_PHONE = 'Invoice-Parties > Seller > PhoneNumber';
export const SELLER_LEGAL_ADDRESS_CITY_QUERY =
    'Invoice-Parties > Seller > CityName';
export const SELLER_LEGAL_ADDRESS_POSTAL_CODE_QUERY =
    'Invoice-Parties > Seller > PostalCode';
export const SELLER_EDRPOU_QUERY =
    'Invoice-Parties > Seller > UtilizationRegisterNumber';
export const SELLER_TAX_ID_QUERY = 'Invoice-Parties > Seller > TaxID';
export const SELLER_NAME_QUERY = 'Invoice-Parties > Seller > Name';

export const GOODS_QUERY = 'Invoice-Lines';
export const GOODS_ITEM_QUERY = 'Line';
export const GOODS_UNIT_QUERY = 'UnitOfMeasure';
export const GOODS_ITEM_NAME_QUERY = 'ItemDescription';
export const EAN_ITEM_CODE_QUERY = 'EAN';
export const GOODS_ITEM_CODE_QUERY = 'BuyerItemCode';
export const GOODS_ITEM_CODE_SELF_QUERY = 'BuyerItemCodeSelf';
export const GOODS_ITEM_NUMBER_QUERY = 'LineNumber';
export const GOODS_ITEM_PRICE_QUERY = 'InvoiceUnitNetPrice';
export const GOODS_ITEM_SUM_WITH_TAX_PRICE_QUERY = 'NetAmount';
export const GOODS_ITEM_TAX_AMOUNT = 'TaxAmount';
export const GOODS_ITEM_TAX_RATE = 'TaxRate';
export const GOODS_ITEM_QUANTITY_QUERY = 'InvoiceQuantity';
export const GOODS_ITEM_ADDITIONAL_QUANTITY_QUERY = 'AdditionalInvoiceQuantity';
export const GOODS_ITEM_ADDITIONAL_PRICE_QUERY =
    'AdditionalInvoiceUnitNetPrice';
export const GOODS_ITEM_EXTERNAL_CODE_QUERY = 'ExternalItemCode';
export const GOODS_ITEM_SUPPLIER_CODE_QUERY = 'SupplierItemCode';

export const GOODS_SUM_QUERY = 'Invoice-Summary > TotalNetAmount';
export const GOODS_EXICE_TAXES_QUERY =
    'Invoice-Summary > Tax-Summary > Tax-Summary-Line > ExciseDuty';
export const GOODS_SUM_TAXES_QUERY = 'Invoice-Summary > TotalTaxAmount';
export const GOODS_SUM_WITH_TAX_QUERY = ' TotalGrossAmount';
export const GOODS_SUM_WITH_TAX_TEXT_QUERY =
    'Invoice-Summary > TotalGrossAmountText';

export const EXTRA_BUYER_DELIVERY_POINT_QUERY =
    'Invoice-Parties > DeliveryPoint > ILN';
export const EXTRA_SELLER_GLN_QUERY = 'Invoice-Parties > Seller > ILN';
export const ROUNDING_SUM_QUERY = 'Invoice-Summary > Rounding';
export const TOTAL_GROSS_AMOUNT_WTH_ROUNDING_QUERY =
    'Invoice-Summary > TotalGrossWithRounding';
export const CODE_BY_BUYER = 'Invoice-Parties > Seller > CodeByBuyer';
