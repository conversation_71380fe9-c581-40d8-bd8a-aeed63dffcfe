import React, { <PERSON> } from 'react';

import { XmlProducts } from '../../../../types/xmlDocuments';

import { formatPrice } from '../../../../lib/numbers';

import css from './servicesTable.css';

interface Props {
    data: XmlProducts;
}

const ServicesTable: FC<React.PropsWithChildren<Props>> = ({
    data: { items, totalDocumentSum, totalSum, totalTaxes },
}) => (
    <div className={css.root}>
        <table className={css.table}>
            <thead>
                <tr>
                    <th className={css.number}>№</th>
                    <th className={css.name}>Найменування робіт, послуг</th>
                    <th className={css.quantity}>Кіл-сть</th>
                    <th className={css.item}>Од.</th>
                    <th className={css.price}>Ціна без ПДВ</th>
                    <th className={css.sum}>Сума без ПДВ</th>
                </tr>
            </thead>
            <tbody>
                {items?.map((service, index) => (
                    <tr key={`product-${service.number || index}`}>
                        <td className={css.textCenter}>
                            {service.number || index + 1}
                        </td>
                        <td>{service.name}</td>
                        <td className={css.textRight}>{service.quantity}</td>
                        <td className={css.textCenter}>{service.unit}</td>
                        <td className={css.textRight}>
                            {formatPrice(service.price)}
                        </td>
                        <td className={css.textRight}>
                            {formatPrice(service.sum)}
                        </td>
                    </tr>
                ))}
            </tbody>
        </table>

        <table className={css.totalTable}>
            <tbody>
                <tr>
                    <td className={css.totalName}>Всього:</td>
                    <td>{formatPrice(totalSum)}</td>
                </tr>

                <tr>
                    <td className={css.totalName}>Сума ПДВ:</td>
                    <td>{formatPrice(totalTaxes)}</td>
                </tr>

                <tr>
                    <td className={css.totalName}>Всього із ПДВ:</td>
                    <td>{formatPrice(totalDocumentSum)}</td>
                </tr>
            </tbody>
        </table>
    </div>
);

export default ServicesTable;
