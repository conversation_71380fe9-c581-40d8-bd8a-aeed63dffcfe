import React, { <PERSON> } from 'react';

import { XmlTemplateProps } from '../../../types/xmlDocuments';

import { ComdocTypes } from '../Comdoc/constants';
import ServiceDeed from './serviceDeed/serviceDeed';
import TransferDeed from './transferDeed/transferDeed';

const DOC_TYPE_TO_VIEW_MAP: Record<
    string,
    FC<React.PropsWithChildren<unknown>>
> = {
    [ComdocTypes.SALES_INVOICE]: TransferDeed,
    [ComdocTypes.INCOME_INVOICE]: TransferDeed,
    [ComdocTypes.SERVICE_DEED]: ServiceDeed,
};

const Deed: FC<React.PropsWithChildren<XmlTemplateProps>> = (props) => {
    const { documentType } = props.data;

    if (documentType) {
        const View = DOC_TYPE_TO_VIEW_MAP[documentType];

        return View !== undefined ? (
            <View {...props} />
        ) : (
            <div>Невідомий код типу документу: {documentType}</div>
        );
    }

    return <div>Не вказано код типу документу</div>;
};

export default Deed;
