.headerTable {
    width: 100%;
    font-size: 90%;
}

.headerTable td {
    padding-bottom: 1em;
    vertical-align: top;
}

.headerTable td:nth-child(1) {
    width: 3.5cm;
}

.documentTitle {
    padding-bottom: 0.05cm;
    margin-bottom: 0.4cm;
    font-size: 110%;
    font-weight: bold;
    text-align: center;
}

.documentTrivia,
.documentTriviaBold,
.documentTriviaBorder,
.documentTriviaMargin {
    width: 85%;
    line-height: 110%;
}

.documentTriviaBold {
    font-weight: bold;
}

.documentTriviaBorder {
    width: 100%;
    padding-bottom: 0.2cm;
    border-bottom: 0.075cm solid var(--content-color);
    margin-top: 0.4cm;
}

.documentTriviaMargin {
    margin: 0.4cm 0;
}

.footerTable {
    width: 100%;
    margin-top: 0.4cm;
    font-size: 90%;
}

.footerTable thead th {
    width: 50%;
    font-weight: bold;
    text-align: left;
    vertical-align: top;
}

.footerTable tbody {
    font-size: 80%;
}

.footerTable tbody td {
    padding-right: 0.25cm;
    vertical-align: top;
}

@media (min-width: 720px) {
    .footerTable tbody td {
        padding-right: 1.25cm;
    }
}

.representativeRow {
    font-size: 120%;
}

.blankPlace {
    margin-left: 0.5cm;
}

.volumes {
    width: 50%;
    margin: 1.5cm 0;
    font-weight: bold;
}

.cellCentered {
    text-align: center;
}
