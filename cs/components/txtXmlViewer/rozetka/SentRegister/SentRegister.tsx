import React from 'react';

import cn from 'classnames';
import moment from 'moment';

import { DATE_FORMAT } from '../../constants';
import { SentRegisterData } from './parser';

import css from './SentRegister.css';

const signLane = <span>{'_'.repeat(25)}</span>;

const PaddedUnderline: React.FC<React.PropsWithChildren<unknown>> = ({
    children,
}) => <u>&nbsp;{children}&nbsp;</u>;

const SentRegister: React.FC<
    React.PropsWithChildren<{ data: SentRegisterData }>
> = ({ data }) => (
    <div>
        <div className={cn(css.row, css.rightALign, css.paragraph)}>
            <p>
                Реєстр відправлень №
                <PaddedUnderline>{data.number}</PaddedUnderline> від{' '}
                <PaddedUnderline>
                    {moment(data.date, DATE_FORMAT).format(DATE_FORMAT)}
                </PaddedUnderline>
                р.
            </p>
            <p>
                до Договору про надання послуг №
                <PaddedUnderline>{data.agreementName}</PaddedUnderline> від{' '}
                <PaddedUnderline>
                    {moment(data.agreementDate, DATE_FORMAT).format(
                        DATE_FORMAT,
                    )}
                </PaddedUnderline>
                р.
            </p>
        </div>
        <div className={cn(css.row, css.paragraph)}>
            Цей Реєстр видачі складений про те, що в точці видачі за адресою:
            {''}
            <PaddedUnderline>{data.services.items[0].address}</PaddedUnderline>
            <br />
            Замовником <PaddedUnderline>{data.owner.fullName}</PaddedUnderline>,
            було передано
            <br />
            Виконавцю <PaddedUnderline>{data.partner.fullName}</PaddedUnderline>
            , відправлення для видачі отримувачам в кількості{' '}
            <PaddedUnderline>{data.servicesQuantity}</PaddedUnderline> штук.
        </div>
        <table className={cn(css.goodsTable, css.row)}>
            <thead>
                <tr>
                    <th className={css.sCol}>№</th>
                    <th className={css.lCol}>Замовник</th>
                    <th className={css.lCol}>Адреса точки</th>
                    <th className={css.lCol}>Отримувач ПІБ</th>
                    <th className={css.mdCol}>
                        Номер замовлення сайту rozetka.com.ua
                    </th>
                    <th className={css.mdCol}>ЕН відправлення</th>
                    <th className={css.mdCol}>Вартість вмісту відправлення</th>
                </tr>
            </thead>
            <tbody>
                {data.services.items.map((item, i) => (
                    // it's intented to be static over re-renders
                    <tr key={i}>
                        <td>{item.number}</td>
                        <td>{data.owner.fullName}</td>
                        <td>{item.address}</td>
                        <td>{item.recipient}</td>
                        <td>{item.externalCode}</td>
                        <td>{item.externalCode}</td>
                        <td>{item.price}</td>
                    </tr>
                ))}
            </tbody>
        </table>
        <div className={cn(css.row, css.signsLine, css.paragraph)}>
            <p>Підписи:</p>
            <p>Представник Замовника передав: {signLane}</p>
            <p>Представник Виконавця прийняв: {signLane}</p>
        </div>
    </div>
);

export default SentRegister;
