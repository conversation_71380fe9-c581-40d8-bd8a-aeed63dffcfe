import React from 'react';

import { formatPriceWithCommaAndSpaces } from 'components/txtXmlViewer/carlsbergUkraine/utils';
import { formatDate } from 'lib/date';

import { normalizePosition } from './utils';

import { parseContent } from './parcer';

import css from './TransferOfCertificatesTOVFOP.css';

interface TransferOfCertificatesTOVFOPProps {
    data: ReturnType<typeof parseContent>;
    variant: 'FOP' | 'TOV';
}

const TransferOfCertificatesTOVFOP: React.FC<TransferOfCertificatesTOVFOPProps> = ({
    data,
    variant,
}) => {
    return (
        <div className={css.root}>
            <h1 className={css.title}>АКТ ПРИЙМАННЯ-ПЕРЕДАЧІ СЕРТИФІКАТІВ</h1>
            <div>
                <div className={css.meta}>Місце укладання: {data.city}</div>
                <div className={css.meta}>
                    Дата укладення: {formatDate(data.date, 'LL')}
                </div>
            </div>
            <p>
                <b>
                    Товариство з обмеженою відповідальністю «{data.company.name}
                    »
                </b>
                , в особі {normalizePosition(data.company.actor.position)}{' '}
                {data.company.actor.fullName}, який діє на підставі Статуту
                (далі – <b>Замовник</b>), з однієї сторони, та{' '}
                <b>
                    {variant === 'TOV' &&
                        `Товариство з обмеженою відповідальністю`}
                    {variant === 'FOP' && `Фізична особа-підприємець`} «
                    {data.counterparty.name}»,{' '}
                </b>
                {variant === 'TOV' &&
                    `в особі
${normalizePosition(data.counterparty.actor.position)} ${
                        data.counterparty.actor.fullName
                    }, який діє на підставі Статуту`}{' '}
                (далі - <b>Виконавець</b>), з другої сторони, які надалі разом
                за текстом іменуються як Сторони, а кожна окремо - Сторона,
                склали цей Акт приймання-передачі сертифікатів про наступне:
            </p>
            <p>
                1. У відповідності до умов Договору про надання послуг №{' '}
                {data.counterpartyAgreement.number}{' '}
                {data.counterpartyAgreement.date &&
                    `від ${formatDate(
                        data.counterpartyAgreement.date,
                        'LL',
                    )}`}{' '}
                (далі – Договір), Замовник передає, а Виконавець приймає на
                зберігання та видачу наступні Сертифікати:
            </p>
            <table>
                <thead>
                    <tr>
                        <th>№</th>
                        <th>Найменування сертифікату</th>
                        <th>Номінал</th>
                        <th>Кількість</th>
                        <th>Сума</th>
                    </tr>
                </thead>
                <tbody>
                    {data.table.map((row) => (
                        <tr key={row.index}>
                            <td>{row.index}</td>
                            <td>{row.name}</td>
                            <td align="right">{row.nominal}</td>
                            <td align="right">{row.quantity}</td>
                            <td align="right">{row.sum}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
            <div>
                <p>
                    2. Загальна сума переданих Сертифікатів становить{' '}
                    <b>
                        {formatPriceWithCommaAndSpaces(data.sum)} (
                        {data.sumString}), в т.ч. ПДВ 20%.
                    </b>
                </p>
                <p>
                    3. Виконавець підтверджує отримання Сертифікатів та
                    зобов’язується їх зберігати та видавати відповідно до умов
                    Договору.
                </p>
                <p>
                    4. Цей Акт укладається Сторонами шляхом його підписання в
                    паперовій або електронній формі з використанням
                    онлайн-сервісу електронного документообігу «ВЧАСНО».
                </p>
                <p>
                    5. Цей Акт набуває чинності та застосовується до
                    правовідносин Сторін починаючи з дати зазначеної в тексті
                    цього Акту як дата його укладання (дата, зазначена у самому
                    Акті як його обов’язковий реквізит), незалежно від дати його
                    підписання та/або дати накладання КЕП/УЕП.
                </p>
            </div>

            <table>
                <colgroup>
                    <col style={{ width: '50%' }} />
                    <col style={{ width: '50%' }} />
                </colgroup>
                <thead>
                    <tr>
                        <th align="center">ЗАМОВНИК:</th>
                        <th align="center">ВИКОНАВЕЦЬ:</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style={{ borderBottom: 'none' }}>
                            <b>{data.company.name}</b>
                            <br />
                            Код ЄДРПОУ {data.company.edrpou}
                            <br />
                            Банк {data.company.bank.name}
                            <br />
                            МФО {data.company.bank.mfo}
                            <br />
                            IBAN {data.company.bank.number}
                            <br />
                            ІПН {data.company.inn}
                            <br />
                            Свідоцтво платника ПДВ №{' '}
                            {data.company.taxNumber || '-'}
                            <br />
                            Юридична адреса: {data.company.address}
                            <br />
                            Тел./факс {data.company.telephone}
                            <br />
                            {data.company.email && (
                                <>
                                    E-mail:{' '}
                                    <a href={`mailto:${data.company.email}`}>
                                        {data.company.email}
                                    </a>
                                    <br />
                                </>
                            )}
                            <i>{data.company.bank.taxTypeInfo}</i>
                        </td>
                        <td style={{ borderBottom: 'none' }}>
                            <b>{data.counterparty.name}</b>
                            <br />
                            Код {variant == 'TOV' && 'ЄДРПОУ'}{' '}
                            {data.counterparty.edrpou}
                            <br />
                            Банк {data.counterparty.bank.name}
                            <br />
                            МФО {data.counterparty.bank.mfo}
                            <br />
                            IBAN {data.counterparty.bank.number}
                            <br />
                            ІПН {data.counterparty.inn}
                            <br />
                            Свідоцтво платника ПДВ №{' '}
                            {data.counterparty.taxNumber || '-'}
                            <br />
                            Юридична адреса: {data.counterparty.address}
                            <br />
                            {data.counterparty.telephone && (
                                <>
                                    Тел./факс {data.counterparty.telephone}
                                    <br />
                                </>
                            )}
                            {data.counterparty.email && (
                                <>
                                    E-mail:{' '}
                                    <a
                                        href={`mailto:${data.counterparty.email}`}
                                    >
                                        {data.counterparty.email}
                                    </a>
                                    <br />
                                </>
                            )}
                            <i>{data.counterparty.bank.taxTypeInfo}</i>
                        </td>
                    </tr>
                    <tr>
                        <td style={{ borderTop: 'none', paddingTop: '1em' }}>
                            {data.company.actor.position}
                            _______________
                            {data.company.actor.fullName}
                        </td>
                        <td style={{ borderTop: 'none', paddingTop: '1em' }}>
                            {data.counterparty.actor.position}
                            _______________
                            {data.counterparty.actor.fullName}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
};

export default TransferOfCertificatesTOVFOP;
