import React, { FC, createElement } from 'react';

import { joinStrings } from 'lib/helpers';
import { XmlCompany } from 'records/xml';

import CompanyRequisites from './../../../company/companyRequisites';

type CompanyInfo =
    | 'fullName'
    | 'bank'
    | 'requisites'
    | 'address'
    | 'ipn'
    | 'taxCertificate';

interface CompanyProps {
    // just for reference - it not works in typescript
    data: InstanceType<typeof XmlCompany>;
    boldFullName?: boolean;
    order?: CompanyInfo[];
    separator?: React.ReactNode;
}

const defaultOrder: CompanyInfo[] = [
    'fullName',
    'requisites',
    'bank',
    'address',
    'ipn',
    'taxCertificate',
];

const Company: FC<CompanyProps> = ({
    boldFullName,
    data,
    order = defaultOrder,
    separator = <br />,
}) => {
    const composeNode = (type: CompanyInfo) => {
        switch (type) {
            case 'fullName':
                return createElement(
                    boldFullName ? 'b' : 'span',
                    {},
                    data.fullName,
                );
            case 'bank':
                return joinStrings(
                    [
                        data.iban && `п/р ${data.iban}`,
                        data.name && `у банку ${data.name}`,
                        data.mfo && `МФО ${data.mfo}`,
                    ],
                    ', ',
                );
            case 'requisites':
                return <CompanyRequisites data={data} />;
            case 'address':
                return data.address;
            case 'taxCertificate':
                return data.taxCertificate
                    ? `№ свід. ${data.taxCertificate}`
                    : null;
            default:
                return null;
        }
    };

    return (
        <span>
            {order
                .map((key) => ({
                    type: key,
                    node: composeNode(key),
                }))
                .filter((item) => item.node)
                .map((item) => (
                    <React.Fragment key={item.type}>
                        {item.node}
                        {separator}
                    </React.Fragment>
                ))}
        </span>
    );
};

export default Company;
