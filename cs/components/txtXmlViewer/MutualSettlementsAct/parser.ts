import moment from 'moment';

import { XmlBank, XmlCompany } from '../../../types/xmlDocuments';
import { XmlCompanyProduct, XmlData, XmlProduct, XmlProducts } from './types';

import { parseTableData, textContent } from '../utils';

import * as constants from './constants';

export const parseXmlOwnerBank = (xml: XMLDocument): XmlBank => {
    // @ts-expect-error TS2554 [FIXME] Comment is autogenerated
    const account = textContent(xml, constants.OWNER_BANK_ACCOUNT_QUERY, false);
    // @ts-expect-error TS2554 [FIXME] Comment is autogenerated
    const mfo = textContent(xml, constants.OWNER_BANK_MFO_QUERY, false);

    return { account, mfo };
};

export const parseXmlOwner = (xml: XMLDocument): XmlCompany => {
    return {
        bank: parseXmlOwnerBank(xml),
        fullName: textContent(xml, constants.OWNER_FULL_NAME_QUERY),
        status: textContent(xml, constants.OWNER_STATUS_QUERY),
        edrpou: textContent(xml, constants.OWNER_EDRPOU_QUERY),
        ipn: textContent(xml, constants.OWNER_IPN_QUERY),
    };
};

export const parseXmlPartnerBank = (xml: XMLDocument): XmlBank => {
    const account = textContent(
        xml,
        constants.PARTNER_BANK_ACCOUNT_QUERY,
        // @ts-expect-error TS2554 [FIXME] Comment is autogenerated
        false,
    );
    // @ts-expect-error TS2554 [FIXME] Comment is autogenerated
    const mfo = textContent(xml, constants.PARTNER_BANK_MFO_QUERY, false);

    return { account, mfo };
};
export const parseXmlPartner = (xml: XMLDocument): XmlCompany => {
    return {
        bank: parseXmlPartnerBank(xml),
        fullName: textContent(xml, constants.PARTNER_FULL_NAME_QUERY),
        status: textContent(xml, constants.PARTNER_STATUS_QUERY),
        edrpou: textContent(xml, constants.PARTNER_EDRPOU_QUERY),
        ipn: textContent(xml, constants.PARTNER_IPN_QUERY),
    };
};

export const parseXmlService = (
    serviceXml: XMLDocument,
    ownerGLN: string,
    partnerGLN: string,
): XmlProduct => {
    const getCompanyData = (gln: string): XmlCompanyProduct => {
        const date = textContent(
            serviceXml,
            `Взаєморозрахунки[GLN="${gln}"] > Дата`,
        ).trim();

        return {
            date: date && moment(date),
            number: textContent(
                serviceXml,
                `Взаєморозрахунки[GLN="${gln}"] > Номер`,
            ),
            name: textContent(
                serviceXml,
                `Взаєморозрахунки[GLN="${gln}"] > Найменування`,
            ),
            debit: parseFloat(
                textContent(
                    serviceXml,
                    `Взаєморозрахунки[GLN="${gln}"] > Дебет`,
                ),
            ),
            credit: parseFloat(
                textContent(
                    serviceXml,
                    `Взаєморозрахунки[GLN="${gln}"] > Кредит`,
                ),
            ),
        };
    };

    return {
        owner: getCompanyData(ownerGLN),
        partner: getCompanyData(partnerGLN),
    };
};

export const parseXmlServices = (xml: XMLDocument): XmlProducts => {
    const serviceXmls = xml.querySelector(constants.SERVICES_QUERY)
        ? parseTableData(
              xml,
              constants.SERVICES_QUERY,
              constants.SERVICE_ITEM_QUERY,
          )
        : [];
    const ownerGLN = textContent(xml, constants.OWNER_GLN_QUERY);
    const partnerGLN = textContent(xml, constants.PARTNER_GLN_QUERY);
    const items = serviceXmls.map((serviceXML: XMLDocument) =>
        parseXmlService(serviceXML, ownerGLN, partnerGLN),
    );

    return {
        items,
        ownerInitialDebitBalance: parseFloat(
            textContent(
                xml,
                `ЗагальнеПоКонтрагенту[GLN="${ownerGLN}"] > СальдоПочатковеДебет`,
            ),
        ),
        ownerInitialCreditBalance: parseFloat(
            textContent(
                xml,
                `ЗагальнеПоКонтрагенту[GLN="${ownerGLN}"] > СальдоПочатковеКредит`,
            ),
        ),
        ownerTurnoverPeriodDebit: parseFloat(
            textContent(
                xml,
                `ЗагальнеПоКонтрагенту[GLN="${ownerGLN}"] > ОборотиЗаПеріодДебет`,
            ),
        ),
        ownerTurnoverPeriodCredit: parseFloat(
            textContent(
                xml,
                `ЗагальнеПоКонтрагенту[GLN="${ownerGLN}"] > ОборотиЗаПеріодКредит`,
            ),
        ),
        ownerFinalBalanceDebit: parseFloat(
            textContent(
                xml,
                `ЗагальнеПоКонтрагенту[GLN="${ownerGLN}"] > СальдоКінцевеДебет`,
            ),
        ),
        ownerFinalBalanceCredit: parseFloat(
            textContent(
                xml,
                `ЗагальнеПоКонтрагенту[GLN="${ownerGLN}"] > СальдоКінцевеКредит`,
            ),
        ),
        partnerInitialDebitBalance: parseFloat(
            textContent(
                xml,
                `ЗагальнеПоКонтрагенту[GLN="${partnerGLN}"] > СальдоПочатковеДебет`,
            ),
        ),
        partnerInitialCreditBalance: parseFloat(
            textContent(
                xml,
                `ЗагальнеПоКонтрагенту[GLN="${partnerGLN}"] > СальдоПочатковеКредит`,
            ),
        ),
        partnerTurnoverPeriodDebit: parseFloat(
            textContent(
                xml,
                `ЗагальнеПоКонтрагенту[GLN="${partnerGLN}"] > ОборотиЗаПеріодДебет`,
            ),
        ),
        partnerTurnoverPeriodCredit: parseFloat(
            textContent(
                xml,
                `ЗагальнеПоКонтрагенту[GLN="${partnerGLN}"] > ОборотиЗаПеріодКредит`,
            ),
        ),
        partnerFinalBalanceDebit: parseFloat(
            textContent(
                xml,
                `ЗагальнеПоКонтрагенту[GLN="${partnerGLN}"] > СальдоКінцевеДебет`,
            ),
        ),
        partnerFinalBalanceCredit: parseFloat(
            textContent(
                xml,
                `ЗагальнеПоКонтрагенту[GLN="${partnerGLN}"] > СальдоКінцевеКредит`,
            ),
        ),
        totalSum: parseFloat(textContent(xml, constants.TOTAL_SUM_QUERY)),
        totalDocumentSum: parseFloat(
            textContent(xml, constants.TOTAL_DOCUMENT_SUM_QUERY),
        ),
        totalTaxes: parseFloat(textContent(xml, constants.TOTAL_TAXES_QUERY)),
    };
};

export const parseXml = (xml: XMLDocument): XmlData => {
    const date = textContent(xml, constants.DATE_QUERY).trim();
    const agreementDate = textContent(
        xml,
        constants.AGREEMENT_DATE_QUERY,
    ).trim();
    const parentDate = textContent(xml, constants.PARENT_DOCUMENT_DATE).trim();
    const startServicesProvisionDate = textContent(
        xml,
        constants.START_SERVICES_PROVISION_DATE,
    ).trim();
    const endServicesProvisionDate = textContent(
        xml,
        constants.END_SERVICES_PROVISION_DATE,
    ).trim();

    return {
        date: date && moment(date),
        number: textContent(xml, constants.NUMBER_QUERY),
        documentType: textContent(xml, constants.DOCUMENT_NAME_QUERY),
        documentTypeCode: textContent(xml, constants.DOCUMENT_TYPE_QUERY),
        owner: parseXmlOwner(xml),
        partner: parseXmlPartner(xml),
        agreementName: textContent(xml, constants.AGREEMENT_NAME_QUERY),
        agreementDate: agreementDate && moment(agreementDate),
        agreementPlace: textContent(xml, constants.LOCATION_QUERY),
        startServicesProvisionDate:
            startServicesProvisionDate && moment(startServicesProvisionDate),
        endServicesProvisionDate:
            endServicesProvisionDate && moment(endServicesProvisionDate),

        parentNumber: textContent(xml, constants.PARENT_DOCUMENT_NUMBER),
        parentType: textContent(xml, constants.PARENT_DOCUMENT_TYPE),
        parentDate: parentDate && moment(parentDate),

        servicesAct: parseXmlServices(xml),
    };
};

export default parseXml;
