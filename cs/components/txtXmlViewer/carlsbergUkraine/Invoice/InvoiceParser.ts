import { parseXml } from 'services/xml';

import { parseTableData, textContent } from '../../utils';
import { formatPriceWithCommaAndSpaces } from '../utils';

import {
    FIELDS,
    GOODS_COL_NAME,
    INVOICE_CARD,
    INVOICE_DOCUMENT,
    INVOICE_ORG,
    INVOICE_ROOT,
} from './constants';

const InvoiceParser = (content: string) => {
    const xml = parseXml(content);
    const getTagValue = (tag: string): string =>
        textContent(
            xml,
            `${INVOICE_ROOT} > ${INVOICE_ORG} > ${INVOICE_CARD} > ${INVOICE_DOCUMENT} > ${tag}`,
        ).trim();

    const replaceTagValueWithComma = (tag: string) =>
        formatPriceWithCommaAndSpaces(getTagValue(tag));

    const isNotPriceOrQuantityColumns = [
        GOODS_COL_NAME.TAB1_F1,
        GOODS_COL_NAME.TAB1_F2,
        GOODS_COL_NAME.TAB2_F1,
    ];

    const GOODS_ITEMS: Record<keyof typeof GOODS_COL_NAME, string>[] = (
        parseTableData(xml, FIELDS.DOCUMENT, FIELDS.ROW) || []
    )
        .map((item: any) => {
            const rowNode = item?.querySelector(FIELDS.ROW);

            return {
                line: rowNode?.getAttribute('LINE'),
                tab: rowNode?.getAttribute('TAB'),
                name: rowNode?.getAttribute('NAME').trim(),
                value: textContent(item, 'VALUE').trim(),
            };
        })
        .reduce(
            (
                acc: Record<keyof typeof GOODS_COL_NAME, string>[],
                item: {
                    line: string;
                    tab: string;
                    name: keyof typeof GOODS_COL_NAME;
                    value: string;
                },
            ) => {
                //also row with tab === 0 it isn't goods table, it's requisites information
                if (!item.line || !item.name || item.tab === '0') {
                    return acc;
                }

                const index = Number(item.line);

                acc[index] = acc[index] || {};

                if (
                    isNotPriceOrQuantityColumns.includes(
                        item.name as typeof isNotPriceOrQuantityColumns[number],
                    )
                ) {
                    acc[index][item.name] = item.value;
                } else {
                    acc[index][item.name] = formatPriceWithCommaAndSpaces(
                        item.value,
                    );
                }

                return acc;
            },
            [],
        )
        .filter(Boolean);

    return {
        NUM: getTagValue(FIELDS.NUM),
        DOCDATE: getTagValue(FIELDS.DOCDATE).split(' ')[0],
        MISZE_SKL: getTagValue(FIELDS.MISZE_SKL),
        FIRM_NAME: getTagValue(FIELDS.FIRM_NAME),
        SIDE_CD_K: getTagValue(FIELDS.SIDE_CD_K),
        WARRANT_NUM: getTagValue(FIELDS.WARRANT_NUM),
        WARRANT_NAME: getTagValue(FIELDS.WARRANT_NAME),
        SUMWITHOUTPDV: replaceTagValueWithComma(FIELDS.SUMWITHOUTPDV),
        SUMPDV: replaceTagValueWithComma(FIELDS.SUMPDV),
        DOCSUM: replaceTagValueWithComma(FIELDS.DOCSUM),
        SIDE_OTV_POS: getTagValue(FIELDS.SIDE_OTV_POS),
        SIDE_OTV_FIO: getTagValue(FIELDS.SIDE_OTV_FIO),
        VO_POS: getTagValue(FIELDS.VO_POS),
        VO_NAME: getTagValue(FIELDS.VO_NAME),
        FIELD1: getTagValue(FIELDS.FIELD1),
        FIELD2: getTagValue(FIELDS.FIELD2),
        FIELD3: getTagValue(FIELDS.FIELD3),
        FIELD4: getTagValue(FIELDS.FIELD4),
        FIELD5: getTagValue(FIELDS.FIELD5),
        FIELD6: getTagValue(FIELDS.FIELD6),
        FIELD7: getTagValue(FIELDS.FIELD7),
        FIELD8: getTagValue(FIELDS.FIELD8),
        FIELD9: getTagValue(FIELDS.FIELD9),
        FIELD10: replaceTagValueWithComma(FIELDS.FIELD10),
        FIELD11: replaceTagValueWithComma(FIELDS.FIELD11),
        FIELD12: getTagValue(FIELDS.FIELD12),
        GOODS_ITEMS,
    } as const;
};

export type InvoiceData = ReturnType<typeof InvoiceParser>;

export default (data: { content: string }) => InvoiceParser(data.content);
