.headerTable {
    width: 100%;
    font-size: 90%;
}

.headerTable td {
    padding-right: 1em;
    padding-bottom: 1em;
    vertical-align: top;
}

.documentTitle {
    padding-bottom: 0.05cm;
    border-bottom: 0.075cm solid var(--content-color);
    margin-bottom: 0.4cm;
    font-size: 110%;
    font-weight: bold;
}

.preWrap {
    white-space: pre-wrap;
}

.amountText {
    font-size: 85%;
    line-height: 110%;
    white-space: pre-wrap;
}

.place {
    margin-top: 0.5cm;
    font-size: 85%;
    line-height: 110%;
}

.footerTable {
    width: 100%;
    margin-top: 0.4cm;
    font-size: 90%;
}

.footerTable thead th {
    width: 50%;
    font-weight: bold;
    text-align: left;
    vertical-align: top;
}

.footerTable tbody {
    font-size: 80%;
}

.footerTable tbody td {
    padding-right: 0.25cm;
    vertical-align: top;
}

@media (min-width: 720px) {
    .footerTable tbody td {
        padding-right: 1.25cm;
    }
}

.goodsTable {
    width: 100%;
    border: 0.075cm solid var(--content-color);
    margin-top: 0.1cm;
    margin-bottom: 0.1cm;
    border-collapse: collapse;
}

.goodsTable thead th {
    padding: 0.1cm;
    border: 0.05cm solid var(--content-color);
    background-color: #fcf9ea;
    font-size: 90%;
    font-weight: bold;
    text-align: center;
}

.goodsTable tbody td {
    padding: 0.1cm;
    border: 0.05cm solid var(--content-color);
    font-size: 75%;
    vertical-align: top;
}

.number {
    width: 4%;
}

.name {
    width: 40%;
}

.code,
.quantity,
.item,
.price,
.sum {
    width: 8%;
}

.totalTable {
    width: 100%;
    margin-top: 0.2cm;
    margin-bottom: 0.2cm;
}

.totalTable td {
    padding-left: 10px;
    font-size: 90%;
    font-weight: bold;
    text-align: right;
}

.totalName {
    width: 90%;
}

.textCenter {
    text-align: center;
}

.textRight {
    text-align: right;
}
