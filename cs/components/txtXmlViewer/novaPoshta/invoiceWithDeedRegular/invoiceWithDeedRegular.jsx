import React from 'react';

import PropTypes from 'prop-types';

import BreakLine from '../../ui/breakLine/breakLine';

import { XmlDocument } from '../../../../records/document';
import { XmlData } from '../../../../records/xml';
import { SERVICE_NAME_DEFAULT_VALUE } from '../constants';
import DeedRegular from '../deedRegular/deedRegular';
import Invoice from '../invoice/invoice';

// styles
import css from './invoiceWithDeedRegular.css';

const InvoiceWithDeedRegular = ({
    data,
    doc,
    renderSignatures,
    renderReviews,
}) => {
    return (
        <div>
            <div className={css.doc}>
                <Invoice
                    isAgreementExtraName
                    detailed
                    data={data}
                    doc={doc}
                    serviceName={
                        data.extra.invoiceServiceName ||
                        SERVICE_NAME_DEFAULT_VALUE
                    }
                    renderSignatures={renderSignatures}
                    renderReviews={renderReviews}
                />
            </div>
            <BreakLine />
            <div className={css.doc}>
                <DeedRegular
                    isAgreementExtraName
                    data={data}
                    doc={doc}
                    renderSignatures={renderSignatures}
                    renderReviews={renderReviews}
                />
            </div>
        </div>
    );
};

InvoiceWithDeedRegular.propTypes = {
    renderSignatures: PropTypes.bool,
    renderReviews: PropTypes.bool,
    data: PropTypes.instanceOf(XmlData).isRequired,
    doc: PropTypes.instanceOf(XmlDocument).isRequired,
};

export default InvoiceWithDeedRegular;
