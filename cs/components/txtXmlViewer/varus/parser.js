import I from 'immutable';
import moment from 'moment';

import { parseTableData, textContent } from '../utils';

import {
    XmlBank,
    XmlCompany,
    XmlData,
    XmlProduct,
    XmlProducts,
} from '../../../records/xml';

const XmlExtra = new I.Record({
    parameters: null,
});

export const parseXmlService = (serviceXml) => {
    const $ = (query, logEmptyText) =>
        textContent(serviceXml, query, logEmptyText);
    return new XmlProduct({
        number: $('НомПоз'),
        name: $('Найменування'),
    });
};

export const parseXmlServices = (xml) => {
    const serviceXmls = parseTableData(xml, 'Таблиця', 'Рядок');
    const items = new I.List(serviceXmls.map(parseXmlService));

    return new XmlProducts({
        items,
    });
};

export const parseXmlCompany = (companyXml) => {
    const $ = (query, logEmptyText) =>
        textContent(companyXml, query, logEmptyText);
    const index = $('ЮрАдреса > Індекс');
    const city = $('ЮрАдреса > Місто');
    const street = $('ЮрАдреса > Вулиця');

    return new XmlCompany({
        status: $('СтатусКонтрагента'),
        name: $('НазваКонтрагента'),
        edrpou: $('КодКонтрагента'),
        ipn: $('ІПН'),
        certificate: $('СвідоцтвоПДВ'),
        phone: $('Телефон'),
        address: [index, city, street].filter(Boolean).join(', '),
        bank: new XmlBank({
            iban: $('IBAN'),
            account: $('ПоточРах'),
            mfo: $('МФО'),
        }),
    });
};

export const parseXmlParties = (xml) => {
    const partiesXmls = parseTableData(xml, 'Сторони', 'Контрагент');
    return new I.List(partiesXmls.map(parseXmlCompany));
};

export const parseXmlExtra = (xml) => {
    const parameters = [];
    xml.querySelectorAll('Параметри > Параметр').forEach((param) =>
        parameters.push({
            name: param.attributes['назва']?.value,
            value: param.textContent,
        }),
    );
    return new XmlExtra({
        parameters,
    });
};

export const parseXml = (xml) => {
    const $ = (query, logEmptyText) => textContent(xml, query, logEmptyText);
    const date = $('Заголовок > ДатаДокументу').trim();

    return new XmlData({
        date: date && moment(date),
        place: $('Заголовок > МісцеСкладання'),
        number: $('Заголовок > НомерДокументу'),
        kind: $('Заголовок > КодТипуДокументу'),
        parties: parseXmlParties(xml),
        services: parseXmlServices(xml, $),
        extra: parseXmlExtra(xml, $),
    });
};

export default parseXml;
