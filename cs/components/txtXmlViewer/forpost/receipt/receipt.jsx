import React from 'react';

import PropTypes from 'prop-types';

import { formatTotalPrice } from '../../utils';

import SignLine from '../../ui/signLine/signLine';

import { formatDate } from '../../../../lib/date';
import { XmlData } from '../../../../records/xml';
import Company from '../../company/company';
import { DATE_FORMAT } from '../../constants';
import ProductsTable from '../../products/productsTable';
import { DATE_FORMAT_FULL, NUMBER_REGEXP } from '../constants';

import css from './receipt.css';

const Deed = ({ data }) => {
    const { agreementName, date, number, owner, partner, services } = data;
    const shortDate = formatDate(date, DATE_FORMAT);
    const shortNumber = number.replace(NUMBER_REGEXP, '');

    return (
        <div className={css.root}>
            <table className={css.headerTable}>
                <tbody>
                    <tr>
                        <td>ЗАТВЕРДЖУЮ</td>
                        <td>ЗАТВЕРДЖУЮ</td>
                    </tr>
                    <tr>
                        <td>{partner.representative}</td>
                        <td>{owner.representative}</td>
                    </tr>
                    <tr>
                        <td>{partner.fullName}</td>
                        <td>{owner.fullName}</td>
                    </tr>
                    <tr>
                        <td>
                            <SignLine />
                        </td>
                        <td>
                            <SignLine />
                        </td>
                    </tr>
                </tbody>
            </table>

            <h1 className={css.documentTitle}>АКТ надання послуг</h1>
            <h2 className={css.documentTitle}>
                № {shortNumber} від {formatDate(date, DATE_FORMAT_FULL)} р.
            </h2>
            <div className={css.separator} />

            <div className={css.documentTrivia}>
                Ми, що нижче підписалися, представники Замовника{' '}
                {owner.fullName} {owner.representative}, з одного боку, і
                представник Виконавця {partner.fullName}{' '}
                {partner.representative}, з іншого боку, склали цей акт про те,
                що на підставі наведених документів:
            </div>
            <table className={css.documentDetailsTable}>
                <tbody>
                    <tr>
                        <td className={css.detailName}>Договір:</td>
                        <td>{agreementName}</td>
                    </tr>
                </tbody>
            </table>

            <div className={css.documentTrivia}>
                Виконавцем були виконані наступні роботи (надані таки послуги):
            </div>
            <ProductsTable
                withoutTaxes
                data={services}
                serviceNameLabel="Найменування робіт, послуг"
            />

            <div className={css.documentTrivia}>
                Загальна вартість робіт (послуг) склала{' '}
                {formatTotalPrice(services, 'totalSum')}.
            </div>
            <div className={css.documentTrivia}>
                Замовник претензій по об'єму, якості та строкам виконання робіт
                (надання послуг) не має.
            </div>
            <div className={css.separator} />

            <table className={css.footerTable}>
                <thead>
                    <tr>
                        <th>
                            Від Виконавця*
                            <br />
                        </th>
                        <th>
                            Від Замовника
                            <br />
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <SignLine />
                        </td>
                        <td>
                            <SignLine />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <b>{partner.representative}</b>
                        </td>
                        <td>
                            <b>{owner.representative}</b>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            * Відповідальний за здійснення господарської
                            операції і правильність її оформлення
                        </td>
                        <td />
                    </tr>
                    <tr>
                        <td className={css.footerDate}>
                            <b>{shortDate}</b>
                        </td>
                        <td className={css.footerDate}>
                            <b>{shortDate}</b>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <Company data={partner} />
                        </td>
                        <td>
                            <Company data={owner} />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
};

Deed.propTypes = {
    data: PropTypes.instanceOf(XmlData).isRequired,
};

export default Deed;
