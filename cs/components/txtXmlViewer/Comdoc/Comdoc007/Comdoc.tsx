import React, { FC } from 'react';

import { Props } from './../types';

import { capitalizeFirstLetter } from '../../../../lib/helpers';
import { generateUATextMoneyRepresentation } from '../../utils';

import SignLine from '../../ui/signLine/signLine';

import { formatDate } from '../../../../lib/date';
import { formatPrice } from '../../../../lib/numbers';
import uuid from '../../../../services/uuid';
import { DATE_FORMAT_FULL, FlowVarusEdrpouList } from './../constants';
import { coalesceJoin, getIsCompanyXmlOwnerByEdrpou } from './../helper';

import css from './../Comdoc.css';

/* eslint-disable complexity */
const ComdocDefault: FC<React.PropsWithChildren<Props>> = (props) => {
    const {
        date,
        number,
        owner,
        partner,
        services,
        agreementName,
        agreementDate,
        documentBasis,
        documentBasisDate,
        agreementPlace,
        parentType,
        parentNumber,
        parentDate,
        parentOrderNumber,
        parentOrderDate,
        parentOrderDatePayment,
        documentType,
        compositionChecked,
        shipped,
        fromPerformer,
        deliveryAddress,
    } = props.data;

    const isVarusOwner = FlowVarusEdrpouList.find((edrpouItem) =>
        getIsCompanyXmlOwnerByEdrpou(edrpouItem, props.data),
    );

    const signerBuyerName = isVarusOwner ? 'Покупець' : 'Від Продавця*';
    const signerSellerName = isVarusOwner ? 'Продавець' : 'Отримав(ла)';

    const totalAmountString =
        services?.totalDocumentSum &&
        capitalizeFirstLetter(
            generateUATextMoneyRepresentation(services?.totalDocumentSum),
        );
    const totalAmountContent = totalAmountString
        ? `(${totalAmountString})`
        : '';

    const isAltQuantityExist =
        (services?.items?.filter((item) => !!item?.altQuantity) || []).length >
        0;
    const isAltPriceWithoutTaxExist =
        (services?.items?.filter((item) => !!item?.altPriceWithoutTax) || [])
            .length > 0;

    return (
        <div>
            <h1 className={css.documentTitle}>
                {documentType} № {number} від{' '}
                {formatDate(date, DATE_FORMAT_FULL)} р.
            </h1>
            <table className={css.headerTable}>
                <tbody>
                    <tr>
                        <td>{partner?.status}:</td>
                        <td>
                            {coalesceJoin(
                                <b>{partner?.fullName}</b>,
                                partner?.edrpou && (
                                    <>код ЄДРПОУ {partner?.edrpou}</>
                                ),
                                partner?.ipn && <>IПН {partner?.ipn}</>,
                                partner?.bank?.account && (
                                    <>П/р {partner?.bank?.account}</>
                                ),
                                partner?.bank?.mfo && (
                                    <>МФО {partner?.bank?.mfo}</>
                                ),
                                partner?.ipn && (
                                    <>Cвідоцтво ПДВ {partner?.certificateVAT}</>
                                ),
                            )}
                        </td>
                    </tr>
                    <tr>
                        <td>{owner?.status}:</td>
                        <td>
                            {coalesceJoin(
                                <b>{owner?.fullName}</b>,
                                owner?.edrpou && (
                                    <>код ЄДРПОУ {owner?.edrpou}</>
                                ),
                                owner?.ipn && <>IПН {owner?.ipn}</>,
                                owner?.bank?.account && (
                                    <>П/р {owner?.bank?.account}</>
                                ),
                                owner?.bank?.mfo && <>МФО {owner?.bank?.mfo}</>,
                                owner?.ipn && (
                                    <>Cвідоцтво ПДВ {owner?.certificateVAT}</>
                                ),
                            )}
                        </td>
                    </tr>
                    <tr>
                        <td>Договір:</td>
                        <td>
                            {agreementName || documentBasis} від{' '}
                            {formatDate(agreementDate || documentBasisDate)}
                        </td>
                    </tr>
                    {parentOrderNumber && (
                        <tr>
                            <td>Замовлення:</td>
                            <td>
                                Замовлення покупця № {parentOrderNumber}{' '}
                                {parentOrderDate && `від ${parentOrderDate}`}
                            </td>
                        </tr>
                    )}
                    {deliveryAddress && (
                        <tr>
                            <td>Адреса доставки:</td>
                            <td>{deliveryAddress}</td>
                        </tr>
                    )}
                    <tr>
                        <td>Документ підстава:</td>
                        <td>
                            {parentType} № {parentNumber} від{' '}
                            {formatDate(parentDate)}
                        </td>
                    </tr>
                    {parentOrderDatePayment && (
                        <tr>
                            <td>Дата оплати:</td>
                            <td>{parentOrderDatePayment}</td>
                        </tr>
                    )}
                    {agreementPlace && (
                        <tr>
                            <td>Місце складання:</td>
                            <td>{agreementPlace}</td>
                        </tr>
                    )}
                </tbody>
            </table>
            <table className={css.goodsTable}>
                <thead>
                    <tr>
                        <th className={css.number}>№</th>
                        <th>Артикул</th>
                        <th className={css.name}>Товар</th>
                        {props.inconsistencyAct && props.disagreementName && (
                            <th className={css.number}>
                                Кількість по накладній
                            </th>
                        )}
                        <th className={css.quantity}>К-сть</th>
                        {props.notAcceptedQuantity && (
                            <th className={css.quantity}>
                                Не прийнята кількість
                            </th>
                        )}
                        {props.inconsistencyAct && (
                            <th className={css.quantity}>Розбіжність</th>
                        )}
                        {props.inconsistencyAct && (
                            <th className={css.item}>Причина</th>
                        )}
                        <th className={css.item}>Од.</th>
                        <th className={css.price}>Ціна без ПДВ</th>
                        <th className={css.sum}>Сума без ПДВ</th>

                        {isAltQuantityExist && (
                            <th className={css.price}>Кіл-сть в КГ</th>
                        )}
                        {isAltPriceWithoutTaxExist && (
                            <th className={css.price}>Ціна за КГ без ПДВ</th>
                        )}
                    </tr>
                </thead>
                <tbody>
                    {services?.items?.map((service, index) => (
                        <tr key={`product-${uuid()}`}>
                            <td className={css.textCenter}>{index + 1}</td>
                            <td>{service.code}</td>
                            <td>{service.name}</td>
                            {props.inconsistencyAct &&
                                props.disagreementName && (
                                    <td>{service.declaredQuantity}</td>
                                )}
                            <td className={css.textRight}>
                                {service.quantity}
                            </td>
                            {props.notAcceptedQuantity && (
                                <td className={css.textRight}>
                                    {service.notAcceptedQuantity}
                                </td>
                            )}
                            {props.inconsistencyAct && (
                                <td className={css.textRight}>
                                    {service.notAcceptedQuantity}
                                </td>
                            )}
                            {props.inconsistencyAct && (
                                <td className={css.textRight}>
                                    {service.reason}
                                </td>
                            )}
                            <td className={css.textRight}>{service.unit}</td>
                            <td className={css.textRight}>
                                {formatPrice(service.price)}
                            </td>
                            <td className={css.textRight}>
                                {formatPrice(service.sum)}
                            </td>

                            {isAltQuantityExist && (
                                <td className={css.textRight}>
                                    {formatPrice(service.altQuantity)}
                                </td>
                            )}
                            {isAltPriceWithoutTaxExist && (
                                <td className={css.textRight}>
                                    {formatPrice(service.altPriceWithoutTax)}
                                </td>
                            )}
                        </tr>
                    ))}
                </tbody>
            </table>
            <table className={css.totalTable}>
                <tbody>
                    <tr>
                        <td className={css.totalName}>Разом:</td>
                        <td>{formatPrice(services?.totalSum)}</td>
                    </tr>
                    <tr>
                        <td className={css.totalName}>Сума ПДВ:</td>
                        <td>{formatPrice(services?.totalTaxes)}</td>
                    </tr>
                    <tr>
                        <td className={css.totalName}>Усього з ПДВ:</td>
                        <td>{formatPrice(services?.totalDocumentSum)}</td>
                    </tr>
                </tbody>
            </table>
            <div className={css.amountText}>
                Всього на суму {formatPrice(services?.totalDocumentSum)} грн{' '}
                {totalAmountContent}
            </div>

            {props.inconsistencyAct && props.disagreementName && (
                <div className={css.place}>
                    Акт є невід'ємною частиною накладної № {parentNumber} від{' '}
                    {formatDate(parentDate)}
                </div>
            )}

            <SignLine full />
            <table className={css.footerTable}>
                <tbody>
                    {props.inconsistencyAct && (
                        <>
                            <tr>
                                <td>{partner?.fullName}</td>
                                <td>{owner?.fullName}</td>
                            </tr>
                            <tr>
                                <td>
                                    {partner?.edrpou && (
                                        <>код ЄДРПОУ {partner?.edrpou}</>
                                    )}
                                </td>
                                <td>
                                    {owner?.edrpou && (
                                        <>код ЄДРПОУ {owner?.edrpou}</>
                                    )}
                                </td>
                            </tr>
                        </>
                    )}

                    <tr>
                        <td>
                            * Відповідальний за здійснення господарської
                            операції і правильність її оформлення
                        </td>
                        <td />
                    </tr>
                    {fromPerformer && (
                        <>
                            <tr>
                                <br />
                            </tr>
                            <tr>
                                <td>Від виконавця: {fromPerformer}</td>
                                <td />
                            </tr>
                        </>
                    )}
                </tbody>
            </table>

            {compositionChecked && (
                <table className={css.footerTable}>
                    <thead>
                        <tr>
                            <th>Перевірено склад: {compositionChecked}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <SignLine />
                            </td>
                        </tr>
                    </tbody>
                </table>
            )}

            {shipped && (
                <table className={css.footerTable}>
                    <thead>
                        <tr>
                            <th>Відвантажено: {shipped}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <SignLine />
                            </td>
                        </tr>
                    </tbody>
                </table>
            )}

            <table className={css.footerTable}>
                <thead>
                    <tr>
                        <th>
                            {signerBuyerName}
                            <br />
                        </th>
                        <th>
                            {signerSellerName}
                            <br />
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    );
};

export default ComdocDefault;
