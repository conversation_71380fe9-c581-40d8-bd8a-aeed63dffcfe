.table {
    width: 100%;
    border: 0.075cm solid var(--content-color);
    margin-top: 0.1cm;
    margin-bottom: 0.1cm;
    border-collapse: collapse;
    table-layout: fixed;
}

.table thead th {
    padding: 0.1cm 0;
    border: 0.05cm solid var(--content-color);
    background-color: #fcf9ea;
    font-size: 100%;
    font-weight: bold;
    text-align: center;
}

.table tbody td {
    padding: 0.1cm;
    border: 0.05cm solid var(--content-color);
    font-size: 100%;
    vertical-align: top;
    word-wrap: break-word;
}

.table i {
    font-style: italic;
}

.number {
    width: 6%;
}

.code {
    width: 9%;
}

.name {
    width: 23%;
}

.trademark {
    width: 14%;
}

.size {
    width: 14%;
}

.price {
    width: 10%;
}

.totalTable {
    width: 100%;
    margin-top: 0.2cm;
    margin-bottom: 0.2cm;
}

.totalTable td {
    padding-left: 10px;
    font-size: 90%;
    font-weight: bold;
    text-align: right;
}

.totalName {
    width: 90%;
}

.textCenter {
    text-align: center;
}

.textRight {
    text-align: right;
}

@media screen and (max-width: 750px) {
    .table thead th {
        font-size: 90%;
    }

    .table tbody td {
        font-size: 80%;
    }
}

@media screen and (max-width: 680px) {
    .table thead th {
        font-size: 80%;
    }

    .table tbody td {
        font-size: 70%;
    }
}
