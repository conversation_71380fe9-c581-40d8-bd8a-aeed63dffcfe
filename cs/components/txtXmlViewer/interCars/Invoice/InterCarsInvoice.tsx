import React from 'react';

import { DATE_FORMAT } from 'lib/constants';
import { formatDate } from 'lib/date';
import { formatPrice } from 'lib/numbers';

import { XmlTemplateProps } from './types';

import FooterRenderer from '../../../footerRenderer/footerRenderer';

import css from './InterCarsInvoice.css';

const InterCarsInvoice: React.FC<XmlTemplateProps> = ({
    data,
    renderSignatures = false,
    renderReviews = false,
    doc,
}) => {
    const {
        date,
        number,
        seller,
        buyer,
        parameters,
        items,
        pdv,
        sumWithoutPdv,
        sum,
    } = data;

    return (
        <div>
            <div className={css.header}>Накладна № {number}</div>
            <table>
                <thead>
                    <tr>
                        <td>
                            <div className={css.pageHeaderSpace} />
                        </td>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div>
                                <table className={css.sellerTable}>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                <b>
                                                                    Постачальник:
                                                                </b>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <p>
                                                                    {
                                                                        seller.name
                                                                    }
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <p>
                                                                    {
                                                                        seller.address
                                                                    }
                                                                </p>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                            <td>
                                                <img
                                                    width="182"
                                                    height="67"
                                                    src={`${config.STATIC_HOST}/images/txt_xml_viewer/inter_cars/inter_cars_logo.png`}
                                                    alt="logo"
                                                />
                                            </td>
                                            <td>
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                <b>
                                                                    Умови
                                                                    поставки:
                                                                </b>
                                                            </td>
                                                            <td>
                                                                {
                                                                    parameters.termsDelivery
                                                                }
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <b>Маршрут:</b>
                                                            </td>
                                                            <td>
                                                                {
                                                                    parameters.route
                                                                }
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <b>
                                                                    Час
                                                                    доставки:
                                                                </b>
                                                            </td>
                                                            <td>
                                                                {
                                                                    parameters.deliveryTime
                                                                }
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                <b>Тел.:</b> {seller.phone};{' '}
                                                <b>Факс:</b> {seller.fax};{' '}
                                                <b>Web-сайт:</b> {seller.web};{' '}
                                                <b>E-mail:</b> {seller.email}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                IBAN: {seller.iban}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                Код ЄДРПОУ: {seller.edrpou};
                                                ІПН: {seller.ipn};
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                <b>Відпустив:</b>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                Філія:{' '}
                                                {parameters.affiliateName}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                {parameters.affiliateAddress};
                                                Тел.:{' '}
                                                {parameters.affiliatePhone}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="center" colSpan={3}>
                                                {parameters.textNds}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div className={css.title}>
                                    Накладна № {number} від{' '}
                                    {formatDate(date, DATE_FORMAT)}
                                </div>
                                <table className={css.buyerTable}>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <b>Код одержувача:</b>
                                            </td>
                                            <td>{buyer.code}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Одержувач:</b>
                                            </td>
                                            <td>{buyer.name}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Договір №:</b>
                                            </td>
                                            <td>{parameters.contractText}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Код ЄДРПОУ:</b>
                                            </td>
                                            <td>{buyer.edrpou}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Адреса:</b>
                                            </td>
                                            <td>{buyer.address}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Тел.:</b>
                                            </td>
                                            <td>{buyer.phone}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Замовл. №:</b>
                                            </td>
                                            <td>{parameters.orderText}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Оплатити до:</b>
                                            </td>
                                            <td>
                                                <table
                                                    className={
                                                        css.buyerTableInnerTable
                                                    }
                                                >
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                {
                                                                    parameters.payBy
                                                                }
                                                            </td>
                                                            <td>
                                                                <b>
                                                                    Через кого:
                                                                </b>
                                                            </td>
                                                            <td>
                                                                {
                                                                    parameters.throughWhom
                                                                }
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Форма оплати:</b>
                                            </td>
                                            <td>
                                                <table
                                                    className={
                                                        css.buyerTableInnerTable
                                                    }
                                                >
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                {
                                                                    parameters.paymentForm
                                                                }
                                                            </td>
                                                            <td>
                                                                <b>
                                                                    Довіреність
                                                                    №:
                                                                </b>
                                                            </td>
                                                            <td>
                                                                {
                                                                    parameters.powerAttorneyText
                                                                }
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <table className={css.goodsTable}>
                                    <thead>
                                        <tr>
                                            <th
                                                className={
                                                    css.goodsTableItemNumber
                                                }
                                            >
                                                №
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemCode
                                                }
                                            >
                                                Код товару
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemName
                                                }
                                            >
                                                Назва товару
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemUnit
                                                }
                                            >
                                                Од. вим.
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemAmount
                                                }
                                            >
                                                Кіл-ть
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemPriceWithoutPdv
                                                }
                                            >
                                                Ціна без ПДВ
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemPdv
                                                }
                                            >
                                                ПДВ, грн.
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemPricePdv
                                                }
                                            >
                                                Ціна з ПДВ
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemSumPdv
                                                }
                                            >
                                                Сума з ПДВ
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {items?.map((item, index) => (
                                            <tr key={item.article}>
                                                <td>{index + 1}</td>
                                                <td>{item.article}</td>
                                                <td>{item.name}</td>
                                                <td>{item.unit}</td>
                                                <td>{item.amount}</td>
                                                <td>
                                                    {formatPrice(
                                                        item.basePrice,
                                                    )}
                                                </td>
                                                <td>{formatPrice(item.pdv)}</td>
                                                <td>
                                                    {formatPrice(item.price)}
                                                </td>
                                                <td>{formatPrice(item.sum)}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>

                                <table className={css.goodsTableFooter}>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <b>Всього без ПДВ, грн.:</b>
                                            </td>
                                            <td>
                                                {formatPrice(sumWithoutPdv)}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>ПДВ, грн.:</td>
                                            <td>{formatPrice(pdv)}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Всього Вкл. ПДВ, грн.:</b>
                                            </td>
                                            <td>{formatPrice(sum)}</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <table className={css.totalSumTable}>
                                    <tbody>
                                        <tr>
                                            <td width="20%">Всього на суму:</td>
                                            <td>{parameters.amountWords}</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <table className={css.signaturesTable}>
                                    <colgroup>
                                        <col width="47%" />
                                        <col width="15%" />
                                        <col width="17%" />
                                        <col width="21%" />
                                    </colgroup>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <b>Місце складання:</b>{' '}
                                                {parameters.assemblyPlace}
                                            </td>
                                            <td />
                                            <td>
                                                <b>Відповідальний:</b>
                                            </td>
                                            <td>{parameters.responsible}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Замовлення виконав:</b>{' '}
                                                {parameters.fulfilledOrder}
                                            </td>
                                            <td colSpan={3} />
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>
                                                    Товар відпустив завскладом:
                                                </b>{' '}
                                                {parameters.productReleased}
                                            </td>
                                            <td align="center">
                                                <p>__________</p>
                                                <p>(підпис)</p>
                                            </td>
                                            <td>
                                                <b>Товар отримав:</b>
                                            </td>
                                            <td>
                                                <p>
                                                    {parameters.productReceived}
                                                    {parameters.productReceived && (
                                                        <br />
                                                    )}
                                                    __________
                                                </p>
                                                <p>(підпис)</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                                <table className={css.attentionTable}>
                                    <colgroup>
                                        <col width="15%" />
                                        <col width="70%" />
                                        <col width="15%" />
                                    </colgroup>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <img
                                                    width="40"
                                                    // height="67"
                                                    src={`${config.STATIC_HOST}/images/txt_xml_viewer/inter_cars/logo_PrivatBank.png`}
                                                    alt="privat bank logo"
                                                />
                                                <p>INTER CARS UKRAINE ТОВ</p>
                                                <p>
                                                    <b>за товар</b>
                                                </p>
                                            </td>
                                            <td align="center">
                                                <b>Шановні покупці!</b>
                                            </td>
                                            <td />
                                        </tr>
                                        <tr>
                                            <td align="center">
                                                <div
                                                    className={
                                                        css.attentionTableQrCode
                                                    }
                                                >
                                                    <img
                                                        width="60"
                                                        src={`${config.STATIC_HOST}/images/txt_xml_viewer/inter_cars/ICUA-Privat-QR.png`}
                                                        alt="qr code"
                                                    />
                                                </div>
                                            </td>
                                            <td>
                                                <p>
                                                    Придбаний товар можна
                                                    повернути або обміняти
                                                    протягом 14-ти календарних
                                                    днів з моменту покупки за
                                                    наявності документів на
                                                    придбання, крім товарів,
                                                    придбаних під замовлення.
                                                </p>
                                                <p>
                                                    Будь-ласка, в призначенні
                                                    платежу вкажіть номер
                                                    клієнта з самого початку:{' '}
                                                    {buyer.code} за товари
                                                    згідно дог. Автозапчастини
                                                </p>
                                            </td>
                                            <td />
                                        </tr>
                                    </tbody>
                                </table>

                                {(renderSignatures || renderReviews) && (
                                    <div
                                        className={css.digitalSignaturesWrapper}
                                    >
                                        <FooterRenderer
                                            renderSignatures={renderSignatures}
                                            renderReviews={renderReviews}
                                            doc={doc}
                                        />
                                    </div>
                                )}
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
};

export default InterCarsInvoice;
