import { AntivirusStatusTypes } from 'services/antivirus/enums';
import { Document } from 'services/documents/ts/types';
import { t } from 'ttag';

import { AntivirusSettings } from '../../types/user';
import { DocumentListDocumentItem } from '../documentList/types';

import { AntivirusPopupState } from './AntivirusPopupReducer';

// for `clean` and `encrypted` statuses we don't need this popup
const getCheckedDocument = (
    doc: Document,
    antivirusConfig: AntivirusSettings | undefined,
) =>
    doc.antivirusChecks?.[0]?.status === AntivirusStatusTypes.infected
        ? antivirusConfig?.is_download_infected_enabled
        : antivirusConfig?.is_download_pending_enabled;

const getCheckedDocuments = (
    docs: AntivirusPopupState['docs'],
    antivirusConfig: AntivirusSettings | undefined,
) => docs.filter((doc: Document) => getCheckedDocument(doc, antivirusConfig));

export const getIsUserHasAccessForDownload = (
    docs: AntivirusPopupState['docs'] | undefined,
    antivirusConfig: AntivirusSettings | undefined,
) =>
    docs
        ? getCheckedDocuments(docs, antivirusConfig).length === docs.length
        : null;

export const getPopupMainText = (
    isAdmin: boolean,
    isUserHasAccessForDownload: Nullable<boolean>,
) => {
    if (isUserHasAccessForDownload) {
        return t`Завантаження почнеться автоматично через 5 секунд, ви ще маєте можливість відмінити завантаження потенційно небезпечного документа`;
    }

    if (isAdmin) {
        return t`Ви не можете завантажити потенційно небезпечні документи. Щоб отримати цю можливість, перейдіть до налаштувань.`;
    }

    return t`Ви не можете завантажити потенційно небезпечні документи. Щоб отримати цю можливість, будь ласка, зверніться до адміністратора вашої компанії.`;
};

export const getIsDangerousDoc = (doc: DocumentListDocumentItem | Document) =>
    doc.antivirusChecks?.[0] &&
    doc.antivirusChecks[0].status !== AntivirusStatusTypes.clean &&
    doc.antivirusChecks[0].status !== AntivirusStatusTypes.encrypted;
