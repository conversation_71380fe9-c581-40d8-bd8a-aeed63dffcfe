import { DocumentListDocumentItem } from 'components/documentList/types';
import { AnyAction } from 'redux';

import actions from './AntivirusPopupActions';

const initState = {
    docs: [] as DocumentListDocumentItem[],
    action: '',
    isOpen: false,
    submitAction: null,
    href: '',
    isAsic: false,
    docVersionId: '',
    isVisualisation: false,
};

export type AntivirusPopupState = typeof initState;

const antivirusPopupReducer = (state = initState, action: AnyAction) => {
    switch (action.type) {
        case actions.ANTIVIRUS_POPUP__SHOW:
            return {
                ...state,
                docs: action.docs,
                action: action.action,
                isAsic: action.isAsic || initState.isAsic,
                href: action.href || initState.href,
                docVersionId: action.docVersionId || initState.docVersionId,
                isVisualisation:
                    action.isVisualisation || initState.isVisualisation,
                isOpen: true,
            };
        case actions.ANTIVIRUS_POPUP__HIDE:
            return {
                ...initState,
            };
        default:
            return state;
    }
};

export default antivirusPopupReducer;
