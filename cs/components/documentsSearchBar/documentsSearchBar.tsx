import React from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';

import { getIsArchivePage } from 'selectors/router.selectors';

import { SearchOptions } from './types';

import {
    getLocationQueryFromOptions,
    getOptionsFromLocationQuery,
} from './utils';

import { getLocationQuery, stringifyLocationQuery } from '../../lib/url';
import SearchBar from './searchBar/searchBar';

const PATH_TO_DOCUMENTS_REDIRECT = '/app/documents';
const PATH_TO_ARCHIVE_REDIRECT = '/app/archive';

const DocumentsSearchBar: React.FC<React.PropsWithChildren<unknown>> = () => {
    const history = useHistory();
    const location = useLocation();
    const locationQuery = getLocationQuery(location);
    const isArchivePage = useSelector(getIsArchivePage);

    // We need to use this workaround
    // because of implicit serialization/parsing of query string strategy differences
    const options: SearchOptions = getOptionsFromLocationQuery(locationQuery);

    const setOptions = (searchOptions: SearchOptions) => {
        const query = getLocationQueryFromOptions(searchOptions);

        history.push({
            pathname: isArchivePage
                ? PATH_TO_ARCHIVE_REDIRECT
                : PATH_TO_DOCUMENTS_REDIRECT,
            search: stringifyLocationQuery({
                ...locationQuery,
                ...query,
                page: undefined,
            }),
        });
    };

    return <SearchBar options={options} setOptions={setOptions} />;
};

export default DocumentsSearchBar;
