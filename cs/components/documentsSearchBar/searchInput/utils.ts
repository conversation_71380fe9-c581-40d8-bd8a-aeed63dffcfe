import { t } from 'ttag';

import { SearchKey, SearchOptions } from '../types';

type TextOptions = Record<SearchKey, string[]>;

export const convertSearchOptionsToText = (
    options: SearchOptions,
): TextOptions => {
    return {
        search: options.search,
        titles: options.titles,
        numbers: options.numbers,
        tags: options.tags,
        companyNames: options.companyNames,
        companyEdrpous: options.companyEdrpous,
        userEmails: options.userEmails,
        parameters: options.parameters.map((p) =>
            p.isNotSet ? t`Пусте значення` : p.value,
        ),
    };
};
