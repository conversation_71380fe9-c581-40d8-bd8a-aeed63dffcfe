import React, { ChangeEvent } from 'react';

import { Datepicker } from '@vchasno/ui-kit';

import { QUERY_DATE_FORMAT } from 'lib/constants';
import { formatDate } from 'lib/date';
import { DocumentField } from 'services/documentFields/types';
import { t } from 'ttag';

import Dropdown from '../../ui/dropdown/dropdown';
import Input from '../../ui/input/input';

import InputWrapper from '../inputWrapper/inputWrapper';

import css from './ParameterInput.css';

interface Props {
    name: string;
    value: string;
    field: DocumentField;
    onChange: (value: string) => void;
    placeholder: string;
    onRemove: () => void;
    onAdd: () => void;
    disabled: boolean;
}

const convertFieldEnumToOptions = (field: DocumentField) => {
    return (field.enumOptions || []).map((value) => ({ value, label: value }));
};

const ParameterInput = ({
    name,
    value,
    field,
    onChange,
    placeholder,
    onRemove,
    onAdd,
    disabled,
}: Props) => {
    const handleInputChange = (evt: ChangeEvent<HTMLInputElement>) => {
        return onChange(evt.currentTarget.value);
    };

    const handleDateChange = (date: Date) => {
        return onChange(formatDate(date, QUERY_DATE_FORMAT) || '');
    };

    return (
        <InputWrapper onAdd={onAdd} onRemove={onRemove}>
            {(field.type === 'text' || field.type === 'number') && (
                <Input
                    type={field.type}
                    name={name}
                    placeholder={placeholder}
                    value={value}
                    onChange={handleInputChange}
                    disabled={disabled}
                />
            )}
            {field.type === 'date' && (
                <Datepicker
                    className={css.datepicker}
                    wide
                    label={t`Оберіть дату`}
                    selected={value ? new Date(value) : null}
                    onChange={handleDateChange}
                    showIcon={false}
                    hideEmptyMeta
                    isClearable={false}
                    disabled={disabled}
                />
            )}
            {field.type === 'enum' && (
                <Dropdown
                    size="big"
                    showIcon={false}
                    value={value}
                    options={convertFieldEnumToOptions(field)}
                    onChange={onChange}
                    disabled={disabled}
                />
            )}
        </InputWrapper>
    );
};

export default ParameterInput;
