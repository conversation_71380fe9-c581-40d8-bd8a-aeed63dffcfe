import React from 'react';

import { FlexBox, Paragraph, Text } from '@vchasno/ui-kit';

import emptySrc from 'components/DocumentCreationFavoriteTemplates/images/favorite-empty.png';
import { t } from 'ttag';

const EmptyScreen: React.FC = () => {
    return (
        <FlexBox
            direction="column"
            justify="center"
            align="center"
            style={{
                maxWidth: 500,
                margin: '0 auto',
                minHeight: 'calc(100vh - 200px)',
            }}
            gap={32}
        >
            <img src={emptySrc} alt="Вчасно" width={140} height={140} />
            <FlexBox direction="column" gap={12} align="center">
                <Text strong style={{ fontSize: 16 }}>{t`Галерея Вчасно`}</Text>
                <Paragraph textAlign="center">{t`Команда Вчасно працює над підготовкою публічних шаблонів, які будуть опубліковані найближчим часом`}</Paragraph>
            </FlexBox>
        </FlexBox>
    );
};

export default EmptyScreen;
