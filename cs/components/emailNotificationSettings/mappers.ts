import { t } from 'ttag';

import { RoleEmailSettings } from '../../types/user';

export const emailSettingToApiPayloadKey: Record<RoleEmailSettings, string> = {
    canReceiveInbox: 'can_receive_inbox',
    canReceiveInboxAsDefault: 'can_receive_inbox_as_default',
    canReceiveComments: 'can_receive_comments',
    canReceiveRejects: 'can_receive_rejects',
    canReceiveReviews: 'can_receive_reviews',
    canReceiveReminders: 'can_receive_reminders',
    canReceiveAccessToDoc: 'can_receive_access_to_doc',
    canReceiveDeleteRequests: 'can_receive_delete_requests',
    canReceiveReviewProcessFinished: 'can_receive_review_process_finished',
    canReceiveReviewProcessFinishedAssigner:
        'can_receive_review_process_finished_assigner',
    canReceiveSignProcessFinished: 'can_receive_sign_process_finished',
    canReceiveSignProcessFinishedAssigner:
        'can_receive_sign_process_finished_assigner',
    canReceiveFinishedDocs: 'can_receive_finished_docs',
    canReceiveNotifications: 'can_receive_notifications',
    canReceiveNewRoles: 'can_receive_new_roles',
    canReceiveTokenExpiration: 'can_receive_token_expiration',
    canReceiveEmailChange: 'can_receive_email_change',
    canReceiveAdminRoleDeletion: 'can_receive_admin_role_deletion',
};

export const emailSettingToLabel: Record<RoleEmailSettings, string> = {
    canReceiveInbox: t`Отримання документів`,
    canReceiveInboxAsDefault: t`Про документи для незареєстрованих користувачів`,
    canReceiveComments: t`Отримання коментарів`,
    canReceiveRejects: t`Відхилення документів`,
    canReceiveReviews: t`Емейл про погодження документів`,
    canReceiveReminders: t`Нагадування про неопрацьовані запити в сервісі`,
    canReceiveAccessToDoc: t`Отримання доступу до документу`,
    canReceiveDeleteRequests: t`Запит на видалення документів`,
    canReceiveReviewProcessFinished: t`Завершення процесу погодження в завантажених мною документах`,
    canReceiveReviewProcessFinishedAssigner: t`Завершення процесу погодження, що ініційоване мною`,
    canReceiveSignProcessFinished: t`Завершення процесу підписання в завантажених мною документах`,
    canReceiveSignProcessFinishedAssigner: t`Завершення процесу підписання, що ініційоване мною`,
    canReceiveFinishedDocs: t`Про завершені документи`,
    canReceiveNotifications: t`Отримувати всі сповіщення про події в цій компанії`,
    canReceiveNewRoles: t`Додавання нового користувача в компанію`,
    canReceiveTokenExpiration: t`Завершення інтеграційного токену по співробітникам`,
    canReceiveEmailChange: t`Заміна email співробітником`,
    canReceiveAdminRoleDeletion: t`Видалення адміністратора з компанії`,
};

export const emailSettingToHint: Partial<Record<RoleEmailSettings, string>> = {
    canReceiveInboxAsDefault: t`Сповіщатимемо вас, якщо документ буде відправлено на користувача, якого ще немає в системі. Для отримання сповіщень користувач повинен бути адміністратором або мати право на перегляд всіх документів зі спільним доступом.`,
};

export const disableEmailSettingToHint: Partial<
    Record<RoleEmailSettings, string>
> = {
    canReceiveInboxAsDefault: t`Сповіщення доступне лише адміністраторам або користувачам з правом перегляду всіх документів зі спільним доступом`,
};
