import React from 'react';

import { Checkbox } from '@vchasno/ui-kit';

import { t } from 'ttag';

// styles
import css from '../companyRolePermission/companyRolePermission.css';

interface AdditionalConfig {
    msViewerEnabled: boolean;
    allow_suggesting_document_meta_with_ai: boolean;
}

interface Props {
    disabled: boolean;
    additionalConfigs: AdditionalConfig;
    onChangeAdditionalConfig: (
        valueForApi: Partial<AdditionalConfig>,
        valueForState: Partial<AdditionalConfig>,
    ) => void;
}

const AdditionalConfigDefaults: AdditionalConfig = {
    msViewerEnabled: true,
    allow_suggesting_document_meta_with_ai: true,
};

const CompanyAdditionalConfig = ({
    disabled,
    additionalConfigs,
    onChangeAdditionalConfig,
}: Props) => {
    const configs = additionalConfigs
        ? additionalConfigs
        : AdditionalConfigDefaults;
    return (
        <>
            <div className={css.paragraph}>
                {t`Співробітники компанії матимуть такі налашування:`}
            </div>
            <div className={css.checkbox}>
                <Checkbox
                    disabled={disabled}
                    checked={configs.msViewerEnabled}
                    label={t`Microsoft переглядач увімкнено`}
                    onChange={(value) => {
                        onChangeAdditionalConfig(
                            { msViewerEnabled: value.currentTarget.checked },
                            { msViewerEnabled: value.currentTarget.checked },
                        );
                    }}
                />
            </div>
            <div className={css.checkbox}>
                <Checkbox
                    disabled={disabled}
                    checked={configs.allow_suggesting_document_meta_with_ai}
                    label={t`Розпізнавання даних за допомогою штучного інтелекту`}
                    onChange={(value) => {
                        onChangeAdditionalConfig(
                            {
                                allow_suggesting_document_meta_with_ai:
                                    value.currentTarget.checked,
                            },
                            {
                                allow_suggesting_document_meta_with_ai:
                                    value.currentTarget.checked,
                            },
                        );
                    }}
                />
            </div>
        </>
    );
};

export default CompanyAdditionalConfig;
