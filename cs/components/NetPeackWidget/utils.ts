import { t } from 'ttag';

import { NetpeakFormFields } from './types';

import { EMAIL_PATTERN } from '../../lib/constants';
import { subscribeToEsputnik } from '../../services/user';

export const validate = (fields: NetpeakFormFields) => {
    const res: Partial<Record<keyof NetpeakFormFields, string>> = {};

    if (!fields.consent) {
        res.consent = t`Будь ласка, погодьтеся на обробку даних`;
    }

    if (!fields.email) {
        res.email = t`Будь ласка, заповніть email-адресу`;
    } else if (!EMAIL_PATTERN.test(fields.email)) {
        res.email = t`Будь ласка, введіть коректну email-адресу`;
    }

    if (!fields.name) {
        res.name = t`Будь ласка, заповніть своє ім'я`;
    }

    return res;
};

export const subscribeFooter = async (fields: NetpeakFormFields) => {
    await subscribeToEsputnik({
        name: fields.name,
        email: fields.email,
        source: 'footer',
    });
};

export const subscribePopup = async (fields: NetpeakFormFields) => {
    await subscribeToEsputnik({
        name: fields.name,
        email: fields.email,
        source: 'popup',
    });
};
