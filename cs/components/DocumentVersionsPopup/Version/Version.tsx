import React from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { BlackTooltip } from '@vchasno/ui-kit';

import cn from 'classnames';
import { formatDate } from 'lib/date';
import {
    getCurrentDocument,
    getDocumentActualVersion,
} from 'selectors/document.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { DocumentVersion } from 'services/documents/ts/types';
import { t } from 'ttag';

import Icon from '../../ui/icon/icon';
import PseudoLink from '../../ui/pseudolink/pseudolink';

import DeleteVersionButton from '../../DeleteVersionButton/DeleteVersionButton';
import DownloadDocumentVersionButton from '../../DownloadDocumentVersionButton/DownloadDocumentVersionButton';
import AntivirusStatus from '../../documentList/iconsBlock/AntivirusStatus/AntivirusStatus';

import NewTabSvg from '../../../icons/new-tab.svg';

import css from './Version.css';

interface Props {
    version: DocumentVersion;
    isActive?: boolean;
}
const Version: React.FC<React.PropsWithChildren<Props>> = ({
    version,
    isActive,
}) => {
    const history = useHistory();
    const document = useSelector(getCurrentDocument);
    const documentActualVersion = useSelector(getDocumentActualVersion);
    const onClickPseudolink = () => {
        eventTracking.sendToGTM({
            category: 'versions_pop-up',
            action: 'click_open_version_link',
        });
        history.push(`/app/documents/${document.id}/versions/${version.id}`);
    };

    const isShowDeleteButton = documentActualVersion.id === version.id;

    const onClickOpenInNewTab = () => {
        eventTracking.sendToGTM({
            category: 'versions_pop-up',
            action: 'open_version_new_window',
        });
    };

    const getUploadType = () => {
        switch (version.type) {
            case 'converted_format':
                return t`Конвертована (PDF)`;
            case 'editor_created':
                return t`Створена`;
            case 'new_upload':
            default:
                return t`Завантажена`;
        }
    };

    return (
        <li
            className={cn(css.version, {
                [css.active]: isActive,
            })}
        >
            <div className={css.titleBlock}>
                <p className={css.title}>
                    {t`Версія`} {version.name}
                </p>
                {/*
                 // @ts-expect-error TS2345 [FIXME] Comment is autogenerated */}
                <p className={css.date}>{formatDate(version.dateCreated)}</p>
            </div>
            <div className={css.mainBlock}>
                <p>
                    {getUploadType()}:{' '}
                    <a href={`mailto:${version.role.user.email}`}>
                        {version.role.user.email}
                    </a>
                </p>
                <p>
                    {t`ЄДРПОУ/ІПН`}: {version.role.company.edrpou}
                </p>
                <p>{version.role.company.name}</p>
            </div>
            <div className={css.actions}>
                <div className={css.icons}>
                    <div className={css.icon}>
                        <AntivirusStatus
                            suppressPortal
                            status={version.antivirusChecks?.[0].status}
                        />
                    </div>
                    <div className={css.icon}>
                        <DownloadDocumentVersionButton
                            isInPopup
                            doc={document}
                            versionId={version.id}
                        />
                    </div>
                    <div
                        className={cn(css.icon, {
                            [css.disableIcon]: isActive,
                        })}
                    >
                        <BlackTooltip
                            title={t`Відкрити в новій вкладці`}
                            disableInteractive
                        >
                            <a
                                target="_blank"
                                rel="noopener noreferrer"
                                href={
                                    !isActive
                                        ? `/app/documents/${document.id}/versions/${version.id}`
                                        : undefined
                                }
                                onClick={onClickOpenInNewTab}
                            >
                                <Icon glyph={NewTabSvg} />
                            </a>
                        </BlackTooltip>
                    </div>
                    {isShowDeleteButton && (
                        <div className={css.icon}>
                            <DeleteVersionButton
                                doc={document}
                                version={version}
                            />
                        </div>
                    )}
                </div>
                {isActive && (
                    <p className={css.greenText}>{t`Відображається`}</p>
                )}
                {!isActive && (
                    <PseudoLink onClick={onClickPseudolink}>
                        {t`Відкрити`}
                    </PseudoLink>
                )}
            </div>
        </li>
    );
};

export default Version;
