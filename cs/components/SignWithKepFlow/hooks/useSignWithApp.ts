import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { useQuery } from '@tanstack/react-query';

import {
    acquireSign,
    getMultipleSignStatus,
    getSerialNumbers,
    releaseOperationId,
} from 'components/SignWithKepFlow/api';
import {
    MAX_SIGN_CHUNK_SIZE,
    RESEND_NOTIFICATION_TIMEOUT_SEC,
    SIGN_STATUS_POLLING_INTERVAL_SEC,
} from 'components/SignWithKepFlow/constants';
import { useResendPushNotificationMutation } from 'components/SignWithKepFlow/hooks';
import {
    CloudCertificateInfo,
    SignResult,
} from 'components/SignWithKepFlow/types';
import { calculateDocumentsHashes } from 'components/SignWithKepFlow/utils';
import { getDocumentsGraphArguments } from 'lib/helpers';
import {
    GET_KEP_SIGN_ALL_DOCS,
    GET_KEP_SIGN_OPERATION,
    GET_KEP_SIGN_OPERATION_STATUS,
} from 'lib/queriesConstants';
import { useInterval } from 'lib/reactHelpers/hooks';
import { getAppFlags, getCurrentUser } from 'selectors/app.selectors';
import { getDocumentLocationVersionId } from 'selectors/router.selectors';
import {
    DOCUMENT_MULTI_SIGN_FRAGMENT,
    getDocumentsByPartially,
} from 'services/documents/api';
import { Document } from 'services/documents/ts/types';
import { t } from 'ttag';

import { signWithKepFlowSelectors } from '../signWithKepFlowSlice';

type UseSignWithAppOptions = {
    certificates: CloudCertificateInfo[];
    documents: Document[];
    onSignComplete: (signResults: SignResult[]) => void;
};

export const useSignWithApp = (options: UseSignWithAppOptions) => {
    const [
        clientCertificate = null,
        clientCertificate2 = null,
    ] = options.certificates;
    const clientId = clientCertificate?.acsk_key_id;
    const clientId2 = clientCertificate2?.acsk_key_id;
    const documentLocationVersionId = useSelector(getDocumentLocationVersionId);
    const meta = useSelector(signWithKepFlowSelectors.selectFlowMetaInfo);
    const currentUser = useSelector(getCurrentUser);
    const isEsSearchEnabled = useSelector(getAppFlags).ES_SEARCH;

    const resendPushNotificationMutation = useResendPushNotificationMutation();

    const [countdown, setCountdown] = useState<{
        status: 'idle' | 'running';
        value: number;
    }>({
        status: 'idle',
        value: RESEND_NOTIFICATION_TIMEOUT_SEC,
    });

    const allDocsQuery = useQuery({
        queryKey: [
            GET_KEP_SIGN_ALL_DOCS,
            {
                selectedDocuments: options.documents,
                location,
                selectedDocumentsCount: options.documents.length,
                totalDocsCount: meta.totalDocsCount,
            },
        ],
        queryFn: async () => {
            // case where "select all by filter" option is enabled
            // so we need to get all documents from all pages, not just from current one
            if (meta.totalDocsCount > options.documents.length) {
                const docs = await getDocumentsByPartially({
                    args: getDocumentsGraphArguments(location),
                    isEsSearchEnabled,
                    fragment: `
                        ${DOCUMENT_MULTI_SIGN_FRAGMENT}
                    `,
                    currentUser,
                });

                return docs as Document[];
            }

            // otherwise, return only documents from current page
            return options.documents;
        },
    });

    const allDocs = allDocsQuery.data?.slice(0, MAX_SIGN_CHUNK_SIZE) || [];

    const signOperationQuery = useQuery({
        queryKey: [
            GET_KEP_SIGN_OPERATION,
            {
                clientId,
                clientId2,
                documents: allDocs,
                documentLocationVersionId,
                method: 'app',
            },
        ],
        queryFn: async () => {
            if (!clientId) throw new Error('clientId not provided');

            const hashes = await calculateDocumentsHashes(
                allDocs,
                documentLocationVersionId,
            );

            const { operationId } = await acquireSign({
                clientId,
                clientId2,
                descriptions: allDocs.map(
                    (doc) => `${t`Підпис`} "${doc.type || doc.title}"`,
                ),
                hashes,
                sendNotification: true,
                sendSms: false,
            });

            setCountdown((prev) => ({
                ...prev,
                status: 'running',
                value: RESEND_NOTIFICATION_TIMEOUT_SEC,
            }));

            return operationId;
        },
        staleTime: 3 * 60 * 1000,
        cacheTime: 0,
        enabled: !!clientId && allDocs.length !== 0,
        retry: false,
    });

    const operationId = signOperationQuery.data || null;

    const signStatusQuery = useQuery({
        queryKey: [GET_KEP_SIGN_OPERATION_STATUS, { clientId, operationId }],
        queryFn: async (): Promise<SignResult[] | null> => {
            if (!clientId || !operationId) return null;

            const response = await getMultipleSignStatus({
                clientId,
                operationId,
            });

            if (response.status === 2) {
                const promises = response.signatures.map(
                    async (signature, i) => {
                        const results: SignResult[] = [];

                        const serialNumbers = await getSerialNumbers({
                            signature: signature.signature,
                            signature2: signature.signature2 || null,
                        });

                        results.push({
                            signature: signature.signature,
                            serialNumber: serialNumbers.signature,
                            doc: allDocs[i],
                            signatureType: clientCertificate.signature_type,
                        });

                        if (
                            signature.signature2 &&
                            serialNumbers.signature2 &&
                            clientCertificate2
                        ) {
                            results.push({
                                signature: signature.signature2,
                                serialNumber: serialNumbers.signature2,
                                doc: allDocs[i],
                                signatureType:
                                    clientCertificate2.signature_type,
                            });
                        }

                        return results;
                    },
                );

                const signResultsTuples = await Promise.all(promises);
                const signResults = signResultsTuples.flat();

                setCountdown((prev) => ({ ...prev, status: 'idle' }));
                options.onSignComplete(signResults);

                return signResults;
            }

            return null;
        },
        refetchInterval: (data, query) => {
            // stop polling once we have data or error
            if (!!data || !!query.state.error || signOperationQuery.isError) {
                return false;
            }

            // otherwise, continue polling
            return SIGN_STATUS_POLLING_INTERVAL_SEC * 1000;
        },
        staleTime: 0,
        cacheTime: 0,
        retry: false,
        enabled: !!clientId && !!operationId,
    });

    useInterval(
        () => {
            setCountdown((prev) => ({
                value: prev.value - 1,
                status: prev.value - 1 === 0 ? 'idle' : 'running',
            }));
        },
        countdown.status === 'running' ? 1000 : null,
    );

    useEffect(() => {
        if (signOperationQuery.isError || signStatusQuery.isError) {
            setCountdown((prev) => ({ ...prev, status: 'idle' }));
        }
    }, [signOperationQuery.isError, signStatusQuery.isError]);

    useEffect(() => {
        return () => {
            if (clientId && operationId) {
                releaseOperationId({ clientId, operationId }).catch(() => {});
            }
        };
    }, [clientId, operationId]);

    const resendNotification = async () => {
        // if there was an error, release current operation and recreate
        if (signOperationQuery.isError || signStatusQuery.isError) {
            signOperationQuery.refetch();
            return;
        }

        // otherwise, simply resend push notification
        if (operationId) {
            resendPushNotificationMutation.mutate(
                { operationId },
                {
                    onSuccess: () => {
                        setCountdown((prev) => ({
                            ...prev,
                            status: 'running',
                            value: RESEND_NOTIFICATION_TIMEOUT_SEC,
                        }));
                    },
                },
            );
        }
    };

    return {
        countdown,
        resendPushNotification: resendNotification,
        isSignOperationLoading: signOperationQuery.isLoading,
        signOperationError: signOperationQuery.error || allDocsQuery.error,
        signStatusError: signStatusQuery.error,
        isSignStatusLoading: signStatusQuery.isLoading,
        isResendingPushNotification:
            resendPushNotificationMutation.isLoading ||
            signOperationQuery.isRefetching,
    };
};
