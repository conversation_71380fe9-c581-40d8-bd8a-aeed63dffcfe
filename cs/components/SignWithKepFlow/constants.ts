export const PREFERRED_SIGNATURE_LS_KEY =
    'SIGN_WITH_KEP_FLOW_PREFERRED_SIGNATURE';
export const PREFERRED_STAMP_LS_KEY = 'SIGN_WITH_KEP_FLOW_PREFERRED_STAMP';

export const PROXY_ENDPOINT_URL = '/internal-api/proxy/signer';
export const CLOUD_SIGNER_URL = `${PROXY_ENDPOINT_URL}?address=${config.CSIGNER_HOST}`;

export const SIGN_STATUS_POLLING_INTERVAL_SEC = 1;
export const MAX_SIGN_CHUNK_SIZE = 250;
export const RESEND_NOTIFICATION_TIMEOUT_SEC = 15;

export const KEP_APP_GOOGLE_PLAY_URL = 'https://cutt.ly/QegB6Yo4';
export const KEP_APP_APP_STORE_URL = 'https://cutt.ly/degNwALN';

export const KEP_SIGN_FLOW_QUERY_PARAM = 'kep-sign-flow';

export const KEP_SIGN_FLOW_ANALYTICS_EVENT = {
    SIGN_DOC_PAGE: 'ec_kepsignflow_sign_doc_page',
    SIGN_DOC_LIST_PAGE: 'ec_kepsignflow_sign_doc_list_page',
    SIGN_AFTER_UPLOAD: 'ec_kepsignflow_sign_after_upload',

    APP_SIGN_SHOW: 'ec_kepsignflow_app_sign_show',
    APP_SIGN_SUCCESS: 'ec_kepsignflow_app_sign_success',
    APP_SIGN_RETRY: 'ec_kepsignflow_app_sign_retry',
    APP_SIGN_NO_APP: 'ec_kepsignflow_app_sign_no_app',

    PIN_TAB_SHOW: 'ec_kepsignflow_pin_tab_show',
    PIN_TAB_SUBMIT: 'ec_kepsignflow_pin_tab_submit',
    PIN_TAB_ERROR: 'ec_kepsignflow_pin_tab_error',
    PIN_TAB_FORGOT_PIN: 'ec_kepsignflow_pin_tab_forgot',
    PIN_TAB_FALLBACK: 'ec_kepsignflow_pin_tab_fallback',

    PASSWORD_TAB_SHOW: 'ec_kepsignflow_password_tab_show',
    PASSWORD_TAB_SUBMIT: 'ec_kepsignflow_password_tab_submit',
    PASSWORD_TAB_ERROR: 'ec_kepsignflow_password_tab_error',
    PASSWORD_TAB_FORGOT_PASSWORD: 'ec_kepsignflow_password_tab_forgot',
    PASSWORD_TAB_FALLBACK: 'ec_kepsignflow_password_tab_fallback',

    QR_TAB_SHOW: 'ec_kepsignflow_qr_tab_show',
    QR_TAB_FALLBACK: 'ec_kepsignflow_qr_tab_fallback',

    AD_SHOW: 'ec_kepsignflow_ad_show',
    AD_FALLBACK: 'ec_kepsignflow_ad_fallback',
    AD_CREATE_NEW_KEP: 'ec_kepsignflow_ad_create_new_kep',
    AD_DOWNLOAD_KEP_APP: 'ec_kepsignflow_ad_download_kep_app',

    SUCCESS_SHOW: 'ec_kepsignflow_success_show',
};
