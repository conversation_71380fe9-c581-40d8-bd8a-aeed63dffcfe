import { CloudSignerSignatureItemOption } from 'components/signPopup/signPopupActionCreators';
import { redirectToNewKepProxy } from 'lib/routing/utils';
import { getLocalStorageItem, setLocalStorageItem } from 'lib/webStorage';
import groupBy from 'lodash/groupBy';
import { Document } from 'services/documents/ts/types';
import { getDocumentVersionHash } from 'services/documents/ts/utils';
import { getDocumentHash } from 'services/signer/utils';

import { SignResult } from './types';

import { KEP_SIGN_FLOW_QUERY_PARAM, MAX_SIGN_CHUNK_SIZE } from './constants';

const composeKepAdPopupLsKey = (user: {
    id: string | null;
    email: string | null;
    companyEdrpou: string | null;
}) =>
    `SIGN_WITH_KEP_FLOW_AD_TIMESTAMP/${user.id || user.email || '*'}/${
        user.companyEdrpou || '*'
    }`;

export const composeRolloutLsKey = (companyEdrpou: string) => {
    return `SIGN_WITH_KEP_FLOW_ROLLOUT/${companyEdrpou}`;
};

export const formatUserName = (
    payload: { surname: string; name: string; patronymic?: string | null },
    options: {
        format: 'full' | 'surname-with-initials' | 'name-and-surname';
    } = { format: 'name-and-surname' },
) => {
    const { format } = options;

    if (format === 'full') {
        return [payload.surname, payload.name, payload.patronymic]
            .filter(Boolean)
            .join(' ');
    }

    if (format === 'surname-with-initials') {
        const initials = [
            payload.name ? `${payload.name[0].toUpperCase()}.` : '',
            payload.patronymic ? `${payload.patronymic[0].toUpperCase()}.` : '',
        ].join('');

        return [payload.surname, initials].filter(Boolean).join(' ');
    }

    if (format === 'name-and-surname') {
        return [payload.name, payload.surname].filter(Boolean).join(' ');
    }

    throw new Error('Invalid format');
};

export const calculateIsSignWithAppAvailable = (ctx: {
    isFlagEnabled: boolean;
    isMobileAppLogged: boolean;
    totalDocsCount: number;
}): boolean => {
    // we do not show signing with app popup when:
    // 1. feature flag is disabled
    if (!ctx.isFlagEnabled) return false;
    // 2. there is no information that user has a mobile app
    if (!ctx.isMobileAppLogged) return false;
    // 3. there are no docs to sign
    if (ctx.totalDocsCount === 0) return false;
    // 4. there are too much docs to sign (bigger then cloud signer could handle in one sign operation)
    if (ctx.totalDocsCount > MAX_SIGN_CHUNK_SIZE) return false;

    // otherwise, we could show signing via app popup
    return true;
};

export const shouldShowKepAdPopup = (currentUser: {
    id: string | null;
    email: string | null;
    companyEdrpou: string | null;
}) => {
    const oldKey = composeKepAdPopupLsKey({
        id: null,
        email: currentUser.email,
        companyEdrpou: currentUser.companyEdrpou,
    });
    const newKey = composeKepAdPopupLsKey(currentUser);

    // Previously we stored last show date using only email and edrpou,
    // but now we prefer give priority to user id. Migrate old data to new key
    // if it exists. You can remove this code after 30 days since the release.
    let lastShowDateTimestamp = getLocalStorageItem(oldKey);
    if (lastShowDateTimestamp) {
        setLocalStorageItem(newKey, lastShowDateTimestamp);
        localStorage.removeItem(oldKey);
    }
    if (!lastShowDateTimestamp) {
        lastShowDateTimestamp = getLocalStorageItem(newKey);
    }

    // if user has already seen the popup in the last 30 days, skip it
    const THIRTY_DAYS_IN_MS = 30 * 24 * 60 * 60 * 1000;

    if (
        lastShowDateTimestamp &&
        Date.now() - lastShowDateTimestamp < THIRTY_DAYS_IN_MS
    ) {
        return false;
    }

    // otherwise, show the popup
    return true;
};

export const markKepAdPopupAsShown = (currentUser: {
    email: string | null;
    id: string | null;
    companyEdrpou: string | null;
}) => setLocalStorageItem(composeKepAdPopupLsKey(currentUser), Date.now());

export const calculateDocumentsHashes = async (
    docs: Document[],
    documentLocationVersionId: string,
) => {
    return Promise.all(
        docs.map(async (doc) => {
            const versionHash = getDocumentVersionHash(
                doc,
                documentLocationVersionId || doc.versions[0]?.id,
            );

            const hash: string =
                versionHash ||
                doc.metadata?.contentHash ||
                (await getDocumentHash(doc.id));

            return hash;
        }),
    );
};

export const convertToLegacyFormat = (
    signResults: SignResult[],
): CloudSignerSignatureItemOption[] => {
    // Group sign results by document id (to combine signature and stamp results for the same document)
    const groupedByDoc = groupBy(signResults, (result) => result.doc.id);

    // Convert each group to CloudSignerSignatureItemOption format
    return Object.values(groupedByDoc).map((group) => {
        const signatureResult = group.find(
            (result) => result.signatureType === 'signature',
        );
        const stampResult = group.find((result) =>
            ['stamp', 'rro'].includes(result.signatureType),
        );

        return {
            doc: group[0].doc, // All docs in group are the same, take first
            pkSign: signatureResult?.signature || null,
            pkSerialNumber: signatureResult?.serialNumber || null,
            stampSign: stampResult?.signature || null,
            stampSerialNumber: stampResult?.serialNumber || null,
        };
    });
};

export const redirectToVchasnoKepProxy = (ctx: { isLegal: boolean }) => {
    const { isLegal } = ctx;
    const url = new URL(location.href);

    // append special query param to continue flow automatically after kep successful redirect
    url.searchParams.set(KEP_SIGN_FLOW_QUERY_PARAM, isLegal ? 'legal' : 'fop');

    redirectToNewKepProxy({
        signatureType: 'signature',
        isLegal,
        successRedirectUrl: url.toString(),
        successBtnLabel: 'Повернутись і підписати',
    });
};
