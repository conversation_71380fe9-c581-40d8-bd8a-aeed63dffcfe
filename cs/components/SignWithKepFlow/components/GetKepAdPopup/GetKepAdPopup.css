.root {
    overflow: auto;
    width: 1200px;
    max-width: calc(100vw - 80px);
    height: 800px;
    max-height: calc(100vh - 80px);
    padding: 20px;
    margin: 40px auto;
    background: var(--white-bg);
    border-radius: 12px;
}

.overlay>div {
    overflow: hidden;
    align-items: flex-start;
    justify-content: center;
    padding-bottom: 0;
}

.root>div:last-of-type {
    display: flex;
    width: 100%;
    height: 100%;
    gap: 20px;
}

.root>div:first-of-type {
    top: 40px;
    right: 40px;
}

.logo {
    width: 60px;
    height: 60px;
    margin-bottom: 40px;
}

.title {
    margin-bottom: 24px;
    color: var(--content-color);
    font-size: 24px;
    font-weight: 500;
    line-height: 28px;
    white-space: pre-line;
}

.left {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
}

.leftContent {
    display: flex;
    width: 100%;
    max-width: 370px;
    flex: 1;
    align-items: center;
    justify-content: center;
}

.leftFooter {
    margin-bottom: 32px;
    color: var(--content-secondary-color);
    font-size: 14px;
}

.statistics {
    display: flex;
    align-items: center;
    margin-bottom: 40px;
    gap: 16px;
}

.statisticsPersons {
    display: flex;
}

.statisticsPersonsItem {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.statisticsPersonsItem:not(:first-of-type) {
    margin-left: -16px;
}

.statisticsDescription {
    color: #6b8091;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    white-space: pre-line;
}

.buttons {
    display: flex;
    gap: 16px;
}

.buttonSecondary {
    white-space: nowrap;
}

.buttonPrimary {
    flex-shrink: 0;
}

.right {
    display: flex;
    width: 578px;
    align-items: center;
    justify-content: center;
    background: linear-gradient(169.58deg, rgba(214, 233, 255, 0.5) 6.26%, rgba(255, 217, 182, 0.5) 56.6%, rgba(255, 148, 152, 0.5) 95.36%);
    border-radius: 12px;
}

.frame {
    overflow: hidden;
    border: 8px solid #262626;
    border-radius: 12px;
    outline: 3px solid #EAEAEB;
}

.lottie {
    width: 506px;
    height: 285px;
}

@media screen and (max-width: 1280px) {
    .right {
        width: 462px;
    }

    .lottie {
        width: 404px;
        height: 228px
    }
}

@media screen and (max-width: 1024px) {
    .root>div:last-of-type {
        flex-direction: column-reverse;
    }

    .right {
        width: 100%;
        height: 250px;
    }

    .lottie {
        width: 323px;
        height: 182px;
    }
}

@media screen and (max-width: 768px) {
    .root {
        width: 100%;
        height: 600px;
        max-height: calc(100vh - 20px);
        margin: 10px auto;
    }

    .right {
        display: none;
    }

    .overlay>div {
        align-items: flex-end;
    }

    .logo {
        margin-right: auto;
        margin-left: auto;
    }

    .buttons {
        flex-direction: column-reverse;
    }

    .buttonPrimary {
        width: 100%;
    }
}
