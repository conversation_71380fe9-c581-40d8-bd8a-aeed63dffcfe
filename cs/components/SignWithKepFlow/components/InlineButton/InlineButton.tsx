import React, { ButtonHTMLAttributes } from 'react';

import cn from 'classnames';

import css from './InlineButton.css';

interface InlineButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
    className?: string;
}

const InlineButton: React.FC<InlineButtonProps> = ({
    children,
    className,
    ...props
}) => {
    return (
        <button className={cn(css.root, className)} type="button" {...props}>
            {children}
        </button>
    );
};

export default InlineButton;
