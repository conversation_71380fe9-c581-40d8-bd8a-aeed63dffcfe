import { CloudCertificateInfo } from 'components/SignWithKepFlow/types';
import { getLocalStorageItem, removeLocalStorageItem } from 'lib/webStorage';

export const NO_PREFERRED_CERT_ID = 'NONE';

export const getPreferredCert = (
    certs: CloudCertificateInfo[],
    cacheKey: string,
) => {
    const preferredCertId = getLocalStorageItem(cacheKey);
    const defaultCert = certs[0] || null;

    // if there is no preferred cert, return first cert from the list
    if (!preferredCertId) {
        return defaultCert;
    }

    // if user explicitly set preferred cert to NONE (picked nothing from the list), return null
    if (preferredCertId === NO_PREFERRED_CERT_ID) {
        return null;
    }

    const preferredCert = certs.find(
        (cert) => cert.acsk_key_id === preferredCertId,
    );

    // if there is no preferred cert, cleanup local storage to avoid confusion
    if (!preferredCert) {
        removeLocalStorageItem(cacheKey);
        return defaultCert;
    }

    return preferredCert;
};
