import { encryptedPost } from 'components/KepSigner/api';
import { t } from 'ttag';

import { CLOUD_SIGNER_URL } from '../constants';

type ValidSignature = { hash: string; signature: string; signature2?: string };
type InvalidSignature = {
    hash: string;
    errorCode: number;
    errorMessage: string;
};

type GetMultipleSignStatusResponse =
    | {
          status: 2;
          signatures: (ValidSignature | InvalidSignature)[];
      }
    | { status: 0 | 1 | 3 | 4 }
    | { errorCode: number; errorMessage: string };

type GetMultipleSignStatusResult =
    | { status: 0 }
    | { status: 1 }
    | { status: 2; signatures: ValidSignature[] };

export const getMultipleSignStatus = async (payload: {
    clientId: string;
    operationId: string;
}): Promise<GetMultipleSignStatusResult> => {
    const response = await encryptedPost<GetMultipleSignStatusResponse>(
        `${CLOUD_SIGNER_URL}/ss/sign-status`,
        {
            clientId: payload.clientId,
            operationId: payload.operationId,
        },
    );

    if ('errorCode' in response) {
        throw new Error(response.errorMessage);
    }

    if (response.status === 3) {
        throw new Error(t`Операція була скасована`);
    }

    if (response.status === 4) {
        throw new Error(t`Виникла неочікувана помилка`);
    }

    if (response.status !== 2) {
        return { status: response.status };
    }

    const hasError = response.signatures.some((s) => 'errorCode' in s);

    if (hasError) {
        throw new Error(t`Виникла неочікувана помилка`);
    }

    return {
        status: response.status,
        signatures: response.signatures as ValidSignature[],
    };
};
