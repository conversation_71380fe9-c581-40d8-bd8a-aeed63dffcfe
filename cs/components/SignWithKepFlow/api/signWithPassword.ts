import { encryptedPost } from 'components/KepSigner/api';

import { CLOUD_SIGNER_URL } from '../constants';

type SignWithPasswordResponse =
    | {
          status: 1;
      }
    | {
          errorCode: number;
          errorMessage: string;
      };

export const signWithPassword = async (payload: {
    operationId: string;
    password: string;
}) => {
    const response = await encryptedPost<SignWithPasswordResponse>(
        `${CLOUD_SIGNER_URL}/api/internal/encrypted/sign`,
        {
            operation_id: payload.operationId,
            password: payload.password,
        },
    );

    if ('errorCode' in response) {
        throw new Error(response.errorMessage);
    }

    return response;
};
