import { encryptedPost } from 'components/KepSigner/api';

import { CLOUD_SIGNER_URL } from '../constants';

type ReleaseOperationIdResponse =
    | {
          status: 2;
      }
    | {
          errorCode: number;
          errorMessage: string;
      };

export const releaseOperationId = async (payload: {
    clientId: string;
    operationId: string;
}) => {
    const response = await encryptedPost<ReleaseOperationIdResponse>(
        `${CLOUD_SIGNER_URL}/ss/release-operation-id`,
        {
            clientId: payload.clientId,
            operationId: payload.operationId,
        },
    );

    if ('errorCode' in response) {
        throw new Error(response.errorMessage);
    }

    return response;
};
