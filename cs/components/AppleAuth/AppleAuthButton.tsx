import React from 'react';
import AppleLogin from 'react-apple-login';

import { LoginSuccessEffectOptions } from 'components/LoginForm/useLoginSuccessEffect';
import { useRegistrationRedirectLink } from 'components/registration/hooks/useRegistrationRedirectLink';
import { useRegistrationSource } from 'components/registration/hooks/useRegistrationSource';
import { RegistrationSuccessEffect } from 'components/registration/hooks/useRegistrationSuccessEffect';
import { useVerifiedInviteTokenData } from 'components/registration/hooks/useVerifiedInviteTokenData';
import { useUrlQueryParam } from 'hooks/useUrlQueryParam';
import eventTracking from 'services/analytics/eventTracking';
import { appleAuth } from 'services/auth';
import logger from 'services/logger';
import { t } from 'ttag';
import AuthButton from 'ui/AuthButton';

import { AppleResponseData, AppleResponseError } from './types';

import {
    composeAppleRedirectURI,
    convertAppleResponseUserToPayload,
} from './utils';

import AppleSVG from './icons/apple.svg';

export interface AppleAuthButtonProps {
    redirectURI?: string;
    clientId?: string;
    type?: 'standard' | 'icon';
    onLogin?: (options: LoginSuccessEffectOptions) => void;
    errorHandler?: (error: { code: string; reason: string }) => void;
    onRegister?: RegistrationSuccessEffect;
}

const AppleAuthButton: React.FC<AppleAuthButtonProps> = ({
    redirectURI,
    clientId,
    type = 'standard',
    errorHandler,
    onLogin = () => undefined,
    onRegister = () => undefined,
}) => {
    const [isPending, setIsPending] = React.useState<boolean>(false);
    const referrer = useUrlQueryParam('ref') || '';
    const activeProTrial = useUrlQueryParam('activeProTrial');
    const source = useRegistrationSource();
    const redirect = useRegistrationRedirectLink();
    const { email: invite_email } = useVerifiedInviteTokenData();
    const appleAuthResponse = async (
        response: AppleResponseData | AppleResponseError,
    ) => {
        if (config.DEBUG) {
            console.log('AppleResponseData');
            console.log(response);
        }

        if ('error' in response) {
            logger.error('Apple auth popup error', response.error);
            return;
        }

        if (response.user) {
            logger.log('Apple auth popup response user', response.user);
        }

        try {
            setIsPending(true);

            const { nextUrl, is2FAEnabled, flow, email } = await appleAuth({
                code: response.authorization.code,
                ...(response.user
                    ? { user: convertAppleResponseUserToPayload(response.user) }
                    : {}),
                source,
                redirect,
                referrer,
                isActiveProTrial: Boolean(activeProTrial),
                invite_email: invite_email || null,
            });

            if (flow === 'login') {
                onLogin({
                    nextUrl: nextUrl,
                    is2FAEnabled: is2FAEnabled,
                    method: 'apple',
                });
                return;
            } else if (flow === 'registration') {
                eventTracking.sendToGTM({
                    event: 'funnel_reg_step_2_apple',
                    action: '',
                    category: '',
                });
                onRegister({
                    nextUrl: nextUrl,
                    login: email,
                    registrationMethod: 'apple',
                });
                return;
            }
        } catch (error) {
            errorHandler?.(error);
        } finally {
            setIsPending(false);
        }
    };

    return (
        <AppleLogin
            clientId={clientId || config.APPLE_AUTH_CLIENT_ID}
            redirectURI={redirectURI || composeAppleRedirectURI()}
            usePopup
            callback={appleAuthResponse}
            scope="email name"
            responseMode="query"
            responseType="code"
            render={(renderProps) => (
                <AuthButton
                    icon={AppleSVG}
                    type={type}
                    disabled={isPending || renderProps.disabled}
                    isPending={isPending}
                    onClick={renderProps.onClick}
                    pendingText="Apple"
                    iconSize={24}
                >{t`Вхід через Apple`}</AuthButton>
            )}
        />
    );
};

export default AppleAuthButton;
