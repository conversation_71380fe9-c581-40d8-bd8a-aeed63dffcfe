import { toDate } from 'lib/date';
import { getBannersList, updateBanner } from 'services/banners';

import { Thunk } from '../../types';

import editFormActions from '../bannerEditForm/bannerEditFormActions';
import actions from './bannersListActions';

import { Banner, BannerStatuses } from '../infoBanner/infoBannerTypes';

function fetchBanners(): Thunk {
    return async (dispatch) => {
        try {
            const banners = await getBannersList();
            dispatch({
                type: actions.BANNERS_LIST__FETCH_BANNERS,
                banners: banners.map((item: Banner) => {
                    return {
                        ...item,
                        dateTo: toDate(item.dateTo),
                        dateFrom: toDate(item.dateFrom),
                        positions: item.positions || [],
                        rates: item.rates || [],
                    } as Banner;
                }),
            });
        } catch (err) {
            dispatch({
                type: actions.BANNERS_LIST__SHOW_ERROR_MESSAGE,
                errorMessage: err.message,
            });
        }
    };
}

function onAddBanner(): Thunk {
    return (dispatch) => {
        dispatch({ type: editFormActions.BANNER_EDIT_FORM__SHOW });
    };
}

function onEdit(data: Banner): Thunk {
    return (dispatch) => {
        dispatch({ type: editFormActions.BANNER_EDIT_FORM__SHOW, data });
    };
}

function onSetVisible(data: Banner): Thunk {
    return async (dispatch) => {
        try {
            await updateBanner({
                ...data,
                status:
                    data.status === BannerStatuses.ACTIVE
                        ? BannerStatuses.HIDDEN
                        : BannerStatuses.ACTIVE,
            });
            dispatch(fetchBanners());
        } catch (err) {
            dispatch({
                type: actions.BANNERS_LIST__SHOW_ERROR_MESSAGE,
                errorMessage: err.message,
            });
        }
    };
}

function onDelete(data: Banner): Thunk {
    return async (dispatch) => {
        try {
            await updateBanner({ ...data, status: BannerStatuses.DELETED });
            dispatch(fetchBanners());
        } catch (err) {
            dispatch({
                type: actions.BANNERS_LIST__SHOW_ERROR_MESSAGE,
                errorMessage: err.message,
            });
        }
    };
}

export default {
    fetchBanners,
    onAddBanner,
    onSetVisible,
    onEdit,
    onDelete,
};
