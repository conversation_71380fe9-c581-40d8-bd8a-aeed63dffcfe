import React from 'react';

import { BlackTooltip } from '@vchasno/ui-kit';

import { t } from 'ttag';

import IconButton from '../ui/iconButton/iconButton';

import SvgDownload from './images/download.svg';

interface Props {
    isDisabled: boolean;
    onClick: VoidFunction;
}

const PopoverButton: React.FC<React.PropsWithChildren<Props>> = (props) => {
    return (
        <BlackTooltip title={t`Завантажити`}>
            <span>
                <IconButton
                    disabled={props.isDisabled}
                    svg={SvgDownload}
                    onClick={props.onClick}
                />
            </span>
        </BlackTooltip>
    );
};

export default PopoverButton;
