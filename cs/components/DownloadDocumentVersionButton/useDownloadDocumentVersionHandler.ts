import { useDispatch, useSelector } from 'react-redux';

import { DocumentListDocumentItem } from 'components/documentList/types';
import notificationCenterActionCreators from 'components/notificationCenter/notificationCenterActionCreators';
import {
    getIsSharedDocumentViewMode,
    getIsSignSessionMode,
} from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { Document } from 'services/documents/ts/types';
import { getDocumentUrl } from 'services/documents/utils';
import { downloadFile } from 'services/downloads';
import { t } from 'ttag';

export const useDownloadDocumentVersionHandler = (
    doc: DocumentListDocumentItem | Document,
    versionId?: string,
) => {
    const dispatch = useDispatch();
    const isSignSessionMode = useSelector(getIsSignSessionMode);
    const isSharedDocumentViewMode = useSelector(getIsSharedDocumentViewMode);

    const trackingLabel = isSignSessionMode
        ? `${doc.edrpouOwner} ${doc.companyNameOwner}`
        : '';
    const getDocumentHeaderTrackingName = () => {
        if (isSignSessionMode) {
            return 'sign_session';
        }
        if (isSharedDocumentViewMode) {
            return 'shared_document_view';
        }
        return '';
    };

    const trackingName = getDocumentHeaderTrackingName();

    const downloadDocumentHref = getDocumentUrl(
        doc?.id,
        true,
        '',
        '',
        false,
        versionId,
        true,
    );
    const downloadDocumentVersion = () => {
        if (trackingName) {
            eventTracking.sendEvent(
                trackingName,
                'download_version',
                trackingLabel,
            );
        }
        try {
            downloadFile(downloadDocumentHref);
        } catch (error) {
            dispatch(
                notificationCenterActionCreators.addNotification({
                    title: t`Виникла помилка під час завантаження документу, спробуйте знову через кілька хвилин`,
                    type: 'text',
                    textType: 'error',
                    text: error.message,
                    showCloseButton: true,
                    autoClose: 5000,
                }),
            );
        }
    };

    return {
        downloadDocumentVersion,
    };
};
