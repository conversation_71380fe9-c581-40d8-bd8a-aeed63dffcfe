import { yupResolver } from '@hookform/resolvers/yup';

import * as yup from 'yup';

import { LoginFormFields } from './types';

const loginFormValidationSchema: yup.ObjectSchema<LoginFormFields> = yup
    .object({
        password: yup.string().required(),
        remember: yup.boolean().default(false).required(),
    })
    .required();

export const loginFormResolver = yupResolver(loginFormValidationSchema);
