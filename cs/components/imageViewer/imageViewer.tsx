import React from 'react';

import cn from 'classnames';

import { ViewerDataStatuses } from '../../services/enums';

import { formatError } from '../../lib/helpers';
import { getImageMimetype } from './utils';

import io from '../../services/io';
import loggerService from '../../services/logger';
import { getOriginalUrl } from '../../services/viewer';
import DummyViewer from '../dummyViewer/dummyViewer';
import Loading from '../loading/loading';
import { IImageViewerProps, IImageViewerState } from './ImageViewerTypes';

// styles
import css from './imageViewer.css';

class ImageViewer extends React.Component<
    IImageViewerProps,
    IImageViewerState
> {
    static defaultProps = {
        isPrintMode: false,
        isSaveToFileMode: false,
        renderSignatureInInterface: true,
        renderSignatureOnPrintDocument: true,
        renderReviewInInterface: true,
        renderReviewOnPrintDocument: true,
    };

    constructor(props: IImageViewerProps) {
        super(props);
        this.state = {
            url: '',
            src: '',
            status: '',
        };
    }

    componentDidMount() {
        if (this.props.url) {
            this.loadData();
        }
    }

    printImage = () => {
        if (this.props.isPrintMode) {
            window.print();
        }
    };

    async loadContent(url: string) {
        this.setState({
            status: ViewerDataStatuses.contentLoading,
        });

        try {
            const response = await io.getAsArrayBuffer(io.buildUrl(url));
            const arrayBufferView = new Uint8Array(response);

            // get fileType from binary
            const bytes: Array<string> = [];
            arrayBufferView.forEach((byte) => {
                bytes.push(byte.toString(16));
            });
            const hex = bytes.join('').toUpperCase().slice(0, 8);

            const fileType = getImageMimetype(hex);
            const blob = new Blob([arrayBufferView], { type: fileType });
            const urlCreator: typeof URL = window.URL || window.webkitURL;

            this.setState({
                src: urlCreator.createObjectURL(blob),
                status: ViewerDataStatuses.contentLoaded,
            });
        } catch (err) {
            loggerService.error('Unable to load or process content data', {
                err: formatError(err),
            });
            this.setState({
                status: ViewerDataStatuses.contentError,
            });
        }
    }

    async loadData() {
        this.setState({
            status: ViewerDataStatuses.dataLoading,
        });

        try {
            const data = await io.get(this.props.url);

            this.setState(
                {
                    status: ViewerDataStatuses.dataLoaded,
                },
                async () => {
                    await this.loadContent(data.url);
                },
            );
        } catch (err) {
            loggerService.error('Unable to load viewer data', {
                err: formatError(err),
            });
            this.setState({
                status: ViewerDataStatuses.dataError,
            });
        }
    }

    render() {
        const { isPrintMode, url } = this.props;
        const { src, status } = this.state;

        const originalUrl = url ? getOriginalUrl(url) : url;
        const rootClassNames = cn({
            [css.root]: !isPrintMode,
            [css.rootPrint]: isPrintMode,
        });

        if (status === ViewerDataStatuses.contentError) {
            return (
                <DummyViewer
                    frame
                    title="Неможливо завантажити зміст документу"
                    url={originalUrl}
                />
            );
        } else if (status === ViewerDataStatuses.dataError) {
            return <DummyViewer frame url={originalUrl} />;
        }

        if (!src) {
            return (
                <div className={rootClassNames}>
                    <div className={css.loading}>
                        <Loading />
                    </div>
                </div>
            );
        }

        return (
            <div className={css.root}>
                <img
                    className={css.image}
                    src={src}
                    onLoad={this.printImage}
                    alt={''}
                />
            </div>
        );
    }
}

export default ImageViewer;
