.btn {
    --blue-border: var(--default-border);
    --blue-color: var(--slate-grey-color);
    --light-blue-color: var(--hover-bg);
}

.icon {
    width: 20px;
    aspect-ratio: 1/1;
}

.icon svg {
    width: 100%;
    height: auto;
}

.active:global(.vchasno-ui-button.--secondary) {
    background-color: var(--vchasno-ui-btn-secondary-hover-bg);
}

.tabsWrapper {
    flex-wrap: wrap;
}

@media (max-width: 480px) {
    .tabsWrapper {
        flex-wrap: nowrap;
        margin-top: 10px;
        margin-bottom: 15px;
        overflow-x: auto;

        /* Firefox browser scrollbar hide */
        scrollbar-width: none;
    }

    .tabsWrapper::-webkit-scrollbar {
        /* Chrome, Edge и Safari browser scrollbar hide */
        height: 0;
    }
}
