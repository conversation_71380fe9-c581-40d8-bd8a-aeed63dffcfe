import React from 'react';
import { useSelector } from 'react-redux';

import { Text } from '@vchasno/ui-kit';

import FlexBox from 'components/FlexBox';
import { getCurrentUser } from 'selectors/app.selectors';
import { t } from 'ttag';

import { getDefaultValueForParameter } from './helpers';

import DocumentParametersList from './DocumentParametersList';
import GoToSettingsLink from './GoToSettingsLink';
import SearchDocumentFieldsAutosuggest from './SearchDocumentFieldsAutosuggest';
import { useDocumentParametersArray } from './useDocumentParametersArray';

const AdditionalParametersSection: React.FC = () => {
    const currentUser = useSelector(getCurrentUser);
    const documentsParametersArray = useDocumentParametersArray();

    return (
        <FlexBox direction="column">
            <Text type="secondary">{t`За допомогою додаткових параметрів ви можете вказати інформацію, котрої не вистачає стандартній конфігурації документу, наприклад, дату оплати.`}</Text>
            <FlexBox direction="column" gap={30}>
                {currentUser?.currentRole.isAdmin && <GoToSettingsLink />}
                <SearchDocumentFieldsAutosuggest
                    onSelect={(item) => {
                        documentsParametersArray.append({
                            ...item,
                            value: getDefaultValueForParameter(item),
                        });
                    }}
                />
                <DocumentParametersList
                    documentsParametersArray={documentsParametersArray}
                    onDelete={documentsParametersArray.remove}
                />
            </FlexBox>
        </FlexBox>
    );
};

export default AdditionalParametersSection;
