import React, { useContext } from 'react';

import { CounterpartyFields } from 'components/DocumentsUploader/types';
import { Document } from 'services/documents/ts/types';

export interface CounterpartyListItemAction {
    sign: boolean;
    order: boolean;
    remove: boolean;
}

type CounterpartyItem = CounterpartyFields['counterparties'][number];

export interface CounterpartyContextValue {
    doc?: Document;
    disabledActions: (item: CounterpartyItem) => CounterpartyListItemAction;
    isSigned: (item: CounterpartyItem) => boolean;
    editable?: boolean; // якщо true, то можна редагувати єдрпоу та email
    suppressAutoRemoveOwner?: boolean; // якщо true, то видаляємо автоматично власну компанію зі списку якщо залишається тільки вона
}

export const disabledActions = () => ({
    order: false, // якщо true, то не можна змінити порядок
    sign: false, // якщо true, то не можна підписати
    remove: false, // якщо true, то не можна видалити зі списку
});

export const isSigned = () => false; // якщо true, то контрагент підписав документ

const initValue: CounterpartyContextValue = {
    disabledActions,
    isSigned,
};

export const CounterpartyContext = React.createContext<CounterpartyContextValue>(
    initValue,
);

export const useCounterpartyContext = () => useContext(CounterpartyContext);
