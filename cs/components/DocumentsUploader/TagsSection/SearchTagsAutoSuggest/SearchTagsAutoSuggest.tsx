import React, { useState } from 'react';
import { useSelector } from 'react-redux';

import { useQuery } from '@tanstack/react-query';

import SearchSvg from 'icons/search.svg';
import { CACHE_TIME_MS } from 'lib/queriesConstants';
import { SEARCH_COMPANY_TAGS } from 'lib/queriesConstants';
import { hasPermission } from 'records/user';
import { getCurrentUser } from 'selectors/app.selectors';
import { getCurrentCompanyTags } from 'services/user';
import { c, t } from 'ttag';
import Autosuggest from 'ui/autosuggest/autosuggest';
import TextShorten from 'ui/textShorten/textShorten';

import { Tag } from '../types';

import { useUploadDocumentsFormContext } from '../../useUploadDocumentsFormContext';

interface SearchTagsAutoSuggestProps {
    onSelect: (item: Tag) => void;
    onAddNewTag: (item: Tag) => void;
    className?: string;
    renderSuggestion?: (suggestion: Tag) => React.ReactNode;
    suppressTagCreation?: boolean;
    onFilter?: (tag: Tag) => boolean;
    disabled?: boolean;
}

const defaultRenderSuggestion = (suggestion: Tag) => {
    return <TextShorten>{suggestion.name}</TextShorten>;
};

const defaultOnFilter: SearchTagsAutoSuggestProps['onFilter'] = () => true;

const SearchTagsAutoSuggest: React.FC<SearchTagsAutoSuggestProps> = ({
    onSelect,
    onAddNewTag,
    className,
    renderSuggestion = defaultRenderSuggestion,
    suppressTagCreation = false,
    onFilter = defaultOnFilter,
    disabled,
}) => {
    const [isShow, setShow] = useState<boolean>(false);
    const [search, setSearch] = useState('');
    const methods = useUploadDocumentsFormContext();
    const currentUser = useSelector(getCurrentUser);
    const selectedTags = methods.watch('selectedTags');
    const canCreateTag =
        hasPermission(currentUser, 'canCreateTags') && !suppressTagCreation;

    const { data = [], isLoading } = useQuery({
        queryFn: async () => await getCurrentCompanyTags({ search: '' }),
        queryKey: [SEARCH_COMPANY_TAGS, currentUser.roleId],
        enabled: Boolean(isShow && !disabled),
        keepPreviousData: true,
        retry: false,
        retryOnMount: false,
        staleTime: CACHE_TIME_MS,
    });

    const suggestionTags = data
        .filter(onFilter)
        .filter(
            (tag: Tag) =>
                !selectedTags.some(
                    (selectedItem) => selectedItem.name === tag.name,
                ),
        )
        .filter((tag: Tag) =>
            tag.name.toLowerCase().includes(search.toLowerCase()),
        );

    const showOptionButton = Boolean(
        search && suggestionTags.length === 0 && canCreateTag,
    );

    const handleSelectSuggestion = (item: Tag) => {
        onSelect(item);
        setSearch('');
        setShow(false);
    };

    const handleChange: React.ChangeEventHandler<HTMLInputElement> = (
        event,
    ) => {
        setSearch(event.target.value);
        setShow(true);
    };

    const handleOptionButtonClick = () => {
        onAddNewTag({
            id: '',
            name: search,
            isNew: true,
        });
        setShow(false);
        setSearch('');
    };

    return (
        <Autosuggest
            className={className}
            disabled={methods.formState.disabled || disabled}
            placeholder={t`Вкажіть існуючий або новий ярлик`}
            leadingIcon={SearchSvg}
            isLoading={isLoading}
            value={search}
            suggestionsData={suggestionTags}
            showSuggestions={isShow}
            onFocus={() => setShow(true)}
            onSuggestionClick={handleSelectSuggestion}
            renderSuggestion={renderSuggestion}
            onChange={handleChange}
            onCloseSuggestion={() => setShow(false)}
            optionButtonText={c('search').t`Створити новий ярлик "${search}"`}
            onOptionButtonClick={
                showOptionButton ? handleOptionButtonClick : null
            }
        />
    );
};

export default SearchTagsAutoSuggest;
