import { useEffect, useRef } from 'react';

import { snackbarToast } from '@vchasno/ui-kit';

import { AIDocumentSettings } from 'services/ai';
import { t } from 'ttag';

import { useAIFetch } from './useAIFetch';

interface AiRequestHandlerParameters {
    fileOrDocId: Parameters<typeof useAIFetch>[0];
    onData: (data: AIDocumentSettings) => void;
    onEmptyData?: () => void;
    onError?: (error: ReturnType<typeof useAIFetch>['error']) => void;
}

export const useAiRequestHandler = ({
    onData,
    onError,
    onEmptyData,
    fileOrDocId,
}: AiRequestHandlerParameters) => {
    const cbRef = useRef({ onError, onData, onEmptyData });
    cbRef.current = { onError, onData, onEmptyData };

    const { fetchAIDocumentSettings, isLoading, data, error } = useAIFetch(
        fileOrDocId,
    );

    useEffect(() => {
        if (!data) {
            // якщо початковий стан, то виходимо
            return;
        }

        if (Object.values(data).every((value) => value === null)) {
            if (cbRef.current.onEmptyData) {
                cbRef.current?.onEmptyData();
            } else {
                snackbarToast.error(
                    t`Не вдалося визначити дані за допомогою ШІ`,
                );
            }

            return;
        }

        // якщо вдалося визначити дані, то викликаємо onData
        cbRef.current.onData(data);
    }, [data]);

    useEffect(() => {
        if (!error) {
            return;
        }

        // якщо виникла помилка, то викликаємо onError або показуємо повідомлення за замовчуванням
        cbRef.current.onError?.(error);
    }, [error]);

    return { fetchAIDocumentSettings, isLoading, data, error } as const;
};
