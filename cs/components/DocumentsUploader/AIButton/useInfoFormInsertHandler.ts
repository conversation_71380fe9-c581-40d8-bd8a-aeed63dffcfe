import { useFormContext } from 'react-hook-form';

import { EditDocumentForm } from 'components/DocumentEdit/types';
import {
    insertingValueLikeAi,
    isDocumentPublicCategory,
} from 'components/DocumentsUploader/AIButton/utils';
import { useRecipientDisableSignCategoriesHandler } from 'components/DocumentsUploader/CounterpartySection/useRecipientDisableSignCategoriesHandler';
import { InfoForm } from 'components/DocumentsUploader/DocumentInfo/types';
import { CounterpartyFields } from 'components/DocumentsUploader/types';
import moment from 'moment';
import { AIDocumentSettings } from 'services/ai';

export const useInfoFormInsertHandler = () => {
    const { setValue, getValues } = useFormContext<
        CounterpartyFields & EditDocumentForm
    >();
    const recipientDisableSignCategoriesHandler = useRecipientDisableSignCategoriesHandler();

    return (
        data: AIDocumentSettings,
        skipFields: Partial<Record<keyof InfoForm, boolean>> = {},
    ) => {
        setValue('dataAI', data);

        // вставляємо значення поступово, як це робить AI
        if (data.title && !skipFields.title) {
            insertingValueLikeAi(data.title, (result) => {
                setValue('title', result);
            });
        }

        if (data.amount && !skipFields.amount) {
            insertingValueLikeAi(data.amount, (result) => {
                setValue('amount', Number(result));
            });
        }

        if (data.number && !skipFields.number) {
            insertingValueLikeAi(data.number, (result) => {
                setValue('number', result);
            });
        }

        if (data.date && !skipFields.date) {
            setValue('date', moment(data.date).toDate());
        }

        if (data.category && !skipFields.category) {
            setValue('category', data.category);
            const willSetCounterparties = Boolean(
                data.companyEdrpou &&
                    data.companyEdrpou !== getValues('companyEdrpou'),
            );

            // якщо контрагента ми не підставили в документ (тобто його нема або єдрпоу який витягнув ші == єдрпоу завантажувача)
            // і визначило внутрішній тип, то тоді підставляємо внутрішній тип і включаємо тогл "внутрішній документ"
            if (
                data.category &&
                !isDocumentPublicCategory(data.category) &&
                !willSetCounterparties
            ) {
                setValue('isInternal', true);
            }

            // еффекти залежно від обраної публічної категорії
            if (isDocumentPublicCategory(data.category)) {
                recipientDisableSignCategoriesHandler();
            }
        }
    };
};
