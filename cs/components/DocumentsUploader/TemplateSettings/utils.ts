import { EmployeesReviewForm } from 'components/DocumentsUploader/EmployeesReviewersSection/types';
import { EmployeesShareForm } from 'components/DocumentsUploader/EmployeesShareSection/types';
import { EmployeesSectionForm } from 'components/DocumentsUploader/EmployeesSignSection/types';
import { TagsForm } from 'components/DocumentsUploader/TagsSection/types';
import { TemplateForm } from 'components/DocumentsUploader/TemplateSettings/types';
import { Template } from 'components/documentTemplates/types';
import { getLocalStorageItem, setLocalStorageItem } from 'lib/webStorage';
import { DocumentFieldType } from 'services/documentFields/enums';
import { DocumentField } from 'services/documentFields/types';
import logger from 'services/logger';

import {
    AdditionalParameter,
    AdditionalParametersForm,
} from '../AdditionalParametersSection/types';
import { Actor, DocumentsUploadForm, RoleActor } from '../types';

export type ComposedTemplateData = Pick<
    DocumentsUploadForm,
    | keyof EmployeesSectionForm
    | keyof EmployeesReviewForm
    | keyof EmployeesShareForm
    | keyof TemplateForm
    | keyof TagsForm
    | keyof AdditionalParametersForm
>;

export const composeFormDataFromTemplate = (
    template: Template,
    documentFields: DocumentField[] = [],
): ComposedTemplateData => {
    const data: ComposedTemplateData = {
        templateId: template.id,
        employeesSignIsOrdered: Boolean(template.signersSettings?.is_ordered),
        employeesReviewersIsOrdered: Boolean(
            template.reviewSettings?.is_ordered,
        ),
        employeesIsSignAfterReview: Boolean(
            template.reviewSettings?.is_required,
        ),
        employeesSigners: template.signers.map((signer) => ({
            id: signer.id,
            type: signer.type,
            source: signer.type === 'role' ? signer.role : signer.group,
        })) as Actor[],

        employeesReviewers: template.reviewers.map((reviewer) => ({
            id: reviewer.id,
            type: reviewer.type,
            source: reviewer.type === 'role' ? reviewer.role : reviewer.group,
        })) as Actor[],

        employeesViewers: [
            ...(template.viewerRoles.map((role) => ({
                id: role.id,
                type: 'role',
                source: role,
            })) as RoleActor[]),
            ...(template.viewerGroups.map((group) => ({
                id: group.id,
                type: 'group',
                source: group,
            })) as Actor[]),
        ] as Actor[],

        selectedTags: template?.tags ?? [],
        documentParameters: [],
    };

    if (template?.fieldsSettings && template.fieldsSettings.fields.length > 0) {
        data.documentParameters = template.fieldsSettings.fields
            .map((field) => {
                const documentField = documentFields.find(
                    (item) => item.id === field.field_id,
                );

                if (!documentField) {
                    logger.error(
                        '[upload document] Failed to apply additional filed from template',
                        {
                            templateId: template.id,
                            additionalFieldIds: documentFields.map(
                                (item) => item.id,
                            ),
                            fieldFromTemplate: field,
                        },
                    );
                    return null;
                }

                const documentParameter: AdditionalParameter = {
                    ...documentField,
                    isRequired: field.is_required,
                    value: field.value!,
                };

                if (
                    documentField.type === DocumentFieldType.date &&
                    documentParameter.value
                ) {
                    documentParameter.value = new Date(documentParameter.value);
                }

                if (
                    [DocumentFieldType.number, DocumentFieldType.text].includes(
                        documentField.type,
                    ) &&
                    documentParameter.value === null
                ) {
                    documentParameter.value = '';
                }

                return documentParameter;
            })
            .filter(Boolean) as AdditionalParameter[];

        if (data.documentParameters.length === 0) {
            // якщо видаляти додаткові поля вони можуть залишиються в шаблоні, але не зможуть відображатися
            // тому можливо що filter(Boolean) лишить пустий масив
            data.documentParameters = [];
        }
    }

    return data;
};

const getTemplateRateLSKey = (key: string) => `templatesSelectedRate_${key}`;

export const getTemplateSelectedRateMap = (
    key: string,
): Record<string, number> => {
    try {
        return getLocalStorageItem(getTemplateRateLSKey(key)) || {};
    } catch (_error) {
        return {};
    }
};

export const setTemplateSelectedRateMap = (
    key: string,
    templateId: string,
): void => {
    const selectedRateMap = getTemplateSelectedRateMap(key);
    selectedRateMap[templateId] = (selectedRateMap[templateId] || 0) + 1;
    setLocalStorageItem(getTemplateRateLSKey(key), selectedRateMap);
};
