import { useQuery } from '@tanstack/react-query';

import { SEARCH_COMPANY_REQUIRED_FIELDS } from 'lib/queriesConstants';
import { getAllDocumentRequiredFields } from 'services/documents/api';
import { AllDocumentRequiredFields } from 'services/documents/ts/types';

interface UseCompanyRequiredFieldsQueryParams {
    edrpou: string;
}
export const useCompanyRequiredFieldsQuery = ({
    edrpou,
}: UseCompanyRequiredFieldsQueryParams) => {
    return useQuery<AllDocumentRequiredFields[]>({
        queryFn: async () =>
            await getAllDocumentRequiredFields({ edrpous: [edrpou] }),
        queryKey: [SEARCH_COMPANY_REQUIRED_FIELDS, edrpou],
        staleTime: Infinity,
        enabled: !!edrpou,
        retryOnMount: false,
        placeholderData: [] as AllDocumentRequiredFields[],
    });
};
