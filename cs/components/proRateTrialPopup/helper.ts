import { IUser } from '../../types/user';

import { PRO_TRIALS_SET } from '../../services/billing/constants';

export const isUserUsedTrial = (currentUser: IUser) => {
    // Find at least one pro rate account among all trial rates that
    // was activated for current company
    const rates = currentUser.currentCompany.trialRates;

    return !!rates.find((item) => PRO_TRIALS_SET.has(item.rate));
};
