import * as React from 'react';
import { connect } from 'react-redux';

import { t } from 'ttag';

import { StoreState } from '../../types/store';

import actionCreators from './resolveDeleteRequestPopupActionCreators';

import {
    mapStateToCurrentUser,
    mapStatetoHasPermission,
} from '../../store/utils';
import {
    genKey,
    getRecipientsTitles,
    groupDocumentsByReceiverEdrpou,
} from '../createDeleteRequestPopup/utils';

import Button from '../ui/button/button';
import Popup from '../ui/popup/popup';
import PseudoLink from '../ui/pseudolink/pseudolink';
import Textarea from '../ui/textarea/textarea';

import StatusText from '../statusText/statusText';
import { IDispatchToProps, State } from './resolveDeleteRequestPopupTypes';

// styles
import css from './resolveDeleteRequestPopup.css';

interface IndexComponentPassedProps extends State, IDispatchToProps {
    isDocListOpen: boolean;
}

const mapStateToProps = (state: StoreState): State => ({
    ...state.resolveDeleteRequestPopup,
    ...mapStatetoHasPermission(state),
    ...mapStateToCurrentUser(state),
});

const ResolveDeleteRequestPopup = (props: IndexComponentPassedProps) => {
    const onReject = (evt: React.SyntheticEvent) => {
        evt.preventDefault();
        props.onReject({
            documents: props.documents,
            docPage: props.docPage,
        });
    };

    const onApply = (evt: React.SyntheticEvent) => {
        evt.preventDefault();
        props.onApply({
            documents: props.documents,
            docPage: props.docPage,
        });
    };

    const docsLength = props.documents.length;
    const doc = props.documents[0];

    const popupTitle = () => {
        if (!props.documents.length) return null;
        if (props.showReject) {
            return docsLength > 1
                ? t`Вiдхилення запиту на видалення документiв: ${docsLength}`
                : t`Вiдхилення запиту на видалення документу ${doc.title}`;
        }
        if (props.showDelete) {
            return docsLength > 1
                ? t`Ви впевненi що хочете видалити документи: ${docsLength}?`
                : t`Ви впевненi що хочете видалити документ?`;
        }
        return docsLength > 1
            ? t`Контрагент надiслав запит на видалення документiв: ${docsLength}`
            : t`Контрагент надiслав запит на видалення документу ${doc.title}`;
    };

    const Documents = () => {
        if (docsLength > 1) {
            return (
                <div>
                    <ul>
                        {props.documents.map((docItem: any) => {
                            return <li key={genKey()}>{docItem.title}</li>;
                        })}
                    </ul>
                </div>
            );
        }
        return null;
    };

    const DeleteRequestMessage = () => {
        if (!docsLength || docsLength > 1) {
            return null;
        }
        return (
            <div>
                <b>{t`Та вказав таку причину:`}</b>
                <div className={css.comment}>
                    {doc ? doc.deleteRequest.message : ''}
                </div>
            </div>
        );
    };

    const DefaultState = () => {
        if (props.showReject || props.showDelete) return null;

        const hasPermissionToDelete =
            props.hasPermission('canDeleteDocument') ||
            props.hasPermission('canDeleteDocumentExtended');

        return (
            <div>
                <Documents />
                <DeleteRequestMessage />
                <div className={css.buttons}>
                    <div className={css.button}>
                        <Button
                            disabled={!hasPermissionToDelete}
                            typeContour
                            type="submit"
                            theme="blue"
                            onClick={onReject}
                        >
                            {t`Вiдхилити запит на видалення`}
                        </Button>
                    </div>
                    <div className={css.button}>
                        <Button
                            disabled={!hasPermissionToDelete}
                            typeContour
                            type="submit"
                            theme="red"
                            onClick={onApply}
                        >
                            {t`Видалити`}
                        </Button>
                    </div>
                    <PseudoLink
                        onClick={props.onClose}
                    >{t`Відмінити`}</PseudoLink>
                </div>
            </div>
        );
    };

    const DocumentGroup = ({ documents }: { documents: Array<any> }) => {
        const currentEdrpou = props.currentUser.currentCompany.edrpou;
        return (
            <div>
                <b>
                    {documents[0].isMultilateral
                        ? t`Інформація про контрагентів`
                        : t`Інформація про контрагента`}
                </b>
                <ul>
                    {getRecipientsTitles(documents[0], currentEdrpou).map(
                        (title) => {
                            return <li key={genKey()}>{title}</li>;
                        },
                    )}
                    {documents.map((docItem: any) => {
                        return <li key={genKey()}>{docItem.title}</li>;
                    })}
                </ul>
            </div>
        );
    };

    const DocumentsList = () => {
        if (docsLength === 1) return null;
        return (
            <div>
                <div hidden={!props.isDocListOpen}>
                    {groupDocumentsByReceiverEdrpou(
                        props.documents,
                        props.currentUser.currentCompany.edrpou,
                    ).map((item: any) => {
                        return <DocumentGroup key={genKey()} {...item} />;
                    })}
                </div>
                {docsLength > 1 ? (
                    <div>
                        <PseudoLink onClick={props.onToggleDocList}>
                            {!props.isDocListOpen
                                ? t`Переглянути перелiк документiв`
                                : t`Приховати перелiк документiв`}
                        </PseudoLink>
                    </div>
                ) : null}
            </div>
        );
    };

    const ApplyState = () => {
        if (!props.showDelete) return null;

        const hasPermissionToDelete =
            props.hasPermission('canDeleteDocument') ||
            props.hasPermission('canDeleteDocumentExtended');

        const DeleteButtons = () => {
            return (
                <div className={css.buttons}>
                    <div className={css.button}>
                        <PseudoLink
                            onClick={props.onClose}
                            dataQa="qa_stop_delete"
                        >
                            {t`Не видаляти`}
                        </PseudoLink>
                    </div>
                    <Button
                        disabled={!hasPermissionToDelete}
                        isLoading={props.isLoading}
                        type="submit"
                        theme="cta"
                        onClick={props.onDelete}
                    >
                        {t`Видалити`}
                    </Button>
                </div>
            );
        };

        const Error = () => {
            return props.error ? <div>{props.error}</div> : null;
        };

        if (docsLength === 1) {
            const currentEdrpou = props.currentUser.currentCompany.edrpou;
            return (
                <div>
                    <div>
                        <StatusText>
                            <b>{doc.title}</b>
                        </StatusText>
                    </div>
                    <div>
                        <b>
                            {doc.isMultilateral
                                ? t`Інформація про контрагентів`
                                : t`Інформація про контрагента`}
                        </b>
                        <br />
                        <ul>
                            {getRecipientsTitles(doc, currentEdrpou).map(
                                (title) => {
                                    return <li key={genKey()}>{title}</li>;
                                },
                            )}
                        </ul>
                    </div>
                    <Error />
                    <DeleteButtons />
                </div>
            );
        }
        return (
            <div>
                <DocumentsList />
                <Error />
                <DeleteButtons />
            </div>
        );
    };

    const hasPermissionToDelete =
        props.hasPermission('canDeleteDocument') ||
        props.hasPermission('canDeleteDocumentExtended');

    return (
        <Popup
            active={props.isActive}
            title={popupTitle()}
            onClose={props.onClose}
        >
            <DefaultState />
            <ApplyState />
            <div hidden={!props.showReject}>
                <form onSubmit={props.onSubmit}>
                    <label htmlFor="message">
                        {t`Напишiть комментар, чому ви вирiшили вiдхилити запит на видалення, якщо потрiбно`}
                    </label>
                    <Textarea
                        key="message"
                        value={props.rejectMessage}
                        onChange={props.onChangeMessage}
                        name="message"
                    />
                    {props.error ? <div>{props.error}</div> : null}
                    <DocumentsList />
                    <div className={css.buttons}>
                        <div className={css.button}>
                            <Button
                                disabled={!hasPermissionToDelete}
                                isLoading={props.isLoading}
                                type="submit"
                                theme="cta"
                            >
                                {t`Вiдхилити запит на видалення`}
                            </Button>
                        </div>
                        <PseudoLink onClick={props.onClose}>
                            {t`Відмінити`}
                        </PseudoLink>
                    </div>
                </form>
            </div>
        </Popup>
    );
};

export default connect<State, IDispatchToProps>(
    mapStateToProps,
    actionCreators,
)(ResolveDeleteRequestPopup);
