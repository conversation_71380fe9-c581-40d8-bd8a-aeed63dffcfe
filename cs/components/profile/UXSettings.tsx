import React from 'react';

import { FlexBox, Switch } from '@vchasno/ui-kit';

import { useDocumentProcessesContext } from 'contexts/documentProcesses';
import { t } from 'ttag';
import PseudoLink from 'ui/pseudolink/pseudolink';

const UxSettings: React.FC = () => {
    const {
        isNewUpload,
        setNewUpload,
        onFeedbackClick,
    } = useDocumentProcessesContext();

    return (
        <FlexBox direction="column" align="flex-start">
            <Switch
                value={isNewUpload}
                size="sm"
                onChange={(event) => setNewUpload(event.target.checked)}
                label={t`Новий дизайн Завантаження/Налаштування документів`}
            />
            <FlexBox style={{ width: '100%' }} justify="flex-end">
                <PseudoLink
                    type="normal"
                    onClick={(event) => {
                        event.stopPropagation();
                        onFeedbackClick();
                    }}
                >{t`Залишити відгук`}</PseudoLink>
            </FlexBox>
        </FlexBox>
    );
};

export default UxSettings;
