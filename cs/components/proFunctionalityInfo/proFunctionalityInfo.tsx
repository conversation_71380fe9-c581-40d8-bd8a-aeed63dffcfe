import React, { FC, ReactNode } from 'react';

import cn from 'classnames';
import { t } from 'ttag';

import Icon from '../ui/icon/icon';

import ExtraFieldsIcon from './images/extra_fields.svg';
import ImportantFieldsIcon from './images/important_fields_for_docs.svg';
import InternalDocsIcon from './images/internal_documents.svg';
import LongTermStorageIcon from './images/long_term_storage.svg';
import ReviewIcon from './images/review.svg';
import TagsIcon from './images/tags.svg';
import TemplatesIcon from './images/templates.svg';

import css from './proFunctionalityInfo.css';

export type ProFuncId =
    | 'tags'
    | 'templates'
    | 'extraFields'
    | 'internalDocuments'
    | 'reviewers'
    | 'longTermStorage'
    | 'importantFields';

interface Props {
    funcId: ProFuncId;
    direction?: 'row' | 'column';
}
interface Context {
    icon: any;
    title: string;
    text: ReactNode;
}

const getContext = (funcId: ProFuncId): Context => {
    switch (funcId) {
        case 'tags':
            return {
                icon: (
                    <div className={css.iconImg}>
                        <Icon glyph={TagsIcon} />
                    </div>
                ),
                title: t`Ярлики.`,
                text: (
                    <>
                        {t`Ця функція дозволяє`}:
                        <ul className={css.dashList}>
                            <li>{t`Зручно групувати документи на зразок папок`}</li>
                            <li>
                                {t`Надати співробітникам доступ до документів тільки з певними ярликами`}
                            </li>
                            <li>{t`Налаштувати надходження вхідних та вихідних документів одразу на потрібних колег.`}</li>
                        </ul>
                    </>
                ),
            };
        case 'templates':
            return {
                icon: (
                    <div className={css.iconImg}>
                        <Icon glyph={TemplatesIcon} />
                    </div>
                ),
                title: t`Сценарії документів.`,
                text: t`
                    Автоматизуйте процес погодження та підписання документів.
                    Завдяки налаштованим сценаріям документи миттєво потраплять
                    до потрібних співробітників і у правильній послідовності.
                `,
            };
        case 'extraFields':
            return {
                icon: (
                    <div className={css.iconImg}>
                        <Icon glyph={ExtraFieldsIcon} />
                    </div>
                ),
                title: t`Додаткові параметри документів.`,
                text: t`
                    Додавайте будь-яку допоміжну інформацію до документу, наприклад,
                    призначення платежу, статтю витрат тощо. Це дозволить легко знайти,
                    одразу зрозуміти важливі деталі, швидко погодити і підписати документ.
                    Витрачайте мінімум часу на обробку документів.
                `,
            };
        case 'internalDocuments':
            return {
                icon: (
                    <div className={css.iconImg}>
                        <Icon glyph={InternalDocsIcon} />
                    </div>
                ),
                title: t`Внутрішні документи.`,
                text: t`
                    Впорядкуйте та автоматизуйте внутрішній документообіг. Погоджуйте та підписуйте
                    всередині компанії заяви, накази по підприємству, звіти по відрядженню, введення в експлуатацію тощо.
                `,
            };
        case 'reviewers':
            return {
                icon: (
                    <div className={css.iconImg}>
                        <Icon glyph={ReviewIcon} />
                    </div>
                ),
                title: t`Внутрішнє погодження.`,
                text: (
                    <>
                        {t`
                            Налаштуйте процес погодження документів всередині компанії, економте свій час.
                            Завдяки цій функції перед фінальним підписанням документи будуть вже перевірені
                            і узгоджені усіма відповідальними співробітниками. 
                        `}
                        <div>{t`Погоджуйте`}:</div>
                        <ul className={css.dashList}>
                            <li>{t`почергово – один співробітник за одним;`}</li>
                            <li>{t`параллельно у будь-якомі порядку.`}</li>
                        </ul>
                    </>
                ),
            };
        case 'longTermStorage':
            return {
                icon: (
                    <div className={css.iconImg}>
                        <Icon glyph={LongTermStorageIcon} />
                    </div>
                ),
                title: t`Зберігання документів необмежено.`,
                text: t`
                    Ваш електронний архів надійно зберігається у найзахищеніших хмарних дата-центрах Amazon.
                `,
            };
        case 'importantFields':
            return {
                icon: (
                    <div className={css.iconImg}>
                        <Icon glyph={ImportantFieldsIcon} />
                    </div>
                ),
                title: t`Обовʼязкові поля для вхідних документів.`,
                text: t`
                Встановіть дані документа, які має обов’язково вказати ваш контрагент перед відправкою документа у вашу компанію.
            `,
            };
        default:
            return { icon: '', title: '', text: '' };
    }
};

const ProFunctionalityInfo: FC<React.PropsWithChildren<Props>> = ({
    funcId,
    direction = 'row',
    children,
}) => {
    const { icon, title, text } = getContext(funcId);
    const rootClasses = cn(css.root, {
        [css.rootDirectionRow]: direction === 'row',
        [css.rootDirectionColumn]: direction === 'column',
    });

    return (
        <div className={rootClasses}>
            <div className={css.iconBlock}>
                <div className={css.icon}>{icon}</div>
            </div>
            <div className={css.content}>
                <div>
                    <div className={css.title}>{title}</div>
                    <div className={css.text}>{text}</div>
                </div>
                {children}
            </div>
        </div>
    );
};

export default ProFunctionalityInfo;
