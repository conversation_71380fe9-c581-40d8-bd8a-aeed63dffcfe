.root {
    display: flex;
    width: 100%;
    align-items: flex-end;
    gap: 10px;
}

.rootFullWidth {
    display: flex;
    align-items: flex-end;
}

.rootFullWidth .buttonHolder {
    display: block;
    width: 120px;
}

.input {
    display: block;
    width: 100%;
    box-sizing: border-box;
    padding: 10px 15px;
    border: 1px solid var(--default-border);
    background-color: var(--white-bg);
    border-radius: 8px 0 0 8px;
    box-shadow: none;
    font-family: 'Roboto', Arial, Tahoma, Helvetica, 'Liberation Sans', sans-serif;
    font-size: 13px;
    line-height: 18px;
    vertical-align: middle;
}

.input:focus {
    border-color: var(--primary-cta-color);
}

.chooseCs {
    margin-top: 15px;
}

.inputHolder {
    display: block;
    flex: 1 1 0;
    border: none;
}

.buttonHolder {
    display: table-cell;
    width: 118px;
    vertical-align: top;
}

.error {
    margin-top: 4px;
    color: var(--red-color);
}

.errorPanel {
    padding: 8px 12px 8px 16px;
    border-left: 3px solid #fcaf43;
    margin-top: 10px;
    background-color: var(--linen-color);
    border-radius: var(--border-radius);
    font-size: 13px;
    line-height: 15px;
}

.info {
    margin: 15px 0 15px;
}

.infoItem {
    font-size: 14px;
}

.hidden {
    display: none;
}

.buttonContainer {
    position: relative;
}

.hintIcon {
    position: absolute;
    top: 7px;
    right: -20px;
    width: 20px;
}

.hint {
    width: 200px;
}

.alert {
    margin-top: 15px;
}
