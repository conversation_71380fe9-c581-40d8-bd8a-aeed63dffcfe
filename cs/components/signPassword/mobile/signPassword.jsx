import React from 'react';

import PropTypes from 'prop-types';
import { t } from 'ttag';

import { getPKCodeTitle } from '../../../lib/helpers';

import Button from '../../ui/button/button';
import Dropdown from '../../ui/dropdown/dropdown';
import FakePasswordInput from '../../ui/fakePasswordInput/fakePasswordInput';
import Hint from '../../ui/hint/hint';

import CheckCertificateDate from '../../checkCertificateDate/checkCertificateDate';

// styles
import css from './signPassword.css';

const CA_ERROR_MESSAGE = t`Вкажіть АЦСК, щоб підписати`;
const PASSWORD_ERROR_MESSAGE = t`Вкажіть пароль, щоб підписати документ`;

class SignPassword extends React.Component {
    static propTypes = {
        isUploadedKey: PropTypes.bool.isRequired,
        isEditDisabled: PropTypes.bool.isRequired,
        isLoading: PropTypes.bool.isRequired,
        isChecked: PropTypes.bool.isRequired,
        isShownKeyInfo: PropTypes.bool,
        isCAServerEditDisabled: PropTypes.bool,
        showHint: PropTypes.bool,
        fullWidth: PropTypes.bool,
        keyInfo: PropTypes.object,
        certificateInfo: PropTypes.object,
        caServers: PropTypes.array.isRequired,
        caServerIdx: PropTypes.number,
        password: PropTypes.string,
        errorMessage: PropTypes.string,
        onSetPassword: PropTypes.func.isRequired,
        onChangeCAServer: PropTypes.func,
        onClick: PropTypes.func.isRequired,
        onValidationError: PropTypes.func,
        onSubmitTry: PropTypes.func,
    };

    static defaultProps = {
        isShownKeyInfo: true,
    };

    state = {
        isCAInvalid: false,
        isPasswordInvalid: false,
    };

    checkError = () => {
        let error = '';

        if (this.props.errorMessage) {
            error = this.props.errorMessage;
        } else if (this.state.isCAInvalid && this.state.isPasswordInvalid) {
            error = t`Вкажіть АЦСК і пароль, щоб підписати`;
        } else if (this.state.isCAInvalid) {
            error = CA_ERROR_MESSAGE;
        } else if (this.state.isPasswordInvalid) {
            error = PASSWORD_ERROR_MESSAGE;
        }

        return error;
    };

    validateCAInput = () => {
        const isValid =
            typeof this.props.caServerIdx === 'number' &&
            !isNaN(this.props.caServerIdx);
        this.setState({ isCAInvalid: !isValid });
        if (!isValid && typeof this.props.onValidationError === 'function') {
            this.props.onValidationError('caInput', CA_ERROR_MESSAGE);
        }
        return isValid;
    };

    validatePasswordInput = () => {
        const isValid =
            typeof this.props.password === 'string' &&
            this.props.password.length > 0;
        this.setState({ isPasswordInvalid: !isValid });
        if (!isValid && typeof this.props.onValidationError === 'function') {
            this.props.onValidationError('password', PASSWORD_ERROR_MESSAGE);
        }
        return isValid;
    };

    handlePasswordChange = (value) => {
        this.props.onSetPassword(value);
        this.setState({ isPasswordInvalid: false });
    };

    handleCAServerChange = (idx) => {
        this.props.onChangeCAServer(idx);
        this.setState({ isCAInvalid: false });
    };

    handleSignButtonClick = () => {
        if (typeof this.props.onSubmitTry === 'function') {
            this.props.onSubmitTry();
        }
        if (this.validateCAInput() && this.validatePasswordInput()) {
            this.props.onClick();
        }
    };

    render() {
        const { keyInfo, certificateInfo } = this.props;
        const caServers = Array.from(this.props.caServers);
        const errorMessage = this.checkError();
        const isButtonDisabled =
            this.props.showHint ||
            !this.props.isUploadedKey ||
            this.props.isEditDisabled;

        const caServersOptions = caServers.map((ca, idx) => {
            return {
                value: idx,
                label: ca.name || ca.issuerCNs[0],
                title: ca.issuerCNs[0],
            };
        });
        const caServerName =
            typeof this.props.caServerIdx === 'number' &&
            caServersOptions[this.props.caServerIdx].title;

        return (
            <div>
                {!this.props.isChecked && (
                    <div className={css.root}>
                        <div className={css.inputHolder}>
                            <Dropdown
                                name="caServer"
                                value={this.props.caServerIdx}
                                error={this.state.isCAInvalid}
                                placeholder={t`Оберіть АЦСК`}
                                options={caServersOptions}
                                size="big"
                                onChange={(idx) =>
                                    this.handleCAServerChange(idx)
                                }
                                disabled={
                                    this.props.isEditDisabled ||
                                    this.props.isCAServerEditDisabled
                                }
                            />
                        </div>
                        <div className={css.inputHolder}>
                            {/* added hidden input to prevent save-password prompt */}
                            <div className={css.hidden}>
                                <input type="password" />
                            </div>
                            <FakePasswordInput
                                autoComplete="off"
                                name="key-password"
                                value={this.props.password || ''}
                                error={this.state.isPasswordInvalid}
                                placeholder={t`Вкажіть пароль ключа`}
                                onChange={this.handlePasswordChange}
                                disabled={this.props.isEditDisabled}
                            />
                        </div>
                        <div className={css.buttonHolder}>
                            <div className={css.buttonContainer}>
                                <Button
                                    name="signButton"
                                    disabled={isButtonDisabled}
                                    theme="cta"
                                    width="full"
                                    isLoading={this.props.isLoading}
                                    onClick={this.handleSignButtonClick}
                                >
                                    {t`Зчитати`}
                                </Button>
                                {this.props.showHint && (
                                    <div className={css.hintIcon}>
                                        <Hint black position="right">
                                            <div className={css.hint}>
                                                {t`Щоб зчитати ключ, додайте<br />
                                                сертифікат захисту ключа.<br />
                                                Зазвичай це файли у форматі`}
                                                <br />
                                                <b>crt</b>, <b>cer</b>.
                                            </div>
                                        </Hint>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                )}
                {this.props.isShownKeyInfo && keyInfo && keyInfo.serialNumber && (
                    <div className={css.info}>
                        <div className={css.code}>
                            {getPKCodeTitle(keyInfo)}: {keyInfo.edrpou},
                        </div>
                        <div />
                        {t`Власник ключа`}: {keyInfo.ownerFullName}
                    </div>
                )}
                {errorMessage && (
                    <div className={css.error}>{errorMessage}</div>
                )}
                {(certificateInfo.keyEndDate ||
                    certificateInfo.certEndDate) && (
                    <CheckCertificateDate
                        certificateInfo={certificateInfo}
                        caServerName={caServerName}
                    />
                )}
            </div>
        );
    }
}

export default SignPassword;
