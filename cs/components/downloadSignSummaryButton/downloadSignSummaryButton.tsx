import React, { FC } from 'react';

import { BlackTooltip } from '@vchasno/ui-kit';

import { useDownloadSignSummary } from 'components/downloadSignSummaryButton/useDownloadSignSummary';
import { sendToGTMV4 } from 'services/analytics/gtm';
import { t } from 'ttag';

import IconButton from '../ui/iconButton/iconButton';
import Spinner from '../ui/spinner/spinner';

import SvgSave from './images/save.svg';

const DownloadSignSummaryButton: FC = () => {
    const {
        isSignSummaryLoading,
        handleDownloadSignSummary,
    } = useDownloadSignSummary();

    const onClickDownloadSignSummary = () => {
        sendToGTMV4({
            event: 'ec_docpage_icon_save_receipt_click',
        });
        handleDownloadSignSummary();
    };

    return (
        <BlackTooltip title={t`Зберегти квитанцію`}>
            {isSignSummaryLoading ? (
                <Spinner />
            ) : (
                <span>
                    <IconButton
                        svg={SvgSave}
                        onClick={onClickDownloadSignSummary}
                    />
                </span>
            )}
        </BlackTooltip>
    );
};

export default DownloadSignSummaryButton;
