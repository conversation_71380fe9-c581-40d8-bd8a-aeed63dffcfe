import React, { ChangeEvent, useRef } from 'react';
import <PERSON>Query from 'react-responsive';

import Button from '../../../ui/button/button';
import HiddenFileInput from '../../../ui/hiddenFileInput/hiddenFileInput';
import PopupMobile from '../../../ui/popup/mobile/popup';
import Popup from '../../../ui/popup/popup';
import PseudoLink from '../../../ui/pseudolink/pseudolink';

import { MEDIA_WIDTH } from '../../../../lib/constants';

import css from './ContractorsUploadPopup.css';

type Props = {
    isOpen: boolean;
    isUploadProcess: boolean;
    onClose: () => void;
    onUploadContactsFile: (file: File) => void;
};

const CONTACTS_TEMPLATE_FILE_LINK = `${config.STATIC_HOST}/files/company_check_csv_template.xlsx`;

const ContractorsUploadPopup: React.FC<React.PropsWithChildren<Props>> = ({
    isOpen,
    isUploadProcess,
    onClose,
    onUploadContactsFile,
}) => {
    const fileInputRef = useRef<HTMLInputElement>(null);

    if (!isOpen) {
        return null;
    }

    const handleUpload = (evt: ChangeEvent<HTMLInputElement>) => {
        const { files } = evt.currentTarget;
        if (files && files.length === 1) {
            onUploadContactsFile(files[0]);
            onClose();
        }
    };

    const popupProps = {
        active: true,
        onClose,
        title: 'Завантажити список контрагентів',
        subtitle:
            'Ви можете створити свій список контрагентів або скористатися нашим шаблоном',
    };

    const content = (
        <div className={css.root}>
            <HiddenFileInput
                ref={fileInputRef}
                accept=".csv,.xlsx"
                onChange={handleUpload}
            />

            <div className={css.step}>1. Створіть файл у вигляді таблиці</div>

            <div className={css.content}>
                <table className={css.table}>
                    <tbody>
                        <tr>
                            <th>
                                <b>ЄДРПОУ або ІПН</b>
                            </th>
                        </tr>
                        <tr>
                            <td>26709563</td>
                        </tr>
                        <tr>
                            <td>67804786</td>
                        </tr>
                    </tbody>
                </table>
                <div>
                    <div className={css.subtext}>
                        Створити таку таблицю можна у Microsoft Excel, Google
                        Tables, Numbers і в будь-яких аналогічних сервісах. Або
                        скористайтеся нашим шаблоном:
                    </div>
                    <a href={CONTACTS_TEMPLATE_FILE_LINK}>
                        <PseudoLink>Зберегти шаблон</PseudoLink>
                    </a>
                </div>
            </div>

            <div className={css.hint}>
                Ви можете завантажити не більш ніж 3000 ЄДРПОУ або ІПН в одній
                таблиці
            </div>

            <div className={css.step}>
                2. Експортуйте готовий файл у формат CSV або XLSX та завантажте
            </div>

            <div className={css.buttons}>
                <Button
                    isLoading={isUploadProcess}
                    theme="blue"
                    onClick={() => fileInputRef.current?.click()}
                >
                    Завантажити файл
                </Button>
                <PseudoLink onClick={onClose}>Відмінити</PseudoLink>
            </div>
        </div>
    );

    return (
        <>
            <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                <Popup centered {...popupProps}>
                    {content}
                </Popup>
            </MediaQuery>
            <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                <PopupMobile {...popupProps}>{content}</PopupMobile>
            </MediaQuery>
        </>
    );
};

export default ContractorsUploadPopup;
