.root {
    position: relative;
    display: flex;
    max-height: 650px;
    flex: 1;
    justify-content: center;
    padding: 40px;
    background-color: var(--white-bg);
}

.img {
    min-width: 460px;
    max-width: 460px;
    height: 220px;
    object-fit: contain;
}

.title {
    max-width: 460px;
    font-size: 30px;
    font-weight: bold;
}

.subtitle {
    max-width: 400px;
}

.buttons {
    display: grid;
    grid-column-gap: 10px;
    grid-row: 4;
    grid-template-columns: minmax(auto, 270px) minmax(auto, 180px);
}

.buttonBlock + .buttonBlock {
    margin-left: 10px;
}

.hintBlock {
    position: absolute;
    top: -25px;
    left: -45px;
    display: grid;
    height: 100px;
    color: var(--slate-grey-color);
    font-size: 14px;
    font-style: italic;
    grid-template-columns: 100px 200px;
    place-content: center;
    place-items: center;
}
