.root {
    position: relative;

    --document-creation-modal-width: 810px;
    --document-creation-modal-ver-padding: 40px;
    --document-creation-modal-hor-padding: 15px;
    --vchasno-ui-input-font-size: 14px;
}

.container {
    max-width: var(--document-creation-modal-width);
    box-sizing: border-box;
    padding: var(--document-creation-modal-ver-padding) var(--document-creation-modal-hor-padding);
    margin: 0 auto;
}

.logoWrapper {
    width: max-content;
    height: 40px;
}

.logoBox {
    min-width: 200px;
}

.spinnerWrapper {
    position: absolute;
    z-index: 2;
    top: var(--header-height);
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.9);
}

.header {
    align-items: center;
}

.createEmptyButton {
    display: none;
}

@media screen and (max-width: 1024px) {
    .createEmptyButton {
        display: block;
    }
}

@media screen and (max-width: 768px) {
    .root .header:has(button) {
        flex-direction: column;
        align-items: flex-start;
    }

    .header button {
        width: 100%;
    }
}

@media screen and (max-width: 480px) {
    .createEmptyButton:global(.vchasno-ui-button.--secondary) {
        position: fixed;
        z-index: 3;
        right: 10px;
        bottom: 10px;
        left: 10px;
        width: auto;
        background-color: var(--white-bg);
    }
}

