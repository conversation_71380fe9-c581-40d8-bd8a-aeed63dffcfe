import React, { useEffect } from 'react';
import {
    Controller,
    SubmitHandler,
    useFieldArray,
    useForm,
} from 'react-hook-form';

import {
    BlackTooltip,
    Button,
    Checkbox,
    FlexBox,
    ScrollableBox,
    Select,
    SelectOption,
    Text,
    TextInput,
    Title,
} from '@vchasno/ui-kit';

import cn from 'classnames';
import {
    getAutofillForm,
    getAutofillTypeSelectOptions,
    saveAutofillFormFields,
} from 'components/DocumentCreation/TemplateReplaceForm/utils';
import ArrowDown from 'icons/arrow-down.svg';
import DeleteSvg from 'icons/delete.svg';
import { CreationTemplate } from 'services/creationTemplates';
import { t } from 'ttag';
import CloseButton from 'ui/closeButton/closeButton';
import Icon from 'ui/icon';

import type { ReplaceFormField, ReplaceFormFieldType } from './types';

import SettingSVG from '../../../icons/setting-burger.svg';

import css from './TemplateReplaceForm.css';

const options = getAutofillTypeSelectOptions();

interface AddFieldForm {
    name: string;
    type: ReplaceFormFieldType;
    isRequired: boolean;
}

interface BuildReplaceForm {
    fields: Array<ReplaceFormField>;
}

export interface TemplateReplaceFormProps {
    className?: string;
    visible: boolean;
    onHide: () => void;
    title?: string;
    template: CreationTemplate;
}
// Тільки для демонстрації використання змінних та накладення їх на шаблон Collabora
const TemplateReplaceForm = ({
    className,
    visible,
    onHide,
    title,
    template,
}: TemplateReplaceFormProps) => {
    const [storedReplaceForm] = React.useState(() => getAutofillForm(template));
    const autofilledForm = useForm<BuildReplaceForm>({
        defaultValues: {
            fields: storedReplaceForm?.fields || [],
        },
    });

    const fieldsValue = autofilledForm.watch('fields');

    const addNewFieldForm = useForm<AddFieldForm>({
        defaultValues: {
            name: '',
            type: 'text',
            isRequired: false,
        },
    });

    const fields = useFieldArray({
        name: 'fields',
        control: autofilledForm.control,
    });

    useEffect(() => {
        if (!visible) {
            addNewFieldForm.reset();
        }
    }, [visible]);

    const commitForm = () =>
        saveAutofillFormFields(template, autofilledForm.getValues('fields'));

    const onSaveForm: SubmitHandler<BuildReplaceForm> = () => {
        commitForm();
    };

    const onSubmit: SubmitHandler<AddFieldForm> = (data) => {
        const isNameExist = fieldsValue.find((item) => item.name === data.name);

        if (isNameExist) {
            addNewFieldForm.setError('name', {
                type: 'manual',
                message: t`Поле з такою назвою вже існує`,
            });
        }

        if (data.name.trim() === '') {
            addNewFieldForm.setError('name', {
                type: 'manual',
                message: t`Назва поля не може бути пустою`,
            });
        }

        if (isNameExist || data.name.trim() === '') {
            return;
        }

        fields.append({
            name: data.name,
            type: data.type,
            value: '',
            validation: {
                required: data.isRequired,
            },
        });
        addNewFieldForm.reset();
        commitForm();
    };

    return (
        <>
            <aside
                className={cn(css.root, className, { [css.visible]: visible })}
            >
                <FlexBox
                    justify="space-between"
                    align="center"
                    className={css.header}
                >
                    <Title level={4}>{title}</Title>
                    <CloseButton withHover position="static" onClose={onHide} />
                </FlexBox>
                <ScrollableBox
                    scrollHeight="calc(100vh - 100px)"
                    className={css.content}
                    hideScroll
                >
                    <form
                        id="buildForm"
                        autoComplete="off"
                        noValidate
                        onSubmit={autofilledForm.handleSubmit(onSaveForm)}
                    >
                        <FlexBox direction="column">
                            {fields.fields.map((field, index) => (
                                <FlexBox
                                    key={field.id}
                                    direction="column"
                                    gap={5}
                                    className={css.fieldWrapper}
                                >
                                    <FlexBox
                                        justify="space-between"
                                        align="center"
                                    >
                                        <TextInput
                                            wide
                                            label={field.name}
                                            required={field.validation.required}
                                            placeholder={t`Значення за замовчуванням`}
                                            value={
                                                fieldsValue[index].value || ''
                                            }
                                            onChange={(event) => {
                                                autofilledForm.setValue(
                                                    `fields.${index}.value`,
                                                    event.target.value,
                                                );
                                                commitForm();
                                            }}
                                            hideEmptyMeta
                                        />
                                        <BlackTooltip
                                            title={t`Скопіювати до буфер обміну маркер`}
                                        >
                                            <Text
                                                ellipsis
                                                type="link"
                                                style={{
                                                    flexShrink: 0,
                                                    maxWidth: 100,
                                                }}
                                                onClick={() => {
                                                    navigator.clipboard.writeText(
                                                        `{{${field.name.toUpperCase()}}}`,
                                                    );
                                                }}
                                            >
                                                {`{{${field.name.toUpperCase()}}}`}
                                            </Text>
                                        </BlackTooltip>
                                        <BlackTooltip title={t`Видалити поле`}>
                                            <Button
                                                theme="danger"
                                                suppressPadding
                                                size="sm"
                                                style={{
                                                    flexShrink: 0,
                                                    alignSelf: 'flex-start',
                                                }}
                                                onClick={() => {
                                                    fields.remove(index);
                                                    commitForm();
                                                }}
                                            >
                                                <Icon
                                                    glyph={DeleteSvg}
                                                    className={css.icon}
                                                />
                                            </Button>
                                        </BlackTooltip>
                                    </FlexBox>
                                </FlexBox>
                            ))}
                        </FlexBox>
                    </form>
                    <FlexBox
                        direction="column"
                        align="center"
                        style={{ padding: 10 }}
                    >
                        <Text type="secondary">
                            <Icon
                                glyph={ArrowDown}
                                className={css.icon}
                                style={{ transform: 'rotate(180deg)' }}
                            />
                        </Text>
                        <Text type="secondary">{t`Щоб додати поле натисни Enter`}</Text>
                    </FlexBox>
                    <form
                        style={{ marginTop: 10 }}
                        onSubmit={addNewFieldForm.handleSubmit(onSubmit)}
                    >
                        <FlexBox direction="column">
                            <FlexBox>
                                <Controller
                                    name="name"
                                    control={addNewFieldForm.control}
                                    render={({ field, fieldState }) => (
                                        <TextInput
                                            wide
                                            {...field}
                                            label={t`Назва поля`}
                                            placeholder={t`Введіть назву поля`}
                                            hint={t`Назва поля має бути унікальною`}
                                            error={fieldState.error?.message}
                                        />
                                    )}
                                />
                                <Controller
                                    name="type"
                                    control={addNewFieldForm.control}
                                    render={({ field }) => (
                                        <Select
                                            hideEmptyMeta
                                            isSearchable={false}
                                            options={options}
                                            value={options.find(
                                                (item) =>
                                                    item.value === field.value,
                                            )}
                                            onChange={(option: SelectOption) =>
                                                field.onChange(option.value)
                                            }
                                            label={t`Тип поля`}
                                        />
                                    )}
                                />
                                <input type="submit" hidden />
                            </FlexBox>
                            <FlexBox direction="column">
                                <Controller
                                    name="isRequired"
                                    control={addNewFieldForm.control}
                                    render={({ field }) => (
                                        <Checkbox
                                            label={t`Обов'язкове`}
                                            checked={field.value as boolean}
                                            onChange={field.onChange}
                                        />
                                    )}
                                />
                            </FlexBox>
                        </FlexBox>
                    </form>
                </ScrollableBox>
            </aside>
        </>
    );
};

interface TemplateReplaceFormButtonProps {
    className?: string;
    onShow: () => void;
    template: CreationTemplate;
}

const TemplateReplaceFormButton = ({
    className,
    onShow,
}: TemplateReplaceFormButtonProps) => {
    return (
        <Button
            className={cn(css.btn, className)}
            onClick={onShow}
            theme="secondary"
        >
            {t`Поля`}
            <Icon glyph={SettingSVG} className={css.icon} />
        </Button>
    );
};

TemplateReplaceForm.Button = TemplateReplaceFormButton;

export default TemplateReplaceForm;
