import { SelectOption } from '@vchasno/ui-kit';

import { getLocalStorageItem, setLocalStorageItem } from 'lib/webStorage';
import { CreationTemplate } from 'services/creationTemplates';
import { t } from 'ttag';

import type { ReplaceFormField, StoredReplaceFormField } from './types';

export const getAutofillTypeSelectOptions = (): SelectOption[] => {
    return [
        {
            value: 'text',
            label: t`Текст`,
        },
        {
            value: 'date',
            label: t`Дата (дд.мм.рррр)`,
        },
        {
            value: 'number',
            label: t`Число (тільки цифри)`,
        },
    ];
};

const AUTOFILL_TEMPLATE_PREFIX = 'AUTOFILL_TEMPLATE_PREFIX_' as const;

export const composeAutofillTemplateKey = (id: string) => {
    return `${AUTOFILL_TEMPLATE_PREFIX}${id}` as const;
};

export const getAllLocalStorageAutofillTemplates = () => {
    return Object.keys(localStorage)
        .filter((key) => key.startsWith(AUTOFILL_TEMPLATE_PREFIX))
        .map((key) => getLocalStorageItem(key))
        .filter(isStoredReplaceFormField);
};

const isStoredReplaceFormField = (
    item: any,
): item is StoredReplaceFormField => {
    return item?.id && item?.title && item?.fields;
};

export const getAutofillForm = (template: CreationTemplate) => {
    const storedReplaceForm = getLocalStorageItem(
        composeAutofillTemplateKey(template.id),
    );

    if (isStoredReplaceFormField(storedReplaceForm)) {
        return storedReplaceForm;
    }

    return null;
};

export const saveAutofillFormFields = (
    template: CreationTemplate,
    fields: ReplaceFormField[],
) => {
    setLocalStorageItem(composeAutofillTemplateKey(template.id), {
        id: template.id,
        title: template.title,
        fields,
    } as StoredReplaceFormField);
};
