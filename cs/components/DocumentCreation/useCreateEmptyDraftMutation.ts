import { useMutation, useQueryClient } from '@tanstack/react-query';

import { GET_DOCUMENT_DRAFT_LIST } from 'lib/queriesConstants';
import { createEmptyDraft } from 'services/creationTemplates';

export const useCreateEmptyDraftMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (params: Parameters<typeof createEmptyDraft>) =>
            createEmptyDraft(...params),
        onSuccess: () => {
            queryClient.invalidateQueries([GET_DOCUMENT_DRAFT_LIST]);
        },
    });
};
