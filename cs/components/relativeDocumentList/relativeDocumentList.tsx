import * as React from 'react';

import { DocumentLink } from '../../services/documents/ts/types';

import Icon from '../ui/icon/icon';
import TextShorten from '../ui/textShorten/textShorten';

import { formatDate } from '../../lib/date';
import StatusText from '../statusText/statusText';

import SvgCross from './images/cross.svg';

import css from './relativeDocumentList.css';

interface Props {
    currentCompanyEdrpou: string;
    links: DocumentLink[];
    parentId?: string;
    childId?: string;
    onDelete: (a: string, b: string) => void;
}

const RelativeDocumentsList = (props: Props) => {
    return (
        <ul className={css.root}>
            {props.links
                .filter(
                    (link): link is Required<DocumentLink> =>
                        link.document !== undefined,
                )
                .map((link) => {
                    const item = link.document;
                    return (
                        <li className={css.item} key={item.id}>
                            <a
                                className={css.link}
                                href={`/app/documents/${item.id}`}
                            >
                                <TextShorten>{item.type}</TextShorten>
                                <div className={css.info}>
                                    <div className={css.date}>
                                        {formatDate(item.dateCreated)}
                                    </div>
                                    <TextShorten>{item.title}</TextShorten>
                                </div>
                                <StatusText
                                    statusColor={
                                        item.statusColor as
                                            | 'Red'
                                            | 'Orange'
                                            | 'Green'
                                    }
                                >
                                    {item.statusTitle}
                                </StatusText>
                            </a>
                            {props.currentCompanyEdrpou ===
                                link.creatorEdrpou && (
                                <div
                                    className={css.deleteBtn}
                                    onClick={() =>
                                        props.onDelete(
                                            props.parentId || item.id,
                                            props.childId || item.id,
                                        )
                                    }
                                >
                                    <Icon glyph={SvgCross} />
                                </div>
                            )}
                        </li>
                    );
                })}
        </ul>
    );
};

export default RelativeDocumentsList;
