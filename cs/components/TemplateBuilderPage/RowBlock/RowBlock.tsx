import React from 'react';

import cn from 'classnames';

import css from './RowBlock.css';

type Placement = 'top' | 'bottom' | 'left' | 'right';

const placementList: Placement[] = ['top', 'bottom', 'right', 'left'] as const;

export interface RowBlockProps {
    className?: string;
    onClick?: (placement: Placement) => void;
    onRemove?: () => void;
    width?: number | string;
}

const RowBlock: React.FC<RowBlockProps> = ({
    className,
    children,
    onClick,
    width = '100%',
}) => {
    const makeClickHandler = (placement: Placement) => () => {
        onClick?.(placement);
    };

    return (
        <div style={{ width }} className={cn(css.root, className)}>
            {placementList.map((placement) => (
                <div
                    key={placement}
                    onClick={makeClickHandler(placement)}
                    className={cn(css.line)}
                    data-placement={placement}
                />
            ))}

            {children}
        </div>
    );
};

export default RowBlock;
