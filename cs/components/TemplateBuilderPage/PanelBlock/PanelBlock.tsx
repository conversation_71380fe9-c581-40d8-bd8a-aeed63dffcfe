import React from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';

import cn from 'classnames';

import css from './PanelBlock.css';

export interface PanelBlockProps {
    className?: string;
    count?: number;
    renderChildren?: (index: number) => React.ReactNode;
}

const PanelBlock: React.FC<PanelBlockProps> = ({
    className,
    count = 2,
    renderChildren,
}) => {
    const panelList = React.useMemo(() => {
        return Array.from({ length: count }, (_, index) => ({
            id: String(index),
            order: index,
            content: renderChildren
                ? renderChildren(index)
                : `left ${index + 1}`,
        }));
    }, [count, renderChildren]);

    return (
        <PanelGroup autoSaveId="conditional" direction="horizontal">
            {panelList.map((item) => (
                <React.Fragment key={item.id}>
                    {item.order > 0 && (
                        <PanelResizeHandle className={css.separator} />
                    )}
                    <Panel
                        id={item.id}
                        order={item.order}
                        className={cn(css.panel, className)}
                    >
                        {item.content}
                    </Panel>
                </React.Fragment>
            ))}
        </PanelGroup>
    );
};

export default PanelBlock;
