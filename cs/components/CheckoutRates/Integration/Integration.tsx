import React from 'react';
import { useSelector } from 'react-redux';

import { getIntegrationRate } from '../../../selectors/checkout.selectors';

import Rate from './Rate/Rate';

import css from './Integration.css';

const Integration: React.FC = () => {
    const integrationRate = useSelector(getIntegrationRate);

    if (!integrationRate) {
        return null;
    }

    return (
        <div className={css.rate}>
            <Rate
                // @ts-expect-error TS2339 [FIXME] Comment is autogenerated
                isPurchasingRate={!!integrationRate?.can_buy}
                // @ts-expect-error TS2339 [FIXME] Comment is autogenerated
                isExtendRate={!!integrationRate?.can_extend}
                // @ts-expect-error TS2740 [FIXME] Comment is autogenerated
                rate={integrationRate}
                // @ts-expect-error TS2339 [FIXME] Comment is autogenerated
                isPurchasingDocuments={!!integrationRate?.can_buy_documents}
            />
        </div>
    );
};

export default Integration;
