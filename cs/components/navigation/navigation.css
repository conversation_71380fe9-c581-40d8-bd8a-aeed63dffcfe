.topList > .item > .link {
    font-weight: bold;
}

.item {
    position: relative;
    padding: 1px;
}

.item + .item {
    margin-top: 5px;
}

.innerList {
    overflow: hidden;
    max-height: 0;
    padding-left: 20px;
}

@media all and (max-width: 1024px) {
    .innerList {
        padding-left: 20px;
    }
}

.link,
.linkParent {
    display: block;
    padding: 5px 10px;
    border-radius: var(--border-radius);
    color: var(--content-color);
    cursor: pointer;
}

@media all and (max-width: 1024px) {
    .link,
    .linkParent {
        color: var(--link-color);
    }
}

.linkActive,
.link:hover,
.linkParent:hover {
    background: var(--pigeon-bg);
    color: var(--content-color);
    text-decoration: none;
}

@media all and (max-width: 1024px) {
    .linkActive {
        background: var(--corporate-color);
        color: var(--content-color);
    }

    .linkActive:hover {
        background: var(--corporate-color);
        color: var(--content-color);
    }
}

.linkParent {
    display: block;
    padding: 5px 10px 5px 30px;
    cursor: pointer;
    font-weight: bold;
}

@media all and (max-width: 1024px) {
    .linkParent {
        padding: 5px 10px;
        color: var(--link-color);
    }

    .linkParent:hover {
        background: var(--pigeon-bg);
        color: var(--content-color);
        cursor: default;
    }
}

.checkbox,
.arrow,
.trigger {
    position: absolute;
    top: 6px;
    left: 10px;
    width: 14px;
    height: 14px;
}

.arrow {
    box-sizing: border-box;
    padding: 3px;
    border: 1px solid var(--default-border);
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
    cursor: pointer;
}

.arrow::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 8px;
    height: 8px;
    margin: auto;
    background-image: url("data:image/svg+xml;charset=utf8,%3C?xml version='1.0' encoding='utf-8'?%3E%3C!-- Generator: Adobe Illustrator 20.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0) --%3E%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 65 65' style='enable-background:new 0 0 65 65;' xml:space='preserve'%3E%3Cg%3E%3Cpath d='M22,65c-2.1,0-4.3-0.8-5.9-2.4c-3.2-3.2-3.2-8.5,0-11.7l18.1-18.1L16.2,14.2c-3.2-3.2-3.2-8.5,0-11.7s8.5-3.2,11.7,0 l18.1,18.1c6.6,6.6,6.6,17.3,0,23.8L27.9,62.6C26.3,64.2,24.2,65,22,65z'/%3E%3C/g%3E%3C/svg%3E");
    content: '';
}

@media all and (max-width: 1024px) {
    .arrow {
        display: none;
    }
}

.trigger {
    z-index: 2;
    opacity: 0;
}

.trigger:checked + .linkParent .arrow::before {
    background-image: url("data:image/svg+xml;charset=utf8,%3C?xml version='1.0' encoding='utf-8'?%3E%3C!-- Generator: Adobe Illustrator 20.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0) --%3E%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 65 65' style='enable-background:new 0 0 65 65;' xml:space='preserve'%3E%3Cg%3E%3Cpath d='M0,23.2c0-2.1,0.8-4.3,2.4-5.9c3.2-3.2,8.5-3.2,11.7,0l18.1,18.1l18.5-18.1c3.2-3.2,8.5-3.2,11.7,0c3.2,3.2,3.2,8.5,0,11.7 L44.4,47.2c-6.6,6.6-17.3,6.6-23.8,0L2.4,29.1C0.8,27.4,0,25.3,0,23.2z'/%3E%3C/g%3E%3C/svg%3E");
}

.trigger:checked ~ .innerList {
    max-height: 500px;
}

.parent {
    position: relative;
}

.hint {
    width: 220px;
    font-weight: normal;
}
