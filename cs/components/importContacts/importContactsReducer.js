import actions from './importContactsActions';

const initState = {
    isActivePopup: false,
    isImportProcess: false,
    importResult: null,
    errorMessage: '',
};

const importContactsReducer = (state = initState, action) => {
    switch (action.type) {
        case actions.IMPORT_CONTACTS__SHOW:
            return {
                ...initState,
                isActivePopup: true,
            };
        case actions.IMPORT_CONTACTS__HIDE:
            return {
                ...state,
                isActivePopup: false,
            };
        case actions.IMPORT_CONTACTS__IMPORT_START:
            return {
                ...state,
                isImportProcess: true,
                errorMessage: '',
            };
        case actions.IMPORT_CONTACTS__IMPORT_FINISH:
            return {
                ...state,
                isImportProcess: false,
            };
        case actions.IMPORT_CONTACTS__SHOW_IMPORT_RESULT:
            return {
                ...state,
                importResult: action.importResult,
            };
        case actions.IMPORT_CONTACTS__SHOW_ERROR:
            return {
                ...state,
                errorMessage: action.errorMessage,
                isImportProcess: false,
            };
        default:
            return state;
    }
};

export default importContactsReducer;
