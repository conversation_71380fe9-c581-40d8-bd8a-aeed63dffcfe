import React, { useContext } from 'react';
import { useSelector } from 'react-redux';

import { useLocalStoredState } from 'lib/webStorage';
import { getAppFlags } from 'selectors/app.selectors';

type Position = 'top' | 'sidebar' | null;

const Context = React.createContext<{
    position: Position;
    setIsClose: () => void;
}>({
    position: null,
    setIsClose: () => {},
});

export const useDetectedInTimeBannerV3Context = () => useContext(Context);

function getBannerPosition(isClose: boolean, flags: any): Position | null {
    if (isClose || !flags.DONATE_DETECTED_IN_TIME_BANNER_V3) {
        return null;
    }

    return flags.DONATE_DETECTED_IN_TIME_BANNER_V3_SHOW_IN_SIDEBAR
        ? 'sidebar'
        : 'top';
}

const DetectedInTimeBannerV3Provider: React.FC = ({ children }) => {
    const [isClose, setIsClose] = useLocalStoredState<boolean>(
        'DetectedInTimeBannerV3_2025',
        false,
    );
    const flags = useSelector(getAppFlags);

    const position = getBannerPosition(isClose, flags);

    return (
        <Context.Provider
            value={{
                position: position,
                setIsClose: () => {
                    setIsClose(true);
                },
            }}
        >
            {children}
        </Context.Provider>
    );
};

export default DetectedInTimeBannerV3Provider;
