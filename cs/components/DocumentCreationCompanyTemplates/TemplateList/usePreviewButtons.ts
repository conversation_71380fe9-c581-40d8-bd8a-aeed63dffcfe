import { useDispatch } from 'react-redux';

import { ButtonConfig } from 'components/DocumentCreationCompanyTemplates/PreviewItem/types';
import { CreationTemplate } from 'services/creationTemplates';
import { setTemplate, setViewOnlyTemplate } from 'store/documentCreationSlice';
import { t } from 'ttag';

/**
 * Перегляд шаблону з подальшою можливістю перейти в режим редагування
 */
export const usePreviewTemplate = () => {
    const dispatch = useDispatch();

    return (item: CreationTemplate): ButtonConfig => {
        return {
            key: 'view',
            title: t`Переглянути`,
            onClick: () => {
                dispatch(setViewOnlyTemplate(item));
            },
        };
    };
};

/**
 * Відкриття редагування шаблону одразу
 */
export const useEditTemplate = () => {
    const dispatch = useDispatch();

    return (item: CreationTemplate): ButtonConfig => {
        return {
            key: 'edit',
            title: t`Редагувати`,
            onClick: () => {
                dispatch(setTemplate(item));
            },
        };
    };
};
