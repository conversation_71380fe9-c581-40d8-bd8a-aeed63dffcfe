import React from 'react';

import { FlexBox, Text, Title } from '@vchasno/ui-kit';

import { t } from 'ttag';

import welcomeImg from './images/welcome-box.png';

import css from './EmptyWelcomeBox.css';

const EmptyWelcomeBox: React.FC = () => {
    return (
        <FlexBox
            className={css.root}
            grow={1}
            justify="space-between"
            align="center"
        >
            <FlexBox direction="column" gap={12} className={css.textBlock}>
                <Title
                    level={3}
                >{t`Створюйте документи за допомогою шаблонів`}</Title>
                <Text type="secondary">{t`Використовуйте готові форми, в які потрібно лише внести свої дані. Працюйте швидше та без помилок`}</Text>
            </FlexBox>
            <img
                src={welcomeImg}
                style={{ width: 202, flexShrink: 0, height: 154 }}
                alt={t`Вчасно`}
            />
        </FlexBox>
    );
};

export default EmptyWelcomeBox;
