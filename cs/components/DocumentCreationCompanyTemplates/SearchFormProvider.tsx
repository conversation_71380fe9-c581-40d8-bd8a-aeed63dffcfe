import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { SelectOption } from '@vchasno/ui-kit';

export interface SearchBarForm {
    search: string;
    categories: SelectOption[];
}

export const SearchFormProvider: React.FC = ({ children }) => {
    const methods = useForm<SearchBarForm>({
        defaultValues: {
            search: '',
            categories: [],
        },
    });

    return <FormProvider {...methods}>{children}</FormProvider>;
};
