import { t } from 'ttag';
import * as yup from 'yup';

import { EditTemplateForm } from './types';

export const documentCreationTemplateSchema: yup.ObjectSchema<EditTemplateForm> = yup
    .object()
    .shape({
        title: yup
            .string()
            .required()
            .trim()
            .max(100, t`Назва шаблону повинна містити не більше 100 символів`)
            .min(2, t`Назва шаблону повинна містити не менше 2 символів`),
        category: yup.string().nullable().default(null).optional(),
    });
