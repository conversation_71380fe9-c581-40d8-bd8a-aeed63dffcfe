import React from 'react';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';

import { Button, FlexBox, Title } from '@vchasno/ui-kit';

import cn from 'classnames';
import OutlinedInput from 'components/ui/input/OutlinedInput/OutlinedInput';
import Popup from 'components/ui/popup/popup';
import PseudoLink from 'components/ui/pseudolink/pseudolink';
import { getCurrentUserPhone } from 'selectors/app.selectors';
import { getIsEmailChange2FAVerifyPopupActive } from 'selectors/profileForm.selectors';
import { t } from 'ttag';

import { useProfileEmailChange2FAVerifyConfirmPopup } from './useProfileEmailChange2FAVerifyConfirmPopup';

import lockerPng from './images/locker.png';

import css from './ProfileFormEmailChange2FAVerifyPopup.css';

interface ProfileFormEmailChange2FAVerifyPopupProps {}

const ProfileFormEmailChange2FAVerifyPopup: React.FC<ProfileFormEmailChange2FAVerifyPopupProps> = () => {
    const { isOpen, email } = useSelector(getIsEmailChange2FAVerifyPopupActive);
    const phoneNumber = useSelector(getCurrentUserPhone) ?? '-';

    const {
        control2FA,
        formState2FA,
        onResend2faCode,
        onSubmit2fa,
        handleSubmit2FA,
        onClose,
    } = useProfileEmailChange2FAVerifyConfirmPopup(email);

    return (
        <Popup active={isOpen} onClose={onClose} className={css.root}>
            <form className={css.form} onSubmit={handleSubmit2FA(onSubmit2fa)}>
                <FlexBox direction="column" gap={25}>
                    <img
                        className={css.icon}
                        src={lockerPng}
                        alt="Lock picture"
                    />
                    <FlexBox direction="column" gap={20}>
                        <Title
                            level={2}
                            className={cn(css.title, css.textCentered)}
                        >
                            {t`Двофакторна аутентифікація`}
                        </Title>
                        <p className={cn(css.subTitle, css.textCentered)}>
                            {t`Вкажіть код, який було вислано у Viber/SMS на ваш номер ${phoneNumber}`}
                        </p>
                        <FlexBox
                            className={css.codeContainer}
                            direction="column"
                            gap={11}
                        >
                            <Controller
                                control={control2FA}
                                name="code"
                                render={({ field, fieldState }) => (
                                    <OutlinedInput
                                        type="text"
                                        inputMode="numeric"
                                        required
                                        label={t`Введіть код`}
                                        value={field.value}
                                        onChange={(event) =>
                                            field.onChange(
                                                event.target.value.trim(),
                                            )
                                        }
                                        autoFocus
                                        error={fieldState.error?.message}
                                    />
                                )}
                            />
                            <div className={css.resendCodeLinkContainer}>
                                <PseudoLink
                                    className={css.resendCodeLink}
                                    onClick={onResend2faCode}
                                >
                                    {t`Надіслати повторно`}
                                </PseudoLink>
                            </div>
                        </FlexBox>
                    </FlexBox>
                    <FlexBox
                        className={css.buttonWrapper}
                        justify="space-between"
                        align="center"
                        gap={20}
                    >
                        <Button
                            theme="secondary"
                            size="lg"
                            wide
                            onClick={onClose}
                        >{t`Скасувати`}</Button>
                        <Button
                            type="submit"
                            size="lg"
                            wide
                            disabled={!formState2FA.isValid}
                            loading={formState2FA.isSubmitting}
                        >{t`Підтвердити`}</Button>
                    </FlexBox>
                </FlexBox>
            </form>
        </Popup>
    );
};

export default ProfileFormEmailChange2FAVerifyPopup;
