import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import MediaQuery from 'react-responsive';

import { t } from 'ttag';

import { StoreState } from '../../types/store';

import actionCreators from './contactsTagsEditPopupActionCreators';

import PopupMobile from '../ui/popup/mobile/popup';
import Popup from '../ui/popup/popup';
import TextShorten from '../ui/textShorten/textShorten';

import { MEDIA_WIDTH } from '../../lib/constants';
import AutosuggestionList from '../autosuggestionList/autosuggestionList';
import TagsEditForm from '../tagsEditForm/tagsEditForm';
import { IContact, OwnProps, Props } from './contactsTagsEditPopupTypes';

import css from './contactsTagsEditPopup.css';

function mapStateToProps(state: StoreState, props: OwnProps) {
    return {
        // @ts-expect-error TS2783 [FIXME] Comment is autogenerated
        onSubmitCallback: props.onSubmitCallback,
        ...state.contactsTagsEditPopup,
    };
}

const mapDispatchToProps = actionCreators;

class ContactsTagsEditPopup extends Component<Props> {
    formatChosenList = (list: IContact[]) =>
        list.map((contact) => ({
            id: contact.id,
            fields: [
                <TextShorten>
                    {contact.edrpou}
                    {contact.name && `, ${contact.name}`}
                </TextShorten>,
            ],
        }));

    renderSuggestion = (suggestion: IContact) => (
        <div className={css.contactRow}>
            <div>{suggestion.edrpou}</div>
            {suggestion.name && <TextShorten>, {suggestion.name}</TextShorten>}
        </div>
    );

    renderContent = () => {
        const props = this.props;
        const chosenContactsList = this.formatChosenList(props.contactsList);

        return (
            <div className={css.root}>
                <div className={css.autosuggest}>
                    <label className={css.label}>
                        {t`Вкажіть компанії-контрагенти, котрим хочете додати
                        ярлики`}
                    </label>
                    <AutosuggestionList
                        value={props.accessValue}
                        placeholder={t`Вкажіть ЄДРПОУ/ІПН компанії (від 3-ох символів)`}
                        showSuggestions={props.isContactsSuggestionsShown}
                        suggestionsData={props.suggestionContacts}
                        chosenItems={chosenContactsList}
                        onSuggestionClick={props.onContactsSuggestionClick}
                        onCloseSuggestions={props.onCloseContactsSuggestions}
                        onRemoveItem={props.onRemoveContact}
                        onDataChange={props.onAccessValueChange}
                        onAutosuggest={props.onAutosuggestContacts}
                        renderSuggestion={this.renderSuggestion}
                    />
                </div>
                <div className={css.autosuggest}>
                    <label className={css.label}>
                        {t`Вкажіть ярлики, котрі хочете присвоїти вказаним
                        контрагентам`}
                    </label>
                    <TagsEditForm
                        displayPlaceholder={false}
                        isLoading={props.isLoading}
                        isSuggestionsShown={props.isSuggestionsShown}
                        errorMessage={props.errorMessage}
                        newTags={props.newTags}
                        selectedTags={props.selectedTags}
                        suggestedTags={props.suggestedTags}
                        tags={props.tags}
                        onAddNewTag={props.onAddNewTag}
                        onAutosuggestTags={props.onAutosuggestTags}
                        onClose={props.onClose}
                        onCloseSuggestions={props.onCloseSuggestions}
                        onDeleteTagFromList={props.onDeleteTagFromList}
                        onSubmit={() => props.onSubmit(props.onSubmitCallback)}
                        onSuggestionClick={props.onSuggestionClick}
                    />
                </div>
            </div>
        );
    };

    render() {
        const props = this.props;
        const title = t`Додати контрагентам ярлики`;
        const subtitle = t`Додавши компаніям-контрагентам ярлики, вони будуть автоматично
            присвоюватись вхідним документам від вказаних компаній та надати доступ до
            документів співробітникам з такими самими ярликами.`;

        return (
            <Fragment>
                <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                    <Popup
                        greyColor
                        fullContent
                        active={props.isActive}
                        title={title}
                        subtitle={subtitle}
                        onClose={props.onClose}
                    >
                        {this.renderContent()}
                    </Popup>
                </MediaQuery>
                <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                    <PopupMobile
                        active={props.isActive}
                        title={title}
                        subtitle={subtitle}
                        onClose={props.onClose}
                    >
                        {this.renderContent()}
                    </PopupMobile>
                </MediaQuery>
            </Fragment>
        );
    }
}

export default connect(
    mapStateToProps,
    mapDispatchToProps,
    // @ts-expect-error TS2345 [FIXME] Comment is autogenerated
)(ContactsTagsEditPopup);
