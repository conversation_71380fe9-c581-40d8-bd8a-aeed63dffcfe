import React from 'react';

import { FlexBox, Text } from '@vchasno/ui-kit';

import { useAddToDirectoryPopupContext } from 'components/AddToDirectoryPopup/context';
import ArrowRightSvg from 'icons/arrow-right.svg';
import Icon from 'ui/icon';

import DirectorySvg from './images/directory.svg';

import css from './DirectoryItem.css';

interface DirectoryItemProps {
    id: number;
    name: string;
}

const DirectoryItem: React.FC<DirectoryItemProps> = (props) => {
    const { onChangeParentDirectoryId } = useAddToDirectoryPopupContext();

    return (
        <FlexBox
            className={css.container}
            justify="space-between"
            align="center"
            onClick={() => onChangeParentDirectoryId(props.id)}
        >
            <FlexBox gap={12} align="center" grow={1} style={{ width: 200 }}>
                <div className={css.icon}>
                    <Icon glyph={DirectorySvg} />
                </div>
                <Text ellipsis>{props.name}</Text>
            </FlexBox>
            <div className={css.icon}>
                <Icon glyph={ArrowRightSvg} />
            </div>
        </FlexBox>
    );
};

export default DirectoryItem;
