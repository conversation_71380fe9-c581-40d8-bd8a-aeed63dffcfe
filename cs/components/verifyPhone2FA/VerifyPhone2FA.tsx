import React, { FC, useEffect, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useHistory } from 'react-router-dom';

import PartnerLogos from 'components/AuthLayout/PartnerLogos';
import FlexBox from 'components/FlexBox/FlexBox';
import {
    ResendPhoneCodeAlert,
    useResendPhoneCodeLink,
} from 'components/ResendPhoneCodeLink/ResendPhoneCodeLink';
import PageTitle from 'components/pageTitle/pageTitle';
import PseudoLink from 'components/ui/pseudolink/pseudolink';
import { redirect } from 'lib/navigation';
import { getLocationQuery } from 'lib/url';
import eventTracking from 'services/analytics/eventTracking';
import auth from 'services/auth';
import { t } from 'ttag';
import Alert from 'ui/Alert/Alert';
import Button from 'ui/button/button';
import Checkbox from 'ui/checkbox/checkbox';
import Icon from 'ui/icon/icon';
import OutlinedInput from 'ui/input/OutlinedInput/OutlinedInput';

import { Verify2FAFormFields } from './types';

import { verify2FAFormResolver } from './validation';

import LockIcon from './images/lock.svg';

import css from './VerifyPhone2FA.css';

/**
 * @see https://web.dev/articles/sms-otp-form - SMS OTP form best practices
 */
const VerifyPhone2FA: FC = () => {
    const [phoneNumber, setPhoneNumber] = useState('+************');

    const [commonErrorMessage, setCommonErrorMessage] = useState<string>('');

    const history = useHistory();
    const searchQuery = getLocationQuery(history.location);

    const { control, handleSubmit, formState } = useForm<Verify2FAFormFields>({
        resolver: verify2FAFormResolver,
        defaultValues: {
            code: '',
            trusted: false,
        },
    });

    const resendLink = useResendPhoneCodeLink({
        eventCategory: '2fa_verify',
        eventAction: 'resend',
        resend: () => auth.resendPhone2FACode(),
        retryAfterSeconds: 60,
    });

    useEffect(() => {
        auth.getHiddenPhone().then((data) => setPhoneNumber(data.phone));
    }, []);

    const onSubmit: SubmitHandler<Verify2FAFormFields> = async ({
        code,
        trusted,
    }) => {
        try {
            const nextUrl = await auth.verifyPhone2FACode(code, trusted);
            eventTracking.sendEvent('2fa_verify', 'success', null, () =>
                redirect(searchQuery?.redirect || nextUrl),
            );
        } catch (error) {
            const errorMessage = error.reason
                ? error.reason
                : t`Йой! Щось пішло не так.`;
            setCommonErrorMessage(errorMessage);
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <PageTitle>{t`Двофакторна аутентифікація`}</PageTitle>
            <FlexBox className={css.container} justify="center" gap={0}>
                <div className={css.content}>
                    <PartnerLogos className={css.partnersLogos} />
                    <FlexBox
                        className={css.lockIconContainer}
                        align="center"
                        justify="center"
                    >
                        <Icon className={css.lockIcon} glyph={LockIcon} />
                    </FlexBox>
                    <div className={css.titleContainer}>
                        <h1 className={css.title}>
                            {t`Двофакторна аутентифікація`}
                        </h1>
                        <h4 className={css.description}>
                            {t`Вкажіть код, який було вислано у Viber/SMS на ваш номер`}{' '}
                            {phoneNumber}
                        </h4>
                    </div>
                    <FlexBox
                        className={css.codeContainer}
                        direction="column"
                        gap={11}
                    >
                        <Controller
                            control={control}
                            name="code"
                            render={({ field, fieldState }) => (
                                <OutlinedInput
                                    type="text"
                                    inputMode="numeric"
                                    autoComplete="one-time-code"
                                    required
                                    label={t`Введіть код`}
                                    value={field.value}
                                    onChange={(event) =>
                                        field.onChange(
                                            event.target.value.trim(),
                                        )
                                    }
                                    autoFocus
                                    error={fieldState.error?.message}
                                />
                            )}
                        />
                        <PseudoLink
                            className={css.resendLink}
                            onClick={() => resendLink.onClick()}
                            disabled={resendLink.disabled}
                        >{t`Надіслати повторно`}</PseudoLink>
                    </FlexBox>
                    <ResendPhoneCodeAlert
                        timer={resendLink.timer}
                        isSent={resendLink.isSent}
                    />
                    <Controller
                        control={control}
                        name="trusted"
                        render={({ field }) => (
                            // @ts-expect-error TS2741 [FIXME] Comment is autogenerated
                            <Checkbox
                                checked={field.value}
                                onChange={field.onChange}
                                color="deepSkyBlue"
                                text={t`Це надійний пристрій. Підтверджувати себе за кодом SMS/Viber раз на місяць`}
                            />
                        )}
                    />
                    {commonErrorMessage && (
                        <Alert theme="error" hideIcon>
                            {commonErrorMessage}
                        </Alert>
                    )}
                    <Button
                        className={css.submitBtn}
                        type="submit"
                        theme="darkGray"
                        disabled={formState.isSubmitting}
                    >{t`Увійти`}</Button>
                </div>
            </FlexBox>
        </form>
    );
};

export default VerifyPhone2FA;
