import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { jt, t } from 'ttag';

import {
    getSyncRoleErrorBannerEdrpou,
    getSyncRoleErrorBannerErrorCode,
    getSyncRoleErrorBannerErrorDetails,
} from '../../selectors/syncRolesErrorBanner.selectors';

import Alert from '../ui/Alert/Alert';
import Button from '../ui/button/button';
import Icon from '../ui/icon/icon';

import { redirect } from '../../lib/navigation';
import { RATES_PAGE_LINK } from '../../lib/routing/constants';
import FlexBox from '../FlexBox/FlexBox';
import { ERROR_CODES } from './constants';
import syncRolesErrorBannerActionsCreators from './syncRolesErrorBannerActionsCreators';

import CloseIcon from '../SuccessVerifiedUser/components/SuccessVerifiedUserLayout/images/close.svg';
import SadManIcon from './images/sad-man.svg';

import css from './SyncRolesErrorBanner.css';

const SyncRolesErrorBanner: React.FC = () => {
    const dispatch = useDispatch();

    const edrpou = useSelector(getSyncRoleErrorBannerEdrpou);
    const errorCode = useSelector(getSyncRoleErrorBannerErrorCode);
    const errorDetails = useSelector(getSyncRoleErrorBannerErrorDetails);

    const onClose = () => {
        dispatch(syncRolesErrorBannerActionsCreators.setDisplayStatusHidden());
    };

    const onBuyNewRate = () => {
        redirect(RATES_PAGE_LINK);
    };

    const edrpouBoldText = <b key={edrpou}>{edrpou}</b>;
    const domainsBoldTest = (
        <b key={errorDetails?.domains}>{errorDetails?.domains || ''}</b>
    );
    const textsMap: Record<
        string,
        { reason: string; errorDescription: string | string[] }
    > = {
        [ERROR_CODES.overdraft]: {
            reason: t`Йой 😥 у компанії закінчилися місця для нових співробітників`,
            errorDescription: jt`На жаль, не змогли вас додати до компанії ${edrpouBoldText} оскільки вичерпаний ліміт користувачів на тарифі. Зверніться до адміністратора компанії або придбайте більший тариф.`,
        },
        [ERROR_CODES.invalidEmailDomain]: {
            reason: t`Йой 😥 компанія обмежила перелік емейлів для співробітників`,
            errorDescription: jt`У компанії ${edrpouBoldText} для реєстрації дозволена email адреса, що закінчується на ${domainsBoldTest}`,
        },
    };

    return (
        <FlexBox className={css.root} direction="column" gap={20}>
            <h2 className={css.title}>{t`Перевірка компанії`}</h2>
            <div className={css.closeIconContainer} onClick={onClose}>
                <Icon className={css.closeIcon} glyph={CloseIcon} />
            </div>
            <FlexBox className={css.contentBox} gap={0}>
                <FlexBox className={css.content} direction="column" gap={32}>
                    <h1 className={css.reason}>{textsMap[errorCode].reason}</h1>
                    <Alert hideIcon theme="warning">
                        <span className={css.errorDescription}>
                            {textsMap[errorCode].errorDescription}
                        </span>
                    </Alert>
                    <FlexBox align="center" justify="flex-end" gap={15}>
                        <Button
                            theme="blue"
                            onClick={onClose}
                            typeContour
                        >{t`Закрити`}</Button>
                        {errorCode === ERROR_CODES.overdraft && (
                            <Button
                                theme="cta"
                                onClick={onBuyNewRate}
                            >{t`Придбати тариф`}</Button>
                        )}
                    </FlexBox>
                </FlexBox>
                <div className={css.imageContainer}>
                    <Icon className={css.image} glyph={SadManIcon} />
                </div>
            </FlexBox>
        </FlexBox>
    );
};

export default SyncRolesErrorBanner;
