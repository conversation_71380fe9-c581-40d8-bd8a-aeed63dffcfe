import { useSelector } from 'react-redux';

import { useIsDocumentPage } from 'hooks/useIsDocumentPage';
import { useIsEdi } from 'hooks/useIsEdi';
import {
    getIsSharedDocumentViewMode,
    getIsSignSessionMode,
} from 'selectors/app.selectors';

export const useIsShowDocumentEditSettingsIcon = () => {
    const isDocumentPage = useIsDocumentPage();
    const isSignSessionMode = useSelector(getIsSignSessionMode);
    const isSharedDocumentViewMode = useSelector(getIsSharedDocumentViewMode);
    const isEdi = useIsEdi();

    return (
        (isDocumentPage || isSignSessionMode || isSharedDocumentViewMode) &&
        !isEdi
    );
};
