export const employeePositionMap = {
    accountant: 'Бухгалтер',
    chief_accountant: 'Го<PERSON><PERSON><PERSON><PERSON> бухгалтер',
    owner: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    director: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    commercial_director: 'Комерційний директор',
    financial_director: 'Ф<PERSON>нан<PERSON>овий директор',
    it_director: 'IT-директор',
    inspector_of_personnel_department: 'Інспектор відділу кадрів',
    head_of_hr: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ик відділу кадрів',
    lawyer: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    head_legal_department: 'Керівник юридичного відділу',
    sales_specialist: 'Спеціаліст з продажів',
    other: 'Інше',
} as const;

export type EmployeePosition = keyof typeof employeePositionMap;

export const positionOptions: Array<{
    value: EmployeePosition;
    label: string;
}> = Object.entries(employeePositionMap).map(([value, label]) => ({
    value: value as EmployeePosition,
    label,
}));

export type PositionOptions = typeof positionOptions;

export const bannerRateMap = {
    free: 'Базовий',
    start: 'Старт',
    pro: 'Професійний',
    ultimate: 'Максимальний',
    trial: 'Тріал',
    archive_small: 'Архів (1000)',
    archive_big: 'Архів (5000)',
};

export type BannerRate = keyof typeof bannerRateMap;

export const bannerRateOptions: Array<{
    value: BannerRate;
    label: string;
}> = Object.entries(bannerRateMap).map(([value, label]) => ({
    value: value as BannerRate,
    label,
}));

export type BannerRateOptions = typeof bannerRateOptions;

export const audienceMap = {
    TOV: 'ТОВ',
    FOP: 'ФОП',
};

export type AudienceType = keyof typeof audienceMap;

export const audienceOptions: Array<{
    value: AudienceType;
    label: string;
}> = Object.entries(audienceMap).map(([value, label]) => ({
    value: value as AudienceType,
    label,
}));

export type AudienceOptionsType = typeof audienceOptions;

export const audienceSizeOptions = [
    { value: '1-10', label: '1-10' },
    { value: '11-50', label: '11-50' },
    { value: '51-100', label: '51-100' },
    { value: '101-500', label: '101-500' },
    { value: '501-1000', label: '501-1000' },
    { value: '1000 та більше', label: '1000 та більше' },
    { value: 'unknown', label: 'Невідомо' },
];

export type AudienceSizeType =
    | '1-10'
    | '11-50'
    | '51-100'
    | '101-500'
    | '501-1000'
    | '1000 та більше'
    | 'unknown';

export type AudienceSizeOptionsType = typeof audienceSizeOptions;

export const outgoingDocumentsCountOptions = [
    { value: 'zero', label: '0' },
    { value: '1_10', label: '1-10' },
    { value: '11_24', label: '11-24' },
    { value: '25_50', label: '25-50' },
    { value: '51_100', label: '51-100' },
    { value: '100_plus', label: '100 +' },
];

export type OutgoingDocumentsCountType =
    | 'zero'
    | '1_10'
    | '11_24'
    | '25_50'
    | '51_100'
    | '100_plus';

export type OutgoingDocumentsCountOptionsType = typeof outgoingDocumentsCountOptions;

export const incomingDocumentsSignCountOptions = [
    { value: 'zero', label: '0' },
    { value: '1_10', label: '1-10' },
    { value: '11_24', label: '11-24' },
    { value: '25_50', label: '25-50' },
    { value: '51_100', label: '51-100' },
    { value: '100_plus', label: '100 +' },
    { value: 'has_unsigned', label: 'Непідписані вхідні документи' },
];

export type IncomingDocumentsSignCountType =
    | 'zero'
    | '1_10'
    | '11_24'
    | '25_50'
    | '51_100'
    | '100_plus'
    | 'has_unsigned';

export type IncomingDocumentsSignCountOptionsType = typeof incomingDocumentsSignCountOptions;

export const activityPeriodOptions = [
    { value: '1month', label: 'Останній місяць' },
    { value: '2months', label: 'Останні 2 місяці' },
    { value: '3months', label: 'Останні 3 місяці' },
    { value: '6months', label: 'Останні 6 місяців' },
    { value: '1year', label: 'Останні 12 місяців' },
];

export type ActivityPeriodType =
    | '1month'
    | '2months'
    | '3months'
    | '6months'
    | '1year';

export type ActivityPeriodOptionsType = typeof activityPeriodOptions;
