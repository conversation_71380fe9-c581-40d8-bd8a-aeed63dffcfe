import { t } from 'ttag';
import { BillAcount } from 'types/billing';

import { Thunk } from '../../types';
import { CompanyAdminConfig, ICompany } from '../../types/user';

import { onAlertPopupShow } from '../app/appActionCreators';
import actions from './companyAdminCardActions';

import { getCompany } from '../../services/admin';
import { updateCompanyAdminConfig } from '../../services/user';

function loadCompanyData(search: string, id: string): Thunk {
    return async (dispatch) => {
        const company = await getCompany(id);
        dispatch({
            type: actions.COMPANY_ADMIN_CARD__SET_COMPANY_DATA,
            company,
            searchString: search,
        });
    };
}

export function setPreparedCompanyData(
    company: ICompany,
    searchString: string,
): Thunk {
    return async (dispatch) => {
        dispatch({
            type: actions.COMPANY_ADMIN_CARD__SET_COMPANY_DATA,
            company,
            searchString,
        });
    };
}

function clearCompanyData(): Thunk {
    return async (dispatch) => {
        dispatch({
            type: actions.COMPANY_ADMIN_CARD__CLEAR_COMPANY_DATA,
        });
    };
}

function setBillDataForActivate(billForActivate: BillAcount): Thunk {
    return async (dispatch) => {
        dispatch({
            type: actions.COMPANY_ADMIN_CARD__SET_BILL_DATA_FOR_ACTIVATE,
            billForActivate,
        });
    };
}

function clearBillDataForActivate(): Thunk {
    return async (dispatch) => {
        dispatch({
            type: actions.COMPANY_ADMIN_CARD__CLEAR_BILL_DATA_FOR_ACTIVATE,
        });
    };
}

function onChangeAdminConfig(config: CompanyAdminConfig): Thunk {
    return async (dispatch, getState) => {
        const {
            companyAdminCard: {
                company: { id },
                searchString,
            },
        } = getState();
        try {
            await updateCompanyAdminConfig(id, config);
            dispatch(loadCompanyData(searchString, id));
        } catch (err) {
            dispatch(onAlertPopupShow(t`Помилка!`, err.message));
        }
    };
}

export default {
    loadCompanyData,
    clearCompanyData,
    onChangeAdminConfig,
    setBillDataForActivate,
    clearBillDataForActivate,
};
