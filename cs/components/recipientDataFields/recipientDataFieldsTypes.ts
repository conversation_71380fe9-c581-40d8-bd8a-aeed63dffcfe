export interface IProps {
    isEmailHidden: boolean;
    isValidEmail: boolean;
    isValidEdrpou: boolean;
    showEmailSuggestions: boolean;
    showEdrpouSuggestions: boolean;
    suggestionUsers: IContactPerson[];
    suggestionCompanies: IContact[];
    email: string;
    emailErrorMessage: string;
    edrpou: string;
    edrpouErrorMessage: string;
    autosuggestByEmail: (email: string) => void;
    autosuggestByEdrpou: (edrpou: string) => void;
    onAddRecipient: () => void;
    onEmailChange: (email: string) => void;
    onEdrpouChange: (edrpou: string) => void;
    setEmailIsHidden: (value: boolean) => void;
    onCloseSuggestions: () => void;
    onSuggestionClick: (email: string, edrpou: string) => void;
}

export interface IContactPerson {
    mainRecipient?: boolean;
    id: string;
    email: string;
    firstName: string;
    secondName: string;
    lastName: string;
    contact: {
        id: string;
        name: string;
        shortName: string;
        edrpou: string;
    };
}

export interface IContact {
    isRegistered: boolean;
    id: string;
    name: string;
    shortName: string;
    edrpou: string;
    persons: {
        mainRecipient: boolean;
        id: string;
        email: string;
        firstName: string;
        secondName: string;
        lastName: string;
        phones: {
            phone: string;
        };
    };
}
