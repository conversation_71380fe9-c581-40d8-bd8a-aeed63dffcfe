.root {
    padding: 50px 0 20px;
    background-color: var(--white-bg);
}

.root :global(.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active) {
    background-color: #9aaabf;
}

.slideRoot {
    display: flex;
    width: 100%;
    justify-content: center;
    padding-bottom: 60px;
}

.imgBox {
    width: 25%;
    flex-shrink: 0;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
}

.contentBox header h4 {
    color: #6b8091;
    font-size: 16px;
    font-weight: 500;
    line-height: 19px;
    text-align: center;
    text-transform: uppercase;
}

.contentBox p {
    max-width: 700px;
    margin: 10px auto 0;
    color: var(--content-color);
    font-size: 36px;
    font-weight: 700;
    line-height: 40px;
    text-align: center;
}

@media all and (max-width: 768px) {
    .imgBox {
        display: none;
    }

    .contentBox {
        width: 100%;
    }

    .contentBox p {
        font-size: 24px;
        line-height: 26px;
    }
}

.contentBox footer {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}
