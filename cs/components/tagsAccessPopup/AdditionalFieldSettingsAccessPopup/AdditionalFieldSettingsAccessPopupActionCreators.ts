import { t } from 'ttag';

import { Thunk } from '../../../types';

import appActionCreators from '../../app/appActionCreators';
import companyCardActionCreators from '../../companyCard/companyCardActionCreators';
import generateActionCreators from '../accessPopupActionCreators';
import actions from '../tagsAccessPopupActions';

import { splitArrayIntoChunks } from '../../../lib/helpers';

import eventTracking from '../../../services/analytics/eventTracking';
import {
    createDocumentsFieldsAccess,
    deleteDocumentsFieldsAccess,
} from '../../../services/user';
import { ADDITIONAL_FIELDS_TAG, chunkSize } from '../constants';

const actionCreators = generateActionCreators(
    ADDITIONAL_FIELDS_TAG,
    'additionalFieldsAccessPopup',
);

export const onSubmit: () => Thunk = () => async (dispatch, getState) => {
    const {
        additionalFieldsAccessPopup: { usersList, selectedTags },
        app: {
            currentUser: { roleId },
        },
    } = getState();

    try {
        dispatch({ type: actions.ACCESS_POPUP__START_SUBMIT });

        const field = selectedTags[0];

        const initialRoles = field.roles;

        const initialRolesSet = new Set(initialRoles.map(({ id }) => id));

        const selectedRolesSet = new Set(usersList.map(({ id }) => id));

        const addedRoles = usersList.filter(
            (role) => !initialRolesSet.has(role.id),
        );

        const removedRoles = initialRoles.filter(
            (role) => !selectedRolesSet.has(role.id),
        );

        if (addedRoles.length === 0 && removedRoles.length === 0) {
            dispatch({
                type: actions.ACCESS_POPUP__SHOW_ERROR_MESSAGE,
                errorMessage: t`Вкажіть хоча б одного нового або приберіть старих`,
                tag: ADDITIONAL_FIELDS_TAG,
            });

            return;
        }

        const addedRolesChunks =
            addedRoles.length > 0
                ? splitArrayIntoChunks(
                      addedRoles.map(({ id }) => id),
                      chunkSize,
                  )
                : [];

        const removedRolesChunks =
            removedRoles.length > 0
                ? splitArrayIntoChunks(
                      removedRoles.map(({ id }) => id),
                      chunkSize,
                  )
                : [];

        const addRequests = addedRolesChunks.map((ids) => {
            return createDocumentsFieldsAccess({
                fields_ids: [field.id],
                roles_ids: ids,
            });
        });

        const deleteRequests = removedRolesChunks.map((ids) => {
            return deleteDocumentsFieldsAccess({
                fields_ids: [field.id],
                roles_ids: ids,
            });
        });

        await Promise.all([...addRequests, ...deleteRequests]);
        dispatch(companyCardActionCreators.loadCompanyData(roleId));
        await dispatch(appActionCreators.onFetchCompanyDocumentFields());
    } catch (err) {
        dispatch({
            type: actions.ACCESS_POPUP__SHOW_ERROR_MESSAGE,
            errorMessage: err.reason,
            tag: ADDITIONAL_FIELDS_TAG,
        });
        return;
    }

    eventTracking.sendEvent('additional-fields', 'access-rights');
    dispatch(actionCreators.onClose());
};
