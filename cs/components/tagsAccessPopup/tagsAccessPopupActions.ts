const actions = {
    ACCESS_POPUP__OPEN: 'ACCESS_POPUP__OPEN',
    ACCESS_POPUP__OPEN_PRESELECTED: 'ACCESS_POPUP__OPEN_PRESELECTED',
    ACCESS_POPUP__CLOSE: 'ACCESS_POPUP__CLOSE',
    ACCESS_POPUP__SHOW_ERROR_MESSAGE: 'ACCESS_POPUP__SHOW_ERROR_MESSAGE',
    ACCESS_POPUP__START_SUBMIT: 'ACCESS_POPUP__START_SUBMIT',
    ACCESS_POPUP__CHANGE_TAGS_TO_DELETE: 'ACCESS_POPUP__CHANGE_TAGS_TO_DELETE',

    // suggestions
    ACCESS_POPUP__CHANGE_SELECTED_LIST: 'ACCESS_POPUP__CHANGE_SELECTED_LIST',
    ACCESS_POPUP__CHANGE_NEW_TAGS: 'TAGS_ACCESS_POPUP_CHANGE__NEW_TAGS',
    ACCESS_POPUP__SHOW_SUGGESTIONS_TAGS: 'ACCESS_POPUP__SHOW_SUGGESTIONS_TAGS',
    ACCESS_POPUP__CLOSE_SUGGESTIONS_TAGS:
        'ACCESS_POPUP__CLOSE_SUGGESTIONS_TAGS',

    // user access
    ACCESS_POPUP__CHANGE_USERS_LIST: 'ACCESS_POPUP__CHANGE_USERS_LIST',
    ACCESS_POPUP__CLOSE_USER_SUGGESTIONS:
        'ACCESS_POPUP__CLOSE_USER_SUGGESTIONS',
    ACCESS_POPUP__CHANGE_SUGGESTION_USERS:
        'ACCESS_POPUP__CHANGE_SUGGESTION_USERS',
    ACCESS_POPUP__CHANGE_USER_VALUE: 'ACCESS_POPUP__CHANGE_USER_VALUE',
    ACCESS_POPUP__SHOW_SUGGESTIONS_USERS:
        'ACCESS_POPUP__SHOW_SUGGESTIONS_USERS',
};

export default actions;
