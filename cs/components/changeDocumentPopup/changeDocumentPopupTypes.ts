import { ChangeEvent, FormEvent } from 'react';

import { DocumentParameter } from 'services/documentFields/types';
import {
    ReviewSettings as RS,
    UpdateDocumentPayload,
} from 'services/documents/ts/api';
import { DocumentReviewProcess, DocumentSignProcess } from 'services/enums';

import { Nullable } from '../../types/general';
import { ICompany, IRole, IUser, RoleOrGroup } from '../../types/user';
import { DocumentListDocumentItem } from '../documentList/types';
import { Template } from '../documentTemplates/types';

import { ITag } from '../ui/tags/tagsTypes';

// reexport types for smooth migration to api types
export interface DocumentEditableData extends UpdateDocumentPayload {}

export type ReviewSettings = RS;

export type MenuID =
    | 'order'
    | 'info'
    | 'signers'
    | 'reviewers'
    | 'viewers'
    | 'templates'
    | 'tags'
    | 'extraFields';

export interface RoleWithSignedDocument extends RoleOrGroup {
    isRoleSignedDocument: boolean;
}

export interface State {
    id: Nullable<string>;

    isActive: boolean;
    isDocumentPage: boolean;
    documents: DocumentListDocumentItem[];
    isSubmitting: boolean;
    errorMessage: string;
    warningMessage: string;
    selectedItemIndex?: MenuID;
    isTagsSuggestionsShown: boolean;
    selectedTags: ITag[];
    tags: ITag[];
    allTags: ITag[];
    newTags: ITag[];
    suggestedTags: ITag[];

    docTitle?: string;
    docNumber?: string;
    docDate?: Date;
    docStatus: string;
    docAmount?: Nullable<string>;
    docCurrentTitle?: string;

    // order state
    docCategory?: string;

    // signers state
    signerValue: string;
    previousSignersList: IRole[];
    previousSignProcess: DocumentSignProcess;
    signersList: RoleOrGroup[];
    signProcess: DocumentSignProcess;
    showSignersSuggestion: boolean;
    signersSuggestions: IRole[];

    // reviewers state
    reviewersList: RoleOrGroup[];
    deleteReviewersList: string[];
    reviewerValue: string;
    isReviewersSuggestionsShown: boolean;
    reviewersSuggestions: IRole[];
    isReviewRequired: boolean;
    reviewProcess: DocumentReviewProcess;
    reviewProcessEditable: boolean;

    // viewers state
    viewerValue: string;
    viewersList: IRole[];
    isViewersSuggestionShown: boolean;
    viewersSuggestion: IRole[];

    // templates state
    activeDocumentTemplate: Nullable<Template>;
    documentTemplates: Template[];
    documentTemplateOptions: Array<{ value: string; label: string }>;

    documentParameters: DocumentParameter[];

    currentCompany: ICompany;

    prevDocumentEditableData: Nullable<DocumentEditableData>;
}

export interface IDispatchToProps {
    onSubmit: (evt: FormEvent, analyticsService: VoidFunction) => void;
    onChangeDate: (date: Date) => void;
    onChangeField: (evt: ChangeEvent) => void;
    onChangeAmount: (value: string) => void;
    onShow: ({ documents, isDocumentPage }: State) => void;
    onClose: () => void;

    // order actions
    onChangeCategory: (value: string) => void;

    // signers actions
    onSignerDataChange: (value: string) => void;
    onSignersSuggestionClick: (suggestion: IRole) => void;
    onRearrangeSignersItems: (currentIndex: number, indexTo: number) => void;
    onRemoveSigner: (id: string) => void;
    onAutosuggestSigners: (search: string) => void;
    onCloseSignersSuggestions: () => void;
    onSignProcessTypeChange: (value: string) => void;
    onMenuItemClick: (value: string) => void;

    // reviewers actions
    onReviewerSuggestionClick: (suggestion: IRole) => void;
    onCloseReviewerSuggestions: () => void;
    onRemoveReviewer: (id: string) => void;
    onReviewerDataChange: (value: string) => void;
    onAutosuggestReviewers: (search: string) => void;
    onToggleIsRequiredReview: (checked: boolean) => void;
    onReviewerProcessTypeChange: (value: DocumentReviewProcess) => void;
    onRearrangeReviewersItems: (currentIndex: number, indexTo: number) => void;

    // viewers actions
    onViewersSuggestionClick: (suggestion: IRole) => void;
    onCloseViewersSuggestion: () => void;
    onRemoveViewer: (id: string) => void;
    onViewerDataChange: (value: string) => void;
    onAutosuggestViewers: (search: string) => void;

    onDocumentTemplateChange: (value: string) => void;
    onDocumentParametersChange: (data: DocumentParameter[]) => void;

    // tags actions
    onTagsCloseSuggestions: () => void;
    onTagsAutosuggest: (value: string) => void;
    onAddNewTag: (value: string) => void;
    onTagsDelete: (tag: ITag) => void;
    onTagsSuggestionClick: (suggestion: { id: string }) => void;
}

export interface Signer {
    id: string;
    user: IUser;
}
