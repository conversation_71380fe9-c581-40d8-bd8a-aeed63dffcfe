.root {
    display: flex;
    width: 740px;
    min-height: 540px;
}

.buttons {
    display: flex;
    align-items: center;
    margin-top: 20px;
}

.date {
    max-width: 240px;
}

.pseudolink {
    margin-left: 20px;
}

.title {
    max-width: 240px;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: bold;
}

.block {
    margin-top: 20px;
}

.item,
.itemActive {
    padding: 6px 10px;
    cursor: pointer;
}

.item + .item {
    margin-top: 6px;
}

.content {
    flex: 1;
    margin-bottom: 20px;
}

.radiobuttons {
    position: relative;
    z-index: 1;
    margin-top: 15px;
    margin-bottom: 15px;
}

.itemActive {
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
    font-weight: bold;
}

.sidebar {
    min-width: 300px;
    box-sizing: border-box;
    padding: 40px 30px;
}

.settings {
    display: flex;
    min-width: 380px;
    flex-direction: column;
    padding: 36px 30px 40px;
    background-color: var(--white-bg);
    border-radius: 0 8px 8px 0;
}

.section {
    margin-top: 16px;
}

.label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
}

.disabledListItem {
    color: var(--dark-pigeon-color);
}

.proLabel {
    display: inline-block;
    margin-left: 5px;
}

.proRatePrice {
    display: grid;
    margin-top: 30px;
    grid-row-gap: 20px;
}

.info {
    margin-top: 20px;
    color: var(--dark-pigeon-color);
}
