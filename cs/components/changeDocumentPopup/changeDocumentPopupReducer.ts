import { AnyAction } from 'redux';
import { DocumentReviewProcess, DocumentSignProcess } from 'services/enums';

import actions from './changeDocumentPopupActions';

import { State } from './changeDocumentPopupTypes';

const initState: Omit<State, 'docDate' | 'currentCompany'> = {
    id: null,
    isActive: false,
    isDocumentPage: false,
    isTagsSuggestionsShown: false,
    documents: [],
    isSubmitting: false,
    errorMessage: '',
    warningMessage: '',
    selectedItemIndex: 'info',

    docTitle: '',
    docNumber: '',
    docStatus: '',
    docAmount: null,
    docCurrentTitle: '',

    docCategory: '',

    signerValue: '',
    signersList: [],
    signProcess: DocumentSignProcess.PARALLEL_PROCESS_TYPE,
    previousSignersList: [],
    previousSignProcess: DocumentSignProcess.PARALLEL_PROCESS_TYPE,
    showSignersSuggestion: false,
    signersSuggestions: [],

    reviewersList: [],
    deleteReviewersList: [],
    reviewerValue: '',
    isReviewersSuggestionsShown: false,
    reviewersSuggestions: [],
    isReviewRequired: false,
    reviewProcess: DocumentReviewProcess.PARALLEL_PROCESS_TYPE,
    reviewProcessEditable: false,
    selectedTags: [],
    tags: [],
    allTags: [],
    newTags: [],
    suggestedTags: [],

    viewerValue: '',
    viewersList: [],
    isViewersSuggestionShown: false,
    viewersSuggestion: [],

    activeDocumentTemplate: null,
    documentTemplates: [],
    documentTemplateOptions: [],

    documentParameters: [],

    prevDocumentEditableData: null,
};

/* eslint-disable complexity */
const changeDocumentPopupReducer = (
    state: State | undefined,
    action: AnyAction,
): State => {
    if (typeof state === 'undefined') return initState as State;
    switch (action.type) {
        case actions.CHANGE_DOCUMENT_POPUP__INFO_CHANGE_FIELD:
            return {
                ...state,
                docNumber:
                    typeof action.docNumber === 'string'
                        ? action.docNumber
                        : state.docNumber,
                docTitle:
                    typeof action.docTitle === 'string'
                        ? action.docTitle
                        : state.docTitle,
                docAmount:
                    typeof action.docAmount === 'string'
                        ? action.docAmount !== ''
                            ? action.docAmount
                            : null
                        : state.docAmount,
            };
        case actions.CHANGE_DOCUMENT_POPUP__INFO_CHANGE_DATE:
            return {
                ...state,
                docDate: action.docDate,
            };
        case actions.CHANGE_DOCUMENT_POPUP__ORDER_CHANGE_CATEGORY:
            return {
                ...state,
                docCategory: action.docCategory,
            };
        case actions.CHANGE_DOCUMENT_POPUP__SHOW:
            return {
                ...initState,
                id: action.id,
                currentCompany: action.currentCompany,
                // we don't need popup if change document required fields
                isActive:
                    typeof action.isActive !== 'undefined'
                        ? action.isActive
                        : true,
                isDocumentPage: action.isDocumentPage,
                documents: action.documents || state.documents,
                selectedItemIndex: action.selectedItemIndex,

                docTitle: action.docTitle || state.docTitle,
                docNumber: action.docNumber || state.docNumber,
                docAmount: action.docAmount ?? state.docAmount,
                docDate: action.docDate,
                docStatus: action.docStatus,
                docCurrentTitle: action.docTitle,

                docCategory: action.docCategory,

                signersList: action.signersList || state.signersList,
                signProcess:
                    action.signProcess ||
                    DocumentSignProcess.PARALLEL_PROCESS_TYPE,
                previousSignersList: action.signersList || state.signersList,
                previousSignProcess:
                    action.signProcess ||
                    DocumentSignProcess.PARALLEL_PROCESS_TYPE,
                reviewProcess: action.reviewProcess,
                reviewProcessEditable: action.reviewProcessEditable,

                documentParameters: action.documentParameters || [],
                selectedTags: action.selectedTags || state.selectedTags,
                reviewersList: action.reviewersList || state.reviewersList,
                isReviewRequired: action.isReviewRequired || false,
            };
        case actions.CHANGE_DOCUMENT_POPUP__CLOSE:
            return {
                ...state,
                ...initState,
                isActive: false,
            };
        case actions.CHANGE_DOCUMENT_POPUP__SET_SUBMIT_INFO:
            return {
                ...state,
                isSubmitting: action.isSubmitting,
                errorMessage: action.errorMessage,
            };
        case actions.CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_SIGNERS:
            return {
                ...state,
                signersSuggestions: action.signersSuggestions,
            };
        case actions.CHANGE_DOCUMENT_POPUP__CHANGE_SIGNER_VALUE:
            return {
                ...state,
                signerValue: action.signerValue,
            };
        case actions.CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_SIGNERS:
            return {
                ...state,
                showSignersSuggestion: action.showSignersSuggestion,
            };
        case actions.CHANGE_DOCUMENT_POPUP__CHANGE_SIGNERS_LIST:
            return {
                ...state,
                signersList: action.signersList,
                activeDocumentTemplate: initState.activeDocumentTemplate,
            };
        case actions.CHANGE_DOCUMENT_POPUP__TOGGLE_SIGN_PROCESS_TYPE:
            return {
                ...state,
                signProcess: action.signProcess,
                activeDocumentTemplate: initState.activeDocumentTemplate,
            };
        case actions.CHANGE_DOCUMENT_POPUP__ON_MENU_CLICK:
            return {
                ...state,
                selectedItemIndex: action.selectedItemIndex,
            };
        case actions.CHANGE_DOCUMENT_POPUP__CHANGE_REVIEWERS_LIST:
            return {
                ...state,
                reviewersList: action.reviewersList,
                deleteReviewersList:
                    action.deleteReviewersList || state.deleteReviewersList,
                activeDocumentTemplate: initState.activeDocumentTemplate,
            };
        case actions.CHANGE_DOCUMENT_POPUP__CHANGE_REVIEWER_VALUE:
            return {
                ...state,
                reviewerValue: action.reviewerValue,
            };
        case actions.CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_REVIEWERS:
            return {
                ...state,
                isReviewersSuggestionsShown: action.isReviewersSuggestionsShown,
            };
        case actions.CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_REVIEWERS:
            return {
                ...state,
                reviewersSuggestions: action.reviewersSuggestions,
            };
        case actions.CHANGE_DOCUMENT_POPUP__TOGGLE_IS_REVIEW_REQUIRED:
            return {
                ...state,
                isReviewRequired: action.isReviewRequired,
                activeDocumentTemplate: initState.activeDocumentTemplate,
            };
        case actions.CHANGE_DOCUMENT_POPUP__TOGGLE_REVIEW_PROCESS_TYPE:
            return {
                ...state,
                reviewProcess: action.reviewProcess,
            };
        case actions.CHANGE_DOCUMENT_POPUP__CHANGE_VIEWERS_LIST:
            return {
                ...state,
                viewersList: action.viewersList,
                activeDocumentTemplate: initState.activeDocumentTemplate,
            };
        case actions.CHANGE_DOCUMENT_POPUP__CHANGE_VIEWER_VALUE:
            return {
                ...state,
                viewerValue: action.viewerValue,
            };
        case actions.CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_VIEWERS:
            return {
                ...state,
                isViewersSuggestionShown: action.isViewersSuggestionShown,
            };
        case actions.CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_VIEWERS:
            return {
                ...state,
                viewersSuggestion: action.viewersSuggestion,
            };
        case actions.CHANGE_DOCUMENT_POPUP__SET_DOCUMENT_TEMPLATES:
            return {
                ...state,
                documentTemplates: action.documentTemplates,
                documentTemplateOptions: action.documentTemplateOptions,
                tags: action.tags,
            };
        case actions.CHANGE_DOCUMENT_POPUP__SET_ACTIVE_DOCUMENT_TEMPLATE:
            return {
                ...state,
                activeDocumentTemplate: action.activeDocumentTemplate,
                viewersList: action.viewersList || initState.viewersList,
                signersList: action.signersList || initState.signersList,
                reviewersList: action.reviewersList || initState.reviewersList,
                reviewProcess: action.reviewProcess || initState.reviewProcess,
                isReviewRequired:
                    action.isReviewRequired === undefined
                        ? initState.isReviewRequired
                        : action.isReviewRequired,
                selectedTags: action.selectedTags || initState.selectedTags,
                signProcess:
                    action.signProcess === undefined
                        ? initState.signProcess
                        : action.signProcess,
                documentParameters:
                    action.documentParameters || initState.documentParameters,
                warningMessage: action.warningMessage,
            };
        case actions.CHANGE_DOCUMENT_POPUP__SET_DOCUMENT_PARAMETERS:
            return { ...state, documentParameters: action.data };
        // tags reducers
        case actions.CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_NEW:
            return { ...state, newTags: action.newTags };
        case actions.CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_SELECTED:
            return {
                ...state,
                selectedTags: action.selectedTags,
            };
        case actions.CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_SUGGESTION:
            return {
                ...state,
                isTagsSuggestionsShown: action.isTagsSuggestionsShown,
                suggestedTags: action.suggestedTags,
                allTags: action.allTags || state.allTags,
            };
        case actions.CHANGE_DOCUMENT_POPUP__INIT_PREV_DOCUMENT_DATA:
            return {
                ...state,
                prevDocumentEditableData:
                    action.prevDocumentEditableData ||
                    initState.prevDocumentEditableData,
            };

        case actions.CHANGE_DOCUMENT_POPUP__SET_DOCUMENTS:
            return {
                ...state,
                documents: action.payload,
            };
        default:
            return state;
    }
};

export default changeDocumentPopupReducer;
