import React from 'react';
import Helmet from 'react-helmet';
import { useDispatch, useSelector } from 'react-redux';

import { BlackTooltip } from '@vchasno/ui-kit';

import cn from 'classnames';
import { PermissionCategoryToPopupName } from 'components/TrialInfoPopup/constants';
import { getCurrentCompanyPermissionMap } from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { GTMEventName } from 'services/analytics/gtm';
import { setTrialInfoOpenTargetPopup } from 'store/trialInfoPopup';
import { t } from 'ttag';

import { PermissionCategory } from '../proRateInfoPopup/proRateInfoPopupTypes';

import lockOpenPNGSrs from './images/lock_open.png';

import css from './proLabel.css';

const LABELS: Partial<Record<PermissionCategory, string>> = {
    [PermissionCategory.ADDITIONAL_FIELDS]: 'Pro+',
    [PermissionCategory.INTERNAL_DOCUMENTS]: 'Pro',
    [PermissionCategory.REQUIRED_FIELDS]: 'Pro+',
    [PermissionCategory.REVIEWS]: 'Pro',
    [PermissionCategory.TAGS]: 'Pro',
    [PermissionCategory.TEMPLATES]: 'Pro+',
};

interface ProLabelProps {
    classNames?: string;
    permission: PermissionCategory;
    isShowLock?: boolean;
    lockSize?: number;
    clicked?: boolean;
    locationID?: string;
    eventGA?: GTMEventName<string>;
}

const ProLabel: React.FC<React.PropsWithChildren<ProLabelProps>> = ({
    classNames,
    permission,
    isShowLock,
    lockSize = 32,
    clicked,
    locationID,
    eventGA,
}) => {
    const dispatch = useDispatch();
    const showLabel = !useSelector(getCurrentCompanyPermissionMap)[permission];

    if (!showLabel) {
        return null;
    }

    if (isShowLock) {
        return (
            <BlackTooltip title={t`Відкрити`}>
                <div
                    className={cn(css.image, classNames)}
                    style={{
                        width: lockSize,
                        height: lockSize,
                    }}
                    {...(clicked && {
                        onClick: () => {
                            dispatch(
                                setTrialInfoOpenTargetPopup(
                                    PermissionCategoryToPopupName[permission],
                                    locationID || '',
                                ),
                            );

                            if (eventGA) {
                                eventTracking.sendToGTMV4({
                                    event: eventGA,
                                });
                            }
                        },
                    })}
                >
                    <Helmet>
                        <link rel="prefetch" href={lockOpenPNGSrs} />
                    </Helmet>
                </div>
            </BlackTooltip>
        );
    }

    return <div className={cn(css.root, classNames)}>{LABELS[permission]}</div>;
};

export default ProLabel;
