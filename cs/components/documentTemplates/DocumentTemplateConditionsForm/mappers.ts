import {
    DocumentSide,
    DocumentSignProcess,
    TemplateConditionsFormValues,
} from 'components/documentTemplates/DocumentTemplateConditionsForm/types';
import { ConditionsFormInputIds } from 'components/documentTemplates/constants';
import { ConditionsValues } from 'components/documentTemplates/types';

type ConditionToFormMapper<T extends keyof TemplateConditionsFormValues> = (
    conditionList: ConditionsValues[],
) => TemplateConditionsFormValues[T];

export const conditionListToFormCounterpartyList: ConditionToFormMapper<'counterpartyList'> = (
    conditionList,
) => {
    return conditionList
        .filter(
            ([condition]) =>
                condition === ConditionsFormInputIds.DOCUMENT_RECIPIENT_EDRPOU,
        )
        .map(([_, value]) => ({
            edrpou: value as string,
        }));
};

export const conditionListToFormDocumentSide: ConditionToFormMapper<'documentSide'> = (
    conditionList,
) => {
    return conditionList
        .filter(
            ([condition]) => condition === ConditionsFormInputIds.DOCUMENT_SIDE,
        )
        .map(([_, value]) => value as DocumentSide);
};

export const conditionListToFormDocumentCategories: ConditionToFormMapper<'documentCategories'> = (
    conditionList,
) => {
    return conditionList
        .filter(
            ([condition]) =>
                condition === ConditionsFormInputIds.DOCUMENT_CATEGORY,
        )
        .map(([_, value]) => String(value));
};

export const conditionListToFormDocumentSignProcess: ConditionToFormMapper<'documentSignProcess'> = (
    conditionList,
) => {
    return conditionList
        .filter(
            ([condition]) =>
                condition === ConditionsFormInputIds.DOCUMENT_SIGN_PROCESS,
        )
        .map(([_, value]) => value as DocumentSignProcess);
};

export const conditionListToFormEmployeeUploadedBy: ConditionToFormMapper<'employeeUploadedBy'> = (
    conditionList,
) => {
    return conditionList
        .filter(
            ([condition]) =>
                condition === ConditionsFormInputIds.DOCUMENT_UPLOADED_BY,
        )
        .map(([_, value]) => value as string)
        .flat();
};

export const conditionListToFormAmount: ConditionToFormMapper<'amount'> = (
    conditionList,
) => {
    const amountCondition = conditionList.find(
        ([condition]) => condition === '#document_amount',
    );

    return amountCondition
        ? (amountCondition[1] as TemplateConditionsFormValues['amount'])
        : '';
};

export const conditionListToFormEmployeeRecipients: ConditionToFormMapper<'employeeRecipients'> = (
    conditionList,
) => {
    return conditionList
        .filter(
            ([condition]) =>
                condition === ConditionsFormInputIds.DOCUMENT_RECIPIENT_EMAIL,
        )
        .map(([_, value]) => value as string)
        .flat();
};

export const formToConditionListMapper = (
    formValues: TemplateConditionsFormValues,
): ConditionsValues[] => {
    const conditions: ConditionsValues[] = [];

    if (formValues.documentSide.length > 0) {
        formValues.documentSide.forEach((side) => {
            conditions.push([
                ConditionsFormInputIds.DOCUMENT_SIDE,
                side as DocumentSide,
            ]);
        });
    }

    if (formValues.counterpartyList.length > 0) {
        formValues.counterpartyList.forEach((counterparty) => {
            conditions.push([
                ConditionsFormInputIds.DOCUMENT_RECIPIENT_EDRPOU,
                counterparty.edrpou,
            ]);
        });
    }

    if (formValues.documentCategories.length > 0) {
        formValues.documentCategories.forEach((category) => {
            conditions.push([
                ConditionsFormInputIds.DOCUMENT_CATEGORY,
                String(category),
            ]);
        });
    }

    if (formValues.documentSignProcess.length > 0) {
        formValues.documentSignProcess.forEach((process) => {
            conditions.push([
                ConditionsFormInputIds.DOCUMENT_SIGN_PROCESS,
                process as DocumentSignProcess,
            ]);
        });
    }

    if (formValues.employeeUploadedBy.length > 0) {
        formValues.employeeUploadedBy.forEach((employee) => {
            conditions.push([
                ConditionsFormInputIds.DOCUMENT_UPLOADED_BY,
                employee,
            ]);
        });
    }

    if (formValues.employeeRecipients.length > 0) {
        formValues.employeeRecipients.forEach((recipient) => {
            conditions.push([
                ConditionsFormInputIds.DOCUMENT_RECIPIENT_EMAIL,
                recipient,
            ]);
        });
    }

    if (formValues.amount) {
        conditions.push([
            ConditionsFormInputIds.DOCUMENT_AMOUNT,
            formValues.amount as TemplateConditionsFormValues['amount'],
        ]);
    }

    return conditions;
};
