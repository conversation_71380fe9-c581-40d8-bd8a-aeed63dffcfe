import React, { <PERSON> } from 'react';

import cn from 'classnames';

import css from './documentTemplatesPagination.css';

interface Props {
    iterator: number[];
    currentPage: number;
    onCounterClick: (index: number) => void;
}

const PaginationCounters: FC<Props> = ({
    iterator,
    currentPage,
    onCounterClick,
}) => {
    const counterItems = iterator.map((index) => {
        const classes = cn(css.link, {
            [css.linkCurrent]: currentPage === index,
        });
        return (
            <li className={css.item} key={index}>
                <a
                    href="#documentTemplatesSettings"
                    className={classes}
                    onClick={() => onCounterClick(index)}
                >
                    <span className={css.number}>{index}</span>
                </a>
            </li>
        );
    });

    return <ul className={css.list}>{counterItems}</ul>;
};

export default PaginationCounters;
