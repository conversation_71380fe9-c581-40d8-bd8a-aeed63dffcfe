.root {
    border: 1px solid var(--pigeon-color);
    border-radius: var(--border-radius);
}

.root + .root {
    margin-top: 10px;
}

.header {
    display: flex;
    min-height: 70px;
    box-sizing: border-box;
    align-items: center;
    padding: 16px 32px;
    background-color: var(--grey-bg);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.actionButtons {
    display: flex;
}

.icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    margin-left: 20px;
    color: var(--grey-color);
    cursor: pointer;
}

.content {
    display: flex;
    width: 100%;
    box-sizing: border-box;
    padding: 16px 32px;
    gap: 5px;
}

.section {
    width: 1px;
    box-sizing: border-box;
    flex-grow: 1;
    flex-shrink: 0;
}

.inactive {
    opacity: 0.5;
}

.title {
    margin-bottom: 14px;
    font-weight: bold;
}

.switch {
    display: flex;
    align-items: center;
    margin-left: 30px;
}

.assignedTo {
    display: flex;
    align-items: center;
    margin-top: 5px;
    font-size: 14px;
    font-weight: 400;
}

.assignedToEmail {
    display: inline-block;
    margin-left: 2px;
    color: var(--link-color);
}

.assignedToIcon {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-left: 4px;
    color: var(--grey-color);
}

.assignedToAttention,
.assignedToAttention .assignedToEmail,
.assignedToAttention .assignedToIcon {
    color: var(--red-color);
}

@media screen and (max-width: 768px) {
    .header {
        flex-direction: column;
        padding: 16px;
        gap: 20px;
    }

    .switch {
        margin: 0;
    }

    .icon {
        margin: 0 10px;
    }
}

@media screen and (max-width: 480px) {
    .content {
        flex-direction: column;
        padding: 16px;
    }

    .section {
        display: flex;
        width: 100%;
        flex-direction: column;
        margin-top: 15px;
    }

    .title {
        border-bottom: 1px solid var(--default-border);
        font-size: 16px;
    }
}
