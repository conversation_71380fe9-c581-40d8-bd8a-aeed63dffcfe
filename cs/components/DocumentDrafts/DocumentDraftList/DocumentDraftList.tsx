import React from 'react';
import { useDispatch } from 'react-redux';

import { BlackTooltip, Text } from '@vchasno/ui-kit';

import { useDeleteDraft } from 'components/DocumentDrafts/DocumentDraftList/useDeleteDraft';
import { getAlertMessageDraftWillDeleteSoon } from 'components/DocumentDrafts/DocumentDraftList/utils';
import { useDraftFetch } from 'components/DocumentDrafts/DraftFetchProvider';
import { formatActualDate } from 'lib/date';
import { DraftListItemType } from 'services/creationTemplates';
import { setDraftTemplate } from 'store/documentCreationSlice';
import { t } from 'ttag';
import Icon from 'ui/icon';

import AlarmSvg from '../../../icons/alarm.svg';
import DeleteSvg from '../../../icons/delete.svg';

import css from './DocumentDraftList.css';

const draftTypeToLabel: Record<DraftListItemType, string> = {
    standalone: t`Порожній документ`,
    template: t`Шаблон`,
    version: t`Версія`,
};

const DocumentDraftList: React.FC = () => {
    const { data } = useDraftFetch();
    const dispatch = useDispatch();
    const deleteDraftActionConfig = useDeleteDraft();

    return (
        <table className={css.root}>
            <colgroup>
                <col style={{ width: '30%' }} />
                <col />
                <col />
                <col style={{ width: '50px' }} />
                <col style={{ width: '50px' }} />
            </colgroup>
            <thead>
                <tr>
                    <th>{t`Назва`}</th>
                    <th>{t`Останні зміни`}</th>
                    <th>{t`Тип`}</th>
                    <th />
                    <th />
                </tr>
            </thead>
            <tbody>
                {data.map((draft) => {
                    const deleteActionConfig = deleteDraftActionConfig(
                        draft.id,
                    );
                    const deleteAlertMsg = getAlertMessageDraftWillDeleteSoon(
                        draft,
                    );
                    return (
                        <tr
                            key={draft.id}
                            onClick={() => {
                                dispatch(
                                    setDraftTemplate({
                                        id: draft.id,
                                        title: draft.template?.title || '',
                                        category:
                                            draft.template?.category ?? null,
                                    }),
                                );
                            }}
                        >
                            <td>
                                <Text>{draft.template?.title || '-'}</Text>
                            </td>
                            <td>{formatActualDate(draft.dateUpdated)}</td>
                            <td>{draftTypeToLabel[draft.type]}</td>
                            <td>
                                {deleteAlertMsg && (
                                    <BlackTooltip
                                        title={deleteAlertMsg}
                                        disableInteractive
                                    >
                                        <span className={css.alarmIcon}>
                                            <Icon glyph={AlarmSvg} />
                                        </span>
                                    </BlackTooltip>
                                )}
                            </td>
                            <td>
                                <BlackTooltip
                                    title={t`Видалити`}
                                    disableInteractive
                                >
                                    <span
                                        className={css.removeButton}
                                        onClick={(event) => {
                                            event.stopPropagation();
                                            deleteActionConfig.onClick();
                                        }}
                                    >
                                        <Icon glyph={DeleteSvg} />
                                    </span>
                                </BlackTooltip>
                            </td>
                        </tr>
                    );
                })}
            </tbody>
        </table>
    );
};

export default DocumentDraftList;
