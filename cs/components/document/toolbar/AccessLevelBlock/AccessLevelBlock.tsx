import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
    Alert,
    BlackTooltip,
    FlexBox,
    ScrollableBox,
    Text,
} from '@vchasno/ui-kit';

import cn from 'classnames';
import { DocumentAccessToggleSmall } from 'components/DocumentAccessToggle';
import {
    canEditDocumentAccessLevel,
    getEditDocumentAccessLevelValidationReason,
} from 'components/DocumentEdit/utils';
import { onConfirmPopupShow } from 'components/app/appActionCreators';
import documentActionCreators from 'components/document/documentActionCreators';
import { accessValidationError } from 'components/document/toolbar/AccessLevelBlock/mappers';
import { useDocRemoveViewerMutation } from 'components/document/toolbar/AccessLevelBlock/useDocRemoveViewerMutation';
import GroupTooltip from 'components/uploaderPopup/SignersReviewersAutosuggestionList/ChosenListItems/GroupTooltip';
import { formatFullName } from 'lib/helpers';
import { formatUserIdentifier } from 'lib/ts/helpers';
import { getCurrentCompany, getCurrentUser } from 'selectors/app.selectors';
import { getCurrentDocument } from 'selectors/document.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { DocumentAccessLevel } from 'services/documents/ts/types';
import { jt, t } from 'ttag';
import Icon from 'ui/icon';

import { GroupMember, MembersGroup } from '../../../../types/user';

import RoleAccessItem from './RoleAccessItem';
import { useDocAccessLevelMutation } from './useDocAccessLevelMutation';

import PlusSvg from '../../../../icons/plus.svg';

import css from './AccessLevelBlock.css';

const AccessLevelBlock: React.FC = () => {
    const dispatch = useDispatch();
    const doc = useSelector(getCurrentDocument);
    const currentUser = useSelector(getCurrentUser);
    const currentCompany = useSelector(getCurrentCompany);

    const docAccessLevelMutation = useDocAccessLevelMutation();
    const docRemoveViewerMutation = useDocRemoveViewerMutation();

    if (!currentCompany || !doc) {
        return null;
    }

    const canEdit = canEditDocumentAccessLevel(doc, currentUser);

    const reviewRequestsDisplay = useMemo(
        () =>
            doc.reviewRequests.filter((item) => {
                // не повторюємося з підписантом у списку команд
                if (
                    item.toGroupId &&
                    doc.signers.some(
                        (signer) => signer.groupId === item.toGroupId,
                    )
                ) {
                    return false;
                }

                // не повторюємося з підписантом у списку користувачів
                if (
                    item.toRoleId &&
                    doc.signers.some(
                        (signer) => signer.roleId === item.toRoleId,
                    )
                ) {
                    return false;
                }

                // показуємо без повторень груп чи користувачів так як все в одному списку виводимо
                return true;
            }),
        [doc],
    );

    const accessLevel = doc.accessLevel;

    const handleAddViewAccess = () => {
        dispatch(
            documentActionCreators.onOpenChangeDocumentPopup({
                selectedItemIndex: 'viewers',
            }),
        );

        // редагування списку доступів (додавання / видалення людини із доступів, тобто відповідно коли натискають на “+”
        eventTracking.sendToGTMV4({
            event: 'ec_access_change',
        });
    };

    const handleChangeDocAccessLevel = async (level: DocumentAccessLevel) => {
        if (docAccessLevelMutation.isLoading) {
            return;
        }

        if (level === 'private') {
            // увімкнення приватності документу на сторінці перегляду документу
            eventTracking.sendToGTMV4({
                event: 'ec_enable_private_doc_page',
            });
        }

        if (level === 'extended') {
            // перемикання із приватності у спільний доступ на сторінці перегляду документу
            eventTracking.sendToGTMV4({
                event: 'ec_enable_shared_doc_page',
            });
        }

        docAccessLevelMutation.mutate([doc.id, level]);
    };

    const makeHandleRemoveViewerRole = (
        actor: typeof doc.accesses[number],
    ) => () => {
        const userName = formatFullName(actor.role.user);

        dispatch(
            onConfirmPopupShow({
                title: t`Видалити доступ перегляду для користувача?`,
                text: jt`Ви впевнені що, хочете видалити доступ перегляду документу для користувача ${userName}?`,
                buttonTheme: 'danger',
                cancelText: t`Ні`,
                onConfirm: () => {
                    docRemoveViewerMutation.mutate([
                        doc.id,
                        { type: 'role', id: actor.roleId },
                    ]);
                },
            }),
        );

        // редагування списку доступів (додавання / видалення людини із доступів, тобто відповідно коли натискають на “хрестик”
        eventTracking.sendToGTMV4({
            event: 'ec_access_change',
        });
    };

    const makeHandleRemoveViewerGroup = (
        actor: typeof doc.viewerGroups[number],
    ) => () => {
        const groupName = <b>{actor.group.name}</b>;

        dispatch(
            onConfirmPopupShow({
                title: t`Видалити доступ перегляду для команди?`,
                text: jt`Ви впевнені що, хочете видалити доступ перегляду документу для команди ${groupName}?`,
                buttonTheme: 'danger',
                cancelText: t`Ні`,
                onConfirm: () => {
                    docRemoveViewerMutation.mutate([
                        doc.id,
                        { type: 'group', id: actor.group.id },
                    ]);
                },
            }),
        );

        // редагування списку доступів (додавання / видалення людини із доступів, тобто відповідно коли натискають на “хрестик”
        eventTracking.sendToGTMV4({
            event: 'ec_access_change',
        });
    };

    const renderGroupSize = (group?: MembersGroup | null) => {
        if (!group) {
            return null;
        }

        const groupSize = group.members.length;

        if (!groupSize) {
            return null;
        }

        return (
            <GroupTooltip
                title={
                    <FlexBox
                        style={{ padding: 10, fontSize: 14 }}
                        gap={1}
                        direction="column"
                    >
                        {group.members.map((member: GroupMember) => {
                            return (
                                <FlexBox
                                    key={member.id}
                                    direction="column"
                                    gap={4}
                                    style={{ padding: 4, borderRadius: 4 }}
                                >
                                    <Text ellipsis>
                                        <Text strong>
                                            {formatFullName(member.role.user)}{' '}
                                        </Text>
                                        <Text type="secondary">
                                            {formatUserIdentifier(
                                                member.role.user,
                                            )}
                                        </Text>
                                    </Text>
                                </FlexBox>
                            );
                        })}
                    </FlexBox>
                }
            >
                <span data-group={group.id} className={cn(css.groupSize)}>
                    {groupSize}
                </span>
            </GroupTooltip>
        );
    };

    const renderSignActors = (actor: typeof doc.signers[number]) => {
        return (
            <RoleAccessItem
                key={actor.id}
                title={
                    actor.group.name || formatFullName(actor.role?.user) || ''
                }
                variant={actor.groupId ? 'group' : 'user'}
                startElement={renderGroupSize(actor.group)}
                subText={actor.role?.user.email}
            />
        );
    };

    const renderReviewRequestsUsers = (
        actor: typeof doc.reviewRequests[number],
    ) => {
        return (
            <RoleAccessItem
                key={actor.id}
                title={
                    actor.toGroup?.name ||
                    formatFullName(actor.toRole?.user) ||
                    ''
                }
                variant={actor.toGroupId ? 'group' : 'user'}
                startElement={renderGroupSize(actor.toGroup)}
                subText={actor.toRole?.user.email}
            />
        );
    };

    const renderViewers = (actor: typeof doc.accesses[number]) => {
        return (
            <RoleAccessItem
                key={actor.id}
                title={formatFullName(actor.role.user)}
                subText={actor.role.user.email}
                onRemove={makeHandleRemoveViewerRole(actor)}
            />
        );
    };

    const renderViewerGroups = (actor: typeof doc.viewerGroups[number]) => {
        return (
            <RoleAccessItem
                key={actor.id}
                title={actor.group.name}
                variant="group"
                startElement={renderGroupSize(actor.group)}
                onRemove={makeHandleRemoveViewerGroup(actor)}
            />
        );
    };

    const getAccessLevelAlertText = () =>
        accessValidationError[getEditDocumentAccessLevelValidationReason(doc)];

    return (
        <FlexBox direction="column" gap={20}>
            <FlexBox
                direction="column"
                style={{ width: 240, maxWidth: 'unset', marginLeft: -10 }}
            >
                <DocumentAccessToggleSmall
                    isInternal={doc.isInternal}
                    active={accessLevel}
                    onSelect={handleChangeDocAccessLevel}
                    disabled={!canEdit}
                />
                {!canEdit && (
                    <Alert hideIcon>{getAccessLevelAlertText()}</Alert>
                )}
            </FlexBox>
            <FlexBox gap={12}>
                <Text
                    style={{ fontWeight: 500, fontSize: 14 }}
                >{t`Доступ надано`}</Text>
                <BlackTooltip title={t`Додати користувача`} disableInteractive>
                    <span
                        className={css.plusIcon}
                        onClick={handleAddViewAccess}
                    >
                        <Icon glyph={PlusSvg} />
                    </span>
                </BlackTooltip>
            </FlexBox>
            <ScrollableBox
                contentClassName={css.box}
                scrollHeight={500}
                hideScroll
                shadow="vertical"
            >
                {doc.edrpouOwner === currentCompany.edrpou && (
                    <FlexBox key="owner" direction="column" gap={16}>
                        <Text type="secondary">{t`Завантажив документ`}</Text>
                        <RoleAccessItem
                            title={formatFullName(doc.user)}
                            subText={formatUserIdentifier(doc.user)}
                        />
                    </FlexBox>
                )}
                {doc.edrpouRecipient === currentCompany.edrpou &&
                    doc.emailRecipient && (
                        <FlexBox key="recipient" direction="column" gap={16}>
                            <Text type="secondary">{t`Отримав документ`}</Text>
                            <RoleAccessItem title={doc.emailRecipient} />
                        </FlexBox>
                    )}

                {doc.signers.length + reviewRequestsDisplay.length > 0 && (
                    <FlexBox key="signers" direction="column" gap={16}>
                        <Text type="secondary">{t`Підписання/погодження`}</Text>
                        <FlexBox direction="column" gap={8}>
                            {doc.signers.map(renderSignActors)}
                            {reviewRequestsDisplay.map(
                                renderReviewRequestsUsers,
                            )}
                        </FlexBox>
                    </FlexBox>
                )}

                {doc.accesses.length + doc.viewerGroups.length > 0 && (
                    <FlexBox key="onlyViewUsers" direction="column" gap={18}>
                        <Text type="secondary">{t`Лише перегляд`}</Text>
                        <FlexBox direction="column" gap={8}>
                            {doc.accesses.map(renderViewers)}
                            {doc.viewerGroups.map(renderViewerGroups)}
                        </FlexBox>
                    </FlexBox>
                )}
            </ScrollableBox>
        </FlexBox>
    );
};

export default AccessLevelBlock;
