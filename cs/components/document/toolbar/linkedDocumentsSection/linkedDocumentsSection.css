.icon {
    display: inline-block;
    width: 13px;
    height: 13px;
    margin-left: 10px;
    color: var(--pigeon-color);
    vertical-align: middle;
}

.owner {
    color: var(--dark-pigeon-color);
    font-weight: bold;
}

.info {
    margin-top: 15px;
}

.date {
    color: var(--dark-pigeon-color);
    font-size: 11px;
}

.date + .date {
    margin-top: 6px;
}

.cell {
    display: table-cell;
    padding-left: 18px;
}

.list > div + div {
    margin-top: 10px;
}

.block {
    padding: 20px;
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
}

@media all and (max-width: 768px) {
    .block {
        padding: 0 0 10px 0;
    }
}

.delivery {
    color: var(--dark-pigeon-color);
    font-size: 11px;
}

.kind {
    color: var(--dark-pigeon-color);
    font-size: 11px;
}

.actions {
    margin-top: 20px;
}

.title {
    margin-bottom: 15px;
    font-weight: bold;
}

.extraDataBlock + .extraDataBlock {
    margin-top: 10px;
}

.extraDataTitle {
    font-weight: bold;
}

.extraFieldsSettingsLink {
    margin-top: 10px;
}

.antivirusStatus {
    margin-bottom: 12px;
}

.addLinkButton {
    margin-top: 15px;
}

.line {
    border-bottom: 1px solid var(--default-border);
    margin-top: 10px;
    margin-right: -20px;
    margin-left: -40px;
}

.warningBlock {
    display: flex;
    margin-top: 20px;
}

.warningIcon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    margin-right: 10px;
}
