import React, { useEffect, useRef } from 'react';

import logger from 'services/logger';

import { IncomingMessage } from './types';

import { isIncomeMessage } from './utils';

interface usePostMessageSubscribeParams {
    handleMessage: Record<
        IncomingMessage['MessageId'],
        (data: IncomingMessage) => void
    >;
    iframeRef: React.RefObject<HTMLIFrameElement>;
}
export const usePostMessageSubscribe = (
    handleMessage: usePostMessageSubscribeParams['handleMessage'],
    iframeRef: usePostMessageSubscribeParams['iframeRef'],
) => {
    const handleRef = useRef(handleMessage);

    useEffect(() => {
        handleRef.current = handleMessage;
    }, [handleMessage]);

    useEffect(() => {
        const handleEvent = (event: MessageEvent) => {
            const { data, origin, source } = event;

            if (
                origin === window.location.origin &&
                typeof data === 'string' &&
                source === iframeRef.current?.contentWindow
            ) {
                try {
                    const message = JSON.parse(data);

                    if (config.DEBUG) {
                        console?.table({
                            id: message.MessageId,
                            time: new Date(message.SendTime).toTimeString(),
                            ...(message.Values || {}),
                        });
                    }

                    if (isIncomeMessage(message)) {
                        handleRef.current?.[message.MessageId]?.(message);
                    }
                } catch (e) {
                    logger.error('Failed to parse incoming message', e);
                }
            }
        };

        window.addEventListener('message', handleEvent);

        return function cleanup() {
            window.removeEventListener('message', handleEvent);
        };
    });
};
