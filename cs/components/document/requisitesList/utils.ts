import { DocumentFlowItem } from 'services/documents/ts/types';

export const isViewOnlyFlow = (flow: DocumentFlowItem) =>
    flow.pendingSignaturesCount === 0 &&
    flow.signaturesCount === 0 &&
    !flow.canSign;

export const getFlowOrder = (flow: DocumentFlowItem) =>
    typeof flow.order === 'number' ? `${flow.order + 1}` : null;

export const flowCanSign = (
    flowList: DocumentFlowItem[],
    flow: DocumentFlowItem,
) => {
    if (flowList.length === 0 || isViewOnlyFlow(flow)) {
        return false;
    }

    if (flow.order === null) {
        return true;
    }

    const index = flowList.findIndex((item) => item.id === flow.id);

    return !flowList.slice(0, index).find((item) => !item.isComplete);
};
