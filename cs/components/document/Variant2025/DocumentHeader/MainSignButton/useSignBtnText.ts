import { useSelector } from 'react-redux';

import { useDocument } from 'components/document/Variant2025/useDocument';
import { getCurrentCompany, getCurrentUserRole } from 'selectors/app.selectors';
import {
    isExtraSignature,
    shouldSendAfterSign,
} from 'services/documents/utils';
import { t } from 'ttag';

export const useSignBtnText = () => {
    const doc = useDocument();
    const currentCompany = useSelector(getCurrentCompany);
    const currentRole = useSelector(getCurrentUserRole);

    const isExtraSign = isExtraSignature(
        doc,
        currentRole.id,
        currentCompany?.edrpou,
    );

    let signButtonText = isExtraSign ? t`Додати підпис` : t`Підписати`;

    if (shouldSendAfterSign(doc, currentRole.id)) {
        signButtonText = t`Підписати і надіслати`;
    }

    return signButtonText;
};
