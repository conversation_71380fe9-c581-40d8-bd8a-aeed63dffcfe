import headerActionCreators from 'components/header/headerActionCreators';
import { push } from 'connected-react-router';
import { WS_KEY_UNSUCCESSFULLY_SIGNED_DOCUMENTS } from 'lib/constants';
import { redirect } from 'lib/navigation';
import { getLocalStorageItem } from 'lib/webStorage';
import {
    getAppFlags,
    getCurrentCompanyPermissionMap,
    getCurrentUser,
} from 'selectors/app.selectors';
import { getSingleSelectedDocuments } from 'selectors/documentList.selectors';
import { getDocumentLocationVersionId } from 'selectors/router.selectors';
import eventTracking from 'services/analytics/eventTracking';
import {
    cancelDeleteRequest,
    cancelDeleteRequestVote,
    changeDocumentRecipient,
    commentDocument,
    commentVersionedDocument,
    deleteDocument,
    deleteDocumentReview,
    deleteLink,
    downloadPdf,
    downloadSignSummary,
    getCompaniesWhereAccessDocument,
    getDocument,
    rejectDocument,
    reviewDocument,
    sendDocument,
} from 'services/documents/api';
import {
    convertDraftToNewVersionDocument,
    createVersionDraft,
} from 'services/documents/ts/api';
import {
    getDocumentByVersion,
    getDocumentsWithoutRequiredFieldsByEdrpou,
    getIsVersionedDocument,
    isDocumentWithAllRequiredFields,
} from 'services/documents/ts/utils';
import { getDocumentsActions } from 'services/documents/utils';
import { ApplicationMode, DocumentReviewType } from 'services/enums';
import { getLocationId } from 'services/navigation-structure';
import { createSignSession } from 'services/sign-session';
import { setCompanyList, setOpenPopup } from 'store/companyPickerPopupSlice';
import { setTrialInfoOpenTargetPopup } from 'store/trialInfoPopup';
import { t } from 'ttag';

import { RequiredFieldsCallbacks } from '../RequiredFieldsResolver/types';

import requiredFieldsResolverActionCreators from '../RequiredFieldsResolver/requiredFieldsResolverActionCreators';
import appActionCreators from '../app/appActionCreators';
import changeDocumentPopupActionCreators from '../changeDocumentPopup/changeDocumentPopupActionCreators';
import createDeleteRequestActionCreators from '../createDeleteRequestPopup/createDeleteRequestActionCreators';
import documentListActionCreators from '../documentList/documentListActionCreators';
import filtersActionCreators from '../filters/filtersActionCreators';
import notificationCenterActionCreators from '../notificationCenter/notificationCenterActionCreators';
import proRateInfoPopupActionCreators from '../proRateInfoPopup/proRateInfoPopupActionCreators';
import resolveDeleteRequestActionCreators from '../resolveDeleteRequestPopup/resolveDeleteRequestPopupActionCreators';
import reviewHistoryPopupActionCreators from '../reviewHistoryPopup/reviewHistoryPopupActionCreators';
import shareDocumentPopupActionCreators from '../shareDocumentPopup/shareDocumentPopupActionCreators';
import signPopupActionCreators from '../signPopup/signPopupActionCreators';
import * as tagsEditPopupActionCreators from '../tagsEditPopup/tagsEditPopupActionCreators';
import uploaderActionCreators from '../uploader/uploaderActionCreators';
import actions from './documentActions';
import {
    patchDocumentAccessLevel,
    patchDocumentAccesses,
} from './ts/documentActionCreators';

import { ERROR_PHRASES, isApiDocumentEditorError } from './helpers';
import { getLinkedRecipient, shouldRenderReviewOnDoc } from './utils';

import { isDocumentRequiredFieldsError } from '../changeDocumentPopup/helper';
import { PermissionCategory } from '../proRateInfoPopup/proRateInfoPopupTypes';
import { RESPONSE_ERRORS_CODES } from './constants';

function syncDocument(docId) {
    return async (dispatch, getState) => {
        const currentUser = getCurrentUser(getState());

        const document = await getDocument(docId, currentUser);
        const { edrpouRecipient, emailRecipient } = getLinkedRecipient(
            document,
        );

        dispatch({
            type: actions.DOCUMENT__FINISH_LOAD,
            companyEdrpou: currentUser.currentCompany.edrpou,
            document: document,
            userEmail: currentUser.email,
            edrpouRecipient,
            emailRecipient,
            isUnsuccessfullySigned: false,
            signErrorMessage: '',
            documentActions: getDocumentsActions({
                documents: [document],
                isSelectedAll: false,
                location: window.location,
                currentUser,
            }),
        });
    };
}

function onLoadDocument(docId, reloadState = true) {
    return async (dispatch, getState) => {
        const state = getState();

        const {
            app: { currentUser, currentSignSession, applicationMode },
            document: { id: oldDocId, viewSession },
            router: {
                location: { pathname },
            },
        } = state;

        const locationVersionId = getDocumentLocationVersionId(getState());

        const isDocumentViewLockActive = getAppFlags(getState())
            .DOCUMENT_VIEW_LOCK_ON;

        const newViewSession = docId === oldDocId ? viewSession : null;
        dispatch({
            type: actions.DOCUMENT__START_LOAD,
            reloadState,
            viewSession: newViewSession,
        });

        const unsuccessfullySignedDocuments = getLocalStorageItem(
            WS_KEY_UNSUCCESSFULLY_SIGNED_DOCUMENTS,
        );
        const isUnsuccessfullySigned = unsuccessfullySignedDocuments
            ? docId in unsuccessfullySignedDocuments
            : false;
        const signErrorMessage =
            unsuccessfullySignedDocuments &&
            docId in unsuccessfullySignedDocuments
                ? unsuccessfullySignedDocuments[docId].error
                : '';

        let documentObj;

        try {
            documentObj = await getDocument(docId, currentUser, true);
        } catch (error) {
            if (
                error.code.includes(
                    RESPONSE_ERRORS_CODES.DOCUMENT_FOUND_FOR_OTHER_ROLE,
                )
            ) {
                const companiesAccessGranted = await getCompaniesWhereAccessDocument(
                    docId,
                );

                if (!companiesAccessGranted.length) {
                    return;
                }

                if (companiesAccessGranted.length === 1) {
                    dispatch(
                        headerActionCreators.onChangeRole(
                            companiesAccessGranted[0].id,
                            pathname,
                        ),
                    );
                }

                if (companiesAccessGranted.length > 1) {
                    dispatch(setCompanyList(companiesAccessGranted));
                    dispatch(setOpenPopup());
                }
            }
        }

        // Handle case when current role don't have access to document
        if (!documentObj) {
            dispatch({ type: actions.DOCUMENT__NO_ACCESS });
            return;
        }

        // Feature flag DOCUMENT_VIEW_LOCK_ON
        if (
            isDocumentViewLockActive &&
            pathname.startsWith('/app/documents') &&
            documentObj &&
            !documentObj.isViewable
        ) {
            dispatch(
                documentListActionCreators.showDocumentViewLockedPopup(true),
            );
            dispatch({ type: actions.DOCUMENT__NO_ACCESS });
            return;
        }

        // Sort comments
        if (documentObj.comments) {
            documentObj.comments = Array.from(
                documentObj.comments,
            ).sort((curr, prev) =>
                curr.dateCreated.localeCompare(prev.dateCreated),
            );
        }

        const { edrpouRecipient, emailRecipient } = getLinkedRecipient(
            documentObj,
        );

        const isVersionedDocument = getIsVersionedDocument(documentObj);
        let documentObjByVersion = {};

        // functional for document with versions
        if (isVersionedDocument) {
            documentObjByVersion = { ...documentObj };

            // check if location version id exist in document's versions
            if (locationVersionId) {
                const isLocationVersionExist = documentObjByVersion.versions.some(
                    (version) => version.id === locationVersionId,
                );

                if (!isLocationVersionExist) {
                    redirect(`/app/documents/${documentObjByVersion.id}`);
                }
            }

            // documentObj.versions[0] - actual document version
            const versionId =
                locationVersionId ?? documentObjByVersion.versions[0].id;

            documentObjByVersion = getDocumentByVersion(documentObj, versionId);
        }

        const getDoc = () =>
            isVersionedDocument ? documentObjByVersion : documentObj;

        dispatch({
            type: actions.DOCUMENT__FINISH_LOAD,
            companyEdrpou: currentUser.currentCompany.edrpou,
            document: getDoc(),
            userEmail: currentUser.email,
            edrpouRecipient,
            emailRecipient,
            isUnsuccessfullySigned,
            signErrorMessage,
            documentActions: getDocumentsActions({
                documents: [getDoc()],
                isSelectedAll: false,
                location: window.location,
                currentUser,
            }),
        });
        if (applicationMode === ApplicationMode.SIGN_SESSION) {
            eventTracking.sendEvent(
                currentSignSession.type,
                'start',
                `${documentObj.edrpouOwner} ${documentObj.companyNameOwner}`,
            );
        }
        if (applicationMode === ApplicationMode.SHARED_DOCUMENT_VIEW) {
            eventTracking.sendEvent(
                'shared_document_view',
                'start',
                `${documentObj.edrpouOwner} ${documentObj.companyNameOwner}`,
            );
        }
    };
}

function onViewerReload() {
    return { type: actions.DOCUMENT__RELOAD_VIEWER };
}

function onViewSessionOpen() {
    return async (dispatch, getState) => {
        const {
            app: { currentUser, flags },
            document: { id },
        } = getState();
        // Open view session for sending document to Microsoft office file viewer
        try {
            const viewSession = flags.SIGN_SESSION_MS_OFFICE_PARAMETER
                ? await createSignSession({
                      document_id: id,
                      edrpou: currentUser.currentRole.company.edrpou,
                      email: currentUser.email,
                      type: 'view_session',
                      is_internal_view: true,
                  })
                : await createSignSession({
                      document_id: id,
                      edrpou: currentUser.currentRole.company.edrpou,
                      email: currentUser.email,
                      type: 'view_session',
                  });
            dispatch({
                type: actions.DOCUMENT__VIEW_SESSION_CREATE,
                viewSession,
            });
            eventTracking.sendEvent('document', 'show_doc_docx_xls');
        } catch (err) {
            dispatch({ type: actions.DOCUMENT__VIEW_SESSION_CLOSE });
        }
    };
}

function onCancelReject() {
    return (dispatch) => {
        dispatch({ type: actions.DOCUMENT__REJECT_POPUP_HIDE });
    };
}

function onOpenRejectPopup() {
    return (dispatch) => {
        dispatch({ type: actions.DOCUMENT__REJECT_POPUP_SHOW });
    };
}

function onSign({
    emailRecipient = '',
    edrpouRecipient = '',
    isRecipientEmailHidden = false,
    isDiia = false,
    isKep = false,
    isKepNew = false,
    isSignActAnnulment = false,
} = {}) {
    return (dispatch, getState) => {
        const {
            document: { doc },
        } = getState();

        dispatch(
            signPopupActionCreators.showPopup({
                origin: 'document',
                docs: [doc],
                edrpouRecipient,
                emailRecipient,
                isRecipientEmailHidden,
                isDiia,
                isKep,
                isKepNew,
                isSignActAnnulment,
            }),
        );
    };
}

function onReject(comment) {
    return async (dispatch, getState) => {
        const {
            document: state,
            app: { applicationMode },
        } = getState();
        await rejectDocument(state.id, comment);
        dispatch({ type: actions.DOCUMENT__REJECT });
        dispatch(onLoadDocument(state.id));

        // Sign session case
        if (applicationMode === ApplicationMode.SIGN_SESSION) {
            eventTracking.sendEvent(
                'sign_session',
                'decline',
                `${state.edrpouOwner} ${state.companyNameOwner}`,
            );
            dispatch(appActionCreators.onSignSessionCancel());
        }

        // Shared document view case
        if (applicationMode === ApplicationMode.SHARED_DOCUMENT_VIEW) {
            eventTracking.sendEvent(
                'shared_document_view',
                'decline',
                `${state.edrpouOwner} ${state.companyNameOwner}`,
            );
            dispatch(appActionCreators.onSignSessionCancel());
        }
    };
}

function onCommentDoc(docs, comment, isInternal = false) {
    return async (dispatch, getState) => {
        const documentLocationVersionId = getDocumentLocationVersionId(
            getState(),
        );

        const doc = docs[0];
        const docId = doc.id;
        try {
            const isVersionedDoc = getIsVersionedDocument(doc);
            if (isVersionedDoc) {
                await commentVersionedDocument(
                    docId,
                    comment,
                    isInternal,
                    documentLocationVersionId,
                );
            } else {
                await commentDocument(docId, comment, isInternal);
            }

            eventTracking.sendEvent('comments', 'send_comment', {
                type: 'single',
                location: getLocationId(document.location),
            });
            dispatch({ type: actions.DOCUMENT__COMMENT__ADD });

            dispatch(onLoadDocument(docId, false));
        } catch (err) {
            dispatch(
                appActionCreators.onAlertPopupShow(
                    t`Помилка!`,
                    t`Не вдалося відправити коментар. ${err.message}. Повторіть спробу.`,
                ),
            );
            dispatch({ type: actions.DOCUMENT__COMMENT__SHOW_ERROR });
        }
    };
}

function onOpenCommentPopup(commentLevel) {
    return (dispatch) => {
        dispatch({ type: actions.DOCUMENT__COMMENT__OPEN_POPUP, commentLevel });
    };
}

function onCloseCommentPopup() {
    return (dispatch) => {
        dispatch({ type: actions.DOCUMENT__COMMENT__CLOSE_POPUP });
    };
}

function onOpenDeletePopup() {
    return (dispatch) => {
        dispatch({ type: actions.DOCUMENT__OPEN_DELETE_POPUP });
    };
}

function onCloseDeletePopup() {
    return (dispatch) => {
        dispatch({ type: actions.DOCUMENT__CLOSE_DELETE_POPUP });
    };
}

function onDeleteDoc() {
    return async (dispatch, getState) => {
        const doc = getState().document;
        const category =
            doc.parent && doc.parent.id ? 'sub_document' : 'document';

        await deleteDocument(doc.id);
        eventTracking.sendEvent(
            category,
            'delete',
            getLocationId(document.location),
        );

        // Reload tags in the filters
        await dispatch(filtersActionCreators.onReloadTags(true));

        dispatch(onCloseDeletePopup());

        const { router } = getState();

        if (router.location.pathname !== '/app/documents') {
            dispatch(push('/app/documents'));
        }
    };
}

function onOpenChangeRecipientPopup() {
    return (dispatch) => {
        dispatch({ type: actions.DOCUMENT__OPEN_CHANGE_RECIPIENT_POPUP });
    };
}

function onCloseChangeRecipientPopup() {
    return (dispatch) => {
        dispatch({ type: actions.DOCUMENT__CLOSE_CHANGE_RECIPIENT_POPUP });
    };
}

function onChangeRecipient(email, edrpou, isEmailHidden) {
    return async (dispatch, getState) => {
        const doc = getState().document;
        const emails = !isEmailHidden && email ? email.split(',') : null;

        try {
            await changeDocumentRecipient(
                doc.id,
                emails,
                edrpou,
                isEmailHidden,
            );
            dispatch(onLoadDocument(doc.id));
            dispatch({ type: actions.DOCUMENT__CLOSE_CHANGE_RECIPIENT_POPUP });
        } catch (error) {
            if (isDocumentRequiredFieldsError(error)) {
                const withoutRequiredFieldsDocs = await getDocumentsWithoutRequiredFieldsByEdrpou(
                    [doc],
                    [edrpou || doc.edrpouRecipient],
                );

                dispatch(
                    requiredFieldsResolverActionCreators.onSetData({
                        withoutRequiredFieldsDocs,
                        requiredFieldsCallbackName:
                            RequiredFieldsCallbacks.DOCUMENT__ON_CHANGE_RECIPIENT,
                        requiredFieldsCallbackData: {
                            email,
                            edrpou,
                            isEmailHidden,
                        },
                    }),
                );
                dispatch({
                    type: actions.DOCUMENT__SET_REQUIRED_FIELDS_POPUP_STATUS,
                    isOpenRequiredFieldsResolverPopup: true,
                });
            }
        }
    };
}

function openRequiredFieldsResolverPopup({
    withoutRequiredFieldsDocs,
    email,
    edrpou,
    isEmailHidden,
}) {
    return (dispatch) => {
        dispatch(
            requiredFieldsResolverActionCreators.onSetData({
                withoutRequiredFieldsDocs,
                requiredFieldsCallbackName:
                    RequiredFieldsCallbacks.DOCUMENT__ON_SEND_DOCUMENT,
                requiredFieldsCallbackData: {
                    email,
                    edrpou,
                    isEmailHidden,
                },
            }),
        );
        dispatch({
            type: actions.DOCUMENT__SET_REQUIRED_FIELDS_POPUP_STATUS,
            isOpenRequiredFieldsResolverPopup: true,
        });
    };
}

function onAssignRecipient(email, edrpou, isEmailHidden) {
    return async (dispatch, getState) => {
        const {
            document: { id, doc },
        } = getState();
        // TODO refactor this
        let emails =
            !isEmailHidden && email
                ? Array.isArray(email)
                    ? email
                    : email.split(',')
                : null;

        try {
            await changeDocumentRecipient(id, emails, edrpou, isEmailHidden);
            dispatch(onLoadDocument(id));
        } catch (err) {
            if (isDocumentRequiredFieldsError(err)) {
                const withoutRequiredFieldsDocs = await getDocumentsWithoutRequiredFieldsByEdrpou(
                    [doc],
                    [edrpou || doc.edrpouRecipient],
                );

                dispatch(
                    openRequiredFieldsResolverPopup({
                        withoutRequiredFieldsDocs,
                        email,
                        edrpou,
                        isEmailHidden,
                    }),
                );
            }
        }
    };
}

function onSendDocument(email, edrpou, isEmailHidden) {
    return async (dispatch, getState) => {
        const {
            app: {
                currentUser: { currentCompany },
            },
            document: {
                id,
                doc,
                emailRecipient,
                edrpouRecipient,
                isRecipientEmailHidden,
            },
        } = getState();

        let emails = null;
        if (email) {
            // TODO refactor this
            if (Array.isArray(email)) {
                emails = email;
            } else {
                emails = email.split(',');
            }
        } else if (emailRecipient) {
            emails = emailRecipient.split(',');
        }

        try {
            const isMultilateralDoc = doc.flows.length > 0;

            // перевіряємо якщо не всі поля заповнені - чи є у списку контрагентів ті, які вимагабть заповнення обов'язкові полів
            if (isMultilateralDoc && !isDocumentWithAllRequiredFields(doc)) {
                const withoutRequiredFieldsDocs = await getDocumentsWithoutRequiredFieldsByEdrpou(
                    [doc],
                    doc.flows
                        .map((flow) => flow.edrpou)
                        .filter((item) => item !== currentCompany.edrpou),
                );

                if (withoutRequiredFieldsDocs.length) {
                    dispatch(
                        openRequiredFieldsResolverPopup({
                            withoutRequiredFieldsDocs,
                            email,
                            edrpou,
                            isEmailHidden,
                        }),
                    );
                    return;
                }
            }

            await sendDocument(
                id,
                emails,
                edrpou || edrpouRecipient,
                null,
                isEmailHidden || isRecipientEmailHidden,
                isMultilateralDoc,
            );

            if (
                doc.expectedRecipientSignatures !== 0 &&
                doc.firstSignBy === 'recipient'
            ) {
                dispatch(appActionCreators.updateBillingAccounts());
            }

            dispatch(onLoadDocument(id));
        } catch (err) {
            if (isDocumentRequiredFieldsError(err)) {
                const withoutRequiredFieldsDocs = await getDocumentsWithoutRequiredFieldsByEdrpou(
                    [doc],
                    [edrpou || doc.edrpouRecipient],
                );

                dispatch(
                    openRequiredFieldsResolverPopup({
                        withoutRequiredFieldsDocs,
                        email,
                        edrpou,
                        isEmailHidden,
                    }),
                );
            } else {
                dispatch(
                    appActionCreators.onAlertPopupShow(
                        t`Помилка!`,
                        t`Не вдалося відправити документ. ${err.message}. Повторіть спробу.`,
                    ),
                );
            }
        }
    };
}

function onDownloadPdf({ versionId } = {}) {
    return async (dispatch, getState) => {
        const {
            document: { doc },
            router: { location },
        } = getState();
        // for download from main table documents
        const selectedListItem = getSingleSelectedDocuments(getState());
        const isDocumentPage = !!location?.pathname?.split('/')[3];
        const documentId = isDocumentPage ? doc.id : selectedListItem.id;
        const ext = isDocumentPage ? doc.ext : selectedListItem.ext;

        dispatch({ type: actions.DOCUMENT__DOWNLOAD_PDF__START });
        try {
            await downloadPdf(documentId, { ext, versionId });
        } catch (error) {
            dispatch(
                notificationCenterActionCreators.addNotification({
                    type: 'text',
                    textType: 'error',
                    text: `${t`Сталася помилка, зверніться в службу підтримки`}: ${
                        error.message
                    }`,
                    showCloseButton: true,
                    autoClose: 5000,
                }),
            );
        }

        dispatch({ type: actions.DOCUMENT__DOWNLOAD_PDF__FINISH });
    };
}

function onDownloadSignSummary() {
    return async (dispatch, getState) => {
        const {
            document: {
                doc: { id },
            },
        } = getState();
        dispatch({ type: actions.DOCUMENT__DOWNLOAD_SIGN_SUMMARY__START });
        await downloadSignSummary(id);
        dispatch({ type: actions.DOCUMENT__DOWNLOAD_SIGN_SUMMARY__FINISH });
    };
}

function onReview(type) {
    return async (dispatch, getState) => {
        const {
            document: {
                id,
                doc: { status, parent, reviewRequests },
            },
            app: { currentUser },
        } = getState();
        const hasAssignedRequest = reviewRequests.some(
            (rr) => rr.toRoleId === currentUser.roleId,
        );

        const isAccess = getCurrentCompanyPermissionMap(getState())[
            PermissionCategory.REVIEWS
        ];

        if (!hasAssignedRequest && !isAccess) {
            dispatch(
                setTrialInfoOpenTargetPopup(
                    'reviewers',
                    `document/reviewers/review-action-${type}`,
                ),
            );

            return;
        }

        const label =
            type === DocumentReviewType.APPROVE
                ? 'accept_doc_coordination'
                : 'reject_doc_coordination';
        try {
            await reviewDocument({ document_id: id, type });
            const category = parent && parent.id ? 'sub_document' : 'document';
            eventTracking.sendEvent(category, label, status);
            await dispatch(onLoadDocument(id, false));
            if (shouldRenderReviewOnDoc(currentUser)) {
                dispatch(onViewerReload());
            }
        } catch (error) {
            console.error(error);
        }
    };
}

function onDeleteReview(id, action) {
    return async (dispatch, getState) => {
        const {
            document: {
                id: docId,
                doc: { status, parent },
            },
            app: { currentUser },
        } = getState();
        await deleteDocumentReview(docId);
        const category = parent && parent.id ? 'sub_document' : 'document';
        if (action) {
            eventTracking.sendEvent(category, action, status);
        }
        await dispatch(onLoadDocument(docId, false));
        if (shouldRenderReviewOnDoc(currentUser)) {
            dispatch(onViewerReload());
        }
    };
}

function onOpenAddReviewersPopup() {
    return (dispatch, getState) => {
        const isAccess = getCurrentCompanyPermissionMap(getState())[
            PermissionCategory.REVIEWS
        ];

        if (!isAccess) {
            dispatch(
                setTrialInfoOpenTargetPopup(
                    'reviewers',
                    'document/reviewers/add-reviewers',
                ),
            );

            return;
        }

        dispatch(onOpenChangeDocumentPopup({ selectedItemIndex: 'reviewers' }));
    };
}

function onOpenChangeDocumentPopup({ selectedItemIndex }) {
    return (dispatch, getState) => {
        const { document } = getState();
        dispatch(
            changeDocumentPopupActionCreators.onShow({
                documents: [document.doc],
                isDocumentPage: true,
                selectedItemIndex,
            }),
        );
    };
}

function onUploadChildDocument() {
    return (dispatch, getState) => {
        const {
            document: { id },
            uploader: { isDisabled },
        } = getState();
        if (!isDisabled) {
            uploaderActionCreators.onShowPopup('sub_document', id)(
                dispatch,
                getState,
            );
        }
    };
}

function onOpenReviewHistoryPopup() {
    return (dispatch, getState) => {
        const {
            document: { id, title, type },
        } = getState();
        const docTitle = `${type && type !== title ? `${type}: ` : ''}${title}`;
        dispatch(reviewHistoryPopupActionCreators.onOpenPopup(id, docTitle));
    };
}

function onOpenTagsEditPopup() {
    return (dispatch, getState) => {
        const {
            document: {
                id,
                title,
                type,
                doc: { tags },
            },
        } = getState();

        const isAccess = getCurrentCompanyPermissionMap(getState())[
            PermissionCategory.REVIEWS
        ];

        if (!isAccess) {
            dispatch(
                setTrialInfoOpenTargetPopup('tags', 'document/tags/edit-tags'),
            );

            eventTracking.sendToGTMV4({
                event: 'ec_tags_cdocview_trial',
            });

            return;
        }

        const docTitle = `${type && type !== title ? `${type}: ` : ''}${title}`;
        const popupTitle = tags.length
            ? t`Редагувати ярлики документа ${docTitle}`
            : t`Додати ярлики документу ${docTitle}`;
        dispatch(
            tagsEditPopupActionCreators.onOpenPopup(
                [id],
                tags,
                popupTitle,
                true,
            ),
        );
    };
}

function onTagSelect(tag) {
    return (dispatch, getState) => {
        if (
            dispatch(
                proRateInfoPopupActionCreators.showProRatePopupToCompany(
                    'document/reviewers/add-reviewers',
                    PermissionCategory.REVIEWS,
                ),
            )
        )
            return;
        const { router } = getState();

        const searchParams = new URLSearchParams(router.location.search);

        searchParams.set('tag', tag.id);

        dispatch(
            push({
                pathname: '/app/documents',
                search: searchParams.toString(),
            }),
        );
    };
}

function onOpenCreateDeleteRequestPopup(docs) {
    return (dispatch) => {
        dispatch(
            createDeleteRequestActionCreators.onShow({
                documents: docs,
                docPage: true,
            }),
        );
    };
}

function onOpenShareDocumentPopup() {
    return (dispatch, getState) => {
        const {
            document: { id },
        } = getState();
        dispatch(shareDocumentPopupActionCreators.onShow(id));
    };
}

function onOpenResolveDeleteRequestPopup(docs) {
    return (dispatch) => {
        dispatch(
            resolveDeleteRequestActionCreators.onShow({
                documents: docs,
                docPage: true,
            }),
        );
    };
}

function onRejectDeleteRequest(docs) {
    return (dispatch) => {
        dispatch(
            resolveDeleteRequestActionCreators.onReject({
                documents: docs,
                docPage: true,
            }),
        );
    };
}

function onCancelDeleteRequestVote(docs) {
    return async (dispatch) => {
        try {
            await cancelDeleteRequestVote(
                docs.map((item) => {
                    return item.deleteRequest.id;
                }),
            );
            dispatch(
                resolveDeleteRequestActionCreators.onCancelVote({
                    documents: docs,
                    docPage: true,
                }),
            );
        } catch (err) {
            // handling
        }
    };
}

function onAcceptDeleteRequest(docs) {
    return (dispatch) => {
        dispatch(
            resolveDeleteRequestActionCreators.onApply({
                documents: docs,
                docPage: true,
            }),
        );
    };
}

function onCancelDeleteRequestClick() {
    return async (dispatch, getState) => {
        const {
            document: { id },
        } = getState();
        try {
            await cancelDeleteRequest([id]);
            dispatch(onLoadDocument(id, false));
        } catch (err) {
            // TODO[TK]: handle this error if necessary
        }
    };
}

function onUserChange() {
    return (dispatch) => {
        dispatch({ type: actions.DOCUMENT__VIEW_SESSION_CLOSE });
    };
}

function onDeleteLink(parentId, childId) {
    return async (dispatch, getState) => {
        const {
            document: { id },
        } = getState();
        try {
            await deleteLink(parentId, childId);
            dispatch(onLoadDocument(id));
        } catch (err) {
            dispatch(
                notificationCenterActionCreators.addNotification({
                    type: 'text',
                    textType: 'error',
                    text: `${t`Сталася помилка, зверніться в службу підтримки.`}: ${
                        err.message
                    }`,
                    showCloseButton: true,
                    autoClose: 5000,
                }),
            );
            // handle this error if necessary
        }
    };
}

function onLinksUpdate() {
    // Just reload document, other logic handled inside of components
    return async (dispatch, getState) => {
        const {
            document: { id },
        } = getState();
        dispatch(onLoadDocument(id));
    };
}

function onDocumentParametersUpdate(data) {
    return (dispatch) =>
        dispatch({
            type: actions.DOCUMENT__SET_PARAMETERS,
            data,
        });
}

function setEditMode(isEditMode) {
    return (dispatch) =>
        dispatch({
            type: actions.DOCUMENT__SET_EDIT_MODE,
            payload: isEditMode,
        });
}

function onCreateVersionDraft(doc, versionId) {
    return async (dispatch) => {
        try {
            await createVersionDraft(doc.id, versionId);
            await dispatch(onLoadDocument(doc.id, false));
        } catch (error) {
            if (isApiDocumentEditorError(error)) {
                dispatch(
                    appActionCreators.onAlertPopupShow(
                        ERROR_PHRASES.COMMON_ERROR_PHRASE,
                        `${error.reason}, ${ERROR_PHRASES.TRY_MORE_TIME_PHRASE}`,
                    ),
                );
            } else {
                dispatch(
                    appActionCreators.onAlertPopupShow(
                        ERROR_PHRASES.COMMON_ERROR_PHRASE,
                        t`Не вдалося створити чернетку документу для редагування. ${error.message}. ${ERROR_PHRASES.TRY_MORE_TIME_PHRASE}`,
                    ),
                );
            }
        }
    };
}

function onConvertDraftToNewVersion(doc, draftId) {
    return async (dispatch) => {
        try {
            await convertDraftToNewVersionDocument(draftId);
            await dispatch(onLoadDocument(doc.id, false));
        } catch (error) {
            if (isApiDocumentEditorError(error)) {
                dispatch(
                    appActionCreators.onAlertPopupShow(
                        ERROR_PHRASES.COMMON_ERROR_PHRASE,
                        `${error.reason}, ${ERROR_PHRASES.TRY_MORE_TIME_PHRASE}`,
                    ),
                );
            } else {
                dispatch(
                    appActionCreators.onAlertPopupShow(
                        ERROR_PHRASES.COMMON_ERROR_PHRASE,
                        t`Не вдалося створити нову версію документу. ${error.message}. ${ERROR_PHRASES.TRY_MORE_TIME_PHRASE}`,
                    ),
                );
            }
        }
    };
}

export default {
    onAssignRecipient,
    onCancelDeleteRequestClick,
    onCancelReject,
    onChangeRecipient,
    onCloseChangeRecipientPopup,
    onCloseCommentPopup,
    onCloseDeletePopup,
    onCommentDoc,
    onDeleteDoc,
    onDeleteReview,
    onDeleteLink,
    onLinksUpdate,
    syncDocument,
    onLoadDocument,
    onOpenAddReviewersPopup,
    onOpenChangeDocumentPopup,
    onOpenChangeRecipientPopup,
    onOpenCommentPopup,
    onOpenDeletePopup,
    onOpenRejectPopup,
    onOpenReviewHistoryPopup,
    onOpenTagsEditPopup,
    onReject,
    onReview,
    onSendDocument,
    onSign,
    onTagSelect,
    onUploadChildDocument,
    onDownloadPdf,
    onDownloadSignSummary,
    onOpenCreateDeleteRequestPopup,
    onOpenResolveDeleteRequestPopup,
    onRejectDeleteRequest,
    onCancelDeleteRequestVote,
    onAcceptDeleteRequest,
    onViewSessionOpen,
    onOpenShareDocumentPopup,
    onUserChange,
    onDocumentParametersUpdate,
    setEditMode,
    onCreateVersionDraft,
    onConvertDraftToNewVersion,
    patchDocumentAccessLevel,
    patchDocumentAccesses,
};
