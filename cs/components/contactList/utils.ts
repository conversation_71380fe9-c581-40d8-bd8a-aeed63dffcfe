import { queryClient } from 'lib/queries';
import { searchIsContactExistsOrFailure } from 'services/user';

export const waitContactES = async (
    search: string,
    onError?: (error: Error) => void,
) => {
    try {
        await queryClient.fetchQuery({
            queryKey: ['ES_PREFETCH_CONTACTS_BY_SEARCH', search],
            queryFn: () => searchIsContactExistsOrFailure(search),
            retry: (failureCount) => failureCount < 5,
        });
    } catch (err) {
        onError?.(err);
    }
};
