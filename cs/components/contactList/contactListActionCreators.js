import { waitContactES } from 'components/contactList/utils';
import { push } from 'connected-react-router';
import {
    getAppFlags,
    getCurrentCompanyPermissionMap,
} from 'selectors/app.selectors';
import { setTrialInfoOpenTargetPopup } from 'store/trialInfoPopup';
import { t } from 'ttag';

import appActions from '../app/appActions';
import contactsTagsEditPopupActionCreators from '../contactsTagsEditPopup/contactsTagsEditPopupActionCreators';
import importContactsActionCreators from '../importContacts/importContactsActionCreators';
import notificationActionCreators from '../notificationCenter/notificationCenterActionCreators';
import proRateInfoPopupActionCreators from '../proRateInfoPopup/proRateInfoPopupActionCreators';
import actions from './contactListActions';

import { EDRPOU_PATTERN, EMAIL_PATTERN } from '../../lib/constants';
import { getLocationQuery, stringifyLocationQuery } from '../../lib/url';
import {
    getLocalStorageItem,
    getSessionStorageItem,
    setLocalStorageItem,
    setSessionStorageItem,
} from '../../lib/webStorage';
import eventTracking from '../../services/analytics/eventTracking';
import auth from '../../services/auth';
import {
    createContactPerson,
    deleteContactPerson,
    deleteContactsTags,
    getContacts,
    syncContacts,
    updateContact,
    updateContactPerson,
} from '../../services/user';
import { PermissionCategory } from '../proRateInfoPopup/proRateInfoPopupTypes';
import constants from './constants';

function onLoadContacts(query) {
    return async (dispatch, getState) => {
        // Ignore loading data by non-completed user
        const {
            app: { currentUser },
            router,
        } = getState();
        if (!currentUser.registrationCompleted) {
            return;
        }

        if (!query) {
            query = getLocationQuery(router.location);
        }

        const { contactList: state } = getState();
        const pageNumber = query.page ? parseInt(query.page, 10) : 1;
        const offset =
            pageNumber > 1 ? `${(pageNumber - 1) * state.itemsPerPage}` : '0';
        const search = query.q_search;

        dispatch({ type: actions.CONTACT_LIST__START_LOAD });

        // by default not present in query, all users queried, value is undefined
        let isRegistered;

        if (query.isRegistered === 'true') {
            isRegistered = true;
        }

        if (query.isRegistered === 'false') {
            isRegistered = false;
        }

        const { count, contacts, countUnregistered } = await getContacts({
            limit: state.itemsPerPage,
            offset,
            search,
            isRegistered,
        });

        const alreadyInvitedContacts =
            getSessionStorageItem(
                constants.CONTACTS_ALREADY_INVITED_STORAGE_NAME,
            ) || [];
        const filter = constants.FILTER_DATA.find(
            (item) => isRegistered === item.isRegistered,
        ).value;
        dispatch({
            type: actions.CONTACT_LIST__FINISH_LOAD,
            contacts,
            count,
            pageNumber,
            filter,
            countUnregistered,
            alreadyInvitedContacts,
        });
    };
}

function onSetFilter(currentValue) {
    return (dispatch, getState) => {
        const filterObject = constants.FILTER_DATA.find(
            ({ value }) => value === currentValue,
        );
        const isRegistered = filterObject.isRegistered;

        const { router } = getState();

        const query = {
            ...getLocationQuery(router.location),
            isRegistered,
            page: '1',
        };

        dispatch(
            push({
                ...router.location,
                search: stringifyLocationQuery(query),
            }),
        );

        dispatch({
            type: actions.CONTACT_LIST__SET_FILTER,
            filter: currentValue,
        });
    };
}

function onRouteQueryChange() {
    return (dispatch) => {
        dispatch(onLoadContacts());
    };
}

function onShow() {
    return (dispatch) => {
        const storedItemsPerPage = getLocalStorageItem(
            constants.CONTACTS_PER_PAGE_STORAGE_NAME,
        );
        if (storedItemsPerPage) {
            dispatch({
                type: actions.CONTACT_LIST__SET_ITEMS_PER_PAGE,
                itemsPerPage: parseInt(storedItemsPerPage, 10),
            });
        }
        dispatch(onLoadContacts());
    };
}

function onImportContacts() {
    return (dispatch) => {
        dispatch(importContactsActionCreators.onPopupShow());
    };
}

function onItemsPerPageChange(itemsPerPage) {
    return (dispatch, getState) => {
        dispatch({
            type: actions.CONTACT_LIST__SET_ITEMS_PER_PAGE,
            itemsPerPage,
        });
        setLocalStorageItem(
            constants.CONTACTS_PER_PAGE_STORAGE_NAME,
            itemsPerPage,
        );

        const { router } = getState();

        const query = {
            ...getLocationQuery(router.location),
            page: '1',
        };

        dispatch(
            push({
                ...router.location,
                search: stringifyLocationQuery(query),
            }),
        );
    };
}

function onSyncContacts() {
    return async (dispatch) => {
        dispatch({ type: actions.CONTACT_LIST__SYNC_CONTACTS__START });
        try {
            await syncContacts();
            dispatch({ type: actions.CONTACT_LIST__SYNC_CONTACTS__FINISH });
        } catch (err) {
            dispatch({
                type: actions.CONTACT_LIST__SYNC_CONTACTS__ERROR,
                error: err,
            });
        }
    };
}

function onRestartSyncContacts() {
    return (dispatch) => {
        dispatch({ type: actions.CONTACT_LIST__SYNC_CONTACTS__RESTART });
    };
}

function onContactFieldChange(value, fieldName) {
    return (dispatch) => {
        dispatch({
            type:
                actions[
                    `CONTACT_LIST__CHANGE_CONTACT_${fieldName.toUpperCase()}`
                ],
            value,
        });
    };
}

function onOpenEditContactPopup(id, activeItem) {
    return (dispatch, getState) => {
        const {
            contactList: { contacts },
        } = getState();
        const currentContact =
            contacts.find((contact) => {
                return contact.id === id;
            }) || null;

        dispatch({
            type: actions.CONTACT_LIST__OPEN_EDIT_CONTACT_POPUP,
            id: currentContact.id,
            tags: currentContact.tags,
            edrpou: currentContact.edrpou,
        });
        if (currentContact) {
            dispatch({
                type: actions.CONTACT_LIST__SET_CONTACT_EDIT_MENU_ITEM,
                value: activeItem || 'general',
            });
            dispatch({
                type: actions.CONTACT_LIST__CHANGE_CONTACT_NAME,
                value: currentContact.name || '',
            });
            dispatch({
                type: actions.CONTACT_LIST__CHANGE_CONTACT_SHORTNAME,
                value: currentContact.shortName || '',
            });
        }
    };
}

function onCloseEditPopup(popupName) {
    return (dispatch) => {
        dispatch({
            type:
                actions[
                    `CONTACT_LIST__CLOSE_EDIT_${popupName.toUpperCase()}_POPUP`
                ],
        });
    };
}

function onOpenContactsInvitePopup() {
    return (dispatch) => {
        dispatch({ type: appActions.APP__SHOW_INVITE_RECIPIENTS_POPUP });
    };
}

function onSubmitContactData(formTracker) {
    return async (dispatch, getState) => {
        const {
            contactList: {
                currentContactId,
                companyNameValue,
                companyShortNameValue,
            },
        } = getState();
        const flags = getAppFlags(getState());
        const data = {
            company_name: companyNameValue,
            company_short_name: companyShortNameValue,
        };

        try {
            await updateContact(currentContactId, data);
            if (flags.ENABLE_CONTACT_ES_SEARCH) {
                // хак для того, щоб не чекати завершення індексації
                await waitContactES(
                    [companyNameValue, companyShortNameValue]
                        .filter(Boolean)
                        .join(' '),
                );
            }
            dispatch(onLoadContacts());
            dispatch(onCloseEditPopup('contact'));
        } catch (err) {
            formTracker.validationError(
                'form_contact_edit_contractor',
                err.message,
            );
            dispatch({
                type: actions.CONTACT_LIST__SHOW_ERROR_MESSAGE,
                errorMessage: err.message,
            });
        }
    };
}

function onPersonFieldChange(value, fieldName) {
    return (dispatch) => {
        dispatch({
            type:
                actions[
                    `CONTACT_LIST__CHANGE_PERSON_${fieldName.toUpperCase()}`
                ],
            value,
        });
    };
}

function onPersonMainFlagChange() {
    return (dispatch, getState) => {
        const {
            contactList: { personIsMainContactValue },
        } = getState();
        dispatch({
            type: actions.CONTACT_LIST__CHANGE_MAIN_CONTACT_FLAG,
            personIsMainContactValue: !personIsMainContactValue,
        });
    };
}

function onSetCurrentContactData(id) {
    return (dispatch, getState) => {
        const {
            contactList: { contacts },
        } = getState();
        const currentContact = contacts.find((contact) => {
            return (
                contact.persons.filter((person) => {
                    return person.id === id;
                }).length > 0
            );
        });
        const currentPerson = currentContact.persons.find(
            (person) => person.id === id,
        );
        // Such as contact person can have multiple emails (from non frontend sources),
        // select first ordered phone and after update all phones
        // will be rewritten on beckend and contact person will have only one phone.
        const phones = currentPerson.phones
            .filter((item) => item.phone)
            .sort((item) => item.phone);
        const phone = phones.length > 0 ? phones[0].phone : '';
        dispatch({
            type: actions.CONTACT_LIST__SET_PERSON_DATA,
            currentContactEdrpou: currentContact.edrpou || '',
            currentPerson: currentPerson || '',
            personEmailValue: currentPerson.email || '',
            personEmailHidden: currentPerson.isEmailHidden,
            personEdrpouValue: currentContact.edrpou || '',
            personPhoneValue: phone || '',
            personNameValue: currentPerson.firstName || '',
            personSecondNameValue: currentPerson.secondName || '',
            personLastNameValue: currentPerson.lastName || '',
            personIsMainContactValue: currentPerson.mainRecipient,
        });
    };
}

function onOpenEditPersonPopup({ id, edrpou } = {}) {
    return (dispatch) => {
        dispatch({ type: actions.CONTACT_LIST__OPEN_EDIT_PERSON_POPUP });
        if (id) {
            dispatch(onSetCurrentContactData(id));
        }
        if (edrpou) {
            dispatch({
                type: actions.CONTACT_LIST__SET_PERSON_DATA,
                currentPerson: null,
                personEmailValue: '',
                personEdrpouValue: edrpou,
                personEmailHidden: false,
                personPhoneValue: '',
                personNameValue: '',
                personSecondNameValue: '',
                personLastNameValue: '',
                personIsMainContactValue: false,
            });
        }
    };
}

function onOpenDeletePopup(personId, currentContactEdrpou) {
    return (dispatch, getState) => {
        const {
            contactList: { contacts, currentPerson },
        } = getState();
        const person = contacts
            .find((item) => item.edrpou === currentContactEdrpou)
            .persons.find((item) => item.id === personId);
        const currentPersonData = person || currentPerson;
        dispatch({
            type: actions.CONTACT_LIST__OPEN_DELETE_POPUP,
            currentPerson: currentPersonData,
            currentContactEdrpou,
        });
    };
}

function onCloseDeletePopup() {
    return (dispatch) => {
        dispatch({ type: actions.CONTACT_LIST__CLOSE_DELETE_POPUP });
    };
}

function onDeletePerson(personId) {
    return async (dispatch, getState) => {
        const {
            contactList: { currentPerson },
        } = getState();
        const id = personId || currentPerson.id;
        await deleteContactPerson(id);
        dispatch(onLoadContacts());
        dispatch(onCloseDeletePopup());
    };
}

function onSubmitPersonData(formTracker) {
    return async (dispatch, getState) => {
        const {
            contactList: {
                currentPerson,
                personIsMainContactValue,
                personEmailValue,
                personEmailHidden,
                personEdrpouValue,
                personPhoneValue,
                personNameValue,
                personSecondNameValue,
                personLastNameValue,
            },
        } = getState();
        const flags = getAppFlags(getState());
        const isValidEmail =
            personEmailHidden ||
            EMAIL_PATTERN.test(personEmailValue) ||
            !personEmailValue;
        const isValidEdrpou = EDRPOU_PATTERN.test(personEdrpouValue);

        const personData = {
            edrpou: personEdrpouValue,
            first_name: personNameValue,
            email:
                personEmailHidden || !personEmailValue
                    ? null
                    : personEmailValue,
            is_email_hidden: personEmailHidden || false,
            second_name: personSecondNameValue,
            last_name: personLastNameValue,
            main_recipient: `${personIsMainContactValue}`,
            phone: personPhoneValue,
        };
        if (isValidEmail && isValidEdrpou) {
            try {
                if (currentPerson && currentPerson.id) {
                    await updateContactPerson(currentPerson.id, personData);
                    eventTracking.sendEvent(
                        'contacts',
                        'edit_contact_contractor',
                    );
                } else {
                    delete personData.is_email_hidden;
                    await createContactPerson(personData);
                    eventTracking.sendEvent(
                        'contacts',
                        'add_contact_contractor',
                    );
                }

                if (flags.ENABLE_CONTACT_ES_SEARCH) {
                    // хак для того, щоб не чекати завершення індексації
                    await waitContactES(
                        [
                            personData.edrpou,
                            personData.email,
                            personData.first_name,
                            personData.last_name,
                            personData.second_name,
                        ]
                            .filter(Boolean)
                            .join(' '),
                    );
                }

                dispatch(onLoadContacts());
                dispatch(onCloseEditPopup('person'));
            } catch (err) {
                formTracker.validationError(
                    'form_contact_edit_contractor',
                    err.message,
                );
                dispatch({
                    type: actions.CONTACT_LIST__SHOW_ERROR_MESSAGE,
                    errorMessage: err.message,
                });
            }
        } else {
            dispatch({
                type: actions.CONTACT_LIST__SHOW_ERROR_MESSAGE,
                errorMessage: t`Виникла помилка, перевірте введені дані`,
                isInvalidEmail: !isValidEmail,
                isInvalidEdrpou: !isValidEdrpou,
            });
        }
    };
}

function onInviteCompanyContacts(edrpou) {
    return async (dispatch, getState) => {
        const {
            contactList: { contacts },
        } = getState();
        const personsToInvite = contacts
            .find((contact) => contact.edrpou === edrpou)
            .persons.filter(({ email }) => email);
        try {
            const inviteRequests = [];
            for (const person of personsToInvite) {
                inviteRequests.push(
                    auth.inviteUser({ email: person.email, edrpou }),
                );
            }

            await Promise.all(inviteRequests);

            const alreadyInvitedContacts =
                getSessionStorageItem(
                    constants.CONTACTS_ALREADY_INVITED_STORAGE_NAME,
                ) || [];
            setSessionStorageItem(
                constants.CONTACTS_ALREADY_INVITED_STORAGE_NAME,
                [...alreadyInvitedContacts, edrpou],
            );
            dispatch({
                type: actions.CONTACT_LIST__UPDATE_ALREADY_INVITED,
                alreadyInvitedContacts: [...alreadyInvitedContacts, edrpou],
            });
            eventTracking.sendEvent(
                'invite',
                'send_invite_company_contractor',
                `${personsToInvite.length}`,
            );
        } catch (err) {
            const company = t`Компанія`;
            dispatch(
                notificationActionCreators.addNotification({
                    title: t`Запрошення контактів`,
                    textType: 'error',
                    text: `${company} ${edrpou}: ${err.reason}`,
                    type: 'text',
                    showCloseButton: true,
                }),
            );
        }
    };
}

function onOpenTagsAccessPopup() {
    return (dispatch, getState) => {
        const isAccess = getCurrentCompanyPermissionMap(getState())[
            PermissionCategory.TAGS
        ];

        if (!isAccess) {
            dispatch(
                setTrialInfoOpenTargetPopup(
                    'tags',
                    'contacts-list/toolbar/edit-tags',
                ),
            );

            return;
        }

        dispatch(contactsTagsEditPopupActionCreators.onOpenPopup());
    };
}

function handleDeleteTag(tag, contactId) {
    return async (dispatch) => {
        if (
            dispatch(
                proRateInfoPopupActionCreators.showProRatePopupToCompany(
                    'contacts-list/popover/delete-tag',
                    PermissionCategory.TAGS,
                ),
            )
        )
            return;

        try {
            await deleteContactsTags({
                tags_ids: [tag.id],
                contacts_ids: [contactId],
            });
            dispatch(onLoadContacts());
            // dispatch(onGetTagsFilers());
        } catch (err) {
            // error handling
        }
    };
}

export default {
    onImportContacts,
    onItemsPerPageChange,
    onLoadContacts,
    onRestartSyncContacts,
    onRouteQueryChange,
    onShow,
    onSyncContacts,
    onPersonFieldChange,
    onPersonMainFlagChange,
    onOpenEditPersonPopup,
    onOpenContactsInvitePopup,
    onCloseEditPopup,
    onSubmitPersonData,
    onOpenDeletePopup,
    onCloseDeletePopup,
    onDeletePerson,
    onSetCurrentContactData,
    onSetFilter,
    onContactFieldChange,
    onOpenEditContactPopup,
    onSubmitContactData,
    onInviteCompanyContacts,
    onOpenTagsAccessPopup,
    handleDeleteTag,
};
