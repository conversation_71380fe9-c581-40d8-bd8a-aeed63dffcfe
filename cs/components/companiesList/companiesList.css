.titleContainer {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
    gap: 15px;
}

.titleContainer h1 {
    white-space: nowrap;
}

.titleContainer * {
    padding: 0;
    margin: 0;
}

.button {
    font-size: 14px;
    font-weight: 700;
}

.addCompanyButton {
    width: 240px;
}

.listSubTitle {
    margin-bottom: 16px;
    color: var(--content-color);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
}

.company li {
    height: auto;
    min-height: 90px;
    margin: 20px 0;
}

.company a {
    height: 100%;
}

.currentCompany li {
    border: 1px solid var(--green-color);
    border-radius: var(--border-radius);
}

.companyTitleContainer {
    display: flex;
    flex-direction: column;
    margin-left: 15px;
    gap: 8px;
}

.table {
    display: flex;
    height: 100%;
    align-items: center;
    padding: 20px 30px;
}

.cell {
    display: table-cell;
    vertical-align: middle;
}

.cell + .cell {
    padding-left: 10px;
}

.otherCompaniesTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 40px 0 20px;
}

.otherCompaniesTitle span {
    color: var(--grey-color);
}

.companyLI {
    margin-top: 20px;
}

.companyLI:first-child {
    margin-top: 0;
}
