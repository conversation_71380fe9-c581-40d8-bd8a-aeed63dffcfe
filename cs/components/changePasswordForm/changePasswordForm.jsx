import React from 'react';

import { Button, FlexBox, PasswordInput } from '@vchasno/ui-kit';

import { validatePassword } from 'lib/helpers';
import { getPasswordRuleByEmail } from 'lib/ts/helpers';
import PropTypes from 'prop-types';
import auth from 'services/auth';
import { t } from 'ttag';
import StatusButton from 'ui/StatusButton/StatusButton';

// styles
import css from './changePasswordForm.css';

class ChangePasswordForm extends React.Component {
    static propTypes = {
        isAutogeneratedPassword: PropTypes.bool,
    };
    state = {
        pass: '',
        newPass: '',
        invalidPassMessage: '',
        incorrectPass: false,
        errorMessage: '',
        isLoading: false,
        updatedSuccessful: false,
    };

    handlePass = (evt) => {
        this.setState({ pass: evt.target.value, updatedSuccessful: false });
    };

    handleNewPass = (evt) => {
        this.setState({
            newPass: evt.target.value,
            invalidPassMessage: '',
            updatedSuccessful: false,
        });
    };

    submitForm = async (evt) => {
        evt.preventDefault();

        const invalidPassMessage = validatePassword(this.state.newPass);

        if (invalidPassMessage) {
            this.setState({ invalidPassMessage });
        } else {
            try {
                this.setState({ isLoading: true });
                await auth.updatePass({
                    current_password: this.state.pass,
                    new_password: this.state.newPass,
                    isAutogeneratedPassword: this.props.isAutogeneratedPassword,
                });
                this.setState({
                    pass: '',
                    newPass: '',
                    incorrectPass: false,
                    isLoading: false,
                    updatedSuccessful: true,
                });
            } catch (err) {
                this.setState({
                    incorrectPass: true,
                    errorMessage: err.details?.current_password || err.message,
                    invalidPassMessage: err.details?.new_password,
                    isLoading: false,
                });
            }
        }
    };

    render() {
        const passwordMinLength = getPasswordRuleByEmail(this.props.email)
            .minLength;

        return (
            <form onSubmit={this.submitForm} className={css.form}>
                {!this.props.isAutogeneratedPassword && (
                    <PasswordInput
                        key="current-password"
                        wide
                        id="current-password"
                        autoComplete="current-password"
                        label={t`Старий пароль`}
                        value={this.state.pass}
                        onChange={this.handlePass}
                        error={this.state.errorMessage}
                        dataQaErr="qa_password_error"
                    />
                )}
                <PasswordInput
                    wide
                    key="new-password"
                    id="newPassword"
                    label={t`Новий пароль`}
                    autoComplete="new-password"
                    hint={t`Пароль повинен містити щонайменше ${passwordMinLength} символів, хоча б одну цифру і один символ ( ! ' # $ % * - / : ; = ? )`}
                    value={this.state.newPass}
                    onChange={this.handleNewPass}
                    error={this.state.invalidPassMessage}
                />
                <FlexBox>
                    {this.state.updatedSuccessful ? (
                        <StatusButton dataQa="qa_saved">{t`Збережено`}</StatusButton>
                    ) : (
                        <Button
                            theme="secondary"
                            type="submit"
                            loading={this.state.isLoading}
                            dataQa="qa_save"
                        >
                            {t`Зберегти`}
                        </Button>
                    )}
                </FlexBox>
            </form>
        );
    }
}

export default ChangePasswordForm;
